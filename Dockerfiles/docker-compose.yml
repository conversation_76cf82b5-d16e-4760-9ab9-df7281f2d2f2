services:
  # 后端服务
  yjzs_backend:
    image: **************:5000/foshan730/backend:${CI_COMMIT_SHA:-latest}
    container_name: foshan730_backend
    ports:
      - "18082:8080"  # API端口
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /usr/share/fonts:/usr/share/fonts:ro
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - TZ=Asia/Shanghai
      - AGENT_JSON_RPC_SERVER_BIND_HOST=**************:18082

  # 前端服务
  yjzs_front:
    image: **************:5000/foshan730/frontend:${CI_COMMIT_SHA:-latest}
    container_name: foshan730_frontend
    ports:
      - "15175:80"  # 前端端口
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /usr/share/fonts:/usr/share/fonts:ro
