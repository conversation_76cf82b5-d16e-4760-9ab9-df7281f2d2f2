# Vue 3 + TypeScript + Vite

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about the recommended Project Setup and IDE Support in the [Vue Docs TypeScript Guide](https://vuejs.org/guide/typescript/overview.html#project-setup).



于选择和管理法条的Vue组件，支持法条的展示、搜索、选择和删除功能。
### 基本用法

```vue
<template>
  <div>
    <LegalProvisionSelector v-model="selectedProvisions" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LegalProvisionSelector } from '@/components/LegalProvisionSelector'
import type { LegalProvisionRecord } from '@/api/legalProvision'

const selectedProvisions = ref<LegalProvisionRecord[]>([])
</script>
```