import fs from 'fs';
import { exec } from 'child_process';
 
// 封装命令
function execute(command) {
  return new Promise((resolve, reject) => {
    exec(command, function(err, stdout, stderr) {
      if (err != null) {
        resolve(err)
      } else {
        resolve(stdout)
      }
    })
  })
}
 
async function start() {
  // 执行npm打包命令
  console.log("开始打包...")
  const buildInfo = await execute("npm run vite-build")
  console.log(buildInfo)
  console.log("打包完成")
  console.log("开始写入git日志...")
  // 执行git log命令 查看当前分支提交历史
  const _history = await execute("git log -5 --pretty=\"short\"")
  // 日志拼接
  let newStr = '构建时间：' + new Date().toLocaleString()+ '\n\n最后五次提交: \n' +  _history
  // 将日志写入log.txt
  fs.writeFile('dist/log.txt', newStr, 'utf-8', function(err) {
    if (err != null) {
      console.log("ERROR:", err)
    } else {
      console.log("git日志写入完成")
    }
  })
  
}
 
// 执行任务
start()