import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    base: '/',
    plugins: [
      vue(),
      {
        name: 'html-env-replace',
        transformIndexHtml: (html) => {
          return html
            .replace('%VITE_API_BASE_URL%', env.VITE_API_BASE_URL || 'http://localhost:8080')
            .replace('%VITE_SYSTEM_TITLE%', env.VITE_SYSTEM_TITLE || '文书生成辅助工具')
        }
      }
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    server: {
      host: '0.0.0.0', // 允许所有 IP 访问
      port: 5173, // 默认端口号
      open: true // 自动打开浏览器
    }
  }
})
