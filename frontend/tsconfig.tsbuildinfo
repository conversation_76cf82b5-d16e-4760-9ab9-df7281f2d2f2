{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/api/case.ts", "./src/api/config.ts", "./src/api/contradiction.ts", "./src/api/disputefocus.ts", "./src/api/index.ts", "./src/api/prompt.ts", "./src/api/relationgraph.ts", "./src/api/timeline.ts", "./src/components/vuepdfembed/composables.ts", "./src/components/vuepdfembed/index.essential.ts", "./src/components/vuepdfembed/index.ts", "./src/components/vuepdfembed/types.ts", "./src/components/vuepdfembed/utils.ts", "./src/components/import/index.ts", "./src/router/index.ts", "./src/types/api.ts", "./src/types/qs.d.ts", "./src/utils/api-helper.ts", "./src/utils/http.ts", "./src/app.vue", "./src/components/appheader.vue", "./src/components/prompthistorypanel.vue", "./src/components/regeneratedialog.vue", "./src/components/vuepdfembed/vuepdfembed.vue", "./src/components/import/caseimportwizard.vue", "./src/views/case/detail.vue", "./src/views/case/index.vue", "./src/views/case/list.vue", "./src/views/case/components/casetimelinegraph.vue", "./src/views/case/components/conflitindentify.vue", "./src/views/case/components/disputefocuseditdialog.vue", "./src/views/case/components/disputepoints.vue", "./src/views/case/components/fileupload.vue", "./src/views/case/components/leftpanel.vue", "./src/views/case/components/recyclebin.vue", "./src/views/case/components/relationgraph.vue"], "errors": true, "version": "5.8.3"}