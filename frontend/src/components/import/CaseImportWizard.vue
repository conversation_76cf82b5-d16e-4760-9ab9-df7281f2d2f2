<template>
  <el-dialog
    v-model="visible"
    title=""
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    class="import-wizard-dialog"
    center
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <div class="step-indicator">
      <div class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <div class="step-label">选择文件</div>
      </div>
      <div class="step-line" :class="{ active: currentStep > 1 }"></div>
      <div class="step-item" :class="{ active: currentStep >= 2 }">
        <div class="step-number">2</div>
        <div class="step-label">上传文件</div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="wizard-content">
      <div class="import-content">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="robot-container">
            <img src="@/assets/reboot.gif" alt="小天枢机器人" class="robot-gif" />
          </div>
          <h2 class="welcome-title">您好，我是小天枢</h2>
        </div>

        <!-- 上传区域 -->
        <div class="upload-section">
          <div class="upload-container">
            <el-upload
            ref="uploadRef"
            class="upload-area"
            :class="{ 'drag-over': isDragOver, 'has-files': fileList.length > 0 }"
            drag
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
            :show-file-list="false"
            accept=".zip"
            :limit="10"
            :before-upload="beforeUpload"
            @dragover="isDragOver = true"
            @dragleave="isDragOver = false"
            @drop="isDragOver = false"
          >
            <div class="upload-placeholder">
              <el-icon class="upload-icon">
                <UploadFilled v-if="!isDragOver" />
                <FolderOpened v-else />
              </el-icon>
              <div class="upload-text">
                <span v-if="!isDragOver">小天枢为您提供智能阅卷服务，请拖拽文件到此处或点击上传</span>
                <span v-else class="drag-text">释放文件开始上传</span>
              </div>
              <div class="upload-hint">支持 ZIP 格式</div>
            </div>
          </el-upload>

          <!-- 文件列表 -->
          <div v-if="fileList.length > 0" class="file-list">
            <div class="file-list-header">
              <span>已选择文件 ({{ fileList.length }})</span>
              <el-button type="text" size="small" @click="clearAllFiles">清空</el-button>
            </div>
            <div class="file-items">
              <div v-for="file in fileList" :key="file.uid" class="file-item" :class="{ error: file.status === 'fail' }">
                <div class="file-info">
                  <el-icon class="file-icon">
                    <Document v-if="file.status !== 'fail'" />
                    <WarningFilled v-else />
                  </el-icon>
                  <div class="file-details">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  </div>
                  <div class="file-actions">
                    <el-icon class="delete-icon" @click="removeFile(file)">
                      <Delete />
                    </el-icon>
                  </div>
                </div>
                <el-progress
                  v-if="file.status === 'uploading'"
                  :percentage="file.percentage || 0"
                  :show-text="false"
                  class="file-progress"
                />
                <div v-if="file.status === 'fail'" class="file-error">
                  上传失败，请重试
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="wizard-footer">
        <div class="upload-actions">
          <el-button
            type="primary"
            size="large"
            :loading="uploading"
            :disabled="uploading || !hasFiles"
            @click="handleUpload"
            class="upload-btn"
          >
            <el-icon v-if="!uploading"><Upload /></el-icon>
            {{ uploading ? '上传中...' : '开始上传' }}
          </el-button>
          <el-button @click="handleClose" size="large">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { UploadFilled, Document, Delete, Loading, FolderOpened, WarningFilled, Upload, CircleCheck } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
import { caseApi } from '@/api'

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const currentStep = ref(2)
const fileList = ref<UploadFiles>([])
const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const isDragOver = ref(false)

// 计算属性
const hasFiles = computed(() => fileList.value.length > 0)

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetWizard()
  }
})

// 重置向导
const resetWizard = () => {
  currentStep.value = 2
  fileList.value = []
  uploading.value = false
  isDragOver.value = false
}

// 文件大小格式化
const formatFileSize = (size?: number) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(1)} ${units[index]}`
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isZip = file.name.toLowerCase().endsWith('.zip')
  const isLt2G = file.size / 1024 / 1024 / 1024 < 2

  if (!isZip) {
    ElMessage.error('只支持 ZIP 格式文件')
    return false
  }
  if (!isLt2G) {
    ElMessage.error('文件大小不能超过 2GB')
    return false
  }
  return true
}

// 处理文件变化
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  if (!file.name.toLowerCase().endsWith('.zip')) {
    ElMessage.error('只支持 ZIP 格式文件')
    return
  }

  if (file.size && file.size > 2 * 1024 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 2GB')
    return
  }

  file.percentage = 0
  file.status = 'ready'
  fileList.value = files.filter(f => f.name.toLowerCase().endsWith('.zip'))
}

// 删除文件
const removeFile = (file: UploadFile) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 清空所有文件
const clearAllFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
}

// 开始上传
const startUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的卷宗材料文件')
    return
  }

  try {
    // 更新文件状态为上传中
    fileList.value.forEach(file => {
      file.status = 'uploading'
      file.percentage = 0
    })

    const filesToUpload = fileList.value.map(file => file.raw as File)
    await caseApi.uploadBatch(filesToUpload, '1')

    // 上传成功
    fileList.value.forEach(file => {
      file.status = 'success'
      file.percentage = 100
    })

    emit('success')
    handleClose()
  } catch (error) {
    fileList.value.forEach(file => {
      file.status = 'fail'
    })
    ElMessage.error('上传失败，请重试')
  }
}


// 关闭对话框
const handleClose = () => {
  visible.value = false
}



// 处理上传按钮点击
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的卷宗材料文件')
    return
  }
  uploading.value = true
  startUpload().finally(() => {
    uploading.value = false
  })
}
</script>

<style scoped lang="scss">
.import-wizard-dialog {
  :deep(.el-overlay) {
    background: rgba(0, 0, 0, 0.4) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    min-height: 450px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;

    @media (max-width: 768px) {
      width: 90% !important;
      min-height: 400px !important;
      border-radius: 16px !important;
    }
  }

  :deep(.el-dialog__header) {
    display: none !important;
  }

  :deep(.el-dialog__body) {
    padding: 32px 32px 20px 32px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;

    @media (max-width: 768px) {
      padding: 24px 24px 16px 24px !important;
    }
  }

  :deep(.el-dialog__footer) {
    padding: 20px 32px 32px 32px !important;

    @media (max-width: 768px) {
      padding: 16px 24px 24px 24px !important;
    }
  }

  :deep(.el-dialog__close) {
    color: #6b7280 !important;
    font-size: 18px !important;

    &:hover {
      color: #374151 !important;
    }
  }
}

// 步骤指示器
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  padding: 0 20px;

  @media (max-width: 768px) {
    margin-bottom: 20px;
    padding: 0 10px;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #e5e7eb;
      color: #9ca3af;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;

      @media (max-width: 768px) {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }
    }

    .step-label {
      margin-top: 8px;
      font-size: 14px;
      color: #6b7280;
      font-weight: 500;

      @media (max-width: 768px) {
        font-size: 12px;
        margin-top: 6px;
      }
    }

    &.active {
      .step-number {
        background: #3b82f6;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      .step-label {
        color: #3b82f6;
      }
    }

    &.completed {
      .step-number {
        background: #10b981;
        color: white;
      }

      .step-label {
        color: #10b981;
      }
    }
  }

  .step-line {
    width: 80px;
    height: 2px;
    background: #e5e7eb;
    margin: 0 16px;
    transition: all 0.3s ease;

    @media (max-width: 768px) {
      width: 60px;
      margin: 0 12px;
    }

    &.active {
      background: #3b82f6;
    }
  }
}

.wizard-content {
  flex: 1;
  display: flex;
  flex-direction: column;

  .import-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;

    // 欢迎区域样式
    .welcome-section {
      text-align: center;
      padding: 20px 0;

      .robot-container {
        margin-bottom: -20px;

        .robot-gif {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          object-fit: contain;
          animation: float 3s ease-in-out infinite;

          @media (max-width: 768px) {
            width: 80px;
            height: 80px;
          }
        }
      }

      .welcome-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 20px;
        }
      }

      .welcome-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    // 上传区域样式
    .upload-section {
      width: 100%;

      .upload-container {
        margin-bottom: 20px;
      }
    }
  }
}

// 上传区域
.upload-area {
  margin-bottom: 20px;

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed #d1d5db;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(243, 244, 246, 0.9) 100%);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    @media (max-width: 768px) {
      height: 160px;
      border-radius: 12px;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: #3b82f6;
      background: linear-gradient(135deg, rgba(240, 249, 255, 0.9) 0%, rgba(219, 234, 254, 0.9) 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);

      &::before {
        opacity: 1;
      }
    }
  }

  &.drag-over :deep(.el-upload-dragger) {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(236, 253, 245, 0.9) 0%, rgba(209, 250, 229, 0.9) 100%);
    transform: scale(1.02);
    box-shadow: 0 12px 30px rgba(16, 185, 129, 0.2);
  }

  &.has-files :deep(.el-upload-dragger) {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(236, 253, 245, 0.9) 0%, rgba(209, 250, 229, 0.9) 100%);
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    position: relative;
    z-index: 1;
    padding: 20px;

    .upload-icon {
      font-size: 48px;
      color: #9ca3af;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      @media (max-width: 768px) {
        font-size: 40px;
        margin-bottom: 12px;
      }
    }

    .upload-text {
      font-size: 16px;
      color: #6b7280;
      font-weight: 500;
      margin-bottom: 8px;
      text-align: center;

      @media (max-width: 768px) {
        font-size: 14px;
      }

      .drag-text {
        color: #10b981;
        font-weight: 600;
      }
    }

    .upload-hint {
      font-size: 12px;
      color: #9ca3af;
      text-align: center;
    }
  }

  &:hover .upload-placeholder .upload-icon,
  &.drag-over .upload-placeholder .upload-icon {
    color: #3b82f6;
    transform: scale(1.1);
  }

  &.drag-over .upload-placeholder .upload-icon {
    color: #10b981;
  }
}

// 文件列表
.file-list {
  margin-top: 24px;

  .file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;

    span {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
  }

  .file-items {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
  }

  .file-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f3f4f6;
    }

    &.error {
      background: #fef2f2;
      border-color: #fecaca;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .file-icon {
        font-size: 20px;
        color: #3b82f6;
        flex-shrink: 0;

        &.error {
          color: #ef4444;
        }
      }

      .file-details {
        flex: 1;
        min-width: 0;

        .file-name {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          display: block;
          font-size: 12px;
          color: #6b7280;
          margin-top: 2px;
        }
      }

      .file-actions {
        .delete-icon {
          font-size: 16px;
          color: #ef4444;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            background: #fee2e2;
            color: #dc2626;
          }
        }
      }
    }

    .file-progress {
      margin-top: 8px;
    }

    .file-error {
      margin-top: 8px;
      font-size: 12px;
      color: #ef4444;
    }
  }
}

.wizard-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  min-height: 20px; // 保持底部空间，即使没有按钮
}

// 上传按钮
.upload-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    gap: 12px;
  }

  .el-button {
    border-radius: 12px;
    font-weight: 600;
    padding: 14px 32px;
    font-size: 16px;
    transition: all 0.3s ease;
    min-width: 120px;

    @media (max-width: 768px) {
      padding: 12px 24px;
      font-size: 14px;
      min-width: 100px;
    }

    &.el-button--default {
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(209, 213, 219, 0.8);
      color: #6b7280;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        border-color: #3b82f6;
        color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    justify-content: center;

    &:disabled {
      background: #f3f4f6 !important;
      color: #9ca3af !important;
      cursor: not-allowed !important;
      transform: none !important;
      box-shadow: none !important;
    }

    &:not(:disabled) {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      border: none;
      color: white;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
      }

      &:active {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }

    &.is-loading {
      pointer-events: none;
    }
  }
}


// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式优化
@media (max-width: 480px) {
  .import-wizard-dialog {
    :deep(.el-dialog) {
      width: 98% !important;
      margin: 1% !important;
      border-radius: 12px !important;
    }

    :deep(.el-dialog__body) {
      padding: 12px !important;
    }

    :deep(.el-dialog__footer) {
      padding: 0 12px 12px 12px !important;
    }
  }

  .step-indicator {
    .step-line {
      width: 40px;
      margin: 0 8px;
    }

    .step-item {
      .step-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .step-label {
        font-size: 11px;
      }
    }
  }

  .upload-area :deep(.el-upload-dragger) {
    height: 140px;
  }

  .upload-placeholder {
    padding: 16px !important;

    .upload-icon {
      font-size: 32px !important;
    }

    .upload-text {
      font-size: 13px !important;
    }

    .upload-hint {
      font-size: 11px !important;
    }
  }
}
</style>
