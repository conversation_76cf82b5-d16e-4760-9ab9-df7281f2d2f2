<template>
  <div></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { getChromeRecommendation } from '@/api/version';
import { getChromeVersion, isChromeVersionBelow, getOS } from '@/utils/systemDetector';
import { ElMessageBox } from 'element-plus';

/**
 * 检查Chrome浏览器版本并在需要时提示用户更新
 */
const checkChromeVersion = async () => {
  try {
    // 获取当前Chrome版本
    const currentVersion = getChromeVersion();
    
    // 如果不是Chrome浏览器，不进行检查
    if (currentVersion === null) {
      return;
    }
    
    // 获取推荐的Chrome版本
    const os = getOS();
    const recommendation = await getChromeRecommendation(os);
    
    // 使用isChromeVersionBelow比较版本并提示用户
    if (isChromeVersionBelow(recommendation.version)) {
      ElMessageBox.alert(
        `您正在使用的是低版本浏览器，建议您升级浏览器版本以获得更好的AI体验。`,
        '升级提示',
        {
          confirmButtonText: '前往下载',
          cancelButtonText: '稍后提醒',
          type: 'warning',
          distinguishCancelAndClose: true,
          callback: (action: string) => {
            if (action === 'confirm') {
              window.open(recommendation.downloadUrl, '_blank');
            }
          }
        }
      );
    }
  } catch (error) {
    console.warn('检查浏览器版本时出错:', error);
  }
};

onMounted(() => {
  // 页面加载后检查浏览器版本
  checkChromeVersion();
});
</script>