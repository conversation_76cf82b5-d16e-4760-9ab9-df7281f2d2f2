<template>
  <div
    v-if="finalShow"
    class="red-dot-container"
    :class="[
      `red-dot--${size}`,
      { 'red-dot--pulse': pulse }
    ]"
  >
    <div class="red-dot" />
    <span v-if="text" class="red-dot-text">{{ text }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useModuleUpdate } from '@/composables/useModuleUpdate'
import { ModuleType, type ModuleTypeValue, moduleUpdateApi } from '@/api/moduleUpdate'
import { eventBus } from '@/utils/eventBus'

interface Props {
  /** 是否显示红点 */
  show?: boolean
  /** 提示文本 */
  text?: string
  /** 是否启用脉冲动画 */
  pulse?: boolean
  /** 尺寸大小 */
  size?: 'small' | 'medium' | 'large'
  /** 案件导入ID - 用于自动检查模块状态 */
  caseImportId?: number | null
  /** 要监听的模块代码（源模块） */
  moduleCode?: ModuleTypeValue
  /** 要检查更新状态的目标模块代码 */
  operatedModule?: ModuleTypeValue
  /** 是否启用事件监听 */
  enableEventListener?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  text: '',
  pulse: true,
  size: 'medium',
  caseImportId: null,
  moduleCode: undefined,
  operatedModule: undefined,
  enableEventListener: false
})

// 内部状态
const internalShow = ref(props.show)

// 内部模块状态
const moduleNeedsUpdate = ref(false)

// 模块更新状态管理 - 只在需要时使用
const {
  checkModuleUpdateNew,
  markModuleAsOperated
} = useModuleUpdate(props.caseImportId, {
  immediate: false
})

// 计算最终显示状态
const finalShow = computed(() => {
  if (props.operatedModule && props.caseImportId) {
    // 如果指定了要检查的目标模块代码和案件ID，使用内部模块状态
    return moduleNeedsUpdate.value
  }
  // 否则使用传入的show属性或内部状态
  return props.show || internalShow.value
})

// 检查特定模块状态（使用新逻辑）
const checkSpecificModuleStatus = async () => {
  if (props.moduleCode && props.operatedModule && props.caseImportId) {
    try {
      const needsUpdate = await checkModuleUpdateNew(props.moduleCode, props.operatedModule)
      moduleNeedsUpdate.value = needsUpdate
    } catch (error) {
      console.error('检查模块状态失败:', error)
      moduleNeedsUpdate.value = false
    }
  }
}

/**
 * 重置模块状态（使用新逻辑）
 * 当用户点击相关按钮后，调用此方法标记模块已操作
 * 该方法会异步调用后端API，并在成功后自动隐藏红点
 */
const resetStatus = async () => {
  if (!props.moduleCode || !props.operatedModule || !props.caseImportId) {
    return
  }
  
  // 如果当前没有红点，不调用接口
  if (!moduleNeedsUpdate.value) {
    return
  }
  
  try {
    // 调用新的markModuleAsOperated API
    // sourceModule是当前模块，operatedModule是要操作的目标模块
    await markModuleAsOperated(props.moduleCode, props.operatedModule)
    
    // 重置成功后，立即更新本地状态，隐藏红点
    moduleNeedsUpdate.value = false
  } catch (error) {
    console.error('重置模块状态失败:', error)
    // 发生错误时，重新检查状态
    checkSpecificModuleStatus()
  }
}

// 事件监听器
const handleModuleUpdateEvent = (event: { caseImportId: number; moduleCode: string; action: string }) => {
  console.log('RedDot 接收到 module:updated 事件:', event)
  if (props.caseImportId && event.caseImportId === props.caseImportId) {
    console.log('RedDot 事件匹配案件ID:', props.caseImportId, '目标模块代码:', props.operatedModule)
    // 只有当事件的模块代码匹配要检查的目标模块时才刷新
    if (!props.moduleCode || event.moduleCode === props.moduleCode) {
      console.log('RedDot 目标模块代码匹配，开始检查状态')
      checkSpecificModuleStatus()
    } else {
      console.log('RedDot 目标模块代码不匹配，跳过检查')
    }
  } else {
    console.log('RedDot 案件ID不匹配，跳过处理')
  }
}

// 红点重置事件监听器
const handleRedDotResetEvent = (event: { caseImportId: number; sourceModule: string; operatedModule: string }) => {
  console.log('RedDot 接收到 red-dot:reset 事件:', event)
  if (props.caseImportId && event.caseImportId === props.caseImportId && props.moduleCode === event.sourceModule) {
    console.log('RedDot 匹配重置事件，开始执行重置')
    // 当前模块匹配且有红点时，执行重置
    if (moduleNeedsUpdate.value) {
      markModuleAsOperated(event.sourceModule, event.operatedModule).then(() => {
        // 重置成功后，立即更新本地状态，隐藏红点
        moduleNeedsUpdate.value = false
      }).catch((error) => {
        console.error('重置模块状态失败:', error)
        // 发生错误时，重新检查状态
        checkSpecificModuleStatus()
      })
    }
  }
}

// 监听案件ID变化
watch(() => props.caseImportId, (newCaseId) => {
  if (newCaseId && props.operatedModule) {
    checkSpecificModuleStatus()
  }
})

// 监听目标模块代码变化
watch(() => props.operatedModule, (newOperatedModule) => {
  if (newOperatedModule && props.caseImportId) {
    checkSpecificModuleStatus()
  }
})

// 组件挂载时的处理
onMounted(() => {
  // 如果指定了目标模块代码和案件ID，立即检查状态
  if (props.operatedModule && props.caseImportId) {
    checkSpecificModuleStatus()
  }

  // 如果启用了事件监听，注册事件监听器
  if (props.enableEventListener) {
    eventBus.on('module:updated', handleModuleUpdateEvent)
    eventBus.on('red-dot:reset', handleRedDotResetEvent)
  }
})

// 组件卸载时的清理
onUnmounted(() => {
  if (props.enableEventListener) {
    eventBus.off('module:updated', handleModuleUpdateEvent)
    eventBus.off('red-dot:reset', handleRedDotResetEvent)
  }
})

// 暴露方法给父组件
defineExpose({
  /** 手动设置显示状态 */
  setShow: (show: boolean) => {
    internalShow.value = show
  },
  /** 手动刷新模块状态 */
  refresh: () => {
    if (props.operatedModule && props.caseImportId) {
      checkSpecificModuleStatus()
    }
  },
  /** 重置模块状态 */
  resetStatus
})
</script>

<style scoped>
.red-dot-container {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.red-dot {
  border-radius: 50%;
  background-color: #f56565;
  flex-shrink: 0;
}

/* 尺寸变体 */
.red-dot--small .red-dot {
  width: 7px;
  height: 7px;
}

.red-dot--medium .red-dot {
  width: 8px;
  height: 8px;
}

.red-dot--large .red-dot {
  width: 10px;
  height: 10px;
}

/* 脉冲动画 */
.red-dot--pulse .red-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 文本样式 */
.red-dot-text {
  font-size: 12px;
  color: #f56565;
  font-weight: 500;
  white-space: nowrap;
}

.red-dot--small .red-dot-text {
  font-size: 11px;
}

.red-dot--large .red-dot-text {
  font-size: 13px;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .red-dot {
    background-color: #fc8181;
  }
  
  .red-dot-text {
    color: #fc8181;
  }
}
</style>
