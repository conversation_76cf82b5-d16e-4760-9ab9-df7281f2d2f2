<template>
  <div class="test-container">
    <h2>TaskGeneratingStatus 测试页面</h2>
    
    <!-- 任务生成状态组件 -->
    <TaskGeneratingStatus
      ref="taskGeneratingRef"
      :case-import-id="123"
      :task-type="TaskType.LITIGATION_RELATION"
      @task-started="handleTaskStarted"
      @task-completed="handleTaskCompleted"
      @task-failed="handleTaskFailed"
    />
    
    <div class="controls">
      <el-button type="primary" @click="simulateRegenerate">
        模拟重新生成
      </el-button>
      
      <el-button type="success" @click="checkCurrentStatus">
        检查当前状态
      </el-button>
      
      <el-button type="warning" @click="stopMonitoring">
        停止监控
      </el-button>
    </div>
    
    <div class="status-info">
      <h3>状态信息：</h3>
      <pre>{{ JSON.stringify(statusInfo, null, 2) }}</pre>
    </div>
    
    <div class="logs">
      <h3>日志：</h3>
      <div v-for="(log, index) in logs" :key="index" class="log-item">
        <span class="timestamp">{{ log.timestamp }}</span>
        <span class="message">{{ log.message }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'

const taskGeneratingRef = ref()
const logs = ref<Array<{ timestamp: string; message: string }>>([])
const statusInfo = reactive({
  isVisible: false,
  executionStatus: 'idle',
  currentStatus: null,
  elapsedTime: 0
})

// 添加日志
const addLog = (message: string) => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message
  })
  
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 模拟重新生成
const simulateRegenerate = async () => {
  addLog('开始模拟重新生成...')
  
  try {
    // 模拟调用重新生成 API
    addLog('调用重新生成 API...')
    
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    addLog('API 调用完成，开始监控任务状态...')
    
    // 通知组件开始监控任务状态
    await taskGeneratingRef.value?.startTaskMonitoring({
      maxRetries: 5,
      retryInterval: 2000
    })
    
    addLog('任务监控已启动')
  } catch (error) {
    addLog(`重新生成失败: ${error}`)
  }
}

// 检查当前状态
const checkCurrentStatus = async () => {
  if (taskGeneratingRef.value) {
    const state = taskGeneratingRef.value.state
    Object.assign(statusInfo, state)
    addLog(`当前状态: ${JSON.stringify(state)}`)
  }
}

// 停止监控
const stopMonitoring = () => {
  taskGeneratingRef.value?.stopAllTimers()
  addLog('已停止所有监控')
}

// 事件处理
const handleTaskStarted = (event: any) => {
  addLog(`任务开始: ${JSON.stringify(event)}`)
  Object.assign(statusInfo, taskGeneratingRef.value?.state || {})
}

const handleTaskCompleted = (event: any) => {
  addLog(`任务完成: ${JSON.stringify(event)}`)
  Object.assign(statusInfo, taskGeneratingRef.value?.state || {})
}

const handleTaskFailed = (event: any) => {
  addLog(`任务失败: ${JSON.stringify(event)}`)
  Object.assign(statusInfo, taskGeneratingRef.value?.state || {})
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.status-info {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
  
  pre {
    margin: 10px 0 0 0;
    font-size: 12px;
  }
}

.logs {
  margin: 20px 0;
  
  .log-item {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
    font-family: monospace;
    font-size: 12px;
    
    .timestamp {
      color: #666;
      margin-right: 10px;
    }
    
    .message {
      color: #333;
    }
  }
}
</style>
