import type { TaskStatusDTO } from '@/api/taskStatus'

/**
 * 任务生成组件的属性接口
 */
export interface TaskGeneratingProps {
  /** 案件ID（必需） */
  caseImportId: string | number
  /** 任务类型（必需） */
  taskType: string
  /** 任务显示名称（可选，会自动从枚举获取） */
  taskName?: string
  /** 轮询间隔，默认3000ms */
  pollingInterval?: number
  /** 是否自动开始检测，默认true */
  autoStart?: boolean
  /** 是否显示进度条，默认true */
  showProgress?: boolean
  /** 是否显示预计时间，默认true */
  showEstimatedTime?: boolean
  /** 是否自动隐藏，默认true */
  autoHide?: boolean
  /** 是否使用遮罩层，默认false（局部覆盖） */
  overlay?: boolean
  /** 定位方式，默认'absolute'（局部覆盖）或'fixed'（全屏覆盖） */
  position?: 'absolute' | 'fixed'
}

/**
 * 任务生成组件的事件接口
 */
export interface TaskGeneratingEmits {
  /** 任务开始执行 */
  (e: 'task-started', data: { taskType: string; caseImportId: string | number; estimatedTime: string }): void
  /** 任务完成 */
  (e: 'task-completed', data: { taskType: string; caseImportId: string | number; elapsedTime: number }): void
  /** 任务失败 */
  (e: 'task-failed', data: { taskType: string; caseImportId: string | number; errorMessage: string; elapsedTime: number }): void
  /** 状态变化 */
  (e: 'status-changed', status: TaskStatusDTO): void
}

/**
 * 组件内部状态接口
 */
export interface TaskGeneratingState {
  /** 组件是否显示 */
  isVisible: boolean
  /** 执行状态 */
  executionStatus: TaskExecutionStatus
  /** 当前任务状态 */
  currentStatus: TaskStatusDTO | null
  /** 预计完成时间 */
  estimatedTime: string
  /** 已执行时间（秒） */
  elapsedTime: number
  /** 错误信息 */
  errorMessage: string
}

/**
 * 任务执行状态枚举
 */
export enum TaskExecutionStatus {
  /** 未开始 */
  IDLE = 'idle',
  /** 检查中 */
  CHECKING = 'checking',
  /** 执行中 */
  RUNNING = 'running',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 执行失败 */
  FAILED = 'failed'
}
