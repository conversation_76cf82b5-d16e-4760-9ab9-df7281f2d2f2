import TaskGeneratingStatus from './TaskGeneratingStatus.vue'
import type { TaskGeneratingProps, TaskGeneratingEmits, TaskGeneratingState } from './types'

// 导出组件
export { TaskGeneratingStatus }

// 导出类型
export type { TaskGeneratingProps, TaskGeneratingEmits, TaskGeneratingState }

// 导出任务类型常量（从 API 模块重新导出，方便使用）
export { TaskType, TaskStatus, getTaskTypeName, getTaskStatusName, getEstimatedDuration } from '@/api/taskStatus'

// 默认导出
export default TaskGeneratingStatus
