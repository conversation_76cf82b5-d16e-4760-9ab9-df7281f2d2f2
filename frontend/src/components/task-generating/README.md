# TaskGeneratingStatus 组件使用说明

TaskGeneratingStatus 是一个智能的任务状态监控组件，支持全屏覆盖和局部覆盖两种模式，能够自动监控任务执行状态并显示进度信息。

## 功能特性

- ✅ **智能轮询**：只在任务运行时轮询，任务完成后自动停止
- ✅ **异步任务支持**：处理任务创建延迟，等待任务启动后开始监控
- ✅ **双显示模式**：支持全屏覆盖和局部覆盖两种显示方式
- ✅ **自适应布局**：局部模式下自动适应父容器大小
- ✅ **进度显示**：显示执行时间、进度百分比和进度条
- ✅ **错误处理**：自动重试机制和错误信息显示

## 显示模式

### 全屏覆盖模式
- 覆盖整个浏览器窗口
- 适用于重要的全局任务
- 使用 `position: fixed` 定位

### 局部覆盖模式（推荐）
- 只覆盖指定的容器区域
- 适用于页面中的特定功能模块
- 使用 `position: absolute` 定位
- 自动适应父容器大小

## 属性配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `caseImportId` | `string \| number` | - | ✅ | 案件导入ID |
| `taskType` | `string` | - | ✅ | 任务类型（如 `TaskType.LITIGATION_RELATION`） |
| `overlay` | `boolean` | `false` | ❌ | 是否使用全屏覆盖模式 |
| `position` | `'absolute' \| 'fixed'` | `'absolute'` | ❌ | 定位方式 |
| `showProgress` | `boolean` | `true` | ❌ | 是否显示进度条和进度信息 |
| `showEstimatedTime` | `boolean` | `true` | ❌ | 是否显示预计完成时间 |
| `pollingInterval` | `number` | `3000` | ❌ | 轮询间隔（毫秒） |
| `autoHide` | `boolean` | `true` | ❌ | 任务完成后是否自动隐藏 |
| `maxRetries` | `number` | `3` | ❌ | 最大重试次数 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `task-started` | `{ taskType, caseImportId, estimatedTime }` | 任务开始时触发 |
| `task-completed` | `{ taskType, caseImportId, elapsedTime }` | 任务完成时触发 |
| `task-failed` | `{ taskType, caseImportId, errorMessage, elapsedTime }` | 任务失败时触发 |

## 使用示例

### 1. 局部覆盖模式（推荐）

适用于页面中的特定功能模块，只覆盖指定区域：

```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h2>诉辩关系分析</h2>

    <!-- 内容区域 -->
    <div class="content-area" style="position: relative; min-height: 400px;">
      <!-- 任务生成状态组件 -->
      <TaskGeneratingStatus
        ref="taskGeneratingRef"
        :case-import-id="caseImportId"
        :task-type="TaskType.LITIGATION_RELATION"
        :overlay="false"
        :position="'absolute'"
        :show-progress="true"
        :show-estimated-time="true"
        @task-completed="handleTaskCompleted"
        @task-failed="handleTaskFailed"
      />

      <!-- 实际内容 -->
      <div class="dispute-relations">
        <!-- 诉辩关系内容 -->
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button type="primary" @click="handleRegenerate">
        重新生成
      </el-button>
    </div>
  </div>
</template>
```

### 2. 全屏覆盖模式

适用于重要的全局任务：

```vue
<template>
  <div>
    <!-- 任务生成状态组件 - 全屏覆盖 -->
    <TaskGeneratingStatus
      ref="taskGeneratingRef"
      :case-import-id="caseImportId"
      :task-type="TaskType.DISPUTE_FOCUS"
      :overlay="true"
      :position="'fixed'"
      @task-completed="handleTaskCompleted"
      @task-failed="handleTaskFailed"
    />

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 页面内容 -->
    </div>
  </div>
</template>
```

<script setup>
import { ref } from 'vue'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'

const taskGeneratingRef = ref()
const caseImportId = ref(123)

## JavaScript 逻辑

```javascript
<script setup>
import { ref } from 'vue'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'

// 组件引用
const taskGeneratingRef = ref()
const caseImportId = ref(123)

// 重新生成处理
const handleRegenerate = async () => {
  try {
    // 调用重新生成 API
    await litigationPointsApi.regenerate(caseImportId.value)

    // 通知组件开始监控任务状态
    // 组件会自动等待任务创建完成后开始轮询
    taskGeneratingRef.value?.startTaskMonitoring()

    // 也可以自定义等待参数
    // taskGeneratingRef.value?.startTaskMonitoring({
    //   maxRetries: 15,      // 最多重试15次
    //   retryInterval: 500   // 每次间隔500ms
    // })
  } catch (error) {
    console.error('重新生成失败:', error)
  }
}

// 任务完成处理
const handleTaskCompleted = (event) => {
  console.log('任务完成:', event)
  // 刷新数据、显示成功消息等
  ElMessage.success('任务完成！')
}

// 任务失败处理
const handleTaskFailed = (event) => {
  console.error('任务失败:', event)
  ElMessage.error(`任务失败: ${event.errorMessage}`)
}
</script>
```

## 组件方法

通过组件引用可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `startTaskMonitoring` | `options?` | `Promise<void>` | 手动开始任务监控 |
| `checkTaskStatus` | - | `Promise<void>` | 检查任务状态 |
| `stopAllTimers` | - | `void` | 停止所有定时器 |

### startTaskMonitoring 参数

```typescript
interface StartTaskMonitoringOptions {
  maxRetries?: number      // 最大重试次数，默认10次
  retryInterval?: number   // 重试间隔毫秒数，默认1000ms
}
```

## 样式自定义

### 局部模式容器要求

使用局部覆盖模式时，父容器需要满足以下条件：

```css
.parent-container {
  position: relative;  /* 必需：为绝对定位提供参考 */
  min-height: 200px;   /* 建议：确保有足够高度显示加载状态 */
}
```

### 字体大小

组件会根据显示模式自动调整字体大小：

- **全屏模式**：使用默认字体大小
- **局部模式**：使用更大的字体以提高可读性
  - 标题：22px
  - 描述：16px

## 最佳实践

### 1. 容器设置

```vue
<!-- ✅ 正确：设置相对定位和最小高度 -->
<div class="content-area" style="position: relative; min-height: 400px;">
  <TaskGeneratingStatus ... />
  <!-- 内容 -->
</div>

<!-- ❌ 错误：缺少相对定位 -->
<div class="content-area">
  <TaskGeneratingStatus ... />
  <!-- 内容 -->
</div>
```

### 2. 任务类型

使用预定义的任务类型常量：

```javascript
import { TaskType } from '@/components/task-generating'

// ✅ 正确
:task-type="TaskType.LITIGATION_RELATION"

// ❌ 错误：硬编码字符串
:task-type="'litigation_relation'"
```

### 3. 错误处理

```javascript
// ✅ 推荐：统一的错误处理
const handleTaskFailed = (event) => {
  console.error('任务执行失败:', event)
  ElMessage.error(`${event.taskType}失败: ${event.errorMessage}`)

  // 可选：记录错误日志
  logError('task_failed', event)
}
```

### 4. 重新生成流程

```javascript
const handleRegenerate = async () => {
  try {
    // 1. 显示确认对话框
    await ElMessageBox.confirm('确定要重新生成吗？', '确认')

    // 2. 调用API
    await api.regenerate(caseImportId.value)

    // 3. 开始监控
    taskGeneratingRef.value?.startTaskMonitoring()

    // 4. 显示提示
    ElMessage.info('重新生成任务已启动')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新生成失败:', error)
    }
  }
}
```

## 常见问题

### Q: 为什么进度信息没有显示？
A: 检查以下设置：
- 确保 `:show-progress="true"`
- 确保父容器有足够的高度
- 检查是否有CSS样式冲突

### Q: 为什么组件没有覆盖指定区域？
A: 检查父容器是否设置了 `position: relative`

### Q: 如何自定义轮询间隔？
A: 使用 `:polling-interval="5000"` 设置为5秒轮询

### Q: 任务完成后如何自动刷新数据？
A: 在 `@task-completed` 事件中调用数据刷新方法

## 技术实现

### 轮询机制
- 智能轮询：只在任务运行时轮询
- 自动停止：任务完成后立即停止轮询
- 异步支持：等待任务创建完成后开始监控

### 样式适配
- 响应式设计：适配不同屏幕尺寸
- 自适应布局：根据容器大小调整显示
- 模式切换：支持全屏和局部两种模式
