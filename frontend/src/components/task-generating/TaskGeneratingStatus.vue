<template>
  <div
    v-if="state.isVisible"
    class="task-generating-status"
    :class="{
      'overlay-mode': overlay,
      'local-mode': !overlay,
      'position-fixed': position === 'fixed',
      'position-absolute': position === 'absolute'
    }"
    :data-small-height="isSmallContainer"
    :data-error-state="state.executionStatus === 'failed'"
  >
    <div class="generating-content">
      <!-- 加载动画 -->
      <div v-if="state.executionStatus !== 'failed'" class="loading-animation">
        <el-icon class="loading-spinner" :size="32">
          <Loading />
        </el-icon>
      </div>

      <!-- 任务信息 -->
      <div v-if="state.executionStatus !== 'failed'" class="task-info">
        <h3 class="task-title">正在{{ displayTaskName }}分析...</h3>
        <p class="task-description">
          <template v-if="showEstimatedTime && state.estimatedTime">
            预计 {{ state.estimatedTime }} 分析完毕，请耐心等待~
          </template>
          <template v-else>
            正在处理中，请耐心等待...
          </template>
        </p>
      </div>

      <!-- 进度信息 -->
      <div v-if="showProgress && state.executionStatus !== 'failed'" class="progress-info">
        <div class="time-info">
          <span class="elapsed-time">已执行: {{ formatElapsedTime(state.elapsedTime) }}</span>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="state.errorMessage" class="error-info">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon :size="32" color="#F56C6C">
            <CircleClose />
          </el-icon>
        </div>

        <!-- 错误标题和描述 -->
        <div class="error-content">
          <h3 class="error-title">{{ displayTaskName }}失败</h3>
          <p class="error-description">
            {{ state.errorMessage }}
          </p>
          <p class="retry-hint">
            <el-icon :size="14" style="margin-right: 4px; vertical-align: middle;">
              <InfoFilled />
            </el-icon>
            您可以点击右上角的
            <span class="highlight-text">重新生成</span>
            按钮重试
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, readonly } from 'vue'
import { Loading, CircleClose, InfoFilled } from '@element-plus/icons-vue'
import { taskStatusApi, TaskType, TaskStatus, getTaskTypeName, getTaskStatusName, getEstimatedDuration, type TaskStatusDTO } from '@/api/taskStatus'
import { ModuleType, ModuleNames } from '@/api/moduleUpdate'
import type { TaskGeneratingProps, TaskGeneratingEmits, TaskGeneratingState } from './types'
import { TaskExecutionStatus } from './types'

// Props 定义
const props = withDefaults(defineProps<TaskGeneratingProps>(), {
  pollingInterval: 3000,
  showProgress: true,
  showEstimatedTime: true,
  autoHide: true,
  overlay: false,
  position: 'fixed'
})

// Emits 定义
const emit = defineEmits<TaskGeneratingEmits>()

// 状态管理
const state = ref<TaskGeneratingState>({
  isVisible: false,
  executionStatus: TaskExecutionStatus.IDLE,
  currentStatus: null,
  estimatedTime: '',
  elapsedTime: 0,
  errorMessage: ''
})

// 定时器引用
const pollingTimer = ref<NodeJS.Timeout | null>(null)
const elapsedTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const displayTaskName = computed(() => {
  // 优先使用 ModuleNames 中的中文名称
  const chineseName = ModuleNames[props.taskType as keyof typeof ModuleNames]
  if (chineseName) {
    return chineseName
  }
  // 如果没有找到，回退到 getTaskTypeName
  return getTaskTypeName(props.taskType)
})

const overlay = computed(() => props.overlay)
const position = computed(() => props.position)

// 检测是否为小容器（用于调整显示样式）
const isSmallContainer = computed(() => {
  // 在局部模式下，如果同时不显示进度和预计时间，认为是小容器
  return !props.overlay && !props.showProgress && !props.showEstimatedTime
})



// 检查任务状态
const checkTaskStatus = async (): Promise<void> => {
  try {
    // 1. 先检查是否有执行中的任务
    const isRunning = await taskStatusApi.isTaskRunning(props.caseImportId, props.taskType)

    if (isRunning) {
      // 任务正在执行中
      if (!state.value.isVisible) {
        // 任务开始执行，显示组件（自动检测，基于创建时间计算）
        await startTaskExecution(false)
      }

      // 开始轮询（如果还没有开始）
      if (!pollingTimer.value) {
        startPolling()
      }
    } else {
      // 2. 没有执行中的任务，停止轮询
      stopPolling()

      if (state.value.isVisible && state.value.executionStatus === TaskExecutionStatus.RUNNING) {
        // 3. 如果组件正在显示且之前是运行状态，说明任务刚刚结束，获取最新状态
        await checkFinalTaskStatus()
      } else if (state.value.executionStatus === TaskExecutionStatus.IDLE) {
        // 4. 如果是初始状态（如页面刷新），检查是否有失败的任务需要显示
        await checkInitialTaskStatus()
      }
    }
  } catch (error) {
    console.error('检查任务状态失败:', error)
    handleTaskFailure(error instanceof Error ? error.message : '检查任务状态失败')
  }
}

// 开始任务执行
const startTaskExecution = async (isManualStart: boolean = false): Promise<void> => {
  state.value.isVisible = true
  state.value.executionStatus = TaskExecutionStatus.RUNNING
  state.value.errorMessage = ''

  // 如果是手动开始（重新生成），直接从0开始计时
  if (isManualStart) {
    state.value.elapsedTime = 0
  } else {
    // 获取任务状态以获取创建时间
    try {
      const taskStatus = await taskStatusApi.getTaskStatus(props.caseImportId, props.taskType)
      if (taskStatus && taskStatus.createTime) {
        // 基于任务创建时间计算已执行时间
        const createTime = new Date(taskStatus.createTime)
        const now = new Date()
        const elapsedSeconds = Math.floor((now.getTime() - createTime.getTime()) / 1000)
        state.value.elapsedTime = Math.max(0, elapsedSeconds)
      } else {
        // 如果没有创建时间，从0开始
        state.value.elapsedTime = 0
      }
    } catch (error) {
      console.error('获取任务创建时间失败:', error)
      state.value.elapsedTime = 0
    }
  }

  // 获取预估时间
  if (props.showEstimatedTime) {
    const estimatedMinutes = getEstimatedDuration(props.taskType)
    if (estimatedMinutes > 0) {
      state.value.estimatedTime = `${estimatedMinutes}分钟`
    }
  }

  // 开始计时
  startElapsedTimer()

  // 触发开始事件
  emit('task-started', {
    taskType: props.taskType,
    caseImportId: props.caseImportId,
    estimatedTime: state.value.estimatedTime
  })
}

// 检查初始任务状态（页面刷新时调用）
const checkInitialTaskStatus = async (): Promise<void> => {
  try {
    // 获取任务的最新状态
    const taskStatus = await taskStatusApi.getTaskStatus(props.caseImportId, props.taskType)

    if (taskStatus && taskStatus.status === TaskStatus.FAILED) {
      // 如果有失败的任务，显示失败状态
      state.value.isVisible = true
      state.value.currentStatus = taskStatus
      handleTaskFailure(taskStatus.errorMessage || '任务执行失败')
    }
    // 如果任务成功或不存在，不显示组件
  } catch (error) {
    console.error('获取初始任务状态失败:', error)
  }
}

// 检查任务的最终状态（当任务不再运行时调用）
const checkFinalTaskStatus = async (): Promise<void> => {
  try {
    // 获取任务的最新状态
    const taskStatus = await taskStatusApi.getTaskStatus(props.caseImportId, props.taskType)

    if (taskStatus) {
      // 更新当前状态
      state.value.currentStatus = taskStatus

      if (taskStatus.status === TaskStatus.SUCCESS) {
        // 任务成功完成
        handleTaskCompletion()
      } else if (taskStatus.status === TaskStatus.FAILED) {
        // 任务失败，显示错误信息
        handleTaskFailure(taskStatus.errorMessage || '任务执行失败')
      } else {
        // 其他状态（如待执行），可能是异常情况
        console.warn('任务状态异常:', taskStatus)
        handleTaskFailure('任务状态异常')
      }
    } else {
      // 没有找到任务记录，可能任务还未开始或被删除
      console.warn('未找到任务记录')
      handleTaskFailure('未找到任务记录')
    }
  } catch (error) {
    console.error('获取任务最终状态失败:', error)
    handleTaskFailure('获取任务状态失败')
  }
}

// 处理任务完成
const handleTaskCompletion = (): void => {
  state.value.executionStatus = TaskExecutionStatus.COMPLETED

  // 停止定时器
  stopAllTimers()

  // 触发完成事件
  emit('task-completed', {
    taskType: props.taskType,
    caseImportId: props.caseImportId,
    elapsedTime: state.value.elapsedTime
  })

  // 自动隐藏
  if (props.autoHide) {
    setTimeout(() => {
      state.value.isVisible = false
    }, 2000)
  }
}

// 处理任务失败
const handleTaskFailure = (errorMessage: string): void => {
  state.value.executionStatus = TaskExecutionStatus.FAILED
  state.value.errorMessage = errorMessage
  
  // 停止定时器
  stopAllTimers()
  
  // 触发失败事件
  emit('task-failed', {
    taskType: props.taskType,
    caseImportId: props.caseImportId,
    errorMessage,
    elapsedTime: state.value.elapsedTime
  })
}



// 开始轮询
const startPolling = (): void => {
  if (pollingTimer.value) return

  // 立即执行一次状态检查，然后开始定时轮询
  checkTaskStatus()

  pollingTimer.value = setInterval(async () => {
    await checkTaskStatus()
  }, props.pollingInterval)
}

// 停止轮询
const stopPolling = (): void => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

// 开始计时
const startElapsedTimer = (): void => {
  if (elapsedTimer.value) return
  
  elapsedTimer.value = setInterval(() => {
    state.value.elapsedTime++
  }, 1000)
}

// 停止计时
const stopElapsedTimer = (): void => {
  if (elapsedTimer.value) {
    clearInterval(elapsedTimer.value)
    elapsedTimer.value = null
  }
}

// 停止所有定时器
const stopAllTimers = (): void => {
  stopPolling()
  stopElapsedTimer()
}

// 格式化已用时间
const formatElapsedTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`
  }
  return `${remainingSeconds}秒`
}

// 获取状态文本
const getStatusText = (status: TaskStatus): string => {
  return getTaskStatusName(status)
}

// 监听 props 变化
watch([() => props.caseImportId, () => props.taskType], () => {
  // 重新检查任务状态
  nextTick(() => {
    checkTaskStatus()
  })
}, { immediate: false })

// 组件挂载
onMounted(async () => {
  // 初始检查任务状态
  // 如果任务正在运行，checkTaskStatus 内部会自动开始轮询
  await checkTaskStatus()
})

// 组件卸载
onUnmounted(() => {
  stopAllTimers()
})

// 手动开始任务监控（用于重新生成等场景）
const startTaskMonitoring = async (): Promise<void> => {
  // 重置状态
  state.value.errorMessage = ''

  // 手动开始任务执行，从0开始计时
  await startTaskExecution(true)

  // 开始轮询
  if (!pollingTimer.value) {
    startPolling()
  }
}



// 暴露方法给父组件
defineExpose({
  checkTaskStatus,
  startTaskExecution,
  startTaskMonitoring,
  stopAllTimers,
  state: readonly(state)
})
</script>

<style scoped lang="scss">
.task-generating-status {
  display: flex;
  align-items: center;
  justify-content: center;

  // 全屏覆盖模式（原有模式）
  &.overlay-mode.position-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
  }

  // 局部覆盖模式（新增模式）
  &.local-mode.position-absolute {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(6px);
    border-radius: 8px;
    z-index: 50;
    
    // 分析状态：完全遮挡
    &:not([data-error-state="true"]) {
      top: 0;
      bottom: 0;
      height: 100%;
    }
    
    // 错误状态：露出顶部按钮区域
    &[data-error-state="true"] {
      top: 40px;
      bottom: 0;
      height: calc(100% - 40px);
    }
  }

  .generating-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 30px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 90%;
  }

  // 局部模式下的内容样式调整
  &.local-mode .generating-content {
    gap: 20px;
    padding: 30px;
    max-width: none;
    width: auto;
    height: auto;
    min-width: unset;
    box-shadow: none;
    border-radius: 8px;
    background: transparent; // 移除白色背景，使用父容器的半透明背景

    // 局部模式下的字体大小调整
    .task-info {
      .task-title {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #303133 !important;
      }

      .task-description {
        font-size: 14px !important;
        color: #606266 !important;
        margin-top: 8px;
      }
    }

    .loading-animation {
      display: flex;
      align-items: center;
      justify-content: center;

      .loading-spinner {
        color: #409eff;
        animation: spin 1s linear infinite;
      }
    }

    .task-info {
      text-align: center;

      .task-title {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .task-description {
        margin: 0;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
      }
    }

    .progress-info {
      width: 100%;

      .time-info {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #909399;

        .elapsed-time {
          font-weight: 500;
        }
      }
    }

    .error-info {
      .error-content {
        .error-title {
          font-size: 16px !important;
        }

        .error-description {
          font-size: 13px !important;
        }

        .retry-hint {
          font-size: 12px !important;
        }
      }
    }

    .error-info {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .error-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .error-content {
        text-align: center;

        .error-title {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
          color: #F56C6C;
        }

        .error-description {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #606266;
          line-height: 1.5;
          word-break: break-word;
        }

        .retry-hint {
          margin: 0;
          font-size: 13px;
          color: #909399;
          line-height: 1.5;
          display: flex;
          align-items: center;
          justify-content: center;

          .highlight-text {
            color: #409EFF;
            font-weight: 500;
            margin: 0 2px;
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .task-generating-status {
    .generating-content {
      padding: 24px;
      gap: 16px;

      .task-info {
        .task-title {
          font-size: 16px;
        }

        .task-description {
          font-size: 13px;
        }
      }
    }

    // 局部模式在小屏幕下的调整
    &.local-mode .generating-content {
      padding: 16px;
      gap: 12px;

      .task-info {
        .task-title {
          font-size: 14px;
        }

        .task-description {
          font-size: 12px;
        }
      }
    }
  }
}

// 针对非常小的容器的特殊处理（暂时禁用以确保进度信息显示）
// .task-generating-status.local-mode {
//   // 当容器高度很小时，简化显示
//   &[data-small-height="true"] .generating-content {
//     gap: 8px;
//     padding: 12px;

//     .task-info .task-title {
//       font-size: 13px;
//       margin-bottom: 4px;
//     }

//     .task-info .task-description {
//       font-size: 11px;
//     }

//     .progress-info {
//       display: none; // 在很小的容器中隐藏进度信息
//     }
//   }
// }
</style>
