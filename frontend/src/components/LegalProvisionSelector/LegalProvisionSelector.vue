<template>
  <div class="legal-provision-selector">
    <!-- 法条列表展示 -->
    <div class="provision-list">
      <div class="provision-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell drag-handle-header"></div>
          <div class="header-cell provision-number-header">法条编号</div>
          <div class="header-cell content-header">法条原文</div>
          <div class="header-cell action-header">操作</div>
        </div>

        <!-- 可拖拽的表格内容 -->
        <draggable
          v-model="localProvisions"
          item-key="id"
          handle=".drag-handle"
          class="draggable-container"
          @start="onDragStart"
          @end="onDragEnd"
        >
          <template #item="{ element, index }">
            <div class="table-row" :class="{ 'dragging': isDragging }">
              <!-- 拖拽手柄 -->
              <div class="table-cell drag-handle-cell">
                <el-icon class="drag-handle" title="拖拽排序">
                  <DCaret />
                </el-icon>
              </div>

              <!-- 法条编号 -->
              <div class="table-cell provision-number-cell" style="font-size: 13px !important; color: #606266 !important; font-weight: normal !important; font-family: inherit !important; line-height: 1.5 !important;">
                {{ element.provisionNumber }}
              </div>

              <!-- 法条原文 -->
              <div class="table-cell content-cell" style="font-size: 13px !important; color: #606266 !important; font-weight: normal !important; font-family: inherit !important; line-height: 1.5 !important;">
                {{ element.content }}
              </div>

              <!-- 操作按钮 -->
              <div class="table-cell action-cell">
                <el-popconfirm
                  title="确定要删除这条法条吗？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  confirm-button-type="danger"
                  :icon="InfoFilled"
                  icon-color="#f56c6c"
                  @confirm="removeProvision(index)"
                >
                  <template #reference>
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Delete"
                      circle
                    />
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>
        </draggable>

        <!-- 空状态 -->
        <div v-if="!modelValue || modelValue.length === 0" class="empty-state">
          暂无法条
        </div>
      </div>
      
      <!-- 添加按钮 -->
      <div class="add-button-container">
        <el-button
          type="primary"
          :icon="Plus"
          circle
          size="small"
          @click="showSelector = true"
        />
      </div>
    </div>

    <!-- 法条选择对话框 -->
    <el-dialog
      v-model="showSelector"
      title="法条检索"
      width="60%"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="true"
      :lock-scroll="true"
      @close="handleDialogClose"
    >
      <div class="selector-content">
        <!-- 法律选择和内容搜索 -->
        <div class="search-controls">
          <div class="control-item">
            <span class="control-label">法律名称：</span>
            <el-select
              v-model="selectedLawId"
              placeholder="请选择法律"
              filterable
              clearable
              style="width: 300px"
              @change="onLawChange"
            >
              <el-option
                v-for="law in lawList"
                :key="law.lawId"
                :label="law.displayName"
                :value="law.lawId"
              />
            </el-select>
          </div>

          <div class="control-item">
            <span class="control-label">内容搜索：</span>
            <div class="search-input-group">
              <el-input
                v-model="searchText"
                :placeholder="selectedLawId ? '请输入搜索内容（支持数字搜索，如：1、12、100）' : '请先选择法律'"
                style="width: 300px"
                :disabled="!selectedLawId"
                @keyup.enter="searchNext"
              />
              <el-button
                type="primary"
                :icon="Search"
                :disabled="!selectedLawId"
                @click="searchNext"
                style="margin-left: 8px"
              >
                搜索
              </el-button>
            </div>
          </div>
        </div>

        <!-- 搜索结果信息 -->
        <div class="search-info-container" v-if="searchResults.length > 0">
          <span class="search-info">
            找到 {{ searchResults.length }} 个结果，当前第 {{ currentSearchIndex + 1 }} 个
          </span>
        </div>

        <!-- 法条树形结构 -->
        <div class="provision-tree" v-if="selectedLawId">
          <el-tree
            ref="treeRef"
            :data="filteredTreeData"
            :props="treeProps"
            show-checkbox
            node-key="id"
            :default-expand-all="true"
            :check-strictly="false"
            @check="onNodeCheck"
          >
            <template #default="{ node, data }">
              <div
                class="tree-node"
                :id="`node-${instanceId}-${data.id}`"
              >
                <span class="node-title" v-html="highlightText(data.title)"></span>
                <span
                  v-if="!data.children || data.children.length === 0"
                  class="node-content"
                  v-html="highlightText(data.content)"
                ></span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelSelection">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Plus, InfoFilled, Search, DCaret } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import type {
  LawNameResponse,
  LegalProvisionTreeNode,
  LegalProvisionRecord
} from '@/api/legalProvision'
import { getLawNames, getProvisionTree } from '@/api/legalProvision'

// 生成唯一实例ID
let instanceCounter = 0
const generateInstanceId = () => ++instanceCounter

// Props
interface Props {
  modelValue: LegalProvisionRecord[]
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: LegalProvisionRecord[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const instanceId = ref(generateInstanceId()) // 为每个组件实例生成唯一ID
const showSelector = ref(false)
const lawList = ref<LawNameResponse[]>([])
const selectedLawId = ref<number>()
const treeData = ref<LegalProvisionTreeNode[]>([])
const treeRef = ref()
const searchText = ref('')
const searchResults = ref<number[]>([])
const currentSearchIndex = ref(-1)
const currentSearchKeyword = ref('')

// 拖拽相关状态
const isDragging = ref(false)
const localProvisions = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'title'
}

// 计算属性
// 过滤树数据，隐藏法律记录(level=0)，只显示条款项
const filteredTreeData = computed(() => {
  if (!treeData.value.length) return []

  // 找到法律记录(level=0)并返回其children
  const lawNode = treeData.value.find(node => node.level === 0)
  return lawNode ? lawNode.children : []
})

// 方法
const highlightText = (text: string): string => {
  if (!text || !currentSearchKeyword.value) {
    return text
  }

  const keyword = currentSearchKeyword.value
  let highlightedText = text

  // 高亮原始关键词
  const originalRegex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  highlightedText = highlightedText.replace(originalRegex, '<span class="highlight-keyword">$1</span>')

  // 如果输入是数字，同时高亮转换后的中文数字
  const isNumber = /^\d+$/.test(keyword.trim())
  if (isNumber) {
    const num = parseInt(keyword.trim())
    const chineseNumber = convertNumberToChinese(num)
    if (chineseNumber) {
      const chineseRegex = new RegExp(`(${chineseNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
      highlightedText = highlightedText.replace(chineseRegex, '<span class="highlight-keyword">$1</span>')
    }
  }

  return highlightedText
}

const loadLawList = async () => {
  try {
    lawList.value = await getLawNames()
  } catch (error) {
    ElMessage.error('加载法律列表失败')
    console.error(error)
  }
}

const onLawChange = async () => {
  if (!selectedLawId.value) {
    treeData.value = []
    return
  }

  try {
    treeData.value = await getProvisionTree(selectedLawId.value)
    // 清空搜索结果
    searchText.value = ''
    searchResults.value = []
    currentSearchIndex.value = -1
    currentSearchKeyword.value = ''
  } catch (error) {
    ElMessage.error('加载法条数据失败')
    console.error(error)
  }
}

// 阿拉伯数字转中文数字工具函数
const convertNumberToChinese = (num: number): string => {
  if (num < 0 || num > 9999) return ''

  const digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千']

  if (num === 0) return '零'
  if (num < 10) return digits[num]

  let result = ''
  let numStr = num.toString()
  let len = numStr.length
  let needZero = false

  for (let i = 0; i < len; i++) {
    let digit = parseInt(numStr[i])
    let unit = len - i - 1

    if (digit !== 0) {
      // 如果前面需要补零且当前不是最高位，添加零
      if (needZero && result) {
        result += '零'
      }

      // 处理"一十"的特殊情况，应该是"十"
      if (digit === 1 && unit === 1 && len === 2) {
        result += units[unit]
      } else {
        result += digits[digit] + units[unit]
      }
      needZero = false
    } else {
      // 当前位是0，标记可能需要补零
      if (unit > 0 && result) {
        needZero = true
      }
    }
  }

  return result
}

const searchInTree = (nodes: LegalProvisionTreeNode[], text: string): number[] => {
  const results: number[] = []

  // 检测是否为纯数字
  const isNumber = /^\d+$/.test(text.trim())
  let chineseNumber = ''

  if (isNumber) {
    const num = parseInt(text.trim())
    chineseNumber = convertNumberToChinese(num)
  }

  const search = (nodeList: LegalProvisionTreeNode[]) => {
    for (const node of nodeList) {
      let matched = false

      // 原有的文本搜索
      if (node.content.includes(text) || node.title.includes(text)) {
        matched = true
      }

      // 如果输入是数字，同时搜索转换后的中文数字
      if (isNumber && chineseNumber && !matched) {
        if (node.content.includes(chineseNumber) || node.title.includes(chineseNumber)) {
          matched = true
        }
      }

      if (matched) {
        results.push(node.id)
      }

      if (node.children && node.children.length > 0) {
        search(node.children)
      }
    }
  }

  search(nodes)
  return results
}

const searchNext = () => {
  if (!searchText.value.trim()) {
    searchResults.value = []
    currentSearchIndex.value = -1
    currentSearchKeyword.value = ''
    return
  }

  const keyword = searchText.value.trim()

  // 如果是新搜索或关键词改变了，重新搜索
  if (searchResults.value.length === 0 || currentSearchKeyword.value !== keyword) {
    currentSearchKeyword.value = keyword
    searchResults.value = searchInTree(filteredTreeData.value, keyword)
    currentSearchIndex.value = searchResults.value.length > 0 ? 0 : -1
  } else {
    // 继续下一个结果
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
  }

  // 滚动到当前结果
  if (currentSearchIndex.value >= 0) {
    nextTick(() => {
      // 增加延迟确保DOM完全渲染
      setTimeout(() => {
        const nodeId = searchResults.value[currentSearchIndex.value]
        const elementId = `node-${instanceId.value}-${nodeId}`
        const element = document.getElementById(elementId)

        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        } else {
          console.warn(`未找到搜索结果元素: ${elementId}`)
        }
      }, 100) // 增加100ms延迟
    })
  }
}

const onNodeCheck = () => {
  // 节点选择状态改变时的处理
}

const buildProvisionNumber = (node: LegalProvisionTreeNode, allNodes: LegalProvisionTreeNode[]): string => {
  // 找到当前节点的完整路径
  const findPath = (nodes: LegalProvisionTreeNode[], targetId: number, path: LegalProvisionTreeNode[] = []): LegalProvisionTreeNode[] | null => {
    for (const n of nodes) {
      const currentPath = [...path, n]
      if (n.id === targetId) {
        return currentPath
      }
      if (n.children && n.children.length > 0) {
        const result = findPath(n.children, targetId, currentPath)
        if (result) return result
      }
    }
    return null
  }

  const path = findPath(allNodes, node.id)
  if (!path) return node.title

  // 获取法律名称
  const lawNode = allNodes.find(n => n.level === 0)
  let lawPrefix = ''
  if (lawNode) {
    // 简化法律名称，例如"中华人民共和国民法典" -> "民法典"
    const lawName = lawNode.title
    lawPrefix = `${lawName}`
  }

  // 从路径构建编号，跳过法律本身(level=0)
  const relevantPath = path.filter(n => n.level > 0)

  // 根据层级构建完整的法条编号
  let result = ''
  for (let i = 0; i < relevantPath.length; i++) {
    const currentNode = relevantPath[i]
    if (currentNode.level === 1) {
      // 条级别：第XX条
      result += currentNode.title
    } else if (currentNode.level === 2) {
      // 款级别：第XX款
      result += currentNode.title
    } else if (currentNode.level === 3) {
      // 项级别：第（XX）项
      result += currentNode.title
    }
  }

  return lawPrefix + result || node.title
}

const confirmSelection = () => {
  const checkedNodes = treeRef.value?.getCheckedNodes(false, true) || []
  const halfCheckedNodes = treeRef.value?.getHalfCheckedNodes() || []
  const newProvisions: LegalProvisionRecord[] = []

  // 用于跟踪已处理的节点ID，避免重复处理
  const processedNodeIds = new Set<number>()

  // 递归收集节点的所有子内容，保留款、项层级标识
  const collectAllContent = (node: LegalProvisionTreeNode, isRoot: boolean = true): string => {
    console.log(`collectAllContent - 节点: ${node.title}, Level: ${node.level}, isRoot: ${isRoot}`)
    console.log(`节点内容: "${node.content}"`)

    let content = ''

    // 不保留"第x款"、"第x项"等层级标识，直接处理内容
    console.log(`跳过层级标识: "${node.title}" (Level: ${node.level})`)

    // 如果当前节点有内容，添加内容（但要过滤掉与标题重复的内容）
    if (node.content && node.content.trim()) {
      const nodeContent = node.content.trim()

      // 如果节点内容就是标题本身，跳过（避免重复）
      if (nodeContent === node.title) {
        console.log(`跳过重复内容: "${nodeContent}" (与标题相同)`)
      } else {
        content = nodeContent
        console.log(`添加内容: "${content}"`)
      }
    }
    
    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      console.log(`处理 ${node.children.length} 个子节点`)
      const childContents = node.children
        .map((child, index) => {
          console.log(`处理子节点 ${index + 1}: ${child.title}`)
          return collectAllContent(child, false)
        })
        .filter(c => c.length > 0)

      console.log(`子节点内容:`, childContents)

      if (childContents.length > 0) {
        let joinedChildContent = ''

        // 如果有多个子节点内容，尝试提取公共前缀并用原始编号
        if (childContents.length > 1) {
          joinedChildContent = mergeContentsWithCommonPrefix(childContents, node.children)
        } else {
          // 只有一个子节点，直接使用
          joinedChildContent = childContents[0]
        }

        if (content) {
          // 如果当前节点有内容，则用空格连接子节点内容
          content = `${content} ${joinedChildContent}`
        } else {
          // 如果当前节点没有内容，直接使用子节点内容
          content = joinedChildContent
        }
      }
    }
    
    console.log(`最终返回内容: "${content}"`)
    console.log('---')
    return content
  }

  // 提取公共前缀并用原始编号合并内容
  const mergeContentsWithCommonPrefix = (contents: string[], childNodes: LegalProvisionTreeNode[]): string => {
    if (contents.length <= 1) {
      return contents.join('\n')
    }

    console.log('开始分析公共前缀，内容列表:', contents)
    console.log('对应的节点标题:', childNodes.map(node => node.title))

    // 检查是否都是项级别的内容
    const isAllItems = childNodes.every(node =>
      node.title.includes('第（') && node.title.includes('）项')
    )

    if (isAllItems && contents.length > 1) {
      // 查找公共前缀
      let commonPrefix = ''
      const minLength = Math.min(...contents.map(c => c.length))

      for (let i = 0; i < minLength; i++) {
        const char = contents[0][i]
        if (contents.every(content => content[i] === char)) {
          commonPrefix += char
        } else {
          break
        }
      }

      // 寻找最后一个冒号作为前缀结束点
      const lastColonIndex = commonPrefix.lastIndexOf('：')
      if (lastColonIndex > 0) {
        commonPrefix = commonPrefix.substring(0, lastColonIndex + 1)
      } else if (commonPrefix.length < 10) {
        // 如果没有找到冒号且前缀太短，不提取前缀
        commonPrefix = ''
      }

      console.log('提取的公共前缀:', `"${commonPrefix}"`)

      if (commonPrefix.length > 0) {
        // 有公共前缀，先显示公共前缀，然后显示各项的剩余内容
        let result = commonPrefix + '\n'

        contents.forEach((content, index) => {
          const remainingContent = content.substring(commonPrefix.length).trim()
          const nodeTitle = childNodes[index]?.title || ''

          // 提取项编号
          const match = nodeTitle.match(/第（(.+?)）项/)
          if (match) {
            result += `（${match[1]}）${remainingContent}\n`
          } else {
            result += remainingContent + '\n'
          }
        })

        console.log('合并后的结果:', result)
        return result.trim()
      }
    }

    // 没有公共前缀或非项级别内容，直接返回所有内容
    const result = contents.join('\n')
    console.log('直接连接结果:', result)
    return result
  }

  // 标记节点及其所有子节点为已处理
  const markAsProcessed = (node: LegalProvisionTreeNode) => {
    processedNodeIds.add(node.id)
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => markAsProcessed(child))
    }
  }

  // 检查节点是否完全被选中（包括所有子节点）
  const isFullySelected = (node: LegalProvisionTreeNode): boolean => {
    // 如果是叶子节点，检查是否在选中列表中
    if (!node.children || node.children.length === 0) {
      return checkedNodes.some(n => n.id === node.id)
    }
    
    // 如果是父节点，检查所有子节点是否都被选中
    return node.children.every(child => isFullySelected(child))
  }

  // 找到所有顶层被完全选中的节点（优先处理父节点）
  const findTopLevelSelectedNodes = (nodes: LegalProvisionTreeNode[]): LegalProvisionTreeNode[] => {
    const result: LegalProvisionTreeNode[] = []
    
    for (const node of nodes) {
      if (processedNodeIds.has(node.id)) {
        continue
      }
      
      // 如果整个节点（包括所有子节点）都被选中
      if (isFullySelected(node)) {
        result.push(node)
        markAsProcessed(node) // 标记为已处理，避免重复处理子节点
      } else if (node.children && node.children.length > 0) {
        // 递归处理子节点
        result.push(...findTopLevelSelectedNodes(node.children))
      }
    }
    
    return result
  }

  // 处理完全选中的节点
  const fullySelectedNodes = findTopLevelSelectedNodes(filteredTreeData.value)
  
  console.log('=== 完全选中的节点 ===')
  console.log('fullySelectedNodes:', fullySelectedNodes.map(n => ({ id: n.id, title: n.title, level: n.level })))
  
  for (const node of fullySelectedNodes) {
    console.log(`\n处理节点: ${node.title} (ID: ${node.id}, Level: ${node.level})`)
    console.log('节点内容:', node.content)
    console.log('子节点数量:', node.children?.length || 0)
    
    const mergedContent = collectAllContent(node)
    console.log('合并后内容:', mergedContent)
    
    if (mergedContent) {
      const provisionNumber = buildProvisionNumber(node, treeData.value)
      console.log('法条编号:', provisionNumber)
      
      newProvisions.push({
        id: node.id,
        provisionNumber,
        content: mergedContent
      })
    }
  }

  // 处理单独选中的叶子节点（未被父节点包含的）
  for (const node of checkedNodes) {
    if (!processedNodeIds.has(node.id) && (!node.children || node.children.length === 0)) {
      if (node.content && node.content.trim()) {
        const provisionNumber = buildProvisionNumber(node, treeData.value)
        newProvisions.push({
          id: node.id,
          provisionNumber,
          content: node.content
        })
      }
    }
  }

  if (newProvisions.length === 0) {
    ElMessage.warning('请选择至少一个法条')
    return
  }

  // 合并到现有数据中，进行智能去重
  const duplicateProvisions: string[] = []
  const filteredNew = newProvisions.filter(newProvision => {
    // 检查是否存在重复的法条
    const isDuplicate = props.modelValue.some(existing => {
      // 1. 检查ID是否相同
      if (existing.id === newProvision.id) {
        return true
      }

      // 2. 检查法条编号是否相同
      if (existing.provisionNumber === newProvision.provisionNumber) {
        return true
      }

      // 3. 检查内容是否相同（去除空白字符后比较）
      const existingContent = existing.content.replace(/\s+/g, '').trim()
      const newContent = newProvision.content.replace(/\s+/g, '').trim()
      if (existingContent === newContent && existingContent.length > 0) {
        return true
      }

      return false
    })

    if (isDuplicate) {
      duplicateProvisions.push(newProvision.provisionNumber)
    }

    return !isDuplicate
  })

  if (filteredNew.length === 0) {
    if (duplicateProvisions.length > 0) {
      ElMessage.warning(`以下法条已存在：${duplicateProvisions.join('、')}`)
    } else {
      ElMessage.warning('选择的法条已存在')
    }
    return
  }

  // 如果有部分重复，提示用户
  if (duplicateProvisions.length > 0) {
    ElMessage.info(`已跳过重复法条：${duplicateProvisions.join('、')}`)
  }

  emit('update:modelValue', [...props.modelValue, ...filteredNew])

  // 立即关闭对话框并清空状态
  showSelector.value = false
  resetDialogState()
}

const removeProvision = (index: number) => {
  const newValue = [...props.modelValue]
  newValue.splice(index, 1)
  emit('update:modelValue', newValue)
}

// 拖拽相关方法
const onDragStart = (event: any) => {
  isDragging.value = true
  console.log('拖拽开始:', event)
}

const onDragEnd = (event: any) => {
  isDragging.value = false
  console.log('拖拽结束:', event)
  // localProvisions 的 computed setter 会自动触发 emit
}

const cancelSelection = () => {
  // 关闭对话框并重置所有状态
  showSelector.value = false
  resetDialogState()
}

const handleDialogClose = () => {
  // 对话框关闭时重置状态
  resetDialogState()
}

const resetDialogState = () => {
  // 重置所有对话框相关状态
  treeRef.value?.setCheckedKeys([])
  selectedLawId.value = undefined
  treeData.value = []
  searchText.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1
  currentSearchKeyword.value = ''
}

// 监听搜索文本变化，重置搜索结果
watch(searchText, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    searchResults.value = []
    currentSearchIndex.value = -1
    if (!newValue.trim()) {
      currentSearchKeyword.value = ''
    }
  }
})

// 监听对话框打开状态，确保DOM准备就绪
watch(showSelector, (newValue) => {
  if (newValue) {
    // 对话框打开时，等待DOM渲染完成
    nextTick(() => {
      // 重置搜索状态，确保搜索功能正常
      searchResults.value = []
      currentSearchIndex.value = -1
      currentSearchKeyword.value = ''
    })
  }
})

// 生命周期
onMounted(() => {
  loadLawList()
})
</script>

<style scoped>
.legal-provision-selector {
  width: 100%;
}

.provision-list {
  width: 100%;
  padding: 0;
  margin: 0;
}

.add-button-container {
  display: flex;
  justify-content: center;
  margin-top: 0;
  padding: 1px;
  background-color: transparent;
}

.add-button-container:hover {
  background-color: transparent;
}

.selector-content {
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.search-input-group {
  display: flex;
  align-items: center;
}

.search-info-container {
  display: flex;
  justify-content: center;
}

.search-info {
  font-size: 14px;
  color: #606266;
}

.provision-tree {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
}

.tree-node {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 4px 0;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

/* 高亮关键词样式 */
:deep(.highlight-keyword) {
  background-color: #fff3cd;
  color: #856404;
  font-weight: 600;
  padding: 1px 2px;
  border-radius: 2px;
}

.node-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

.node-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 树形组件样式调整 */
:deep(.el-tree-node__content) {
  height: auto !important;
  padding: 8px 0;
  overflow: visible !important;
  white-space: pre-wrap !important;
}

:deep(.el-tree-node__label) {
  width: 100%;
  overflow: visible !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
}

:deep(.el-table .el-table__cell) {
  padding: 0 0 !important;
}



:deep(.el-checkbox) {
  margin-right: 8px;
  flex-shrink: 0;
}

:deep(.el-tree-node) {
  white-space: pre-wrap !important;
}

:deep(.el-tree) {
  overflow-x: hidden !important;
}

/* 对话框样式 - 确保在最外层显示 */
:deep(.el-dialog) {
  margin: 0 auto !important;
}

:deep(.el-dialog__wrapper) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
}

/* 删除确认弹框样式 */
:deep(.el-popconfirm) {
  z-index: 3000 !important;
}

:deep(.el-popconfirm__main) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-popconfirm__action) {
  margin-top: 12px;
}

/* 自定义表格样式 */
.custom-table {
  border: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__header-wrapper) {
  background-color: rgba(90, 135, 237, 0.1);
}

:deep(.custom-table .el-table__header th) {
  background-color: rgba(90, 135, 237, 0.1) !important;
  border-color: #5a87ed !important;
  color: #303133;
  font-weight: 600;
  font-size: 14px !important;
}

:deep(.custom-table .el-table__body td) {
  border-color: #5a87ed !important;
  font-size: 14px !important;
}

:deep(.custom-table .el-table__border-left-patch) {
  border-color: #5a87ed !important;
}

:deep(.custom-table .el-table__border-bottom-patch) {
  border-color: #5a87ed !important;
}

/* 法条原文列样式 - 支持换行和\n字符显示 */
:deep(.custom-table .content-column .cell) {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  line-height: 1.5 !important;
  padding: 12px 8px !important;
  font-size: 14px !important;
}

/* 表格行高度自适应 */
:deep(.custom-table .el-table__row) {
  height: auto !important;
}

:deep(.custom-table .el-table__row td) {
  height: auto !important;
}

/* 拖拽表格样式 */
.provision-table {
  width: 100%;
  border: 1px solid #5a87ed;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: rgba(90, 135, 237, 0.1);
  border-bottom: 1px solid #5a87ed;
  font-weight: 600;
  color: #303133;
  font-size: 13px;
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #5a87ed;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-cell:last-child {
  border-right: none;
}

.drag-handle-header {
  width: 40px;
  flex-shrink: 0;
}

.provision-number-header {
  width: 200px;
  flex-shrink: 0;
}

.content-header {
  flex: 1;
}

.action-header {
  width: 70px;
  flex-shrink: 0;
}

.draggable-container {
  min-height: 50px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #5a87ed;
  transition: all 0.3s ease;
  background-color: #fff;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: rgba(90, 135, 237, 0.05);
}

.table-row.dragging {
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-cell {
  padding: 12px 8px;
  border-right: 1px solid #5a87ed;
  display: flex;
  align-items: center;
}

.table-cell:last-child {
  border-right: none;
}

.drag-handle-cell {
  width: 40px;
  flex-shrink: 0;
  justify-content: center;
}

.provision-number-cell {
  width: 200px;
  flex-shrink: 0;
  font-size: 13px !important;
  color: #606266 !important;
  font-weight: normal !important;
}

.content-cell {
  flex: 1;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.5;
  overflow-wrap: break-word;
  font-size: 13px !important;
  color: #606266 !important;
  font-weight: normal !important;
}

.action-cell {
  width: 70px;
  flex-shrink: 0;
  justify-content: center;
}

.drag-handle {
  cursor: grab;
  color: #909399;
  font-size: 14px;
  transition: color 0.3s ease;
}

.drag-handle:hover {
  color: #5a87ed;
}

.drag-handle:active {
  cursor: grabbing;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #909399;
  font-size: 14px;
  border-bottom: 1px solid #5a87ed;
}

/* 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.5;
  background-color: rgba(90, 135, 237, 0.1);
}

.sortable-chosen {
  background-color: rgba(90, 135, 237, 0.1);
}

.sortable-drag {
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
