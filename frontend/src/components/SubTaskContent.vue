<template>
  <div class="subtask-content">
    <el-row :gutter="16" class="io-row">
      <!-- 模型输入 -->
      <el-col :span="12" class="input-col">
        <el-card class="io-card input-card">
          <template #header>
            <div class="card-header">
              <div class="card-title-section">
                <span class="card-title">模型输入 (Prompt)</span>
                <!-- 时间信息 -->
                <div v-if="subTask.startTime" class="time-info">
                  <el-tag size="small" v-if="subTask.startTime" type="info">
                    开始时间: {{ formatTimestamp(subTask.startTime) }}
                  </el-tag>
                </div>
              </div>
              <div class="card-actions">
                <el-button size="small" @click="handleCopyPrompt">
                  <el-icon><DocumentCopy /></el-icon>
                  复制
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="input-content">
            <pre class="prompt-text">{{ subTask.prompt }}</pre>
          </div>
        </el-card>
      </el-col>

      <!-- 模型输出 -->
      <el-col :span="12" class="output-col">
        <el-card class="io-card output-card">
          <template #header>
            <div class="card-header">
              <div class="card-title-section">
                <span class="card-title">模型输出 (Result)</span>
                <!-- 时间信息 -->
                <div v-if="subTask.duration" class="time-info">
                  <el-tag size="small" v-if="subTask.duration" type="info">
                    执行耗时: {{ formatAgentDuration(subTask.duration) }}
                  </el-tag>
                </div>
                <!-- Token 信息 -->
                <div v-if="subTask.tokens" class="token-info">
                  <el-tag size="small" type="info">
                    输入Token: {{ formatTokens(subTask.tokens.upTokens) }}
                  </el-tag>
                  <el-tag size="small" type="success">
                    输出Token: {{ formatTokens(subTask.tokens.downTokens) }}
                  </el-tag>
                  <el-tag size="small" type="warning">
                    总Token: {{ formatTokens(subTask.tokens.totalTokens) }}
                  </el-tag>
                </div>
              </div>
              <div class="card-actions">
                <el-button size="small" @click="handleCopyResult">
                  <el-icon><DocumentCopy /></el-icon>
                  复制
                </el-button>

                <el-button size="small" @click="handleDownloadResult">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="output-content">
            <pre class="result-text">{{ subTask.result }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { DocumentCopy, Download } from '@element-plus/icons-vue'
import type { AgentSubTaskResult } from '@/api/analysisTask'
import { formatDateTime } from '@/utils/api-helper'

// Props
interface Props {
  subTask: AgentSubTaskResult
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  copyPrompt: [prompt: string]
  copyResult: [result: string]
  downloadResult: [result: string, taskName?: string]
}>()

// 处理函数
const handleCopyPrompt = () => {
  emit('copyPrompt', props.subTask.prompt)
}

const handleCopyResult = () => {
  emit('copyResult', props.subTask.result)
}

const handleDownloadResult = () => {
  emit('downloadResult', props.subTask.result, props.subTask.name)
}

// 格式化 tokens 数量
const formatTokens = (tokens?: number): string => {
  if (!tokens) return '-'
  return tokens.toString()
}

// 格式化时间戳（秒为单位）
const formatTimestamp = (timestamp?: number): string => {
  if (!timestamp) return '-'
  // 将秒转换为毫秒
  const date = new Date(timestamp * 1000)
  return formatDateTime(date)
}

// 格式化 Agent 执行时长（秒转换为可读格式）
const formatAgentDuration = (durationSeconds?: number): string => {
  if (!durationSeconds) return '-'

  // 确保是数字类型
  const duration = Number(durationSeconds)

  if (duration < 60) {
    // 小于1分钟时，显示精确的秒数（1位小数）
    return duration % 1 === 0 ? `${duration}秒` : `${duration.toFixed(1)}秒`
  }

  const minutes = Math.floor(duration / 60)
  const seconds = Math.round(duration % 60) // 四舍五入到整数秒

  if (minutes < 60) {
    return `${minutes}分${seconds}秒`
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}小时${remainingMinutes}分${seconds}秒`
}
</script>

<style scoped>
.subtask-content {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}

.token-info {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.time-info {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.io-row {
  margin: 0;
}

.input-col,
.output-col {
  padding: 0 8px;
}

.io-card {
  height: 100%;
  border-radius: 8px;
}

.input-card {
  border-left: 4px solid #409eff;
}

.output-card {
  border-left: 4px solid #67c23a;
}

.input-content {
  height: 600px;
  max-height: 800px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
}

.output-content {
  height: 600px;
  max-height: 800px;
  overflow-y: auto;
  background: #f0fdf4;
  border-radius: 4px;
}

.prompt-text,
.result-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  padding: 16px;
  min-height: 100%;
  box-sizing: border-box;
  background: transparent;
  display: block;
}

.prompt-text {
  color: #495057;
}

.result-text {
  color: #166534;
}

/* 滚动条样式 */
.input-content::-webkit-scrollbar,
.output-content::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.input-content::-webkit-scrollbar-track,
.output-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.input-content::-webkit-scrollbar-thumb,
.output-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.input-content::-webkit-scrollbar-thumb:hover,
.output-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保滚动条可见 */
.input-content,
.output-content {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>
