<template>
  <div class="pdf-viewer-container">
    <!-- 控制栏 -->
    <div class="pdf-controls" v-if="showControls && !loading && !error">
      <div class="control-group">
        <el-button-group>
          <el-button 
            size="small" 
            @click="zoomOut"
            :disabled="scale <= 0.5"
          >
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button 
            size="small" 
            @click="zoomIn"
            :disabled="scale >= 3.0"
          >
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="control-group">
        <el-button size="small" @click="resetZoom">重置缩放</el-button>
        <el-button size="small" @click="forceReload">刷新PDF</el-button>
      </div>
    </div>

    <!-- PDF内容区域 -->
    <div class="pdf-content" ref="contentRef">
      <!-- 加载状态 -->
      <div v-if="loading" class="pdf-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载PDF...</span>
        <div class="debug-info">
          <div>数据类型: {{ sourceType }}</div>
          <div>数据长度: {{ sourceLength }}</div>
          <div>处理状态: {{ processStatus }}</div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="pdf-error">
        <el-icon class="error-icon"><Warning /></el-icon>
        <p>{{ error }}</p>
        <el-button @click="forceReload" size="small">重试</el-button>
      </div>
      
      <!-- PDF渲染 -->
      <div v-else-if="processedSource" class="pdf-render-area">
        <SimplePdfEmbed
          :key="`pdf-${processedSource.length}-${Date.now()}`"
          :source="processedSource"
          :scale="scale"
          @loaded="handlePdfLoaded"
          @error="handlePdfError"
          @rendered="handlePdfRendered"
        />
      </div>
      
      <!-- 无内容 -->
      <div v-else class="pdf-empty">
        <el-empty description="暂无PDF内容" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElButton, ElButtonGroup, ElIcon, ElEmpty } from 'element-plus'
import { ZoomIn, ZoomOut, Loading, Warning } from '@element-plus/icons-vue'
import SimplePdfEmbed from './SimplePdfEmbed.vue'

// Props
interface Props {
  source?: string | null
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showControls: true
})

// Emits
const emit = defineEmits<{
  loaded: []
  error: [error: string]
  rendered: []
}>()

// 响应式数据
const contentRef = ref<HTMLElement>()
const loading = ref(false)
const error = ref('')
const scale = ref(1.0)
let loadingTimer: NodeJS.Timeout | null = null

// 处理后的PDF源
const processedSource = ref<string | null>(null)

// 调试信息
const sourceType = computed(() => {
  if (!props.source) return '无数据'
  if (typeof props.source === 'string') {
    if (props.source.startsWith('http://') || props.source.startsWith('https://')) return 'HTTP URL'
    if (props.source.startsWith('/')) return '相对URL'
    if (props.source.startsWith('data:application/pdf;base64,')) return 'Data URL'
    if (props.source.startsWith('JVBERi0')) return 'Base64'
    return '字符串'
  }
  return '其他类型'
})

const sourceLength = computed(() => {
  return props.source ? props.source.length : 0
})

const processStatus = computed(() => {
  if (loading.value) return '加载中'
  if (error.value) return '加载失败'
  if (processedSource.value) return '处理完成'
  return '等待处理'
})

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3.0) {
    scale.value = Math.min(scale.value + 0.25, 3.0)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(scale.value - 0.25, 0.5)
  }
}

const resetZoom = () => {
  scale.value = 1.0
}

// 处理PDF数据
const processPdfSource = (source: string | null) => {
  
  if (!source) {
    processedSource.value = null
    return
  }
  
  try {
    let finalSource: string
    
    // 如果是HTTP/HTTPS URL（stream接口）
    if (source.startsWith('http://') || source.startsWith('https://') || source.startsWith('/')) {
      finalSource = source
    }
    // 如果已经是完整的data URL
    else if (source.startsWith('data:application/pdf;base64,')) {
      finalSource = source
    }
    // 如果是纯base64字符串
    else if (source.startsWith('JVBERi0')) {
      finalSource = `data:application/pdf;base64,${source}`
    }
    else {
      throw new Error(`不支持的PDF数据格式: ${source.substring(0, 50)}...`)
    }
    
    // 如果是base64格式，需要验证
    if (finalSource.startsWith('data:application/pdf;base64,')) {
      const base64Data = finalSource.split(',')[1]
      if (!base64Data) {
        throw new Error('无效的data URL格式，缺少base64数据')
      }
      
      // 清理base64字符串
      const cleanBase64 = base64Data.replace(/\s+/g, '')
      
      // 验证base64解码
      const binaryData = atob(cleanBase64)
      
      if (!binaryData.startsWith('%PDF-')) {
        throw new Error(`无效的PDF文件头: ${binaryData.substring(0, 20)}`)
      }
      
      // 构造最终的data URL
      finalSource = `data:application/pdf;base64,${cleanBase64}`
    }
    
    processedSource.value = finalSource
    
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : '未知错误'
    console.error('PDF数据处理失败:', errorMsg)
    error.value = `PDF数据处理失败: ${errorMsg}`
    processedSource.value = null
  }
}

// 强制重新加载
const forceReload = () => {
  
  // 清理状态
  loading.value = false
  error.value = ''
  processedSource.value = null
  
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
  
  // 重新处理数据
  nextTick(() => {
    if (props.source) {
      loading.value = true
      // 设置超时
      loadingTimer = setTimeout(() => {
        if (loading.value) {
          loading.value = false
          error.value = 'PDF加载超时，请重试'
        }
      }, 30000)
      
      processPdfSource(props.source)
    }
  })
}

// 事件处理
const handlePdfLoaded = () => {
  loading.value = false
  error.value = ''
  
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
  
  emit('loaded')
}

const handlePdfError = (errorMsg: string) => {
  console.error('PDF加载错误:', errorMsg)
  loading.value = false
  error.value = errorMsg
  
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
  
  emit('error', errorMsg)
}

const handlePdfRendered = () => {
  emit('rendered')
}

// 监听源数据变化
watch(() => props.source, (newSource) => {
  
  // 清理之前的状态
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
  
  if (newSource) {
    loading.value = true
    error.value = ''
    
    // 设置超时
    loadingTimer = setTimeout(() => {
      if (loading.value) {
        loading.value = false
        error.value = 'PDF加载超时，请重试'
      }
    }, 30000)
    
    // 处理数据
    processPdfSource(newSource)
  } else {
    loading.value = false
    error.value = ''
    processedSource.value = null
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  zoomIn,
  zoomOut,
  resetZoom,
  forceReload,
  getScale: () => scale.value
})
</script>

<style scoped>
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.pdf-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.pdf-loading,
.pdf-error,
.pdf-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #909399;
}

.pdf-loading .is-loading {
  font-size: 24px;
  margin-bottom: 12px;
  color: #409EFF;
}

.debug-info {
  margin-top: 16px;
  padding: 12px;
  background: #f0f0f0;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
}

.debug-info > div {
  margin: 4px 0;
}

.pdf-error .error-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 12px;
}

.pdf-error p {
  margin: 0 0 16px 0;
  font-size: 14px;
  max-width: 400px;
}

.pdf-render-area {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

@media (max-width: 768px) {
  .pdf-controls {
    padding: 8px 12px;
  }

  .control-group {
    gap: 8px;
  }

  .pdf-content {
    padding: 12px;
  }
}
</style>