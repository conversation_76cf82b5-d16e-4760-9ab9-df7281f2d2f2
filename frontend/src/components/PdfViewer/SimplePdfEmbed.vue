<template>
  <div class="simple-pdf-embed" ref="containerRef">
    <canvas 
      v-for="pageNum in pageCount" 
      :key="pageNum"
      :ref="el => setCanvasRef(el, pageNum)"
      class="pdf-page"
      :style="{ transform: `scale(${scale})` }"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { GlobalWorkerOptions, getDocument } from 'pdfjs-dist/legacy/build/pdf.mjs'
import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist'

// 确保PDF.js Worker已设置
import PdfWorker from 'pdfjs-dist/legacy/build/pdf.worker.min.mjs?url'
if (!GlobalWorkerOptions || !GlobalWorkerOptions.workerSrc) {
  GlobalWorkerOptions.workerSrc = PdfWorker
}

// Props
interface Props {
  source: string
  scale?: number
}

const props = withDefaults(defineProps<Props>(), {
  scale: 1.0
})

// Emits
const emit = defineEmits<{
  loaded: []
  error: [error: string]
  rendered: []
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const pageCount = ref(0)
const canvasRefs = new Map<number, HTMLCanvasElement>()
let pdfDoc: PDFDocumentProxy | null = null

// 设置canvas引用
const setCanvasRef = (el: HTMLCanvasElement | null, pageNum: number) => {
  if (el) {
    canvasRefs.set(pageNum, el)
  } else {
    canvasRefs.delete(pageNum)
  }
}

// 加载PDF文档
const loadPdf = async () => {
  try {
    console.log('SimplePdfEmbed: 开始加载PDF文档')
    
    if (!props.source) {
      throw new Error('PDF源数据为空')
    }

    // 清理之前的文档
    if (pdfDoc) {
      pdfDoc.destroy()
      pdfDoc = null
    }

    console.log('SimplePdfEmbed: 调用getDocument，源类型:', typeof props.source)
    console.log('SimplePdfEmbed: 源内容:', props.source.substring(0, 100))
    
    // 配置getDocument参数
    const documentConfig = typeof props.source === 'string' && 
                          (props.source.startsWith('http') || props.source.startsWith('/'))
      ? {
          url: props.source,
          httpHeaders: {
            'Accept': 'application/pdf'
          },
          withCredentials: false
        }
      : props.source
    
    console.log('SimplePdfEmbed: 使用配置:', documentConfig)
    const loadingTask = getDocument(documentConfig)
    
    pdfDoc = await loadingTask.promise
    pageCount.value = pdfDoc.numPages
    
    console.log(`SimplePdfEmbed: PDF加载成功，共${pageCount.value}页`)
    emit('loaded')
    
    // 等待DOM更新后渲染页面
    await nextTick()
    await renderAllPages()
    
  } catch (error) {
    console.error('SimplePdfEmbed: PDF加载失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    emit('error', `PDF加载失败: ${errorMsg}`)
  }
}

// 渲染所有页面
const renderAllPages = async () => {
  if (!pdfDoc) return
  
  try {
    console.log('SimplePdfEmbed: 开始渲染所有页面')
    
    for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
      await renderPage(pageNum)
    }
    
    console.log('SimplePdfEmbed: 所有页面渲染完成')
    emit('rendered')
    
  } catch (error) {
    console.error('SimplePdfEmbed: 页面渲染失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    emit('error', `页面渲染失败: ${errorMsg}`)
  }
}

// 渲染单个页面
const renderPage = async (pageNum: number) => {
  if (!pdfDoc) return
  
  try {
    const canvas = canvasRefs.get(pageNum)
    if (!canvas) {
      console.warn(`SimplePdfEmbed: 找不到第${pageNum}页的canvas`)
      return
    }
    
    const page: PDFPageProxy = await pdfDoc.getPage(pageNum)
    const viewport = page.getViewport({ scale: 1.0 })
    
    // 设置canvas尺寸
    canvas.width = viewport.width
    canvas.height = viewport.height
    canvas.style.width = `${viewport.width}px`
    canvas.style.height = `${viewport.height}px`
    
    const context = canvas.getContext('2d')
    if (!context) {
      throw new Error('无法获取canvas 2d context')
    }
    
    // 渲染页面
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise
    
    console.log(`SimplePdfEmbed: 第${pageNum}页渲染完成`)
    
  } catch (error) {
    console.error(`SimplePdfEmbed: 第${pageNum}页渲染失败:`, error)
    throw error
  }
}

// 监听source变化
watch(() => props.source, (newSource, oldSource) => {
  console.log('SimplePdfEmbed: PDF源数据变化')
  console.log('新源:', newSource)
  console.log('旧源:', oldSource) 
  console.log('源数据类型:', typeof newSource)
  if (newSource) {
    console.log('SimplePdfEmbed: 开始重新加载PDF')
    loadPdf()
  } else {
    console.log('SimplePdfEmbed: 源为空，不加载')
  }
}, { immediate: true })

// 监听缩放变化
watch(() => props.scale, () => {
  console.log('SimplePdfEmbed: 缩放比例变化:', props.scale)
})

// 组件挂载
onMounted(() => {
  console.log('SimplePdfEmbed: 组件已挂载')
  console.log('SimplePdfEmbed: 挂载时的props.source:', props.source)
})

// 清理资源
onUnmounted(() => {
  console.log('SimplePdfEmbed: 组件即将卸载')
  if (pdfDoc) {
    pdfDoc.destroy()
    pdfDoc = null
  }
  canvasRefs.clear()
})
</script>

<style scoped>
.simple-pdf-embed {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px 0;
}

.pdf-page {
  display: block;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background: white;
  transform-origin: center;
  transition: transform 0.2s ease;
}

.pdf-page + .pdf-page {
  margin-top: 20px;
}
</style>