<template>
  <el-dialog
    v-model="dialogVisible"
    :title="config.title"
    width="1200px"
    center
  >
    <div class="regenerate-container">
      <div class="regenerate-main">
        <el-form :model="form" label-width="120px" :rules="formRules" ref="formRef">
          <el-form-item label="提示词" prop="prompt">
            <el-input
              v-model="form.prompt"
              type="textarea"
              :rows="25"
              placeholder="请输入提示词，帮助更好地生成关系图谱"
              class="prompt-textarea"
            />
          </el-form-item>
        </el-form>
      </div>
      <PromptHistoryPanel 
        ref="historyPanelRef"
        :prompt-key="config.promptKey"
        :current-content="form.prompt"
        :current-version="currentVersion"
        @view-version="handleViewVersion"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="generating" @click="handleSubmit">
          {{ generating ? '生成中...' : '确认生成' }}
        </el-button>
        <el-button type="primary" @click="handleSavePrompt">
          保存提示词
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { promptApi } from '@/api'

import PromptHistoryPanel from './PromptHistoryPanel.vue'

const props = defineProps<{
  visible: boolean
  config: {
    title: string
    id: string | number
    promptKey: string
    onRegenerate: (formData: any) => Promise<any>
    handleRegenerateSuccess: () => void
  }
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'refresh-history'): void
}>()

// 表单相关
const formRef = ref<FormInstance>()
const form = ref({
  prompt: ''
})

// 表单验证规则
const formRules = {
  prompt: [
    { required: true, message: '请输入提示词', trigger: 'blur' },
    { min: 5, message: '提示词至少需要5个字符', trigger: 'blur' }
  ]
}

// 生成状态
const generating = ref(false)

// 对话框可见性
const dialogVisible = ref(false)

// 历史面板引用
const historyPanelRef = ref()

// 当前版本号
const currentVersion = ref<number>()

// Add debounce utility
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function (...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 获取提示词
const fetchPrompt = async () => {
  try {
    const result = await promptApi.getByKey(props.config.promptKey)
    if (result) {
      form.value.prompt = result
    }
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取提示词失败:', error)
  }
}

// 监听props.visible的变化
watch(() => props.visible, async (newVal) => {
  await nextTick()
  dialogVisible.value = newVal
  if (newVal) {
    // 当对话框打开时获取提示词
    await fetchPrompt()
  }
})

// 监听dialogVisible的变化
watch(dialogVisible, (newVal) => {
  if (newVal !== props.visible) {
    emit('update:visible', newVal)
  }
})

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.value = {
    prompt: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    generating.value = true

    const result = await props.config.onRegenerate(form.value)
    if (result.success) {
      // 保留业务逻辑成功提示（重新生成状态反馈）
      ElMessage.success('后台重新生成中，请稍后查看结果')
      dialogVisible.value = false
      resetForm()
    } else {
      // 保留业务逻辑错误提示
      ElMessage.error(result.message || '重新生成失败')
    }
  } catch (error: any) {
    if (error.name !== 'ValidationError') {
      // 全局已处理错误提示
      console.error('重新生成失败:', error)
    }
  } finally {
    generating.value = false
  }
}

// 处理保存提示词
const handleSavePrompt = async () => {
  if (!formRef.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    // 调用保存接口（使用全局成功提示）
    await promptApi.save({
      key: props.config.promptKey,
      prompt: form.value.prompt
    }, { showSuccess: true, successMessage: '保存成功' })

    // 刷新历史版本列表
    historyPanelRef.value?.loadHistory()
  } catch (error: any) {
    if (error.name !== 'ValidationError') {
      // 全局已处理错误提示
      console.error('保存提示词失败:', error)
    }
  }
}

// 查看版本内容
const handleViewVersion = (version: any) => {
  historyPanelRef.value?.loadHistory()
  form.value.prompt = version.promptMessage
  currentVersion.value = version.promptVersion
}

// 处理暂存提示词
const handleStaging = debounce(async (content: string) => {
  try {
    await promptApi.staging({
      key: props.config.promptKey,
      prompt: content
    })
    // 刷新历史版本列表
    historyPanelRef.value?.loadHistory()
  } catch (error) {
    console.error('暂存提示词失败:', error)
  }
}, 1000)

// 监听提示词变化
watch(() => form.value.prompt, (newVal) => {
  if (newVal) {
    console.log(newVal)
    handleStaging(newVal)
  }
})

// 暴露表单引用
defineExpose({
  formRef
})
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  margin-top: 15vh !important;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.dialog-footer) {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.regenerate-container {
  display: flex;
  gap: 20px;
  
  .regenerate-main {
    flex: 1;
    min-width: 0; // 防止flex子项溢出

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    .prompt-textarea {
      :deep(.el-textarea__inner) {
        height: 550px !important;
      }
    }
  }
}
</style> 