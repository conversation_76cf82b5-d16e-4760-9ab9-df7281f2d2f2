<template>
  <div class="prompt-history-panel">
    <div class="history-header">
      <h4>历史版本</h4>
      <div class="history-actions">
        <el-button type="info" size="small" @click="loadHistory">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>
    <el-table
      :data="historyList"
      style="width: 100%"
      height="500"
      highlight-current-row
    >
      <el-table-column prop="promptVersion" label="版本" width="100">
        <template #default="{ row }">
          <div class="version-cell">
            <span>{{ row.promptVersion === -1 ? '暂存' : row.promptVersion }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修订时间" min-width="180" />
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button 
            v-if="row.promptVersion !== -1"
            type="primary" 
            link 
            :disabled="row.promptMessage === props.currentContent"
            @click="handleViewVersion(row)"
          >
            恢复
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { promptApi } from '@/api'


interface PromptHistoryItem {
  id: number
  promptKey: string
  promptMessage: string
  promptVersion: number
  createTime: string
}

const props = defineProps<{
  promptKey: string
  currentContent?: string
  currentVersion?: number
}>()

const emit = defineEmits<{
  (e: 'view-version', version: PromptHistoryItem): void
}>()

const historyList = ref<PromptHistoryItem[]>([])

// 加载历史版本
const loadHistory = async () => {
  try {
    const result = await promptApi.getHistory(props.promptKey)
    if (result) {
      historyList.value = result.map((item: any) => ({
        ...item
      }))
    }
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取提示词历史失败:', error)
  }
}

// 查看版本内容
const handleViewVersion = (version: PromptHistoryItem) => {
  emit('view-version', version)
}

// 监听 promptKey 变化
watch(() => props.promptKey, () => {
  loadHistory()
})

// 暴露方法给父组件
defineExpose({
  loadHistory
})

// 初始加载
onMounted(() => {
  loadHistory()
})
</script>

<style scoped lang="scss">
.prompt-history-panel {
  width: 400px;
  border-left: 1px solid #dcdfe6;
  padding-left: 20px;
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #303133;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background-color: #409eff;
        border-radius: 2px;
      }
    }

    .history-actions {
      display: flex;
      gap: 8px;
    }
  }

  .version-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.el-table) {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        height: 44px;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }

    .el-button {
      padding: 4px 8px;
      font-size: 13px;
    }

    .el-table__cell {
      padding: 8px 0;
    }

    .el-table__body-wrapper {
      overflow-x: hidden;
    }
  }
}
</style> 