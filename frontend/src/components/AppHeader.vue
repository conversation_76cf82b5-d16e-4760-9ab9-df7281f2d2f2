<script setup lang="ts">
import { Setting, ArrowDown } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/userStore'
import { computed, watchEffect } from 'vue'
import logoUrl from '@/assets/logo.png'
import courtLogoUrl from '@/assets/court-logo.svg'

const router = useRouter()
const userStore = useUserStore()

// 从环境变量获取系统标题
const systemTitle = import.meta.env.VITE_SYSTEM_TITLE || '文书生成辅助工具'

// 获取当前登录用户姓名
const currentUserName = computed(() => {
  console.log('当前用户信息:', userStore.userInfo)
  console.log('是否已认证:', userStore.isAuthenticated)
  console.log('Token:', userStore.token)
  return userStore.userInfo?.name || '未登录'
})

// 添加监听器，监控用户状态变化
watchEffect(() => {
  console.log('用户状态变化监听:', {
    userInfo: userStore.userInfo,
    token: userStore.token,
    isAuthenticated: userStore.isAuthenticated
  })
})

// Title click handler - navigate to home page
const handleTitleClick = () => {
  router.push('/case')
}

// Settings click handler
const handleSettingsClick = () => {
  console.log('Settings clicked')
  // TODO: Implement settings functionality
}

// User menu command handler
const handleUserMenuCommand = (command: string) => {
  console.log('User menu command:', command)
  if (command === 'logout') {
    // 执行退出登录
    userStore.logout()
    // 跳转到登录页面
    router.push('/login')
  }
}
</script>

<template>
  <div class="app-header">
    <div class="header-left">
      <div class="logo-container">
        <img :src="courtLogoUrl" alt="Court Logo" class="court-logo" />
      </div>
      <span class="system-title" @click="handleTitleClick">{{ systemTitle }}</span>
    </div>
    <div class="header-right">
      <el-button
        :icon="Setting"
        text
        class="settings-btn"
        @click="handleSettingsClick"
      />
      <el-dropdown @command="handleUserMenuCommand" trigger="click">
        <div class="user-info">
          <el-avatar :size="32" :src="logoUrl" />
          <span class="user-name">{{ currentUserName }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 60px;
  width: 100%;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .logo-container {
      display: flex;
      align-items: center;
      padding: 6px;
    }

    .court-logo {
      height: 40px;
      width: auto;
      object-fit: contain;
    }

    .system-title {
      font-size: 20px;
      color: #1f63ff;
      font-weight: 600;
      margin: 0;
      letter-spacing: -0.02em;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      cursor: pointer;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 24px;

    .settings-btn {
      color: #409EFF;
      font-size: 18px;
      padding: 6px;
      border-radius: 6px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      background: transparent;

      &:hover {
        color: #337ecc;
        background-color: rgba(64, 158, 255, 0.08);
        transform: scale(1.05);
      }

      &:focus {
        color: #337ecc;
        background-color: rgba(64, 158, 255, 0.08);
        outline: none;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .el-avatar {
      border: 2px solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
      width: 60px;
      height: 40px;
      border-radius: 20px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 8px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      background: transparent;
      border: none;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);

        .dropdown-icon {
          transform: rotate(180deg);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }

    .user-name {
      color: #1d1d1f;
      font-size: 16px;
      font-weight: 500;
      margin-left: 16px;
      margin-right: 12px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      letter-spacing: -0.01em;
    }

    .dropdown-icon {
      color: #86868b;
      font-size: 12px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}
</style>
