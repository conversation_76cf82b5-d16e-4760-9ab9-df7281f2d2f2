<template>
  <div class="multi-page-viewer-container">
    <!-- 控制面板 - 暂时隐藏 -->
    <div class="control-panel" style="display: none;">
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <el-button-group>
          <el-button @click="zoomOut" :disabled="scale <= minScale">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="scale >= maxScale">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>

      <!-- OCR控制 -->
      <div class="ocr-controls">
        <el-switch
          v-model="showOcrOverlay"
          active-text="显示OCR"
          inactive-text="隐藏OCR"
        />
        <el-switch
          v-model="showTextContent"
          active-text="显示文字"
          inactive-text="隐藏文字"
          :disabled="!showOcrOverlay"
        />
      </div>

      <!-- 页面信息 -->
      <div class="page-info">
        <span class="total-pages">共 {{ pages.length }} 页</span>
        <span class="current-page">当前: 第 {{ currentVisiblePage }} 页</span>
      </div>

      <!-- 搜索框 -->
      <div class="search-controls" v-if="showOcrOverlay">
        <el-input
          v-model="searchText"
          placeholder="搜索文字内容"
          clearable
          @input="handleSearch"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 页面显示区域 -->
    <div class="pages-container" ref="containerRef" @scroll="handleScroll">
      <div 
        v-for="(page, index) in pages" 
        :key="index"
        :ref="el => setPageRef(el, index)"
        class="page-wrapper"
        :data-page="index + 1"
      >
        <!-- 图片和OCR覆盖层 -->
        <div class="page-content" :style="{ width: `${props.width}px`, height: `${props.height}px` }">
          <!-- 图片层 -->
          <div class="image-layer">
            <img
              :src="page.imageUrl"
              :alt="`第${index + 1}页`"
              class="page-image"
              :style="imageStyle"
              @load="handleImageLoad(index, $event)"
              @error="handleImageError(index, $event)"
            />
          </div>

          <!-- OCR文字覆盖层 -->
          <div class="ocr-overlay-layer" :style="{ width: `${props.width}px`, height: `${props.height}px`, position: 'absolute', top: '0', left: '0' }" v-if="showOcrOverlay && page.ocrData">
            <svg
              :width="props.width"
              :height="props.height"
              class="ocr-svg"
            >
              <!-- 渲染OCR文字块 -->
              <g v-for="(coordinate, coordIndex) in page.ocrData.result" :key="coordIndex">
                <g v-for="(block, blockIndex) in coordinate.blocks" :key="blockIndex">
                  <rect
                    :x="getScaledX(block.x)"
                    :y="getScaledY(block.y)"
                    :width="getScaledWidth(block.width)"
                    :height="getScaledHeight(block.height)"
                    :class="[
                      'ocr-text-rect',
                      { 'highlighted': isHighlighted(block) }
                    ]"
                    @click="handleTextBlockClick(block, coordinate, index)"
                    @mouseenter="handleTextBlockHover(block, true)"
                    @mouseleave="handleTextBlockHover(block, false)"
                  />
                  
                  <!-- 文字内容（可选显示） -->
                  <text
                    v-if="showTextContent"
                    :x="getScaledX(block.x) + 2"
                    :y="getScaledY(block.y) + 12"
                    class="ocr-text-content"
                    @click="handleTextBlockClick(block, coordinate, index)"
                  >
                    {{ block.text }}
                  </text>
                </g>
              </g>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElButton, ElButtonGroup, ElSwitch, ElInput, ElIcon, ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, Search } from '@element-plus/icons-vue'

// 定义页面数据类型
interface PageData {
  imageUrl: string
  imageWidth: number
  imageHeight: number
  ocrData?: any
}

// Props
interface Props {
  pages: PageData[]
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600
})

// Emits
const emit = defineEmits<{
  textBlockClick: [block: any, coordinate: any, pageIndex: number]
  textBlockHover: [block: any, isHover: boolean]
  pageChange: [pageIndex: number]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const pageRefs = ref<(HTMLElement | null)[]>([])

// 显示控制
const scale = ref(1)
const minScale = ref(0.1)
const maxScale = ref(5)
const showOcrOverlay = ref(true)
const showTextContent = ref(false)

// 搜索相关
const searchText = ref('')
const highlightedBlocks = ref<any[]>([])

// 当前可见页面
const currentVisiblePage = ref(1)

// 设置页面引用
const setPageRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    pageRefs.value[index] = el
  }
}

// 计算属性
const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain' as const
}))

// 坐标转换方法（基于第一页的尺寸）
const getScaledX = (x: number) => {
  if (props.pages.length === 0) return 0
  const firstPage = props.pages[0]
  return (x / firstPage.imageWidth) * props.width
}

const getScaledY = (y: number) => {
  if (props.pages.length === 0) return 0
  const firstPage = props.pages[0]
  return (y / firstPage.imageHeight) * props.height
}

const getScaledWidth = (width: number) => {
  if (props.pages.length === 0) return 0
  const firstPage = props.pages[0]
  return (width / firstPage.imageWidth) * props.width
}

const getScaledHeight = (height: number) => {
  if (props.pages.length === 0) return 0
  const firstPage = props.pages[0]
  return (height / firstPage.imageHeight) * props.height
}

// 判断文字块是否高亮
const isHighlighted = (block: any) => {
  return highlightedBlocks.value.includes(block)
}

// 事件处理
const handleTextBlockClick = (block: any, coordinate: any, pageIndex: number) => {
  emit('textBlockClick', block, coordinate, pageIndex)
}

const handleTextBlockHover = (block: any, isHover: boolean) => {
  emit('textBlockHover', block, isHover)
}

const handleImageLoad = (pageIndex: number, event: Event) => {
  console.log(`第${pageIndex + 1}页图片加载完成`)
}

const handleImageError = (pageIndex: number, event: Event) => {
  console.error(`第${pageIndex + 1}页图片加载失败`)
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < maxScale.value) {
    scale.value = Math.min(scale.value * 1.2, maxScale.value)
  }
}

const zoomOut = () => {
  if (scale.value > minScale.value) {
    scale.value = Math.max(scale.value / 1.2, minScale.value)
  }
}

const resetZoom = () => {
  scale.value = 1
}

// 搜索功能
const handleSearch = (query: string) => {
  if (!query) {
    highlightedBlocks.value = []
    return
  }

  const matches: any[] = []
  props.pages.forEach(page => {
    if (page.ocrData?.result) {
      page.ocrData.result.forEach((coordinate: any) => {
        coordinate.blocks?.forEach((block: any) => {
          if (block.text.toLowerCase().includes(query.toLowerCase())) {
            matches.push(block)
          }
        })
      })
    }
  })
  
  highlightedBlocks.value = matches
}

// 滚动处理
const handleScroll = () => {
  if (!containerRef.value || pageRefs.value.length === 0) return

  const containerTop = containerRef.value.scrollTop
  const containerHeight = containerRef.value.clientHeight
  const centerY = containerTop + containerHeight / 2

  // 找到当前可见的页面
  for (let i = 0; i < pageRefs.value.length; i++) {
    const pageEl = pageRefs.value[i]
    if (!pageEl) continue

    const pageTop = pageEl.offsetTop
    const pageBottom = pageTop + pageEl.offsetHeight

    if (centerY >= pageTop && centerY <= pageBottom) {
      if (currentVisiblePage.value !== i + 1) {
        currentVisiblePage.value = i + 1
        emit('pageChange', i)
      }
      break
    }
  }
}

// 监听搜索文字变化
watch(searchText, (newValue) => {
  handleSearch(newValue)
})
</script>

<style scoped>
.multi-page-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
}

.control-panel {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #fff;
  border-bottom: 1px solid #ddd;
  flex-shrink: 0;
}

.zoom-controls,
.ocr-controls,
.page-info,
.search-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-pages,
.current-page {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.pages-container {
  flex: 1;
  overflow-y: auto;
  background: #f0f2f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.page-wrapper {
  margin-bottom: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.page-content {
  position: relative;
  margin: 0 auto;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-layer {
  width: 100%;
  height: 100%;
}

.page-image {
  display: block;
  user-select: none;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.ocr-overlay-layer {
  pointer-events: none;
}

.ocr-svg {
  pointer-events: auto;
}

.ocr-text-rect {
  fill: rgba(0, 123, 255, 0.1);
  stroke: rgba(0, 123, 255, 0.3);
  stroke-width: 1;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ocr-text-rect:hover {
  fill: rgba(0, 123, 255, 0.2);
  stroke: rgba(0, 123, 255, 0.6);
  stroke-width: 2;
}

.ocr-text-rect.highlighted {
  fill: rgba(255, 193, 7, 0.3);
  stroke: rgba(255, 193, 7, 0.8);
  stroke-width: 2;
}

.ocr-text-content {
  font-size: 10px;
  fill: #333;
  pointer-events: none;
  user-select: none;
}

.pages-container::-webkit-scrollbar {
  width: 12px;
}

.pages-container::-webkit-scrollbar-track {
  background: #f2f2f7;
  border-radius: 6px;
}

.pages-container::-webkit-scrollbar-thumb {
  background: #c7c7cc;
  border-radius: 6px;
  border: 2px solid #f2f2f7;
}

.pages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
