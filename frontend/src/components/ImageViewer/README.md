# ImageViewer 组件库

图片和文档查看器组件库，支持OCR文字识别覆盖层显示。

## 组件列表

### 1. DocumentViewer (推荐) ⭐

全新的文档查看器组件，支持多页面文档显示，具有现代化的设计和完整的功能。

**功能特性：**
- 📄 **多页面文档**: 垂直滚动显示多页面文档
- 🔍 **缩放控制**: 支持图片缩放，可以放大缩小查看细节
- 📝 **OCR覆盖层**: 在图片上叠加OCR识别的文字区域
- 🎯 **文字选择**: 点击文字区域可以获取详细信息
- 🔎 **全文搜索**: 支持搜索文字内容并高亮显示
- 🎨 **现代设计**: 苹果风格的现代化界面设计
- 🌙 **深色模式**: 自动适配系统深色模式
- ⚡ **性能优化**: 虚拟滚动和懒加载优化

**基础用法：**

```vue
<template>
  <DocumentViewer
    :pages="documentPages"
    :container-width="800"
    :container-height="600"
    :show-controls="true"
    :show-search-panel="true"
    @text-block-click="handleTextBlockClick"
    @text-block-hover="handleTextBlockHover"
    @page-change="handlePageChange"
    @image-load="handleImageLoad"
    @image-error="handleImageError"
  />
</template>

<script setup>
import { DocumentViewer } from '@/components/ImageViewer'

const documentPages = [
  {
    imageUrl: 'page1.jpg',
    originalWidth: 1191,
    originalHeight: 1684,
    displayWidth: 800,
    displayHeight: 1131,
    ocrData: {
      logId: 'local',
      errorCode: 0,
      errorMsg: 'Success',
      result: [
        {
          blocks: [
            {
              x: 100,
              y: 100,
              width: 200,
              height: 30,
              text: '这是文档标题'
            }
          ]
        }
      ]
    },
    loading: false,
    error: undefined
  }
  // ... 更多页面
]

const handleTextBlockClick = (block, coordinate, pageIndex) => {
  console.log(`第${pageIndex + 1}页文字块点击:`, block.text)
}

const handlePageChange = (pageIndex) => {
  console.log(`切换到第${pageIndex + 1}页`)
}
</script>
```

**Props：**

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| pages | PageData[] | [] | 页面数据数组 |
| containerWidth | number | 800 | 容器宽度 |
| containerHeight | number | 600 | 容器高度 |
| showControls | boolean | false | 是否显示控制面板 |
| showSearchPanel | boolean | false | 是否显示搜索面板 |
| defaultScale | number | 1 | 默认缩放比例 |

**Events：**

| 事件名 | 参数 | 说明 |
|--------|------|------|
| text-block-click | (block, coordinate, pageIndex) | 文字块点击事件 |
| text-block-hover | (block, isHover) | 文字块悬停事件 |
| page-change | (pageIndex) | 页面切换事件 |
| image-load | (pageIndex, event) | 图片加载完成事件 |
| image-error | (pageIndex, event) | 图片加载失败事件 |

### 2. ImageViewer (传统)

单页面图片查看器组件，适用于简单的图片显示需求。

**使用方法：**

```vue
<template>
  <ImageViewer
    :image-url="imageUrl"
    :file-name="fileName"
    :ocr-data="ocrData"
    :width="800"
    :height="600"
    @text-block-click="handleTextBlockClick"
    @text-block-hover="handleTextBlockHover"
    @image-load="handleImageLoad"
    @image-error="handleImageError"
  />
</template>
```

### 3. MultiPageImageViewer (传统)

多页面图片查看器组件，支持页面切换。

## 数据结构

### PageData 接口

```typescript
interface PageData {
  imageUrl: string          // 图片URL
  originalWidth: number     // 原始宽度
  originalHeight: number    // 原始高度
  displayWidth: number      // 显示宽度
  displayHeight: number     // 显示高度
  ocrData?: any            // OCR数据
  loading?: boolean        // 加载状态
  error?: string          // 错误信息
}
```

### OCR数据结构

```typescript
interface OcrData {
  logId: string
  errorCode: number
  errorMsg: string
  result: Array<{
    blocks: Array<{
      x: number
      y: number
      width: number
      height: number
      text: string
    }>
  }>
}
```

## 最佳实践

1. **推荐使用 DocumentViewer**：新项目建议使用 DocumentViewer 组件，它提供了更好的用户体验和性能。

2. **页面数据准备**：确保为每个页面提供正确的原始尺寸和显示尺寸，以保证OCR坐标的准确映射。

3. **响应式设计**：组件会自动适应容器尺寸变化，建议在父容器中设置合适的尺寸约束。

4. **性能优化**：对于大量页面的文档，组件内置了虚拟滚动优化，无需额外处理。

5. **错误处理**：合理处理图片加载失败的情况，提供重试机制。

## 控制功能

### 缩放控制
- 放大按钮：放大图片查看细节
- 缩小按钮：缩小图片查看全貌
- 重置按钮：重置为100%缩放

### OCR控制
- 显示/隐藏OCR覆盖层
- 显示/隐藏文字内容

### 搜索功能
- 输入关键词搜索文字内容
- 匹配的文字块会高亮显示
- 支持上一个/下一个搜索结果导航

## 注意事项

1. 图片URL需要支持跨域访问
2. OCR坐标系统基于图片的原始尺寸
3. 组件会自动处理图片缩放和坐标转换
4. 建议为大图片设置合适的容器尺寸以获得最佳性能
5. DocumentViewer 组件支持深色模式，会自动适配系统主题

## 示例演示

- 查看 `DocumentViewerDemo.vue` 文件获取 DocumentViewer 的完整使用示例
- 查看 `ImageViewerDemo.vue` 文件获取传统 ImageViewer 的使用示例

## 迁移指南

从传统的 ImageViewer 迁移到 DocumentViewer：

```vue
<!-- 旧版本 -->
<ImageViewer
  :image-url="imageUrl"
  :ocr-data="ocrData"
  :width="800"
  :height="600"
/>

<!-- 新版本 -->
<DocumentViewer
  :pages="[{
    imageUrl: imageUrl,
    originalWidth: 1191,
    originalHeight: 1684,
    displayWidth: 800,
    displayHeight: 1131,
    ocrData: ocrData
  }]"
  :container-width="800"
  :container-height="600"
  :show-controls="true"
/>
```
