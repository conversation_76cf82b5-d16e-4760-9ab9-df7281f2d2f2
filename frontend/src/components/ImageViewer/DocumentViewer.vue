<template>
  <div class="document-viewer" ref="viewerRef">
    <!-- 控制栏 -->
    <div class="controls-bar" v-if="showControls">
      <div class="controls-left">
        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <el-button-group>
            <el-button @click="zoomOut" :disabled="scale <= minScale" size="small">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="fitToContainer" class="zoom-display" size="small">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button @click="zoomIn" :disabled="scale >= maxScale" size="small">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <!-- 旋转控制 -->
        <div class="rotation-controls">
          <el-button-group>
            <el-button @click="rotateLeft" size="small" title="逆时针旋转90°">
              <el-icon><RefreshLeft /></el-icon>
            </el-button>
            <el-button @click="rotateRight" size="small" title="顺时针旋转90°">
              <el-icon><RefreshRight /></el-icon>
            </el-button>
            <el-button @click="resetRotation" size="small" title="重置旋转" v-if="rotation !== 0">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <!-- OCR控制 -->
        <div class="ocr-controls" v-if="showOcrControls && hasOcrData">
          <el-switch
            v-model="showOcrOverlay"
            size="small"
            active-text="OCR"
            inactive-text="OCR"
            inline-prompt
          />
          <el-switch
            v-model="showTextContent"
            size="small"
            active-text="文字"
            inactive-text="文字"
            inline-prompt
            :disabled="!showOcrOverlay"
          />
        </div>
      </div>

      <div class="controls-right">
        <!-- 搜索控制 -->
        <div class="search-controls" v-if="showSearchPanel">
          <el-input
            v-model="searchText"
            placeholder="搜索文档内容..."
            size="small"
            clearable
            style="width: 200px"
            :disabled="!hasOcrData"
            @input="handleSearchInput"
            @keyup.enter="handleSearchEnter"
            @clear="handleSearchClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="search-navigation" v-if="searchText.trim()">
            <span class="search-count">{{ searchResults.length > 0 ? currentSearchIndex + 1 : 0 }}/{{ searchResults.length }}</span>
            <el-button-group>
              <el-button @click="prevSearchResult" :disabled="searchResults.length === 0" size="small">
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <el-button @click="nextSearchResult" :disabled="searchResults.length === 0" size="small">
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 页面信息 -->
        <div class="page-info" v-if="pages.length > 0">
          <span>{{ currentPageIndex + 1 }} / {{ pages.length }}</span>
        </div>
      </div>
    </div>

    <!-- 文档容器 -->
    <div class="document-container" ref="containerRef" @scroll="handleScroll" @click="handleContainerClick">
      <div class="pages-wrapper">
        <div
          v-for="(page, index) in pages"
          :key="`page-${index}`"
          class="page-item"
          :class="{ 'page-active': currentPageIndex === index }"
          :data-page-index="index"
          @mouseleave="clearDragState"
        >
          <!-- 页面内容 -->
          <div class="page-content" :style="getPageStyle(page)">
            <!-- 图片层 -->
            <div class="image-layer">
              <img
                :src="page.imageUrl"
                :alt="`第${index + 1}页`"
                class="page-image"
                @load="handleImageLoad(index, $event)"
                @error="handleImageError(index, $event)"
                @mousedown="handleImageMouseDown(index, $event)"
                draggable="false"
              />
            </div>

            <!-- 段落悬浮层 - 底层 -->
            <svg
              v-if="showOcrOverlay && page.ocrResult"
              class="ocr-overlay-svg paragraph-hover-layer"
              :width="getPageStyle(page).width"
              :height="getPageStyle(page).height"
              :viewBox="getOcrViewBox(page)"
              preserveAspectRatio="none"
              :style="getOcrOverlayStyle(page)"
            >
              <!-- 段落级别的交互 - 悬浮和点击选择 -->
              <rect
                v-for="(block, blockIndex) in getTextBlocks(page)"
                :key="`paragraph-${index}-${blockIndex}`"
                class="paragraph-rect"
                :class="{
                  'paragraph-hovered': isBlockHovered(index, blockIndex),
                  'paragraph-selected': isParagraphSelected(index, blockIndex)
                }"
                :x="block.x"
                :y="block.y"
                :width="block.width"
                :height="block.height"
                @mouseenter="handleBlockHover(index, blockIndex, true)"
                @mouseleave="handleBlockHover(index, blockIndex, false)"
                @dblclick.stop="handleParagraphClick(index, blockIndex, $event)"
                @click.stop="handleParagraphSingleClick(index, blockIndex, $event)"
              />

              <!-- 段落选择工具栏 - 显示在选中段落的右下角 -->
              <foreignObject
                v-if="getParagraphSelectionBounds(index) && showSelectionToolbar && selectedTextForToolbar"
                :x="getParagraphSelectionBounds(index).x + getParagraphSelectionBounds(index).width - 45"
                :y="getParagraphSelectionBounds(index).y + getParagraphSelectionBounds(index).height - 2"
                width="90"
                height="36"
                class="selection-toolbar-container"
              >
                <div class="selection-toolbar" @mousedown.stop @click.stop>
                  <div class="toolbar-item" @click="handleToolbarCopy" title="复制选中文本 (Ctrl+C)">
                    <el-icon class="toolbar-icon">
                      <DocumentCopy />
                    </el-icon>
                    <span class="toolbar-text">复制</span>
                  </div>
                </div>
              </foreignObject>
            </svg>

            <!-- OCR行级别选择层 - 长按拖拽选择 -->
            <svg
              v-if="showOcrOverlay && page.ocrResult && props.enableTextSelection"
              class="ocr-overlay-svg ocr-line-layer"
              :width="getPageStyle(page).width"
              :height="getPageStyle(page).height"
              :viewBox="getOcrViewBox(page)"
              preserveAspectRatio="none"
              :style="getOcrOverlayStyle(page)"
            >
              <!-- OCR行高亮显示（拖拽过程中和最终选择） -->
              <rect
                v-for="(ocrBlock, ocrIndex) in getOcrBlocks(page)"
                :key="`ocr-highlight-${index}-${ocrIndex}`"
                v-show="isOcrLineHighlighted(index, ocrIndex)"
                class="ocr-line-highlight"
                :x="ocrBlock.x"
                :y="ocrBlock.y"
                :width="ocrBlock.width"
                :height="ocrBlock.height"
              />

              <!-- OCR行交互区域（透明，用于事件处理） -->
              <rect
                v-for="(ocrBlock, ocrIndex) in getOcrBlocks(page)"
                :key="`ocr-line-${index}-${ocrIndex}`"
                class="ocr-line-interaction"
                :x="ocrBlock.x"
                :y="ocrBlock.y"
                :width="ocrBlock.width"
                :height="ocrBlock.height"
                @mousedown.stop="handleOcrLineMouseDown(index, ocrIndex, $event)"
                @mouseenter="handleOcrLineMouseEnter(index, ocrIndex, $event)"
                @mouseleave="handleOcrLineMouseLeave(index, ocrIndex, $event)"
                @mouseup.stop="handleOcrLineMouseUp(index, ocrIndex, $event)"
                @dblclick.stop="handleOcrLineDoubleClick(index, ocrIndex, $event)"
              />

              <!-- 整体选择框 - 框住所有选中的行（只在非拖拽状态下显示） -->
              <rect
                v-if="getOcrSelectionBounds(index) && !isDragging"
                :key="`ocr-selection-${index}`"
                class="ocr-selection-bounds"
                :x="getOcrSelectionBounds(index).x"
                :y="getOcrSelectionBounds(index).y"
                :width="getOcrSelectionBounds(index).width"
                :height="getOcrSelectionBounds(index).height"
              />

              <!-- 选择工具栏 - 显示在选择框右下角 -->
              <foreignObject
                v-if="getOcrSelectionBounds(index) && !isDragging && showSelectionToolbar && selectedTextForToolbar"
                :x="getOcrSelectionBounds(index).x + getOcrSelectionBounds(index).width - 45"
                :y="getOcrSelectionBounds(index).y + getOcrSelectionBounds(index).height + 6"
                width="90"
                height="36"
                class="selection-toolbar-container"
              >
                <div class="selection-toolbar" @mousedown.stop @click.stop>
                  <div class="toolbar-item" @click="handleToolbarCopy" title="复制选中文本 (Ctrl+C)">
                    <el-icon class="toolbar-icon">
                      <DocumentCopy />
                    </el-icon>
                    <span class="toolbar-text">复制</span>
                  </div>
                </div>
              </foreignObject>
            </svg>



            <!-- 搜索高亮层 -->
            <svg
              v-if="showOcrOverlay && page.ocrResult && searchResults.length > 0"
              class="ocr-overlay-svg search-highlight-layer"
              :width="getPageStyle(page).width"
              :height="getPageStyle(page).height"
              :viewBox="getOcrViewBox(page)"
              preserveAspectRatio="none"
              :style="getOcrOverlayStyle(page)"
            >
              <!-- 搜索结果高亮 -->
              <rect
                v-for="(result, resultIndex) in getPageSearchResults(index)"
                :key="`search-${index}-${resultIndex}`"
                class="search-highlight-rect"
                :x="result.block.x"
                :y="result.block.y"
                :width="result.block.width"
                :height="result.block.height"
              />
            </svg>

            <!-- 外部高亮层 - 只显示不与搜索结果重叠的外部高亮 -->
            <svg
              v-if="showOcrOverlay && page.ocrResult && getNonOverlappingExternalHighlights(index).length > 0"
              class="ocr-overlay-svg external-highlight-layer"
              :width="getPageStyle(page).width"
              :height="getPageStyle(page).height"
              :viewBox="getOcrViewBox(page)"
              preserveAspectRatio="none"
              :style="getOcrOverlayStyle(page)"
            >
              <!-- 外部高亮结果 -->
              <rect
                v-for="(result, resultIndex) in getNonOverlappingExternalHighlights(index)"
                :key="`external-${index}-${resultIndex}`"
                class="external-highlight-rect"
                :x="result.block.x"
                :y="result.block.y"
                :width="result.block.width"
                :height="result.block.height"
                @click="handleExternalHighlightClick(result)"
              />
            </svg>

            <!-- 文字内容覆盖层 -->
            <div
              v-if="showOcrOverlay && showTextContent && page.ocrResult"
              class="text-content-overlay"
            >
              <div
                v-for="(block, blockIndex) in getTextBlocks(page)"
                :key="`text-${index}-${blockIndex}`"
                class="text-content-item"
                :style="getTextBlockStyle(block, page)"
              >
                {{ block.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElButton, ElButtonGroup, ElSwitch, ElInput, ElIcon, ElMessage } from 'element-plus'
import { copyToClipboard } from '@/utils/api-helper'
import {
  ZoomIn,
  ZoomOut,
  Search,
  ArrowUp,
  ArrowDown,
  RefreshLeft,
  RefreshRight,
  Refresh,
  DocumentCopy
} from '@element-plus/icons-vue'
import type { UnifiedTextBlock, NewTextBlock, OcrPageResult } from '@/types/ocr'

// 页面数据接口
interface PageData {
  imageUrl: string
  originalWidth: number
  originalHeight: number
  ocrResult?: any
  loading?: boolean
  error?: string
}

// 使用统一的文字块接口
type TextBlock = UnifiedTextBlock

// 搜索结果接口
interface SearchResult {
  pageIndex: number
  block: TextBlock
  similarity?: number // 相似度 (0-1)
}

// 外部高亮数据接口
interface ExternalHighlight {
  pageNumber?: number // 页码变为可选，支持全文搜索
  text: string
  locations?: Array<{
    x: number
    y: number
    width: number
    height: number
  }>
}

// Props
interface Props {
  pages: PageData[]
  containerWidth?: number
  containerHeight?: number
  showControls?: boolean
  showOcrControls?: boolean
  showSearchPanel?: boolean
  defaultScale?: number
  enableTextSelection?: boolean
  externalHighlights?: ExternalHighlight[]
  fuzzyMatchThreshold?: number // 模糊匹配起始阈值 (0-1)，会递减到0.7直到找到匹配
  enableFuzzyMatch?: boolean // 是否启用模糊匹配
}

const props = withDefaults(defineProps<Props>(), {
  containerWidth: 800,
  containerHeight: 600,
  showControls: true,
  showOcrControls: true,
  showSearchPanel: true,
  defaultScale: 1,
  enableTextSelection: true,
  externalHighlights: () => [],
  fuzzyMatchThreshold: 0.8, // 默认80%相似度起始阈值，会递减到0.7
  enableFuzzyMatch: true // 默认启用模糊匹配
})

// Emits
const emit = defineEmits<{
  textBlockClick: [block: TextBlock, page: PageData, pageIndex: number]
  textBlockHover: [block: TextBlock, isHover: boolean]
  textSelection: [selection: any]
  pageChange: [pageIndex: number]
  imageLoad: [pageIndex: number, event: Event]
  imageError: [pageIndex: number, event: Event]
}>()

// 响应式数据
const viewerRef = ref<HTMLElement>()
const containerRef = ref<HTMLElement>()

// 显示控制
const scale = ref(props.defaultScale)
const minScale = ref(0.3)
const maxScale = ref(3)
const showOcrOverlay = ref(true)
const showTextContent = ref(false)

// 旋转控制
const rotation = ref(0) // 旋转角度，0, 90, 180, 270

// 页面状态
const currentPageIndex = ref(0)
const selectedTextBlock = ref<TextBlock | null>(null)

// 搜索相关
const searchText = ref('')
const searchResults = ref<SearchResult[]>([])
const currentSearchIndex = ref(-1)

// 外部高亮相关
const externalHighlightResults = ref<SearchResult[]>([])
const processedExternalHighlights = ref<Set<string>>(new Set())

// 段落选择相关
const selectedParagraph = ref<{ pageIndex: number, paragraphIndex: number } | null>(null)
const hoveredBlock = ref<{ pageIndex: number, blockIndex: number } | null>(null)

// OCR行选择相关（拖拽选择）
const selectedOcrLines = ref<Set<string>>(new Set()) // 使用 "pageIndex-ocrIndex" 作为key
const isLongPressing = ref(false) // 保留用于状态检查
const isDragging = ref(false)
const dragStartLine = ref<{ pageIndex: number, ocrIndex: number } | null>(null)
const dragCurrentLine = ref<{ pageIndex: number, ocrIndex: number } | null>(null)
const justFinishedDragging = ref(false) // 标记刚完成拖拽，防止立即触发容器点击

// 拖拽框选相关
const dragStartPos = ref<{ x: number, y: number, pageIndex: number } | null>(null)
const dragCurrentPos = ref<{ x: number, y: number, pageIndex: number } | null>(null)
const tempSelectedOcrLines = ref<Set<string>>(new Set()) // 拖拽过程中的临时选择

// 文本选择工具栏相关
const showSelectionToolbar = ref(false)
const selectedTextForToolbar = ref('')

// 计算属性
const hasOcrData = computed(() => {
  return props.pages.some(page => {
    // 检查是否有OCR数据，支持多种数据源
    const hasOcrBlocks = page.ocrResult?.ocrBlocks && Array.isArray(page.ocrResult.ocrBlocks) && page.ocrResult.ocrBlocks.length > 0
    const hasBlocks = page.ocrResult?.blocks && Array.isArray(page.ocrResult.blocks) && page.ocrResult.blocks.length > 0
    
    return hasOcrBlocks || hasBlocks
  })
})

// 直接使用传入的容器尺寸
const calculatePageSize = (page: PageData) => {
  if (!page.originalWidth || !page.originalHeight) {
    return { width: 730, height: 1000 }
  }

  return {
    width: props.containerWidth,
    height: props.containerHeight
  }
}

// 获取页面样式
const getPageStyle = (page: PageData) => {
  const { width, height } = calculatePageSize(page)

  // 根据旋转角度调整尺寸
  const isRotated90or270 = rotation.value === 90 || rotation.value === 270
  const displayWidth = isRotated90or270 ? height : width
  const displayHeight = isRotated90or270 ? width : height

  return {
    width: `${displayWidth * scale.value}px`,
    height: `${displayHeight * scale.value}px`,
    transform: `rotate(${rotation.value}deg)`,
    transformOrigin: 'center center'
  }
}

// 获取文字块
const getTextBlocks = (page: PageData): TextBlock[] => {
  if (!page.ocrResult?.blocks || !Array.isArray(page.ocrResult.blocks)) {
    return []
  }

  const blocks: TextBlock[] = []

  page.ocrResult.blocks.forEach((block: NewTextBlock) => {
    // blockBbox 格式: [x1, y1, x2, y2]
    const [x1, y1, x2, y2] = block.blockBbox || [0, 0, 0, 0]

    // 根据旋转角度转换坐标
    const transformedCoords = transformCoordinates(x1, y1, x2, y2, page.originalWidth, page.originalHeight, rotation.value)

    blocks.push({
      x: transformedCoords.x,
      y: transformedCoords.y,
      width: transformedCoords.width,
      height: transformedCoords.height,
      text: block.blockContent || '',
      blockLabel: block.blockLabel
    })
  })

  return blocks
}

// 获取OCR块（行级别）
const getOcrBlocks = (page: PageData): TextBlock[] => {
  // 优先使用 ocrBlocks，如果没有则使用 blocks
  let dataSource = page.ocrResult?.ocrBlocks

  if (!dataSource || !Array.isArray(dataSource)) {
    dataSource = page.ocrResult?.blocks
  }

  if (!dataSource || !Array.isArray(dataSource)) {
    return []
  }

  const blocks: TextBlock[] = []

  dataSource.forEach((ocrBlock: any, index: number) => {
    // 尝试获取文本内容，支持多种字段名
    let blockText = ocrBlock.text || ocrBlock.blockContent || ocrBlock.content || ocrBlock.label || ocrBlock.blockLabel || ''

    // 尝试获取坐标信息，支持多种字段名
    let bbox = ocrBlock.bbox || ocrBlock.blockBbox || ocrBlock.bounds || ocrBlock.coordinates || [0, 0, 0, 0]

    // bbox 格式: [x1, y1, x2, y2]
    const [x1, y1, x2, y2] = Array.isArray(bbox) ? bbox : [0, 0, 0, 0]

    // 根据旋转角度转换坐标
    const transformedCoords = transformCoordinates(x1, y1, x2, y2, page.originalWidth, page.originalHeight, rotation.value)

    blocks.push({
      x: transformedCoords.x,
      y: transformedCoords.y,
      width: transformedCoords.width,
      height: transformedCoords.height,
      text: blockText
    })
  })

  return blocks
}

// 坐标转换函数
const transformCoordinates = (x1: number, y1: number, x2: number, y2: number, pageWidth: number, pageHeight: number, rotationAngle: number) => {
  const width = x2 - x1
  const height = y2 - y1

  switch (rotationAngle) {
    case 90:
      return {
        x: y1,
        y: pageWidth - x2,
        width: height,
        height: width
      }
    case 180:
      return {
        x: pageWidth - x2,
        y: pageHeight - y2,
        width: width,
        height: height
      }
    case 270:
      return {
        x: pageHeight - y2,
        y: x1,
        width: height,
        height: width
      }
    default:
      return {
        x: x1,
        y: y1,
        width: width,
        height: height
      }
  }
}

// 获取OCR覆盖层的viewBox
const getOcrViewBox = (page: PageData) => {
  // 根据旋转角度调整viewBox
  switch (rotation.value) {
    case 90:
      return `0 0 ${page.originalHeight} ${page.originalWidth}`
    case 180:
      return `0 0 ${page.originalWidth} ${page.originalHeight}`
    case 270:
      return `0 0 ${page.originalHeight} ${page.originalWidth}`
    default:
      return `0 0 ${page.originalWidth} ${page.originalHeight}`
  }
}

// 获取OCR覆盖层样式
const getOcrOverlayStyle = (page: PageData) => {
  // 由于我们在坐标转换中已经处理了旋转，这里不需要额外的transform
  return {}
}

// 获取文字块样式
const getTextBlockStyle = (block: TextBlock, page: PageData) => {
  const { width: pageWidth, height: pageHeight } = calculatePageSize(page)
  
  // 计算缩放比例
  const scaleX = pageWidth / page.originalWidth
  const scaleY = pageHeight / page.originalHeight
  
  return {
    position: 'absolute',
    left: `${block.x * scaleX * scale.value}px`,
    top: `${block.y * scaleY * scale.value}px`,
    width: `${block.width * scaleX * scale.value}px`,
    height: `${block.height * scaleY * scale.value}px`
  }
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < maxScale.value) {
    scale.value = Math.min(scale.value * 1.2, maxScale.value)
  }
}

const zoomOut = () => {
  if (scale.value > minScale.value) {
    scale.value = Math.max(scale.value / 1.2, minScale.value)
  }
}

const fitToContainer = () => {
  scale.value = 1
}

// 旋转控制
const rotateLeft = () => {
  rotation.value = (rotation.value - 90 + 360) % 360
}

const rotateRight = () => {
  rotation.value = (rotation.value + 90) % 360
}

const resetRotation = () => {
  rotation.value = 0
}

// 文字块交互
const handleTextBlockClick = (block: TextBlock, page: PageData, pageIndex: number) => {
  selectedTextBlock.value = block
  emit('textBlockClick', block, page, pageIndex)
}

const handleTextBlockHover = (block: TextBlock, isHover: boolean) => {
  emit('textBlockHover', block, isHover)
}

const isBlockSelected = (block: TextBlock) => {
  return selectedTextBlock.value === block
}

const isBlockHighlighted = (block: TextBlock) => {
  if (!searchText.value) return false
  return block.text.toLowerCase().includes(searchText.value.toLowerCase())
}

// 文字选择功能
// 段落选择状态检查
const isParagraphSelected = (pageIndex: number, paragraphIndex: number) => {
  return selectedParagraph.value?.pageIndex === pageIndex &&
         selectedParagraph.value?.paragraphIndex === paragraphIndex
}

const isBlockHovered = (pageIndex: number, blockIndex: number) => {
  return hoveredBlock.value?.pageIndex === pageIndex && hoveredBlock.value?.blockIndex === blockIndex
}

// OCR行选择状态检查
const getOcrLineKey = (pageIndex: number, ocrIndex: number) => {
  return `${pageIndex}-${ocrIndex}`
}

const isOcrLineSelected = (pageIndex: number, ocrIndex: number) => {
  return selectedOcrLines.value.has(getOcrLineKey(pageIndex, ocrIndex))
}

// 判断OCR行是否应该高亮（包括拖拽过程中的临时选择和最终选择）
const isOcrLineHighlighted = (pageIndex: number, ocrIndex: number) => {
  const lineKey = getOcrLineKey(pageIndex, ocrIndex)
  // 拖拽过程中显示临时选择的高亮，非拖拽状态显示最终选择的高亮
  if (isDragging.value) {
    return tempSelectedOcrLines.value.has(lineKey)
  } else {
    return selectedOcrLines.value.has(lineKey)
  }
}

// 计算OCR选择的整体边界框
const getOcrSelectionBounds = (pageIndex: number) => {
  if (selectedOcrLines.value.size === 0) return null

  const page = props.pages[pageIndex]
  if (!page?.ocrResult?.ocrBlocks) return null

  // 获取当前页面选中的OCR行
  const selectedIndices = Array.from(selectedOcrLines.value)
    .map(key => key.split('-').map(Number))
    .filter(([pIndex]) => pIndex === pageIndex)
    .map(([, ocrIndex]) => ocrIndex)

  if (selectedIndices.length === 0) return null

  // 计算所有选中行的边界
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

  selectedIndices.forEach(ocrIndex => {
    const ocrBlock = page.ocrResult.ocrBlocks[ocrIndex]
    if (ocrBlock) {
      const [x1, y1, x2, y2] = ocrBlock.bbox || [0, 0, 0, 0]

      // 根据旋转角度转换坐标
      const transformedCoords = transformCoordinates(x1, y1, x2, y2, page.originalWidth, page.originalHeight, rotation.value)

      minX = Math.min(minX, transformedCoords.x)
      minY = Math.min(minY, transformedCoords.y)
      maxX = Math.max(maxX, transformedCoords.x + transformedCoords.width)
      maxY = Math.max(maxY, transformedCoords.y + transformedCoords.height)
    }
  })

  if (minX === Infinity) return null

  // 添加一些边距，让选择框稍微大一点
  const padding = 8
  return {
    x: minX - padding,
    y: minY - padding,
    width: maxX - minX + padding * 2,
    height: maxY - minY + padding * 2
  }
}

// 计算段落选择的边界框
const getParagraphSelectionBounds = (pageIndex: number) => {
  if (!selectedParagraph.value || selectedParagraph.value.pageIndex !== pageIndex) return null

  const page = props.pages[pageIndex]
  const textBlocks = getTextBlocks(page)

  const paragraphIndex = selectedParagraph.value.paragraphIndex
  const block = textBlocks[paragraphIndex]
  if (!block) return null

  // 直接使用 getTextBlocks 返回的已转换坐标
  // 添加一些边距，让选择框稍微大一点
  const padding = 8
  return {
    x: block.x - padding,
    y: block.y - padding,
    width: block.width + padding * 2,
    height: block.height + padding * 2
  }
}

// 移除 isOcrLineSelecting 函数，现在拖拽过程中直接使用 isOcrLineSelected

// 鼠标悬浮 - 高亮段落（blocks）
const handleBlockHover = (pageIndex: number, blockIndex: number, isHover: boolean) => {
  if (isHover) {
    hoveredBlock.value = { pageIndex, blockIndex }
  } else {
    hoveredBlock.value = null
  }
}

// 点击选择OCR行
// OCR块双击已移除，统一使用段落双击

// 段落双击选择
const handleParagraphClick = (pageIndex: number, paragraphIndex: number, event?: Event) => {
  if (!props.enableTextSelection) return

  // 阻止事件冒泡，避免触发容器点击
  if (event) {
    event.stopPropagation()
  }

  // 清除OCR行选择（互斥）
  selectedOcrLines.value.clear()
  tempSelectedOcrLines.value.clear()

  // 如果点击的是已选中的段落，则取消选择
  if (isParagraphSelected(pageIndex, paragraphIndex)) {
    selectedParagraph.value = null
  } else {
    // 选择新段落（自动取消之前的选择）
    selectedParagraph.value = { pageIndex, paragraphIndex }
  }

  emitParagraphSelection()
}

// 段落单击处理 - 阻止事件冒泡但不进行选择操作
const handleParagraphSingleClick = (pageIndex: number, paragraphIndex: number, event?: Event) => {
  // 阻止事件冒泡，避免触发容器点击
  if (event) {
    event.stopPropagation()
  }
}

// OCR行双击处理 - 选中包含该行的整个段落
const handleOcrLineDoubleClick = (pageIndex: number, ocrIndex: number, event?: Event) => {
  if (!props.enableTextSelection) return

  // 阻止事件冒泡，避免触发容器点击
  if (event) {
    event.stopPropagation()
  }

  // 清除OCR行选择
  selectedOcrLines.value.clear()
  tempSelectedOcrLines.value.clear()

  // 找到包含当前OCR行的段落
  const paragraphIndex = findParagraphContainingOcrLine(pageIndex, ocrIndex)
  if (paragraphIndex !== -1) {
    // 如果点击的是已选中的段落，则取消选择
    if (isParagraphSelected(pageIndex, paragraphIndex)) {
      selectedParagraph.value = null
    } else {
      // 选择包含该OCR行的段落
      selectedParagraph.value = { pageIndex, paragraphIndex }
    }

    emitParagraphSelection()
  }
}

// 处理容器点击 - 取消所有选择
const handleContainerClick = (event: MouseEvent) => {
  // 如果刚完成拖拽，不处理容器点击（防止拖拽结束时意外取消选择）
  if (justFinishedDragging.value) {
    justFinishedDragging.value = false
    return
  }

  // 清除所有选择
  selectedParagraph.value = null
  selectedOcrLines.value.clear()

  // 隐藏工具栏
  hideSelectionToolbar()

  // 发送清除选择事件
  emit('textSelection', null)
}

// 图片区域拖拽选择 - 从图片任意位置开始拖拽
const handleImageMouseDown = (pageIndex: number, event: MouseEvent) => {
  if (!props.enableTextSelection) return

  // 只处理左键点击
  if (event.button !== 0) return

  // 阻止事件冒泡，避免触发容器点击
  event.stopPropagation()
  event.preventDefault()

  // 清除段落选择（互斥）
  selectedParagraph.value = null

  // 先清理之前可能存在的拖拽状态
  clearDragState()

  // 获取鼠标在图片坐标系中的位置
  const imgElement = event.currentTarget as HTMLImageElement
  const rect = imgElement.getBoundingClientRect()
  const page = props.pages[pageIndex]
  if (!page) return

  // 计算鼠标在OCR坐标系中的位置
  const imgX = ((event.clientX - rect.left) / rect.width) * page.originalWidth
  const imgY = ((event.clientY - rect.top) / rect.height) * page.originalHeight

  // 开始拖拽选择
  dragStartPos.value = { x: imgX, y: imgY, pageIndex }
  dragCurrentPos.value = { x: imgX, y: imgY, pageIndex }
  dragStartLine.value = null // 从图片开始，没有起始OCR行
  dragCurrentLine.value = null
  isLongPressing.value = true
  isDragging.value = true

  // 不立即选择，等待鼠标移动时再开始选择

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false })
  document.addEventListener('mouseup', handleGlobalMouseUp, { passive: false })
}

// OCR行拖拽选择 - 鼠标按下时立即开始拖拽选择
const handleOcrLineMouseDown = (pageIndex: number, ocrIndex: number, event: MouseEvent) => {
  if (!props.enableTextSelection) return

  // 只处理左键点击
  if (event.button !== 0) return

  // 阻止事件冒泡，避免触发容器点击
  event.stopPropagation()
  event.preventDefault()

  // 清除段落选择（互斥）
  selectedParagraph.value = null

  // 先清理之前可能存在的拖拽状态
  clearDragState()

  // 获取鼠标在SVG坐标系中的位置
  const svgElement = event.currentTarget.closest('svg')
  if (!svgElement) return

  const rect = svgElement.getBoundingClientRect()
  const page = props.pages[pageIndex]
  if (!page) return

  // 计算鼠标在OCR坐标系中的位置
  const svgX = ((event.clientX - rect.left) / rect.width) * page.originalWidth
  const svgY = ((event.clientY - rect.top) / rect.height) * page.originalHeight

  // 开始拖拽选择
  dragStartPos.value = { x: svgX, y: svgY, pageIndex }
  dragCurrentPos.value = { x: svgX, y: svgY, pageIndex }
  dragStartLine.value = { pageIndex, ocrIndex }
  dragCurrentLine.value = { pageIndex, ocrIndex }
  isLongPressing.value = true
  isDragging.value = true

  // 不立即选择，等待鼠标移动时再开始选择
  // updateDragSelectionByCoordinates(true)

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false })
  document.addEventListener('mouseup', handleGlobalMouseUp, { passive: false })
}

const handleOcrLineMouseEnter = (pageIndex: number, ocrIndex: number, event: MouseEvent) => {
  // 如果不在拖拽状态，触发段落悬浮效果
  if (!isDragging.value) {
    // 找到包含当前OCR行的段落并触发悬浮
    const paragraphIndex = findParagraphContainingOcrLine(pageIndex, ocrIndex)
    if (paragraphIndex !== -1) {
      handleBlockHover(pageIndex, paragraphIndex, true)
    }
  }
}

const handleOcrLineMouseLeave = (pageIndex: number, ocrIndex: number, event: MouseEvent) => {
  // 如果不在拖拽状态，取消段落悬浮效果
  if (!isDragging.value) {
    // 找到包含当前OCR行的段落并取消悬浮
    const paragraphIndex = findParagraphContainingOcrLine(pageIndex, ocrIndex)
    if (paragraphIndex !== -1) {
      handleBlockHover(pageIndex, paragraphIndex, false)
    }
  }
}

// 找到包含指定OCR行的段落索引
const findParagraphContainingOcrLine = (pageIndex: number, ocrIndex: number): number => {
  const page = props.pages[pageIndex]
  if (!page?.ocrResult) return -1

  const ocrBlocks = getOcrBlocks(page)
  const paragraphBlocks = getTextBlocks(page)

  if (!ocrBlocks[ocrIndex] || paragraphBlocks.length === 0) return -1

  const ocrBlock = ocrBlocks[ocrIndex]

  // 找到与OCR行重叠最多的段落
  let bestMatch = -1
  let maxOverlap = 0

  paragraphBlocks.forEach((paragraph, paragraphIndex) => {
    // 计算重叠面积
    const overlapArea = calculateOverlapArea(ocrBlock, paragraph)
    if (overlapArea > maxOverlap) {
      maxOverlap = overlapArea
      bestMatch = paragraphIndex
    }
  })

  return bestMatch
}

// 计算两个矩形的重叠面积
const calculateOverlapArea = (rect1: TextBlock, rect2: TextBlock): number => {
  const left = Math.max(rect1.x, rect2.x)
  const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width)
  const top = Math.max(rect1.y, rect2.y)
  const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height)

  if (left < right && top < bottom) {
    return (right - left) * (bottom - top)
  }
  return 0
}

// 实时更新拖拽选择范围
const updateDragSelection = () => {
  if (!isDragging.value || !dragStartLine.value || !dragCurrentLine.value) return

  const startIndex = Math.min(dragStartLine.value.ocrIndex, dragCurrentLine.value.ocrIndex)
  const endIndex = Math.max(dragStartLine.value.ocrIndex, dragCurrentLine.value.ocrIndex)
  const pageIndex = dragStartLine.value.pageIndex

  // 清除之前的选择
  selectedOcrLines.value.clear()

  // 添加当前拖拽范围内的所有行
  for (let i = startIndex; i <= endIndex; i++) {
    selectedOcrLines.value.add(getOcrLineKey(pageIndex, i))
  }
}

// 基于坐标范围更新拖拽选择（文字选择感觉）- 拖拽过程中使用临时选择
const updateDragSelectionByCoordinates = (useTempSelection: boolean = true) => {
  if (!dragStartPos.value || !dragCurrentPos.value) return

  const pageIndex = dragStartPos.value.pageIndex
  const page = props.pages[pageIndex]
  if (!page?.ocrResult?.ocrBlocks) return

  // 计算选择区域
  const startX = Math.min(dragStartPos.value.x, dragCurrentPos.value.x)
  const startY = Math.min(dragStartPos.value.y, dragCurrentPos.value.y)
  const endX = Math.max(dragStartPos.value.x, dragCurrentPos.value.x)
  const endY = Math.max(dragStartPos.value.y, dragCurrentPos.value.y)

  // 选择使用临时选择还是最终选择
  const targetSelection = useTempSelection ? tempSelectedOcrLines : selectedOcrLines

  // 清除之前的选择
  targetSelection.value.clear()

  // 找到所有与选择区域相交的OCR行
  page.ocrResult.ocrBlocks.forEach((ocrBlock: any, ocrIndex: number) => {
    const [x1, y1, x2, y2] = ocrBlock.bbox || [0, 0, 0, 0]

    // 检查OCR行是否与选择区域相交
    const intersects = !(
      x2 < startX ||
      x1 > endX ||
      y2 < startY ||
      y1 > endY
    )

    if (intersects) {
      targetSelection.value.add(getOcrLineKey(pageIndex, ocrIndex))
    }
  })


}

const handleOcrLineMouseUp = (pageIndex: number, ocrIndex: number, event: MouseEvent) => {
  if (!props.enableTextSelection) return

  // 阻止事件冒泡，避免触发容器点击
  event.stopPropagation()

  // 移除单击清除选择的功能，只保留拖拽选择
  // 所有选择处理都由全局mouseup事件处理
}

// 全局鼠标移动处理（用于拖拽选择）
const handleGlobalMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !dragStartPos.value) {
    return
  }

  // 检查鼠标按键状态，如果左键没有按下，停止拖拽
  if (event.buttons !== 1) {
    handleGlobalMouseUp(event)
    return
  }

  const pageIndex = dragStartPos.value.pageIndex
  const page = props.pages[pageIndex]
  if (!page) return

  // 找到对应页面的图片元素（更通用，支持从图片任意位置开始拖拽）
  const pageElement = document.querySelector(`.page-item:nth-child(${pageIndex + 1})`)
  if (!pageElement) return

  const imgElement = pageElement.querySelector('.page-image')
  if (!imgElement) return

  const rect = imgElement.getBoundingClientRect()

  // 计算鼠标在图片坐标系中的位置（等同于OCR坐标系）
  const imgX = ((event.clientX - rect.left) / rect.width) * page.originalWidth
  const imgY = ((event.clientY - rect.top) / rect.height) * page.originalHeight

  // 更新当前拖拽位置
  dragCurrentPos.value = { x: imgX, y: imgY, pageIndex }

  // 检查是否真正开始拖拽（鼠标移动了一定距离）
  const deltaX = Math.abs(imgX - dragStartPos.value.x)
  const deltaY = Math.abs(imgY - dragStartPos.value.y)
  const minDragDistance = 5 // 最小拖拽距离（像素）

  if (deltaX > minDragDistance || deltaY > minDragDistance) {
    // 真正开始拖拽，更新临时选择区域
    updateDragSelectionByCoordinates(true)
  }
}

// 全局鼠标释放处理
const handleGlobalMouseUp = (event: MouseEvent) => {
  if (!isDragging.value) return

  // 松开鼠标时，将临时选择转为最终选择
  if (dragStartPos.value && dragCurrentPos.value) {
    // 将临时选择复制到最终选择
    selectedOcrLines.value.clear()
    tempSelectedOcrLines.value.forEach(lineKey => {
      selectedOcrLines.value.add(lineKey)
    })
  }

  // 完成选择
  if (selectedOcrLines.value.size > 0) {
    emitOcrLinesSelection(true) // 允许清除，因为有选中内容

    // 标记刚完成拖拽，防止容器点击事件立即触发
    justFinishedDragging.value = true
    setTimeout(() => {
      justFinishedDragging.value = false
    }, 100)
  } else {
    // 不发送任何事件，避免在证据选择模式下清空选择
  }

  // 清理拖拽状态（包括移除事件监听器）
  clearDragState()
}



// 根据坐标找到最接近的OCR行索引
const findClosestOcrLineIndex = (x: number, y: number, pageIndex: number): number => {
  const page = props.pages[pageIndex]
  if (!page?.ocrResult?.ocrBlocks) return 0

  let bestIndex = 0
  let bestScore = -Infinity

  page.ocrResult.ocrBlocks.forEach((ocrBlock: any, index: number) => {
    const [x1, y1, x2, y2] = ocrBlock.bbox || [0, 0, 0, 0]

    // 优先考虑Y坐标（垂直位置更重要）
    const centerY = (y1 + y2) / 2
    const yDistance = Math.abs(y - centerY)

    // 检查是否在行的Y范围内
    const isInYRange = y >= y1 && y <= y2

    // 检查是否在行的X范围内
    const isInXRange = x >= x1 && x <= x2

    // 计算得分（越高越好）
    let score = 0

    if (isInYRange && isInXRange) {
      // 完全在行内，最高优先级
      score = 1000
    } else if (isInYRange) {
      // 在Y范围内但X超出，次高优先级
      score = 500 - Math.abs(x - (x1 + x2) / 2) / 10
    } else {
      // 不在Y范围内，根据Y距离计算得分
      score = 100 - yDistance / 10
    }

    if (score > bestScore) {
      bestScore = score
      bestIndex = index
    }
  })

  return bestIndex
}

// 清理拖拽状态（当鼠标离开页面或其他意外情况）
const clearDragState = () => {
  // 重置所有拖拽相关状态
  isLongPressing.value = false
  isDragging.value = false
  dragStartLine.value = null
  dragCurrentLine.value = null
  dragStartPos.value = null
  dragCurrentPos.value = null

  // 清理临时选择
  tempSelectedOcrLines.value.clear()

  // 移除全局事件监听（防止内存泄漏）
  document.removeEventListener('mousemove', handleGlobalMouseMove)
  document.removeEventListener('mouseup', handleGlobalMouseUp)
  // 注意：这里不重置 justFinishedDragging，让它在容器点击事件中自然重置
}



// 发送OCR行选择事件
const emitOcrLinesSelection = (allowClear: boolean = true) => {
  if (selectedOcrLines.value.size > 0) {
    const selectedLines = Array.from(selectedOcrLines.value).map(lineKey => {
      const [pageIndex, ocrIndex] = lineKey.split('-').map(Number)
      const page = props.pages[pageIndex]
      const ocrBlock = page?.ocrResult?.ocrBlocks?.[ocrIndex]

      return {
        pageIndex,
        ocrIndex,
        text: ocrBlock?.text || '',
        bbox: ocrBlock?.bbox || [0, 0, 0, 0]
      }
    })

    const combinedText = selectedLines.map(line => line.text).join('\n')

    const selection = {
      type: 'ocr-lines',
      selectedLines,
      combinedText,
      totalLines: selectedLines.length
    }

    emit('textSelection', selection)

    // 更新工具栏状态
    updateToolbarForOcrSelection(selectedLines, combinedText)
  } else if (allowClear) {
    // 只有在允许清除且没有段落选择时才清除
    if (!selectedParagraph.value) {
      emit('textSelection', null)
      hideSelectionToolbar()
    }
  }
}

// 发送段落选择事件
const emitParagraphSelection = () => {
  if (selectedParagraph.value) {
    const { pageIndex, paragraphIndex } = selectedParagraph.value
    const page = props.pages[pageIndex]
    const paragraph = page?.ocrResult?.blocks?.[paragraphIndex]

    const selection = {
      type: 'paragraph',
      pageIndex,
      paragraphIndex,
      paragraphContent: paragraph?.blockContent || '',
      paragraphBbox: paragraph?.blockBbox || [0, 0, 0, 0]
    }

    emit('textSelection', selection)

    // 更新工具栏状态
    updateToolbarForParagraphSelection(pageIndex, paragraphIndex, paragraph?.blockContent || '', paragraph?.blockBbox || [0, 0, 0, 0])
  } else {
    // 只有在没有OCR行选择时才清除
    if (selectedOcrLines.value.size === 0) {
      emit('textSelection', null)
      hideSelectionToolbar()
    }
  }
}

// 工具栏相关方法
const updateToolbarForOcrSelection = (selectedLines: any[], combinedText: string) => {
  if (selectedLines.length === 0) {
    hideSelectionToolbar()
    return
  }

  showSelectionToolbar.value = true
  selectedTextForToolbar.value = combinedText
}

const updateToolbarForParagraphSelection = (pageIndex: number, paragraphIndex: number, content: string, bbox: number[]) => {
  if (!content) {
    hideSelectionToolbar()
    return
  }

  showSelectionToolbar.value = true
  selectedTextForToolbar.value = content
}

const hideSelectionToolbar = () => {
  showSelectionToolbar.value = false
  selectedTextForToolbar.value = ''
}

const handleToolbarCopy = async () => {
  if (!selectedTextForToolbar.value) {
    ElMessage.warning('没有选中的文本')
    return
  }

  try {
    const success = await copyToClipboard(selectedTextForToolbar.value)
    if (success) {
      ElMessage.success('已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    console.error('复制文本失败:', error)
    ElMessage.error('复制失败')
  }
}

// 获取当前页面的搜索结果
const getPageSearchResults = (pageIndex: number) => {
  return searchResults.value.filter(result => result.pageIndex === pageIndex)
}

// 获取当前页面的外部高亮结果
const getPageExternalHighlights = (pageIndex: number) => {
  return externalHighlightResults.value.filter(result => result.pageIndex === pageIndex)
}

// 获取不与搜索结果重叠的外部高亮结果
const getNonOverlappingExternalHighlights = (pageIndex: number) => {
  const pageExternalHighlights = getPageExternalHighlights(pageIndex)
  const pageSearchResults = getPageSearchResults(pageIndex)

  // 如果没有搜索结果，返回所有外部高亮
  if (pageSearchResults.length === 0) {
    return pageExternalHighlights
  }

  // 过滤掉与搜索结果重叠的外部高亮
  return pageExternalHighlights.filter(externalResult => {
    return !pageSearchResults.some(searchResult => {
      return isBlockOverlapping(externalResult.block, searchResult.block)
    })
  })
}

// 判断两个文本块是否重叠
const isBlockOverlapping = (block1: TextBlock, block2: TextBlock) => {
  // 计算重叠面积
  const overlapArea = calculateOverlapArea(block1, block2)
  const block1Area = block1.width * block1.height
  const block2Area = block2.width * block2.height
  const minArea = Math.min(block1Area, block2Area)

  // 如果重叠面积超过较小块面积的50%，认为是重叠
  return overlapArea > minArea * 0.5
}

// 移除当前搜索结果检查函数，统一使用黄色高亮

// 构建全局文本索引的搜索功能
const handleSearch = (text: string) => {
  if (!text.trim()) {
    searchResults.value = []
    currentSearchIndex.value = -1
    return
  }

  // 使用模糊匹配搜索
  const searchThreshold = props.enableFuzzyMatch ? (props.fuzzyMatchThreshold || 0.8) : 0.95
  const results = searchTextInPages(text, props.enableFuzzyMatch, searchThreshold)

  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1

  if (results.length > 0) {
    scrollToSearchResult(0)
  }
}

// 构建全局文本索引
interface BlockInfo {
  pageIndex: number
  blockIndex: number
  block: TextBlock
  startPos: number
  endPos: number
}

interface GlobalTextIndex {
  fullText: string
  blockInfos: BlockInfo[]
}

const buildGlobalTextIndex = (): GlobalTextIndex => {
  let fullText = ''
  const blockInfos: BlockInfo[] = []

  props.pages.forEach((page, pageIndex) => {
    const ocrBlocks = getOcrBlocks(page)

    ocrBlocks.forEach((block, blockIndex) => {
      const blockText = block.text || ''
      const startPos = fullText.length

      // 记录block信息和位置（在添加文本之前记录）
      blockInfos.push({
        pageIndex,
        blockIndex,
        block,
        startPos,
        endPos: startPos + blockText.length
      })

      // 添加文本到全局文本中
      fullText += blockText
    })
  })


  return { fullText, blockInfos }
}

// 根据文本位置获取对应的OCR行
const getBlocksAtTextPosition = (startPos: number, endPos: number, index: GlobalTextIndex): BlockInfo[] => {
  const matchedBlocks: BlockInfo[] = []

  index.blockInfos.forEach(blockInfo => {
    // 检查搜索范围是否与block范围有重叠
    const hasOverlap = !(endPos <= blockInfo.startPos || startPos >= blockInfo.endPos)

    if (hasOverlap) {
      matchedBlocks.push(blockInfo)
    }
  })

  return matchedBlocks
}

// 去重函数
const deduplicateResults = (results: SearchResult[]): SearchResult[] => {
  const seen = new Set<string>()
  return results.filter(result => {
    const key = `${result.pageIndex}-${result.block.x}-${result.block.y}-${result.block.text}`
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

const nextSearchResult = () => {
  if (searchResults.value.length === 0) return
  currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
  scrollToSearchResult(currentSearchIndex.value)
}

const prevSearchResult = () => {
  if (searchResults.value.length === 0) return
  currentSearchIndex.value = currentSearchIndex.value === 0 
    ? searchResults.value.length - 1 
    : currentSearchIndex.value - 1
  scrollToSearchResult(currentSearchIndex.value)
}

const scrollToSearchResult = (index: number) => {
  const result = searchResults.value[index]
  if (!result) return
  
  currentPageIndex.value = result.pageIndex
  selectedTextBlock.value = result.block
  
  // 滚动到对应页面
  nextTick(() => {
    const pageElement = document.querySelector(`[data-page-index="${result.pageIndex}"]`)
    if (pageElement) {
      pageElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })
}

// 图片加载处理
const handleImageLoad = (pageIndex: number, event: Event) => {
  const page = props.pages[pageIndex]
  if (page) {
    page.loading = false
    page.error = undefined
    
    // 如果没有原始尺寸信息，从图片获取
    const img = event.target as HTMLImageElement
    if (img && (!page.originalWidth || !page.originalHeight)) {
      page.originalWidth = img.naturalWidth
      page.originalHeight = img.naturalHeight
    }
  }
  emit('imageLoad', pageIndex, event)
}

const handleImageError = (pageIndex: number, event: Event) => {
  const page = props.pages[pageIndex]
  if (page) {
    page.loading = false
    page.error = '图片加载失败'
  }
  emit('imageError', pageIndex, event)
}

// 滚动处理
const handleScroll = () => {
  // 检测当前可见页面
  if (!containerRef.value) return

  const container = containerRef.value
  const containerRect = container.getBoundingClientRect()
  const containerCenter = containerRect.top + containerRect.height / 2

  let closestPageIndex = 0
  let closestDistance = Infinity

  props.pages.forEach((_, index) => {
    const pageElement = document.querySelector(`[data-page-index="${index}"]`)
    if (pageElement) {
      const pageRect = pageElement.getBoundingClientRect()
      const pageCenter = pageRect.top + pageRect.height / 2
      const distance = Math.abs(pageCenter - containerCenter)

      if (distance < closestDistance) {
        closestDistance = distance
        closestPageIndex = index
      }
    }
  })

  if (closestPageIndex !== currentPageIndex.value) {
    currentPageIndex.value = closestPageIndex
    emit('pageChange', closestPageIndex)
  }
}

// 处理外部高亮数据
const processExternalHighlights = () => {
  if (!props.externalHighlights || props.externalHighlights.length === 0) {
    externalHighlightResults.value = []
    processedExternalHighlights.value.clear()
    return
  }

  // 清空之前的处理记录，避免重复处理问题
  processedExternalHighlights.value.clear()

  let results: SearchResult[] = []

  // 为每个外部高亮项处理
  props.externalHighlights.forEach((highlight, highlightIndex) => {
    const highlightKey = `${highlight.pageNumber || 'all'}-${highlight.text}-${highlightIndex}`

    // 如果有具体的位置信息，直接使用
    if (highlight.locations && highlight.locations.length > 0) {
      highlight.locations.forEach((location, locationIndex) => {
        const pageIndex = (highlight.pageNumber || 1) - 1 // 转换为0基索引
        if (pageIndex >= 0 && pageIndex < props.pages.length) {
          results.push({
            pageIndex,
            block: {
              text: highlight.text,
              x: location.x,
              y: location.y,
              width: location.width,
              height: location.height
            }
          })
        }
      })
    } else {
      // 如果没有位置信息，通过文本搜索来找到位置
      const searchResults = searchTextInPages(highlight.text, props.enableFuzzyMatch, props.fuzzyMatchThreshold)

      // 使用全文搜索，不限制页码范围，提高匹配成功率
      results.push(...searchResults)

      // 如果指定了页码且全文搜索有结果，优先显示指定页面的结果
      if (highlight.pageNumber && searchResults.length > 0) {
        const targetPageIndex = highlight.pageNumber - 1
        const pageResults = searchResults.filter(result => result.pageIndex === targetPageIndex)
        if (pageResults.length > 0) {
          // 如果指定页面有匹配结果，将其排在前面
          results = [...pageResults, ...searchResults.filter(result => result.pageIndex !== targetPageIndex)]
        }
      }
    }

    processedExternalHighlights.value.add(highlightKey)
  })

  externalHighlightResults.value = results

  // 如果有高亮结果，自动跳转到第一个高亮所在的页面
  if (results.length > 0) {
    const firstHighlightPageIndex = results[0].pageIndex
    if (currentPageIndex.value !== firstHighlightPageIndex) {
      currentPageIndex.value = firstHighlightPageIndex
      emit('pageChange', firstHighlightPageIndex)

      // 滚动到对应页面
      nextTick(() => {
        const pageElement = document.querySelector(`[data-page-index="${firstHighlightPageIndex}"]`)
        if (pageElement) {
          pageElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      })
    }
  }
}

// 在页面中搜索文本（精确匹配返回所有结果，模糊匹配返回最佳段落组，最低阈值0.5）
const searchTextInPages = (text: string, useFuzzyMatch: boolean = true, threshold: number = 0.8): SearchResult[] => {
  if (!text.trim()) return []

  let results: SearchResult[] = []

  // 第一步：尝试去空格后的精确匹配
  results = findExactMatches(text)

  if (results.length > 0) {
    // 精确匹配时返回所有结果
    console.log(`精确匹配成功，找到 ${results.length} 个结果，返回所有结果`)
    return results
  }

  // 第二步：如果精确匹配失败且启用模糊匹配，则尝试递减阈值模糊匹配
  if (useFuzzyMatch) {
    // 从配置的阈值开始，每次降低0.1，直到0.7
    let currentThreshold = Math.max(0.7, Math.min(1.0, threshold)) // 确保阈值在合理范围内

    while (currentThreshold >= 0.7) {
      console.log(`尝试模糊匹配，当前阈值: ${currentThreshold.toFixed(1)}`)

      results = findSimilarBlocks(text, currentThreshold)

      // 第三步：对模糊匹配结果进行开头结尾校验
      if (results.length > 0) {
        results = validateMatchBoundaries(text, results)

        // 如果找到结果，选择相似度最高的段落组合，不再降低阈值
        if (results.length > 0) {
          const bestGroup = selectBestSimilarityGroup(results)
          console.log(`模糊匹配成功，阈值: ${currentThreshold.toFixed(1)}, 找到 ${results.length} 个结果，返回最佳组合: ${bestGroup.length} 个块`)

          // 打印每个块的相似度
          bestGroup.forEach((result, index) => {
            console.log(`  块 ${index + 1}: 相似度 ${(result.similarity || 0).toFixed(3)}, 文本: "${result.block.text.substring(0, 50)}${result.block.text.length > 50 ? '...' : ''}"`)
          })

          return bestGroup
        }
      }

      // 降低阈值，继续尝试
      currentThreshold = Math.round((currentThreshold - 0.1) * 10) / 10 // 避免浮点数精度问题
    }

    console.log('所有阈值尝试完毕，未找到匹配结果')
  }

  return results
}





// 精确匹配：去空格后进行精确匹配
const findExactMatches = (searchText: string): SearchResult[] => {
  if (!searchText.trim()) return []

  const results: SearchResult[] = []
  const cleanSearchText = searchText.replace(/\s+/g, '').toLowerCase()

  // 构建全局文本索引
  const globalTextIndex = buildGlobalTextIndex()

  // 在全局文本中搜索（去除空格）
  const globalText = globalTextIndex.fullText.replace(/\s+/g, '').toLowerCase()
  let searchIndex = 0

  while (true) {
    const foundIndex = globalText.indexOf(cleanSearchText, searchIndex)
    if (foundIndex === -1) {
      break
    }

    // 找到匹配的块
    const matchingBlocks = findMatchingBlocksAtPosition(foundIndex, cleanSearchText.length, globalTextIndex)

    matchingBlocks.forEach(blockInfo => {
      results.push({
        pageIndex: blockInfo.pageIndex,
        block: blockInfo.block,
        similarity: 1.0 // 精确匹配相似度为1
      })
    })

    searchIndex = foundIndex + 1
  }

  return results
}

// 在指定位置找到匹配的文本块
const findMatchingBlocksAtPosition = (foundIndex: number, matchLength: number, globalTextIndex: GlobalTextIndex): BlockInfo[] => {
  const results: BlockInfo[] = []

  // 重新构建去除空格的文本索引映射
  let noSpaceIndex = 0
  const blockMapping: Array<{ blockInfo: BlockInfo, startIndex: number, endIndex: number }> = []

  globalTextIndex.blockInfos.forEach(blockInfo => {
    const blockTextNoSpaces = blockInfo.block.text.replace(/\s+/g, '')
    if (blockTextNoSpaces.length > 0) {
      blockMapping.push({
        blockInfo,
        startIndex: noSpaceIndex,
        endIndex: noSpaceIndex + blockTextNoSpaces.length
      })
      noSpaceIndex += blockTextNoSpaces.length
    }
  })

  // 找到匹配范围内的块
  const matchEnd = foundIndex + matchLength
  blockMapping.forEach(mapping => {
    if (mapping.startIndex < matchEnd && mapping.endIndex > foundIndex) {
      results.push(mapping.blockInfo)
    }
  })

  return results
}

// 选择相似度最高的段落组合
const selectBestSimilarityGroup = (results: SearchResult[]): SearchResult[] => {
  if (results.length === 0) return []
  if (results.length === 1) return results

  // 按段落分组
  const paragraphGroups = groupResultsByParagraph(results)

  // 计算每个段落组的最高相似度
  let bestGroup = paragraphGroups[0]
  let bestSimilarity = getGroupMaxSimilarity(bestGroup)

  paragraphGroups.forEach((group, index) => {
    const maxSimilarity = getGroupMaxSimilarity(group)

    if (maxSimilarity > bestSimilarity) {
      bestSimilarity = maxSimilarity
      bestGroup = group
    }
  })
  return bestGroup
}

// 按段落对结果进行分组
const groupResultsByParagraph = (results: SearchResult[]): SearchResult[][] => {
  const paragraphMap = new Map<string, SearchResult[]>()

  results.forEach(result => {
    const paragraphKey = getResultParagraphKey(result)
    if (!paragraphMap.has(paragraphKey)) {
      paragraphMap.set(paragraphKey, [])
    }
    paragraphMap.get(paragraphKey)!.push(result)
  })

  return Array.from(paragraphMap.values())
}

// 获取结果的段落标识
const getResultParagraphKey = (result: SearchResult): string => {
  const pageIndex = result.pageIndex
  const page = props.pages[pageIndex]
  if (!page?.ocrResult) return `${pageIndex}-unknown`

  const ocrBlocks = getOcrBlocks(page)

  // 找到与当前块重叠最多的OCR行
  let bestOcrIndex = -1
  let maxOverlap = 0

  ocrBlocks.forEach((ocrBlock, ocrIndex) => {
    const overlapArea = calculateOverlapArea(result.block, ocrBlock)
    if (overlapArea > maxOverlap) {
      maxOverlap = overlapArea
      bestOcrIndex = ocrIndex
    }
  })

  if (bestOcrIndex === -1) return `${pageIndex}-unknown`

  // 找到包含该OCR行的段落
  const paragraphIndex = findParagraphContainingOcrLine(pageIndex, bestOcrIndex)

  return `${pageIndex}-${paragraphIndex}`
}

// 获取组内最高相似度
const getGroupMaxSimilarity = (group: SearchResult[]): number => {
  if (group.length === 0) return 0
  return Math.max(...group.map(result => result.similarity || 0))
}

// 删除了复杂的文本规范化函数，现在使用纯模糊匹配

// 简单的相似度计算：基于公共字符比例
// 计算编辑距离 (Levenshtein Distance)
const calculateEditDistance = (str1: string, str2: string): number => {
  const m = str1.length
  const n = str2.length
  
  // 创建二维数组
  const dp: number[][] = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0))
  
  // 初始化第一行和第一列
  for (let i = 0; i <= m; i++) dp[i][0] = i
  for (let j = 0; j <= n; j++) dp[0][j] = j
  
  // 动态规划计算编辑距离
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1]
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,     // 删除
          dp[i][j - 1] + 1,     // 插入
          dp[i - 1][j - 1] + 1  // 替换
        )
      }
    }
  }
  
  return dp[m][n]
}

const calculateSimilarity = (inputText: string, blockText: string): number => {
  if (!inputText || !blockText) return 0
  if (inputText === blockText) return 1

  // 方法1：完全包含检查
  if (inputText.includes(blockText)) {
    const similarity = blockText.length / inputText.length
    return similarity
  }

  if (blockText.includes(inputText)) {
    const similarity = inputText.length / blockText.length
    return similarity
  }

  // 方法2：基于编辑距离的相似度计算（改进版）
  const editDistance = calculateEditDistance(inputText, blockText)
  const maxLength = Math.max(inputText.length, blockText.length)
  
  // 相似度 = 1 - (编辑距离 / 最大长度)
  const similarity = 1 - (editDistance / maxLength)
  
  return Math.max(0, similarity) // 确保不返回负值
}

// 简单的模糊匹配：找到最相似的几行
const findSimilarBlocks = (searchText: string, threshold: number = 0.95): SearchResult[] => {
  if (!searchText.trim()) return []

  const allCandidates: SearchResult[] = []

  // 构建全局文本索引
  const globalTextIndex = buildGlobalTextIndex()

  // 1. 单个块匹配
  globalTextIndex.blockInfos.forEach((blockInfo, index) => {
    const blockText = blockInfo.block.text
    const similarity = calculateSimilarity(searchText, blockText)

    allCandidates.push({
      pageIndex: blockInfo.pageIndex,
      block: blockInfo.block,
      similarity: similarity
    })
  })

  // 2. 段落组合匹配（同一段落内的相邻块）
  const paragraphCombinations = findParagraphCombinations(searchText, globalTextIndex)
  allCandidates.push(...paragraphCombinations)

  // 3. 去重：同一个文本块只保留相似度最高的那个
  const blockMap = new Map<string, SearchResult>()

  allCandidates.forEach(candidate => {
    const blockKey = `${candidate.pageIndex}-${candidate.block.x}-${candidate.block.y}-${candidate.block.width}-${candidate.block.height}`
    const existing = blockMap.get(blockKey)

    if (!existing || (candidate.similarity || 0) > (existing.similarity || 0)) {
      blockMap.set(blockKey, candidate)
    }
  })

  // 4. 按相似度排序，取前几个
  const uniqueCandidates = Array.from(blockMap.values())
  uniqueCandidates.sort((a, b) => (b.similarity || 0) - (a.similarity || 0))

  // 只返回相似度达到阈值的结果
  const results = uniqueCandidates.filter(candidate => (candidate.similarity || 0) >= threshold)

  return results
}

// 段落组合匹配：在同一段落内组合相邻的文本块
const findParagraphCombinations = (searchText: string, globalTextIndex: GlobalTextIndex): SearchResult[] => {
  const results: SearchResult[] = []

  // 按页面和段落分组
  const pageGroups = new Map<number, Map<number, BlockInfo[]>>()

  globalTextIndex.blockInfos.forEach(blockInfo => {
    const pageIndex = blockInfo.pageIndex
    const paragraphIndex = getParagraphIndex(blockInfo)

    if (!pageGroups.has(pageIndex)) {
      pageGroups.set(pageIndex, new Map())
    }

    const pageGroup = pageGroups.get(pageIndex)!
    if (!pageGroup.has(paragraphIndex)) {
      pageGroup.set(paragraphIndex, [])
    }

    pageGroup.get(paragraphIndex)!.push(blockInfo)
  })

  // 在每个段落内尝试组合
  pageGroups.forEach((pageGroup, pageIndex) => {
    pageGroup.forEach((blocks, paragraphIndex) => {
      if (blocks.length < 2) return // 至少需要2个块才能组合

      // 按Y坐标排序（确保按行顺序）
      blocks.sort((a, b) => a.block.y - b.block.y)

      // 尝试不同长度的组合
      for (let start = 0; start < blocks.length; start++) {
        for (let length = 2; length <= Math.min(20, blocks.length - start); length++) {
          const combination = blocks.slice(start, start + length)
          const combinedText = combination.map(b => b.block.text).join('')
          const similarity = calculateSimilarity(searchText, combinedText)

          if (similarity > 0) {
            // 为组合中的每个块创建结果，使用相同的相似度
            combination.forEach(blockInfo => {
              results.push({
                pageIndex: blockInfo.pageIndex,
                block: blockInfo.block,
                similarity: similarity
              })
            })
          }
        }
      }
    })
  })

  return results
}

// 获取文本块的段落索引
const getParagraphIndex = (blockInfo: BlockInfo): number => {
  const page = props.pages[blockInfo.pageIndex]
  if (!page?.ocrResult) return -1

  const ocrBlocks = getOcrBlocks(page)

  // 找到与当前块重叠最多的OCR行
  let bestOcrIndex = -1
  let maxOverlap = 0

  ocrBlocks.forEach((ocrBlock, ocrIndex) => {
    const overlapArea = calculateOverlapArea(blockInfo.block, ocrBlock)
    if (overlapArea > maxOverlap) {
      maxOverlap = overlapArea
      bestOcrIndex = ocrIndex
    }
  })

  if (bestOcrIndex === -1) return -1

  // 找到包含该OCR行的段落
  return findParagraphContainingOcrLine(blockInfo.pageIndex, bestOcrIndex)
}

// 滑动窗口匹配：在全局文本中寻找最佳匹配位置
const findSlidingWindowMatches = (searchText: string, globalTextIndex: GlobalTextIndex, threshold: number): SearchResult[] => {
  const results: SearchResult[] = []
  const globalText = globalTextIndex.fullText
  const searchLength = searchText.length

  // 滑动窗口大小：搜索文本长度的 0.8 到 1.5 倍
  const minWindowSize = Math.floor(searchLength * 0.8)
  const maxWindowSize = Math.floor(searchLength * 1.5)

  console.log(`滑动窗口范围: ${minWindowSize} - ${maxWindowSize} 字符`)

  let bestMatch: {similarity: number, startPos: number, endPos: number} | null = null

  // 尝试不同的窗口大小
  for (let windowSize = minWindowSize; windowSize <= maxWindowSize; windowSize++) {
    // 滑动窗口
    for (let i = 0; i <= globalText.length - windowSize; i += Math.max(1, Math.floor(windowSize * 0.1))) {
      const windowText = globalText.substring(i, i + windowSize)
      const similarity = calculateSimilarity(searchText, windowText)

      if (similarity >= threshold) {
        if (!bestMatch || similarity > bestMatch.similarity) {
          bestMatch = {
            similarity: similarity,
            startPos: i,
            endPos: i + windowSize
          }
        }
      }
    }
  }

  // 如果找到最佳匹配，找到对应的文本块
  if (bestMatch) {
    console.log(`找到最佳窗口匹配: 位置${bestMatch.startPos}-${bestMatch.endPos}, 相似度${bestMatch.similarity.toFixed(3)}`)
    const matchingBlocks = findBlocksAtTextRange(bestMatch.startPos, bestMatch.endPos, globalTextIndex)

    matchingBlocks.forEach(blockInfo => {
      results.push({
        pageIndex: blockInfo.pageIndex,
        block: blockInfo.block,
        similarity: bestMatch!.similarity
      })
    })
  }

  return results
}

// 根据文本范围找到对应的文本块
const findBlocksAtTextRange = (startPos: number, endPos: number, globalTextIndex: GlobalTextIndex): BlockInfo[] => {
  const results: BlockInfo[] = []

  globalTextIndex.blockInfos.forEach(blockInfo => {
    // 检查文本块是否与范围有重叠
    if (blockInfo.startPos < endPos && blockInfo.endPos > startPos) {
      results.push(blockInfo)
    }
  })

  return results
}





// 对模糊匹配结果进行开头结尾校验，避免高亮多余的行
const validateMatchBoundaries = (searchText: string, results: SearchResult[]): SearchResult[] => {
  if (!searchText.trim() || results.length === 0) return results

  const validatedResults: SearchResult[] = []
  const searchWords = searchText.trim().split(/\s+/)
  const firstWord = searchWords[0]
  const lastWord = searchWords[searchWords.length - 1]

  // 按页面和相似度分组
  const pageGroups = new Map<number, SearchResult[]>()
  results.forEach(result => {
    if (!pageGroups.has(result.pageIndex)) {
      pageGroups.set(result.pageIndex, [])
    }
    pageGroups.get(result.pageIndex)!.push(result)
  })

  // 对每个页面的结果进行校验
  pageGroups.forEach((pageResults, pageIndex) => {
    // 按Y坐标排序
    pageResults.sort((a, b) => a.block.y - b.block.y)

    // 找到包含首词和末词的文本块
    let firstWordBlockIndex = -1
    let lastWordBlockIndex = -1

    pageResults.forEach((result, index) => {
      const blockText = result.block.text.toLowerCase()

      if (firstWordBlockIndex === -1 && blockText.includes(firstWord.toLowerCase())) {
        firstWordBlockIndex = index
      }

      if (blockText.includes(lastWord.toLowerCase())) {
        lastWordBlockIndex = index
      }
    })

    // 如果找到了首词和末词，只保留它们之间的块（包含首末块）
    if (firstWordBlockIndex !== -1 && lastWordBlockIndex !== -1) {
      const startIndex = Math.min(firstWordBlockIndex, lastWordBlockIndex)
      const endIndex = Math.max(firstWordBlockIndex, lastWordBlockIndex)

      for (let i = startIndex; i <= endIndex; i++) {
        validatedResults.push(pageResults[i])
      }
    } else {
      // 如果没有找到明确的首末词，保留所有结果（可能是部分匹配）
      validatedResults.push(...pageResults)
    }
  })

  return validatedResults
}

// 处理外部高亮点击事件
const handleExternalHighlightClick = (result: SearchResult) => {
  const targetPageIndex = result.pageIndex

  // 跳转到对应页面
  if (currentPageIndex.value !== targetPageIndex) {
    currentPageIndex.value = targetPageIndex
    emit('pageChange', targetPageIndex)

    // 滚动到对应页面
    nextTick(() => {
      const pageElement = document.querySelector(`[data-page-index="${targetPageIndex}"]`)
      if (pageElement) {
        pageElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    })
  }

  // 设置选中的文本块（可选，用于高亮显示）
  selectedTextBlock.value = result.block

  // 发出文本块点击事件
  emit('textBlockClick', {
    block: result.block,
    pageIndex: targetPageIndex,
    type: 'external-highlight'
  })
}

// 监听器
watch(searchText, handleSearch)

// 搜索输入框事件处理
const handleSearchInput = (value: string) => {
  // 这里不需要额外处理，因为watch已经监听了searchText的变化
}

const handleSearchEnter = () => {
  // 回车时触发搜索
  handleSearch(searchText.value)
}

const handleSearchClear = () => {
  // 清除时清空搜索结果
  searchResults.value = []
  currentSearchIndex.value = -1
}



// 监听外部高亮数据变化
watch(() => props.externalHighlights, () => {
  processExternalHighlights()
}, { deep: true, immediate: true })

watch([() => props.containerWidth, () => props.containerHeight], () => {
  // 容器尺寸变化时，页面会自动重新计算尺寸
}, { immediate: false })

// 页面失去焦点时停止拖拽
const handleWindowBlur = () => {
  if (isDragging.value) {
    clearDragState()
  }
}

// 组件挂载时添加额外的事件监听器
onMounted(() => {
  window.addEventListener('blur', handleWindowBlur)
  // 监听contextmenu事件，右键时停止拖拽
  document.addEventListener('contextmenu', () => {
    if (isDragging.value) {
      clearDragState()
    }
  })
})

// 组件卸载时清理全局事件监听器
onUnmounted(() => {
  clearDragState()
  window.removeEventListener('blur', handleWindowBlur)
})

// 页面跳转方法
const jumpToPage = (pageIndex: number) => {
  if (pageIndex >= 0 && pageIndex < props.pages.length) {
    currentPageIndex.value = pageIndex
    
    // 滚动到目标页面
    const targetPage = document.querySelector(`[data-page-index="${pageIndex}"]`) as HTMLElement
    if (targetPage && containerRef.value) {
      targetPage.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      })
    }
    
    // 触发页面变化事件
    emit('pageChange', pageIndex)
    
    console.log(`跳转到第 ${pageIndex + 1} 页`)
  }
}

// 暴露方法给父组件
defineExpose({
  jumpToPage
})
</script>

<style scoped>
.document-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.controls-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e5ea;
  flex-shrink: 0;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.zoom-controls {
  display: flex;
  align-items: center;
}

.zoom-display {
  min-width: 60px;
  font-weight: 500;
}

.rotation-controls {
  display: flex;
  align-items: center;
}

.ocr-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-count {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.page-info {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.document-container {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
  scroll-behavior: smooth;
  background: #ffffff;
}

.pages-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  gap: 20px;
  min-height: 100%;
  background: #ffffff;
}

.page-item {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  /* 禁用默认选择行为 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}



.page-item.page-active {
  /* 移除激活状态的边框和阴影 */
}

.page-content {
  position: relative;
  margin: 0 auto;
  background: #ffffff;
  transition: transform 0.3s ease;
  /* 禁用默认的文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.image-layer {
  width: 100%;
  height: 100%;
  position: relative;
}

.page-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  /* 禁用图片选择和拖拽 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: none;
  /* 禁用图片拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* SVG OCR 覆盖层样式 */
.ocr-overlay-svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

/* 交互层样式 */
.interaction-layer {
  z-index: 2;
}

/* 段落交互样式 */
.paragraph-rect {
  fill: transparent !important;
  stroke: transparent !important;
  cursor: text;
  pointer-events: auto;
  transition: all 0.2s ease;
}

/* 段落悬浮效果 */
.paragraph-rect.paragraph-hovered {
  fill: rgba(64, 158, 255, 0.15) !important;
  stroke: rgba(64, 158, 255, 0.3) !important;
  stroke-width: 1;
  rx: 4;
  ry: 4;
}

/* 段落选中效果 - 流动虚线 */
.paragraph-rect.paragraph-selected {
  fill: rgba(64, 158, 255, 0.25) !important;
  stroke: #007AFF !important;
  stroke-width: 2;
  stroke-dasharray: 8,4;
  stroke-dashoffset: 0;
  rx: 6;
  ry: 6;
  animation: dash-flow 0.4s linear infinite;
}

/* OCR行级别选择层样式 */
.ocr-line-layer {
  z-index: 3;
}

/* OCR行高亮样式 - 拖拽过程中和最终选择的高亮效果 */
.ocr-line-highlight {
  fill: rgba(64, 158, 255, 0.3) !important;
  stroke: transparent !important;
  pointer-events: none;
  transition: fill 0.1s ease;
}

/* OCR行交互区域样式 - 透明，仅用于事件处理 */
.ocr-line-interaction {
  fill: transparent !important;
  stroke: transparent !important;
  cursor: text;
  pointer-events: auto;
}

/* OCR整体选择框样式 - 流动虚线 */
.ocr-selection-bounds {
  fill: rgba(64, 158, 255, 0.25) !important;
  stroke: #007AFF !important;
  stroke-width: 2;
  stroke-dasharray: 8,4;
  stroke-dashoffset: 0;
  rx: 6;
  ry: 6;
  pointer-events: none;
  animation: dash-flow 0.4s linear infinite;
}

/* OCR选择样式 */
.ocr-selection-rect {
  fill: transparent !important;
  stroke: transparent !important;
  cursor: text;
  pointer-events: auto;
  transition: all 0.2s ease;
}

.ocr-selection-rect.ocr-selected {
  fill: rgba(64, 158, 255, 0.3) !important;
  stroke: #007AFF !important;
  stroke-width: 1;
  stroke-dasharray: 3,2;
}

/* 保留原有的文字块样式用于搜索高亮 */
.text-block-rect {
  fill: transparent;
  stroke: transparent;
  stroke-width: 1;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.2s ease;
}

.text-block-rect:hover {
  stroke: #409eff;
  fill: rgba(64, 158, 255, 0.1);
}

.text-block-rect.text-block-highlighted {
  stroke: #f56c6c !important;
  fill: rgba(245, 108, 108, 0.2) !important;
}

.text-block-rect.text-block-selected {
  stroke: #67c23a !important;
  fill: rgba(103, 194, 58, 0.2) !important;
}

/* 流动虚线动画 */
@keyframes dash-flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 12;
  }
}

/* 搜索高亮层样式 */
.search-highlight-layer {
  z-index: 5;
}

/* 搜索高亮样式 - 统一橙色动画高亮 */
.search-highlight-rect {
  fill: rgba(255, 149, 0, 0.4) !important;
  stroke: #FF9500 !important;
  stroke-width: 2;
  pointer-events: none;
  rx: 3;
  ry: 3;
  stroke-dasharray: 8 4;
  animation: dash-flow 1s linear infinite;
  transition: fill 0.2s ease;
}

/* 外部高亮层样式 */
.external-highlight-layer {
  z-index: 6;
}

/* 外部高亮样式 - 橙色动画高亮 */
.external-highlight-rect {
  fill: rgba(255, 149, 0, 0.4) !important;
  stroke: #FF9500 !important;
  stroke-width: 2;
  pointer-events: auto;
  cursor: pointer;
  rx: 3;
  ry: 3;
  stroke-dasharray: 8 4;
  animation: dash-flow 1s linear infinite;
  transition: fill 0.2s ease;
}

.external-highlight-rect:hover {
  fill: rgba(255, 149, 0, 0.6) !important;
}

/* 文字内容覆盖层样式 */
.text-content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-content-item {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  padding: 2px;
  box-sizing: border-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
}

/* 选择工具栏样式 */
.selection-toolbar-container {
  pointer-events: auto;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.12));
}

.selection-toolbar {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  user-select: none;
  pointer-events: auto;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
}

.selection-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  pointer-events: none;
}

.toolbar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  padding: 8px 14px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

.toolbar-item:hover {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  transform: translateY(-1px);
}

.toolbar-item:active {
  transform: translateY(0);
}

.toolbar-icon {
  font-size: 14px;
  color: #606266;
  transition: all 0.2s ease;
}

.toolbar-text {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
}

.toolbar-item:hover .toolbar-icon,
.toolbar-item:hover .toolbar-text {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-bar {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;
  }

  .controls-left,
  .controls-right {
    width: 100%;
    justify-content: center;
  }

  .search-controls {
    flex-direction: column;
    gap: 8px;
  }

  .pages-wrapper {
    padding: 12px;
    gap: 12px;
  }

  .toolbar-item {
    padding: 5px 8px;
    gap: 4px;
  }

  .toolbar-text {
    font-size: 11px;
  }

  .toolbar-icon {
    font-size: 12px;
  }
}
</style>
