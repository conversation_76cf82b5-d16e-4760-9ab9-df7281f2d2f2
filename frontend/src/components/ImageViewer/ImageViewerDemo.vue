<template>
  <div class="image-viewer-demo">
    <div class="demo-header">
      <h2>图片OCR展示组件演示</h2>
      <div class="demo-controls">
        <el-select v-model="selectedFileId" placeholder="选择文件" @change="handleFileChange">
          <el-option
            v-for="file in fileList"
            :key="file.id"
            :label="file.fileName"
            :value="file.id"
          />
        </el-select>
        <el-button @click="loadOcrData" :loading="loading">加载OCR数据</el-button>
      </div>
    </div>

    <div class="demo-content">
      <div class="viewer-section">
        <ImageViewer
          v-if="currentImageUrl"
          :image-url="currentImageUrl"
          :file-name="currentFileName"
          :ocr-data="ocrData"
          :width="800"
          :height="600"
          @text-block-click="handleTextBlockClick"
          @text-block-hover="handleTextBlockHover"
          @image-load="handleImageLoad"
          @image-error="handleImageError"
        />
        <div v-else class="no-image">
          <el-empty description="请选择要查看的图片文件" />
        </div>
      </div>

      <div class="info-section">
        <el-card header="文字块信息">
          <div v-if="selectedTextBlock">
            <p><strong>文字内容：</strong>{{ selectedTextBlock.text }}</p>
            <p><strong>坐标：</strong>x={{ selectedTextBlock.x }}, y={{ selectedTextBlock.y }}</p>
            <p><strong>尺寸：</strong>{{ selectedTextBlock.width }} × {{ selectedTextBlock.height }}</p>
            <p><strong>所属段落：</strong>{{ selectedCoordinate?.text }}</p>
          </div>
          <div v-else class="no-selection">
            点击图片中的文字块查看详细信息
          </div>
        </el-card>

        <el-card header="OCR统计信息" style="margin-top: 16px;">
          <div v-if="ocrData">
            <p><strong>识别段落数：</strong>{{ ocrData.result?.length || 0 }}</p>
            <p><strong>文字块总数：</strong>{{ totalTextBlocks }}</p>
            <p><strong>识别状态：</strong>
              <el-tag :type="ocrData.errorCode === 0 ? 'success' : 'danger'">
                {{ ocrData.errorCode === 0 ? '成功' : '失败' }}
              </el-tag>
            </p>
            <p v-if="ocrData.errorMsg"><strong>错误信息：</strong>{{ ocrData.errorMsg }}</p>
          </div>
          <div v-else class="no-data">
            暂无OCR数据
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElSelect, ElOption, ElButton, ElCard, ElEmpty, ElTag, ElMessage } from 'element-plus'
import ImageViewer from './ImageViewer.vue'
import { caseApi } from '@/api'

// 类型定义
interface FileRecord {
  id: number
  fileName: string
  originalPath: string
  pdfPath: string
  ocrResult?: string
}

interface OcrPosition {
  x: number
  y: number
}

interface OcrTextBlock {
  text: string
  x: number
  y: number
  width: number
  height: number
  pos: OcrPosition[]
}

interface OcrCoordinate {
  index: number
  type: string
  text: string
  blocks: OcrTextBlock[]
}

interface OcrData {
  logId: string
  errorCode: number
  errorMsg?: string
  result: OcrCoordinate[]
}

// 响应式数据
const selectedFileId = ref<number>()
const fileList = ref<FileRecord[]>([])
const loading = ref(false)
const ocrData = ref<OcrData | null>(null)
const selectedTextBlock = ref<OcrTextBlock | null>(null)
const selectedCoordinate = ref<OcrCoordinate | null>(null)

// 计算属性
const currentFile = computed(() => {
  return fileList.value.find(file => file.id === selectedFileId.value)
})

const currentImageUrl = computed(() => {
  if (!currentFile.value) return ''
  // 这里需要根据实际的文件访问URL格式来构建
  // 假设可以通过API获取文件流
  return `/api/file/download/stream/${currentFile.value.id}`
})

const currentFileName = computed(() => {
  return currentFile.value?.fileName || ''
})

const totalTextBlocks = computed(() => {
  if (!ocrData.value) return 0
  return ocrData.value.result.reduce((total, coordinate) => {
    return total + coordinate.blocks.length
  }, 0)
})

// 方法
const loadFileList = async () => {
  try {
    // 这里需要调用实际的API获取文件列表
    // const response = await caseApi.getFileList(caseId)
    // fileList.value = response.data
    
    // 模拟数据
    fileList.value = [
      {
        id: 1,
        fileName: '测试图片1.jpg',
        originalPath: 'test1.jpg',
        pdfPath: 'test1.pdf'
      },
      {
        id: 2,
        fileName: '测试图片2.png',
        originalPath: 'test2.png',
        pdfPath: 'test2.pdf'
      }
    ]
  } catch (error) {
    // 保留演示组件的错误提示
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  }
}

const loadOcrData = async () => {
  if (!selectedFileId.value) {
    // 保留演示组件的警告提示
    ElMessage.warning('请先选择文件')
    return
  }

  loading.value = true
  try {
    // 调用API获取OCR数据
    // const response = await caseApi.getFileOcrResult(selectedFileId.value)
    // if (response.data) {
    //   ocrData.value = JSON.parse(response.data)
    // }

    // 模拟OCR数据
    ocrData.value = {
      logId: 'test-log-id',
      errorCode: 0,
      result: [
        {
          index: 0,
          type: 'paragraph',
          text: '测试文字段落',
          blocks: [
            {
              text: '测试',
              x: 100,
              y: 50,
              width: 80,
              height: 30,
              pos: [
                { x: 100, y: 50 },
                { x: 180, y: 50 },
                { x: 180, y: 80 },
                { x: 100, y: 80 }
              ]
            },
            {
              text: '文字',
              x: 200,
              y: 50,
              width: 80,
              height: 30,
              pos: [
                { x: 200, y: 50 },
                { x: 280, y: 50 },
                { x: 280, y: 80 },
                { x: 200, y: 80 }
              ]
            }
          ]
        }
      ]
    }

    // 保留演示组件的成功提示
    ElMessage.success('OCR数据加载成功')
  } catch (error) {
    // 保留演示组件的错误提示
    console.error('加载OCR数据失败:', error)
    ElMessage.error('加载OCR数据失败')
  } finally {
    loading.value = false
  }
}

const handleFileChange = () => {
  ocrData.value = null
  selectedTextBlock.value = null
  selectedCoordinate.value = null
}

const handleTextBlockClick = (block: OcrTextBlock, coordinate: OcrCoordinate) => {
  selectedTextBlock.value = block
  selectedCoordinate.value = coordinate
  console.log('点击文字块:', block.text)
}

const handleTextBlockHover = (block: OcrTextBlock, isHover: boolean) => {
  if (isHover) {
    console.log('悬停文字块:', block.text)
  }
}

const handleImageLoad = (event: Event) => {
  console.log('图片加载完成')
}

const handleImageError = (event: Event) => {
  console.log('图片加载失败')
}

// 生命周期
onMounted(() => {
  loadFileList()
})
</script>

<style scoped>
.image-viewer-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.demo-header h2 {
  margin: 0;
  color: #333;
}

.demo-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.demo-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

.viewer-section {
  flex: 1;
  min-width: 0;
}

.info-section {
  width: 300px;
  flex-shrink: 0;
}

.no-image {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fafafa;
}

.no-selection,
.no-data {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}
</style>
