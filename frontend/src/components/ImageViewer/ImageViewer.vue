<template>
  <div class="image-viewer-container">
    <!-- 图片展示区域 -->
    <div class="image-display-area" ref="containerRef">
      <!-- 图片层 -->
      <div class="image-layer" :style="imageContainerStyle">
        <img
          ref="imageRef"
          :src="imageUrl"
          :alt="fileName"
          class="main-image"
          @load="handleImageLoad"
          @error="handleImageError"
        />
      </div>

      <!-- OCR文字覆盖层 -->
      <div class="ocr-overlay-layer" :style="overlayStyle" v-if="showOcrOverlay && ocrData">
        <svg
          :width="overlayStyle.width"
          :height="overlayStyle.height"
          class="ocr-svg"
        >
          <!-- 渲染OCR文字块 -->
          <g v-for="(coordinate, index) in ocrData.result" :key="coordinate.index || index">
            <g v-for="(block, blockIndex) in coordinate.blocks" :key="blockIndex">
              <rect
                :x="getScaledX(block.x)"
                :y="getScaledY(block.y)"
                :width="getScaledWidth(block.width)"
                :height="getScaledHeight(block.height)"
                :class="[
                  'ocr-text-rect',
                  { 'highlighted': isHighlighted(block) }
                ]"
                @click="handleTextBlockClick(block, coordinate)"
                @mouseenter="handleTextBlockHover(block, true)"
                @mouseleave="handleTextBlockHover(block, false)"
              />
              
              <!-- 文字内容（可选显示） -->
              <text
                v-if="showTextContent"
                :x="getScaledX(block.x) + 2"
                :y="getScaledY(block.y) + 12"
                class="ocr-text-content"
                @click="handleTextBlockClick(block, coordinate)"
              >
                {{ block.text }}
              </text>
            </g>
          </g>
        </svg>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <el-button-group>
          <el-button @click="zoomOut" :disabled="scale <= minScale">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button @click="zoomIn" :disabled="scale >= maxScale">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>

      <!-- OCR控制 -->
      <div class="ocr-controls">
        <el-switch
          v-model="showOcrOverlay"
          active-text="显示OCR"
          inactive-text="隐藏OCR"
        />
        <el-switch
          v-model="showTextContent"
          active-text="显示文字"
          inactive-text="隐藏文字"
          :disabled="!showOcrOverlay"
        />
      </div>

      <!-- 搜索框 -->
      <div class="search-controls" v-if="showOcrOverlay">
        <el-input
          v-model="searchText"
          placeholder="搜索文字内容"
          clearable
          @input="handleSearch"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElButton, ElButtonGroup, ElSwitch, ElInput, ElIcon, ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, Search } from '@element-plus/icons-vue'
import type { OcrResult, OcrTextBlock, OcrCoordinate } from '@/types/ocr'

// Props
interface Props {
  imageUrl: string
  fileName?: string
  ocrData?: OcrResult | null
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  fileName: '图片',
  ocrData: null,
  width: 800,
  height: 600
})

// Emits
const emit = defineEmits<{
  textBlockClick: [block: OcrTextBlock, coordinate: OcrCoordinate]
  textBlockHover: [block: OcrTextBlock, isHover: boolean]
  imageLoad: [event: Event]
  imageError: [event: Event]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()

// 图片状态
const imageLoaded = ref(false)
const imageNaturalWidth = ref(0)
const imageNaturalHeight = ref(0)
const imageDisplayWidth = ref(0)
const imageDisplayHeight = ref(0)

// 缩放和显示控制
const scale = ref(1)
const minScale = ref(0.1)
const maxScale = ref(5)
const showOcrOverlay = ref(true)
const showTextContent = ref(false)

// 搜索相关
const searchText = ref('')
const highlightedBlocks = ref<OcrTextBlock[]>([])

// 计算属性
const imageContainerStyle = computed(() => ({
  width: `${imageDisplayWidth.value * scale.value}px`,
  height: `${imageDisplayHeight.value * scale.value}px`,
}))

const overlayStyle = computed(() => ({
  width: `${imageDisplayWidth.value * scale.value}px`,
  height: `${imageDisplayHeight.value * scale.value}px`,
  position: 'absolute' as const,
  top: '0',
  left: '0',
  pointerEvents: 'auto' as const
}))

// 坐标转换方法
const getScaledX = (x: number) => {
  return (x / imageNaturalWidth.value) * imageDisplayWidth.value * scale.value
}

const getScaledY = (y: number) => {
  return (y / imageNaturalHeight.value) * imageDisplayHeight.value * scale.value
}

const getScaledWidth = (width: number) => {
  return (width / imageNaturalWidth.value) * imageDisplayWidth.value * scale.value
}

const getScaledHeight = (height: number) => {
  return (height / imageNaturalHeight.value) * imageDisplayHeight.value * scale.value
}

// 判断文字块是否高亮
const isHighlighted = (block: OcrTextBlock) => {
  return highlightedBlocks.value.includes(block)
}

// 事件处理
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  imageNaturalWidth.value = img.naturalWidth
  imageNaturalHeight.value = img.naturalHeight
  
  // 计算适合容器的显示尺寸
  calculateDisplaySize()
  imageLoaded.value = true
  
  emit('imageLoad', event)
}

const handleImageError = (event: Event) => {
  // 保留组件级别的错误提示（非API调用错误）
  ElMessage.error('图片加载失败')
  emit('imageError', event)
}

const handleTextBlockClick = (block: OcrTextBlock, coordinate: OcrCoordinate) => {
  emit('textBlockClick', block, coordinate)
}

const handleTextBlockHover = (block: OcrTextBlock, isHover: boolean) => {
  emit('textBlockHover', block, isHover)
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < maxScale.value) {
    scale.value = Math.min(scale.value * 1.2, maxScale.value)
  }
}

const zoomOut = () => {
  if (scale.value > minScale.value) {
    scale.value = Math.max(scale.value / 1.2, minScale.value)
  }
}

const resetZoom = () => {
  scale.value = 1
}

// 搜索功能
const handleSearch = (query: string) => {
  if (!query || !props.ocrData) {
    highlightedBlocks.value = []
    return
  }

  const matches: OcrTextBlock[] = []
  props.ocrData.result.forEach(coordinate => {
    coordinate.blocks.forEach(block => {
      if (block.text.toLowerCase().includes(query.toLowerCase())) {
        matches.push(block)
      }
    })
  })
  
  highlightedBlocks.value = matches
}

// 计算图片显示尺寸
const calculateDisplaySize = () => {
  if (!imageNaturalWidth.value || !imageNaturalHeight.value) return
  
  const containerWidth = props.width
  const containerHeight = props.height
  
  const aspectRatio = imageNaturalWidth.value / imageNaturalHeight.value
  
  if (aspectRatio > containerWidth / containerHeight) {
    // 图片更宽，以宽度为准
    imageDisplayWidth.value = containerWidth
    imageDisplayHeight.value = containerWidth / aspectRatio
  } else {
    // 图片更高，以高度为准
    imageDisplayHeight.value = containerHeight
    imageDisplayWidth.value = containerHeight * aspectRatio
  }
}

// 监听容器尺寸变化
const resizeObserver = ref<ResizeObserver>()

onMounted(() => {
  if (containerRef.value) {
    resizeObserver.value = new ResizeObserver(() => {
      calculateDisplaySize()
    })
    resizeObserver.value.observe(containerRef.value)
  }
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})

// 监听搜索文字变化
watch(searchText, (newValue) => {
  handleSearch(newValue)
})
</script>

<style scoped>
.image-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.image-display-area {
  flex: 1;
  position: relative;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  border: 1px solid #ddd;
}

.image-layer {
  position: relative;
  display: inline-block;
}

.main-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  user-select: none;
}

.ocr-overlay-layer {
  pointer-events: none;
}

.ocr-svg {
  pointer-events: auto;
}

.ocr-text-rect {
  fill: rgba(0, 123, 255, 0.1);
  stroke: rgba(0, 123, 255, 0.3);
  stroke-width: 1;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ocr-text-rect:hover {
  fill: rgba(0, 123, 255, 0.2);
  stroke: rgba(0, 123, 255, 0.6);
  stroke-width: 2;
}

.ocr-text-rect.highlighted {
  fill: rgba(255, 193, 7, 0.3);
  stroke: rgba(255, 193, 7, 0.8);
  stroke-width: 2;
}

.ocr-text-content {
  font-size: 10px;
  fill: #333;
  pointer-events: none;
  user-select: none;
}

.control-panel {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #fff;
  border-top: 1px solid #ddd;
  flex-shrink: 0;
}

.zoom-controls,
.ocr-controls,
.search-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
