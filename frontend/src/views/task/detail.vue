<template>
  <div class="task-detail-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span class="card-title">任务详情</span>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <div v-if="taskRecord" class="task-detail-content">
        <!-- 基本信息 -->
        <el-descriptions :column="3" border class="task-info">
          <el-descriptions-item label="任务ID">{{ taskRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskRecord.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeName(taskRecord.taskType) }}</el-descriptions-item>
          <el-descriptions-item label="案件ID">
            <el-link type="primary" @click="goToCaseDetail(taskRecord.caseImportId)">
              {{ taskRecord.caseImportId }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getTaskStatusTagType(taskRecord.status)">
              {{ getTaskStatusText(taskRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">{{ formatDuration(taskRecord.durationMs) }}</el-descriptions-item>
          <el-descriptions-item label="输入Token">{{ formatTokens(aggregatedTokens.upTokens) }}</el-descriptions-item>
          <el-descriptions-item label="输出Token">{{ formatTokens(aggregatedTokens.downTokens) }}</el-descriptions-item>
          <el-descriptions-item label="总Token">{{ formatTokens(aggregatedTokens.totalTokens) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ taskRecord.createTime }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ taskRecord.startTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ taskRecord.endTime || '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 错误信息 -->
        <el-alert 
          v-if="taskRecord.errorMessage" 
          type="error" 
          :title="taskRecord.errorMessage"
          show-icon
          class="error-alert"
        />

        <!-- Agent任务结果 -->
        <div v-if="taskRecord && taskRecord.agentTaskInfo && Array.isArray(taskRecord.agentTaskInfo) && taskRecord.agentTaskInfo.length > 0" class="agent-tasks-section">
          <h3 class="section-title">模型任务详情</h3>

          <!-- 每个AgentTaskInfo显示一个区域 -->
          <div
            v-for="(agentInfo, agentIndex) in taskRecord.agentTaskInfo"
            :key="agentIndex"
            class="agent-task-item"
          >
            <div class="agent-task-header">
              <h4 class="agent-task-title">{{ agentInfo.name || `Agent任务 ${agentIndex + 1}` }}</h4>
              <div class="agent-task-meta">
                <el-tag :type="getAgentStateTagType(agentInfo.state)" size="small">
                  {{ agentInfo.state }}
                </el-tag>
                <span class="agent-duration">耗时: {{ formatAgentDuration(agentInfo.duration) }}</span>
                <!-- Agent Token 信息 -->
                <div v-if="agentInfo.tokens" class="agent-token-info">
                  <el-tag size="small" type="info">
                    输入Token: {{ formatTokens(agentInfo.tokens.upTokens) }}
                  </el-tag>
                  <el-tag size="small" type="success">
                    输出Token: {{ formatTokens(agentInfo.tokens.downTokens) }}
                  </el-tag>
                  <el-tag size="small" type="warning">
                    总Token: {{ formatTokens(agentInfo.tokens.totalTokens) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 如果有子任务结果，显示标签页 -->
            <div v-if="agentInfo.subTaskResults && agentInfo.subTaskResults.length > 0" class="subtask-section">
              <el-tabs :model-value="'0'" type="card" class="subtask-tabs">
                <el-tab-pane
                  v-for="(subTask, subIndex) in agentInfo.subTaskResults"
                  :key="subIndex"
                  :label="subTask.name || `子任务 ${subIndex + 1}`"
                  :name="subIndex.toString()"
                >
                  <SubTaskContent
                    :sub-task="subTask"
                    @copy-prompt="(prompt) => copyPrompt(prompt)"
                    @copy-result="(result) => copyResult(result)"
                    @download-result="(result, taskName) => downloadResult(result, taskName)"
                  />
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 如果没有子任务结果，直接显示Agent的结果 -->
            <div v-else-if="agentInfo.result" class="agent-result-section">
              <el-card class="result-card">
                <template #header>
                  <div class="card-header">
                    <span class="card-title">Agent执行结果</span>
                    <div class="card-actions">
                      <el-button size="small" @click="copyAgentResult(agentInfo.result)">
                        <el-icon><DocumentCopy /></el-icon>
                        复制
                      </el-button>
                      <el-button size="small" @click="downloadAgentResult(agentInfo.result, agentInfo.name)">
                        <el-icon><Download /></el-icon>
                        下载
                      </el-button>
                    </div>
                  </div>
                </template>
                <div class="result-content">
                  <pre class="result-text">{{ agentInfo.result }}</pre>
                </div>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 无任务结果提示 -->
        <el-empty v-else description="暂无模型执行结果" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, DocumentCopy, Download } from '@element-plus/icons-vue'
import {
  getTaskDetail,
  type AnalysisTaskRecord,
  type AgentSubTaskResult,
  getTaskTypeName,
  getTaskStatusText,
  getTaskStatusTagType,
  formatDuration
} from '@/api/analysisTask'
import { decrypt, encrypt } from '@/utils/crypto'
import { copyToClipboard } from '@/utils/api-helper'
import SubTaskContent from '@/components/SubTaskContent.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const taskRecord = ref<AnalysisTaskRecord | null>(null)


// 计算属性

// 聚合所有 AgentTaskInfo 中的 tokens
const aggregatedTokens = computed(() => {
  if (!taskRecord.value?.agentTaskInfo || !Array.isArray(taskRecord.value.agentTaskInfo)) {
    return { upTokens: 0, downTokens: 0, totalTokens: 0 }
  }

  let upTokens = 0
  let downTokens = 0
  let totalTokens = 0

  taskRecord.value.agentTaskInfo.forEach(agentInfo => {
    if (agentInfo.tokens) {
      upTokens += agentInfo.tokens.upTokens || 0
      downTokens += agentInfo.tokens.downTokens || 0
      totalTokens += agentInfo.tokens.totalTokens || 0
    }
  })

  return { upTokens, downTokens, totalTokens }
})





// 获取任务详情
const fetchTaskDetail = async () => {
  try {
    const encryptedId = route.params.id as string
    const decryptedId = decrypt(encryptedId)
    const taskId = Number(decryptedId)

    if (!taskId) {
      ElMessage.error('任务ID无效')
      goBack()
      return
    }

    loading.value = true
    try {
      const response = await getTaskDetail(taskId)
      console.log('任务详情响应:', response)

      // 处理响应数据结构
      if (response && typeof response === 'object') {
        if ('data' in response) {
          taskRecord.value = response.data
        } else {
          taskRecord.value = response
        }
      }

      console.log('任务记录:', taskRecord.value)
      console.log('Agent任务信息:', taskRecord.value?.agentInfo)
    } catch (error) {
      console.error('获取任务详情失败:', error)
      ElMessage.error('获取任务详情失败')
      goBack()
    } finally {
      loading.value = false
    }
  } catch (error) {
    console.error('解密任务ID失败:', error)
    ElMessage.error('任务ID格式错误')
    goBack()
  }
}

// 返回任务列表页
const goBack = () => {
  router.push('/task')
}

// 跳转到案件详情
const goToCaseDetail = (caseImportId: number) => {
  const encryptedId = encrypt(caseImportId.toString())
  const url = router.resolve(`/case/detail/${encryptedId}`).href
  window.open(url, '_blank')
}

// 复制提示词
const copyPrompt = async (prompt: string) => {
  try {
    const success = await copyToClipboard(prompt)
    if (success) {
      ElMessage.success('已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    console.error('复制提示词失败:', error)
    ElMessage.error('复制失败')
  }
}

// 复制结果
const copyResult = async (result: string) => {
  try {
    const success = await copyToClipboard(result)
    if (success) {
      ElMessage.success('已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    console.error('复制结果失败:', error)
    ElMessage.error('复制失败')
  }
}

// 下载结果
const downloadResult = (result: string, taskName?: string) => {
  try {
    const text = result
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${taskRecord.value?.taskName}_${taskName || 'result'}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 复制Agent结果
const copyAgentResult = async (result: string) => {
  try {
    const success = await copyToClipboard(result)
    if (success) {
      ElMessage.success('已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    console.error('复制Agent结果失败:', error)
    ElMessage.error('复制失败')
  }
}

// 下载Agent结果
const downloadAgentResult = (result: string, agentName?: string) => {
  try {
    const text = result
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${taskRecord.value?.taskName}_${agentName || 'agent_result'}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载Agent结果失败:', error)
    ElMessage.error('下载失败')
  }
}

// 获取Agent状态标签类型
const getAgentStateTagType = (state: string) => {
  switch (state) {
    case 'done':
      return 'success'
    case 'running':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

// 格式化 tokens 数量
const formatTokens = (tokens?: number): string => {
  if (!tokens) return '-'

  return tokens.toString()
}

// 格式化 Agent 执行时长（秒转换为可读格式）
const formatAgentDuration = (durationSeconds?: number): string => {
  if (!durationSeconds) return '-'

  // 确保是数字类型
  const duration = Number(durationSeconds)

  if (duration < 60) {
    // 小于1分钟时，显示精确的秒数（1位小数）
    return duration % 1 === 0 ? `${duration}秒` : `${duration.toFixed(1)}秒`
  }

  const minutes = Math.floor(duration / 60)
  const seconds = Math.round(duration % 60) // 四舍五入到整数秒

  if (minutes < 60) {
    return `${minutes}分${seconds}秒`
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}小时${remainingMinutes}分${seconds}秒`
}

// 初始化
onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped>
.task-detail-container {
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}

.task-detail-content {
  padding: 0;
}

.task-info {
  margin-bottom: 20px;
}

.error-alert {
  margin-bottom: 20px;
}

.section-title {
  margin: 20px 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.agent-tasks-section {
  margin-top: 24px;
}

.agent-task-item {
  margin-bottom: 32px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.agent-task-item:last-child {
  margin-bottom: 0;
}

.agent-task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.agent-task-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.agent-task-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.agent-duration {
  font-size: 14px;
  color: #606266;
}

.agent-token-info {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.subtask-section {
  margin-top: 16px;
}

.subtask-tabs {
  margin-top: 8px;
}

.subtask-tabs :deep(.el-tabs__content) {
  padding: 16px 0 0 0;
}

.subtask-tabs :deep(.el-tab-pane) {
  padding: 0;
}

.agent-result-section {
  margin-top: 16px;
}

.result-card {
  border-radius: 8px;
}

.result-content {
  max-height: 500px;
  overflow-y: auto;
}

.result-text {
  margin: 0;
  padding: 16px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
}







/* 响应式设计 */
@media (max-width: 768px) {
  .io-row .el-col {
    margin-bottom: 16px;
  }
  
  .io-card {
    height: 400px;
  }
  
  .input-content,
  .output-content {
    height: 300px;
  }
  
  .task-info {
    --el-descriptions-item-bordered-label-background: #fafafa;
  }
}

/* 滚动条样式 */
.task-detail-container::-webkit-scrollbar,
.result-content::-webkit-scrollbar,
.subtask-tabs :deep(.input-content)::-webkit-scrollbar,
.subtask-tabs :deep(.output-content)::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.task-detail-container::-webkit-scrollbar-track,
.result-content::-webkit-scrollbar-track,
.subtask-tabs :deep(.input-content)::-webkit-scrollbar-track,
.subtask-tabs :deep(.output-content)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.task-detail-container::-webkit-scrollbar-thumb,
.result-content::-webkit-scrollbar-thumb,
.subtask-tabs :deep(.input-content)::-webkit-scrollbar-thumb,
.subtask-tabs :deep(.output-content)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.task-detail-container::-webkit-scrollbar-thumb:hover,
.result-content::-webkit-scrollbar-thumb:hover,
.subtask-tabs :deep(.input-content)::-webkit-scrollbar-thumb:hover,
.subtask-tabs :deep(.output-content)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保滚动条可见 */
.task-detail-container,
.result-content {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>
