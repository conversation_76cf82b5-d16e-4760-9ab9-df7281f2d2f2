<template>
  <div class="task-list-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">模型任务管理</span>
        </div>
      </template>

      <!-- 筛选条件 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="案件ID">
          <el-input
            v-model="searchForm.caseImportId"
            placeholder="请输入案件ID"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="任务类型">
          <el-select
            v-model="searchForm.taskType"
            placeholder="请选择任务类型"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          >
            <el-option
              v-for="taskType in taskTypes"
              :key="taskType.code"
              :label="taskType.name"
              :value="taskType.code"
            />
          </el-select>

        </el-form-item>
        
        <el-form-item label="任务状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择任务状态"
            clearable
            style="width: 150px"
            @keyup.enter="handleSearch"
          >
            <el-option label="执行中" :value="1" />
            <el-option label="执行完成" :value="2" />
            <el-option label="执行失败" :value="3" />
          </el-select>
        </el-form-item>
        

        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedIds.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>

      <!-- 任务列表表格 -->
      <el-table
        v-loading="loading"
        :data="taskList"
        @selection-change="handleSelectionChange"
        stripe
        class="task-table"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="任务ID" width="80" />
        
        <el-table-column prop="caseImportId" label="案件ID" width="100">
          <template #default="{ row }">
            <el-link 
              type="primary" 
              @click="goToCaseDetail(row.caseImportId)"
            >
              {{ row.caseImportId }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="taskType" label="任务类型" min-width="100">
          <template #default="{ row }">
            {{ getTaskTypeName(row.taskType) }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="任务状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusTagType(row.status)" size="small">
              {{ getTaskStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="startTime" label="开始时间" width="160">
          <template #default="{ row }">
            {{ row.startTime || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="endTime" label="结束时间" width="160">
          <template #default="{ row }">
            {{ row.endTime || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="durationMs" label="执行耗时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.durationMs) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewTaskDetail(row.id)"
            >
              查看详情
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue'

// 定义组件名称用于keep-alive
defineOptions({
  name: 'TaskList'
})
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Delete } from '@element-plus/icons-vue'
import {
  getTaskList,
  deleteTask,
  getTaskTypes,
  type AnalysisTaskRecord,
  type TaskListParams,
  type TaskTypeDTO,
  TASK_STATUS_TEXT,
  getTaskTypeName,
  getTaskStatusText,
  getTaskStatusTagType,
  formatDuration
} from '@/api/analysisTask'
import { encrypt } from '@/utils/crypto'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const taskList = ref<AnalysisTaskRecord[]>([])
const selectedIds = ref<number[]>([])
const taskTypes = ref<TaskTypeDTO[]>([])

// 搜索表单
const searchForm = reactive<TaskListParams>({
  caseImportId: undefined,
  taskType: '',
  status: undefined
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取任务列表
const fetchTaskList = async () => {
  loading.value = true
  try {
    const params: TaskListParams = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }

    console.log('请求参数:', params)
    const response = await getTaskList(params)
    console.log('响应数据:', response)

    // 检查响应数据结构
    if (response && typeof response === 'object') {
      // 如果响应直接包含 records 字段
      if ('records' in response) {
        taskList.value = response.records || []
        pagination.total = response.total || 0
      }
      // 如果响应在 data 字段中
      else if ('data' in response && response.data && 'records' in response.data) {
        taskList.value = response.data.records || []
        pagination.total = response.data.total || 0
      }
      // 如果响应是数组（可能是直接返回的记录列表）
      else if (Array.isArray(response)) {
        taskList.value = response
        pagination.total = response.length
      }
      else {
        console.error('未知的响应数据结构:', response)
        taskList.value = []
        pagination.total = 0
      }
    } else {
      console.error('响应数据格式错误:', response)
      taskList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
    taskList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchTaskList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    caseImportId: undefined,
    taskType: '',
    status: undefined
  })
  pagination.current = 1
  fetchTaskList()
}

// 刷新
const handleRefresh = () => {
  fetchTaskList()
}

// 选择变化
const handleSelectionChange = (selection: AnalysisTaskRecord[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个任务吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用批量删除接口，暂时用循环删除
    for (const id of selectedIds.value) {
      await deleteTask(id)
    }
    
    ElMessage.success('删除成功')
    selectedIds.value = []
    fetchTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 删除单个任务
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteTask(id)
    ElMessage.success('删除成功')
    fetchTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看任务详情
const viewTaskDetail = (id: number) => {
  const encryptedId = encrypt(id.toString())
  router.push(`/task/${encryptedId}`)
}

// 跳转到案件详情
const goToCaseDetail = (caseImportId: number) => {
  const encryptedId = encrypt(caseImportId.toString())
  const url = router.resolve(`/case/detail/${encryptedId}`).href
  window.open(url, '_blank')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchTaskList()
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchTaskList()
}

// 标记是否已经初始化过
const isInitialized = ref(false)

// 初始化任务类型
const initTaskTypes = async () => {
  console.log('=== 开始初始化任务类型 ===')
  console.log('当前 taskTypes.value:', taskTypes.value)

  try {
    console.log('调用 getTaskTypes() 接口...')
    const data = await getTaskTypes()
    console.log('接口返回的数据:', data)
    console.log('数据类型:', typeof data)
    console.log('是否为数组:', Array.isArray(data))

    if (data && Array.isArray(data)) {
      console.log('设置 taskTypes.value 前:', taskTypes.value)
      taskTypes.value = data
      console.log('设置 taskTypes.value 后:', taskTypes.value)
      console.log('taskTypes.value.length:', taskTypes.value.length)
    } else {
      console.log('返回的数据不是数组或为空:', data)
    }
  } catch (error) {
    console.error('获取任务类型失败:', error)
    console.error('错误详情:', error.message)
    console.error('错误堆栈:', error.stack)
  }

  console.log('=== 任务类型初始化完成 ===')
  console.log('最终 taskTypes.value:', taskTypes.value)
}

// 初始化
onMounted(async () => {
  console.log('=== 组件 onMounted 开始 ===')
  console.log('isInitialized.value:', isInitialized.value)

  if (!isInitialized.value) {
    console.log('开始初始化组件...')
    await initTaskTypes()
    console.log('任务类型初始化完成，开始获取任务列表...')
    fetchTaskList()
    isInitialized.value = true
    console.log('组件初始化完成')
  } else {
    console.log('组件已经初始化过，跳过')
  }

  console.log('=== 组件 onMounted 结束 ===')
})

// 当组件被keep-alive激活时
onActivated(async () => {
  // 只在首次激活时加载数据，后续激活不刷新
  if (!isInitialized.value) {
    await initTaskTypes()
    fetchTaskList()
    isInitialized.value = true
  }
})
</script>

<style scoped>
.task-list-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
}

.search-form {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
}

.task-table {
  margin-bottom: 20px;
  width: 100%;
}

.pagination {
  display: flex;
  justify-content: center;
}

/* 确保表格容器有足够的宽度 */
:deep(.el-card__body) {
  padding: 20px;
  overflow-x: auto;
}

/* 表格响应式处理 */
@media (max-width: 1200px) {
  .task-table {
    min-width: 1000px;
  }
}
</style>
