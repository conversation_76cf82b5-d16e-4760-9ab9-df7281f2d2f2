<template>
  <div class="knowledge-base-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <div class="icon-wrapper">
              <Setting />
            </div>
            <div class="title-content">
              <h1 class="page-title">知识库配置</h1>
              <p class="page-description">配置知识库的基本信息、模型参数和提示词模板</p>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button size="large" @click="handleCancel">
            <el-icon class="mr-1"><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" size="large" :loading="saving" @click="handleSave">
            <el-icon class="mr-1" v-if="!saving"><Check /></el-icon>
            {{ saving ? '保存中...' : '保存配置' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="config-main" v-loading="loading">
      <div class="config-container">
        <el-tabs v-model="activeTab" class="config-tabs">
          <!-- 助理设置标签 -->
          <el-tab-pane label="助理设置" name="assistant">
            <div class="tab-content">
              <!-- 基本信息 -->
              <div class="config-section">
                <div class="section-header">
                  <div class="section-title">
                    <InfoFilled class="section-icon" />
                    <h3>基本信息</h3>
                  </div>
                  <p class="section-desc">配置知识库的基础标识信息</p>
                </div>
                <div class="section-content">
                  <el-form :model="config" label-width="120px" class="config-form">
                    <el-form-item label="配置键">
                      <el-input v-model="config.configKey" disabled />
                      <div class="form-help">系统内部标识，不可修改</div>
                    </el-form-item>
                    <el-form-item label="配置名称">
                      <el-input v-model="config.configName" placeholder="请输入配置名称" />
                    </el-form-item>
                    <el-form-item label="数据集ID">
                      <el-input
                        v-model="datasetIdsText"
                        placeholder="请输入数据集ID，多个用逗号分隔"
                        @blur="updateDatasetIds"
                      />
                      <div class="form-help">多个数据集ID请用英文逗号分隔</div>
                    </el-form-item>
                    <el-form-item label="启用状态">
                      <el-switch v-model="config.isEnabled" />
                      <div class="form-help">开启时使用知识库查询法条，关闭时使用Agent模型直接提取法条编号</div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <!-- 助理设置 -->
              <div class="config-section">
                <div class="section-header">
                  <div class="section-title">
                    <ChatDotRound class="section-icon" />
                    <h3>助理设置</h3>
                  </div>
                  <p class="section-desc">配置AI助理的对话行为和响应方式</p>
                </div>
                <div class="section-content">
                  <el-form :model="config.promptConfig" label-width="120px" class="config-form">
                    <el-form-item label="开场白">
                      <el-input 
                        v-model="config.promptConfig.opener" 
                        placeholder="对话开始时的问候语"
                        type="textarea"
                        :rows="3"
                      />
                      <div class="form-help">用户首次对话时显示的欢迎信息</div>
                    </el-form-item>
                    <el-form-item label="空回复">
                      <el-input 
                        v-model="config.promptConfig.emptyResponse" 
                        placeholder="当没有找到相关信息时的回复"
                        type="textarea"
                        :rows="3"
                      />
                      <div class="form-help">知识库中无相关信息时的默认回复</div>
                    </el-form-item>
                    <el-form-item label="显示引用">
                      <div class="switch-wrapper">
                        <el-switch 
                          v-model="config.promptConfig.showQuote" 
                          size="large"
                          active-text="显示"
                          inactive-text="隐藏"
                        />
                        <span class="switch-desc">在回答中显示知识库引用来源</span>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 提示引擎标签 -->
          <el-tab-pane label="提示引擎" name="prompt">
            <div class="tab-content">
              <!-- 提示词模板 -->
              <div class="config-section">
                <div class="section-header">
                  <div class="section-title">
                    <Document class="section-icon" />
                    <h3>提示词模板</h3>
                  </div>
                  <p class="section-desc">定义AI助理的回答模板和格式要求</p>
                </div>
                <div class="section-content">
                  <el-form :model="config.promptConfig" label-width="120px" class="config-form">
                    <el-form-item label="模板内容">
                      <el-input
                        v-model="config.promptConfig.prompt"
                        type="textarea"
                        :rows="12"
                        placeholder="请输入提示词模板，使用 {变量名} 作为占位符"
                        class="prompt-textarea"
                      />
                      <div class="form-help">
                        <div>可用变量：case_material - 案件材料，dispute_description - 争议焦点，{knowledge} - 知识库内容</div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <!-- 检索参数 -->
              <div class="config-section">
                <div class="section-header">
                  <div class="section-title">
                    <Search class="section-icon" />
                    <h3>检索参数</h3>
                  </div>
                  <p class="section-desc">调整知识库检索的精度和范围参数</p>
                </div>
                <div class="section-content">
                  <el-form :model="config.promptConfig" label-width="140px" class="config-form">
                    <el-form-item label="相似度阈值">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.promptConfig.similarityThreshold"
                          :min="0"
                          :max="1"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">设置检索结果的最低相似度要求</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="关键词权重">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.promptConfig.keywordsSimilarityWeight"
                          :min="0"
                          :max="1"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">关键词匹配在相似度计算中的权重</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="返回结果数量">
                      <div class="number-input-wrapper">
                        <el-input-number
                          v-model="config.promptConfig.topN"
                          :min="1"
                          :max="20"
                          size="large"
                          controls-position="right"
                        />
                        <div class="input-help">最多返回的检索结果数量</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="Token限制">
                      <div class="number-input-wrapper">
                        <el-input-number
                          v-model="config.promptConfig.topK"
                          :min="1"
                          :max="2048"
                          size="large"
                          controls-position="right"
                        />
                        <div class="input-help">单次检索的最大Token数量</div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 模型设置标签 -->
          <el-tab-pane label="模型设置" name="model">
            <div class="tab-content">
              <!-- 模型参数 -->
              <div class="config-section">
                <div class="section-header">
                  <div class="section-title">
                    <Cpu class="section-icon" />
                    <h3>模型参数</h3>
                  </div>
                  <p class="section-desc">调整AI模型的生成参数以优化输出质量</p>
                </div>
                <div class="section-content">
                  <el-form :model="config.llmConfig" label-width="120px" class="config-form">
                    <el-form-item label="模型名称">
                      <el-input v-model="config.llmConfig.modelName" placeholder="请输入模型名称" />
                    </el-form-item>
                    <el-form-item label="温度">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.llmConfig.temperature"
                          :min="0"
                          :max="2"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">控制输出的随机性，值越高越随机</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="Top P">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.llmConfig.topP"
                          :min="0"
                          :max="1"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">核采样参数，控制词汇选择范围</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="存在惩罚">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.llmConfig.presencePenalty"
                          :min="0"
                          :max="2"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">减少重复内容的出现</div>
                      </div>
                    </el-form-item>
                    <el-form-item label="频率惩罚">
                      <div class="slider-container">
                        <el-slider
                          v-model="config.llmConfig.frequencyPenalty"
                          :min="0"
                          :max="2"
                          :step="0.1"
                          show-input
                          :show-input-controls="false"
                          class="custom-slider"
                        />
                        <div class="slider-help">根据频率惩罚重复词汇</div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Setting,
  Close,
  Check,
  InfoFilled,
  Key,
  Edit,
  DataBoard,
  Cpu,
  ChatDotRound,
  ChatLineRound,
  QuestionFilled,
  Document,
  Search
} from '@element-plus/icons-vue'
import {
  getKnowledgeBaseConfigByKey,
  updateKnowledgeBaseConfig,
  createKnowledgeBaseConfig,
  type KnowledgeBaseConfig
} from '@/api/knowledgeBase'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const activeTab = ref('assistant')

// 数据集ID文本输入
const datasetIdsText = ref('')

// 配置数据
const config = reactive<KnowledgeBaseConfig>({
  id: undefined,
  configKey: 'LAW_INFO_QUERY',
  configName: '法条信息查询配置',
  datasetIds: ['1590c8f4683811f0a96fe6b44c2d4c16'],
  isEnabled: true,
  llmConfig: {
    modelName: 'Qwen/Qwen3-32B',
    temperature: 0.1,
    topP: 0.3,
    presencePenalty: 0.4,
    frequencyPenalty: 0.7
  },
  promptConfig: {
    similarityThreshold: 0.2,
    keywordsSimilarityWeight: 0.7,
    topN: 6,
    topK: 1024,
    emptyResponse: '',
    opener: '你好！我是你的法律助理，有什么可以帮助你的吗？',
    showQuote: true,
    prompt: '案件材料：\n{case_material}\n\n知识库：{knowledge}\n\n输出格式：\n法律名称，法条编号，法条原文。\n例如：\n《中华人民共和国民法典》，第一零七十九条第一款，夫妻一方要求离婚的，可以由有关组织进行调解或者直接向人民法院提起离婚诉讼。',
    variables: [{ key: 'knowledge', optional: true }]
  }
})

// 方法
const updateDatasetIds = () => {
  // 将文本转换为数组
  if (datasetIdsText.value.trim()) {
    config.datasetIds = datasetIdsText.value.split(',').map(id => id.trim()).filter(id => id)
  } else {
    config.datasetIds = []
  }
}

const updateDatasetIdsText = () => {
  // 将数组转换为文本
  datasetIdsText.value = config.datasetIds.join(', ')
}
const loadConfig = async () => {
  loading.value = true
  try {
    const backendData = await getKnowledgeBaseConfigByKey('LAW_INFO_QUERY')
    console.log('API返回的数据:', backendData) // 调试日志

    if (backendData && backendData.id) {

      // 正确映射数据结构
      config.id = backendData.id
      config.configKey = backendData.configKey
      config.configName = backendData.configName
      config.datasetIds = backendData.datasetIds || []
      config.isEnabled = backendData.isEnabled

      // 映射 llmConfig - 使用 Object.assign 确保响应式更新
      if (backendData.llmConfig) {
        Object.assign(config.llmConfig, {
          modelName: backendData.llmConfig.modelName || '',
          temperature: backendData.llmConfig.temperature || 0.1,
          topP: backendData.llmConfig.topP || 0.3,
          presencePenalty: backendData.llmConfig.presencePenalty || 0.4,
          frequencyPenalty: backendData.llmConfig.frequencyPenalty || 0.7
        })
      }

      // 映射 promptConfig - 使用 Object.assign 确保响应式更新
      if (backendData.promptConfig) {
        Object.assign(config.promptConfig, {
          similarityThreshold: backendData.promptConfig.similarityThreshold || 0.2,
          keywordsSimilarityWeight: backendData.promptConfig.keywordsSimilarityWeight || 0.7,
          topN: backendData.promptConfig.topN || 6,
          topK: backendData.promptConfig.topK || 1024,
          emptyResponse: backendData.promptConfig.emptyResponse || '',
          opener: backendData.promptConfig.opener || '',
          showQuote: backendData.promptConfig.showQuote !== false,
          prompt: backendData.promptConfig.prompt || '',
          variables: backendData.promptConfig.variables || [{ key: 'knowledge', optional: true }]
        })
      }

      console.log('加载的配置ID:', config.id) // 调试日志
      console.log('映射后的配置:', config) // 调试日志

      // 更新数据集文本输入框
      updateDatasetIdsText()

      // 强制触发响应式更新
      await nextTick()
      ElMessage.success('配置加载成功')
    } else {
      console.log('未找到现有配置，将创建新配置')
      console.log('响应数据为空:', backendData)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  } finally {
    loading.value = false
  }
}

const handleSave = async () => {
  saving.value = true
  try {
    console.log('保存配置，当前ID:', config.id) // 调试日志

    if (config.id) {
      // 更新现有配置
      await updateKnowledgeBaseConfig(config.id, config)
      ElMessage.success('配置更新成功')
    } else {
      // 创建新配置
      const response = await createKnowledgeBaseConfig(config)
      if (response.data) {
        Object.assign(config, response.data)
        ElMessage.success('配置创建成功')
      } else {
        ElMessage.error('创建配置失败')
      }
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.knowledge-base-config {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  padding: 0 32px;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.header-left {
  flex: 1;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  background: #409EFF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.icon-wrapper svg {
  width: 20px;
  height: 20px;
}

.title-content {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f63ff;
  line-height: 1.2;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.page-description {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #86868B;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.mr-1 {
  margin-right: 4px;
}

.config-main {
  flex: 1;
  overflow: hidden;
  padding: 24px 32px;
}

.config-container {
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.config-tabs {
  height: 100%;
}

.config-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.config-tabs :deep(.el-tabs__nav-wrap) {
  padding: 16px 0;
}

.config-tabs :deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  padding: 0 24px;
  height: 40px;
  line-height: 40px;
}

.config-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
}

.config-tabs :deep(.el-tabs__active-bar) {
  background-color: #409EFF;
}

.config-tabs :deep(.el-tabs__content) {
  height: calc(100% - 72px);
  overflow-y: auto;
  padding: 0;
}

.tab-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-section:hover {
  border-color: #d1d5db;
}

.section-header {
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f3f4f6;
  background: transparent;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.section-icon {
  width: 20px;
  height: 20px;
  color: #409EFF;
  margin-right: 8px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-desc {
  margin: 0;
  font-size: 12px;
  color: #9ca3af;
  line-height: 1.4;
}

.section-content {
  padding: 16px 0 0 0;
}

.config-form {
  max-width: none;
}

.config-form .el-form-item {
  margin-bottom: 16px;
}

.config-form .el-form-item:last-child {
  margin-bottom: 0;
}

.config-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
}

.config-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
}

.config-form :deep(.el-input__wrapper:hover) {
  border-color: #d1d5db;
}

.config-form :deep(.el-input__wrapper.is-focus) {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.config-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
}

.config-form :deep(.el-textarea__inner:hover) {
  border-color: #d1d5db;
}

.config-form :deep(.el-textarea__inner:focus) {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.slider-container {
  width: 100%;
}

.custom-slider {
  margin-right: 12px;
}

.custom-slider :deep(.el-slider__runway) {
  background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  height: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-slider :deep(.el-slider__bar) {
  background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.custom-slider :deep(.el-slider__button) {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 3px solid #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-slider :deep(.el-slider__button:hover) {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4), 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider-help {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch-desc {
  font-size: 13px;
  color: #6b7280;
}

.number-input-wrapper {
  width: 100%;
}

.number-input-wrapper :deep(.el-input-number) {
  width: 100%;
}

.number-input-wrapper :deep(.el-input__wrapper) {
  border-radius: 8px;
}

.input-help {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.form-help {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
}

.form-help div {
  margin-bottom: 4px;
}

.form-help div:last-child {
  margin-bottom: 0;
}

.prompt-textarea :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

/* 滚动条样式 */
.tab-content::-webkit-scrollbar,
.config-tabs :deep(.el-tabs__content)::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track,
.config-tabs :deep(.el-tabs__content)::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb,
.config-tabs :deep(.el-tabs__content)::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover,
.config-tabs :deep(.el-tabs__content)::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 加载动画 */
.config-main :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 按钮样式 */
.header-actions :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  padding: 12px 24px;
  font-size: 14px;
}

.header-actions :deep(.el-button--primary) {
  background: #409EFF;
  border-color: #409EFF;
  color: #ffffff;
}

.header-actions :deep(.el-button--primary:hover) {
  background: #337ecc;
  border-color: #337ecc;
}

/* 开关样式 */
.config-form :deep(.el-switch) {
  --el-switch-on-color: #409EFF;
  --el-switch-off-color: #dcdfe6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-tabs :deep(.el-tabs__header) {
    padding: 0 16px;
  }
  
  .tab-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .config-main {
    padding: 16px;
  }
  
  .page-header {
    padding: 0 16px;
  }
  
  .header-content {
    height: 64px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .config-section {
    padding: 16px;
  }
  
  .config-tabs :deep(.el-tabs__header) {
    padding: 0 12px;
  }
  
  .config-tabs :deep(.el-tabs__item) {
    padding: 0 16px;
    font-size: 13px;
  }
  
  .tab-content {
    padding: 12px;
  }
  
  .config-form {
    label-width: 100px !important;
  }
}


</style>
