<template>
  <div class="login-container">
    <!-- 左侧登录表单 -->
    <div class="login-form-section">
      <div class="login-form">
        <div class="logo-section">
          <h1 class="app-title">天枢智鉴</h1>
        </div>

        <el-form
            :model="loginForm"
            :rules="rules"
            ref="loginFormRef"
            class="login-form-content"
            size="large"
        >
          <el-form-item prop="username">
            <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
          >
            {{ loading ? '登录中...' : '立即登录' }}
          </el-button>

          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            <el-link type="primary" class="forgot-password">忘记密码？</el-link>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 右侧产品展示 -->
    <div class="product-showcase">
      <div class="showcase-container">
        <!-- 上部分：产品截图展示区域 -->
        <div class="product-screenshot">
          <div class="screenshot-container">
            <div
                v-for="(feature, index) in features"
                :key="index"
                class="screenshot-item"
                :class="{ active: index === currentFeatureIndex }"
            >
              <img :src="feature.image" :alt="feature.title" />
            </div>
          </div>
        </div>

        <!-- 下部分：功能介绍区域 -->
        <div class="feature-intro">
          <div class="intro-content">
            <h3 class="intro-title">{{ features[currentFeatureIndex]?.title }}</h3>
            <p class="intro-description">{{ features[currentFeatureIndex]?.description }}</p>

            <!-- 进度指示器 -->
            <div class="progress-indicators">
              <span
                  v-for="(feature, index) in features"
                  :key="index"
                  class="indicator"
                  :class="{ active: index === currentFeatureIndex }"
                  @click="setCurrentFeature(index)"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/userStore'
import { login } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const currentFeatureIndex = ref(0)
let carouselTimer: number

const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

const features = ref([
  {
    title: '关系图谱',
    description: '智能构建案件当事人关系网络，清晰展示人物关系脉络',
    image: '/src/assets/login-back.png'
  },
  {
    title: '案件时序',
    description: '自动梳理案件发生时间线，重现事件完整发展过程',
    image: '/src/assets/login-back.png'
  },
  {
    title: '争议焦点',
    description: '精准识别案件争议核心，快速定位关键争论点',
    image: '/src/assets/login-back.png'
  },
  {
    title: '矛盾识别',
    description: '智能分析案件材料中的逻辑矛盾和事实冲突',
    image: '/src/assets/login-back.png'
  },
  {
    title: '证据事实',
    description: '自动提取证据关键信息，构建完整事实认定链条',
    image: '/src/assets/login-back.png'
  },
  {
    title: '文书生成',
    description: '基于案件分析自动生成法律文书，提升工作效率',
    image: '/src/assets/login-back.png'
  }
])

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  // 表单验证
  const valid = await loginFormRef.value.validate().catch(() => false)
  if (!valid) return

  loading.value = true

  try {
    // login函数返回的数据需要转换格式
    const response = await login(loginForm.username, loginForm.password)
    console.log('登录返回的原始数据:', response)

    // 转换为前端期望的格式
    const userInfo = {
      token: response.token,
      name: response.userInfo?.xm || '未知用户',
      loginId: response.userInfo?.loginId || '',
      userId: response.userInfo?.id || 0
    }

    // 提取权限信息
    const permissions = response.userInfo?.permissions || []

    console.log('转换后的用户信息:', userInfo)
    console.log('权限信息:', permissions)

    // 保存用户信息到store，同时传递权限
    await userStore.setUserInfo(userInfo, permissions)
    console.log('保存后的用户状态:', userStore.userInfo)

    ElMessage.success('登录成功！')
    router.push('/case')

  } catch (error: any) {
    console.error('登录失败:', error)
    // HTTP工具类已经显示了错误消息，这里不需要重复显示
  } finally {
    loading.value = false
  }
}

const nextFeature = () => {
  currentFeatureIndex.value = (currentFeatureIndex.value + 1) % features.value.length
}

const setCurrentFeature = (index: number) => {
  currentFeatureIndex.value = index
}

const startCarousel = () => {
  carouselTimer = setInterval(() => {
    nextFeature()
  }, 4000) // 4秒自动切换
}

const stopCarousel = () => {
  if (carouselTimer) {
    clearInterval(carouselTimer)
  }
}

onMounted(() => {
  startCarousel()
})

onUnmounted(() => {
  stopCarousel()
})
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  min-height: 100vh;
  background: #f5f5f5;
}

.login-form-section {
  flex: 0 0 45%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  padding: 80px 60px;
}

.login-form {
  width: 100%;
  max-width: 420px;
  background: transparent;
  padding: 0;
}

.logo-section {
  text-align: center;
  margin-bottom: 48px;
}

.app-title {
  color: #A567E0;
  font-size: 40px;
  font-weight: 700;
  margin: 0;
  letter-spacing: 2px;
}

.login-form-content {
  .el-form-item {
    margin-bottom: 28px;
  }

  .el-input {
    height: 56px;

    :deep(.el-input__wrapper) {
      border-radius: 12px;
      box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.2);
      background: #ffffff;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.is-focus {
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.4), 0 4px 12px rgba(102, 126, 234, 0.15);
        background: #ffffff;
        transform: translateY(-1px);
      }

      &:hover {
        box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.3), 0 2px 8px rgba(102, 126, 234, 0.1);
        background: #ffffff;
      }
    }

    :deep(.el-input__inner) {
      font-size: 15px;
      color: #2c3e50;
      font-weight: 500;

      &::placeholder {
        color: #8492a6;
        font-weight: 400;
      }
    }
  }
}

.login-button {
  width: 100%;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: #A567E0;
  border: none;
  margin-top: 8px;
  margin-bottom: 32px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    background: #9456d4;
    box-shadow: 0 8px 25px rgba(165, 103, 224, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #5a6c7d;

  :deep(.el-checkbox__label) {
    color: #5a6c7d;
    font-weight: 500;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }

  .forgot-password {
    text-decoration: none;
    color: #667eea;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
}

.product-showcase {
  flex: 0 0 55%;
  display: flex;
  flex-direction: column;
  background: white;
}

.showcase-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-screenshot {
  flex: 1;
  padding: 0;
  background: white;
  position: relative;
  overflow: hidden;
}

.screenshot-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  border: none;
}

.screenshot-item {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    opacity: 1;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
  }
}

.feature-intro {
  flex: 0 0 180px;
  background: #A567E0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
}

.intro-content {
  text-align: center;
  max-width: 400px;
  position: relative;
  z-index: 1;
}

.intro-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
}

.intro-description {
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 28px;
  color: rgba(255, 255, 255, 0.9);
}

.progress-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: white;
    transform: scale(1.3);
  }

  &:hover {
    background: rgba(255, 255, 255, 0.7);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-form-section {
    flex: none;
    padding: 40px 20px;
  }

  .product-showcase {
    order: -1;
    min-height: 400px;
    flex: none;
  }

  .product-screenshot {
    padding: 20px;
  }

  .feature-intro {
    flex: 0 0 150px;
    padding: 20px;
  }

  .intro-title {
    font-size: 20px;
  }

  .intro-description {
    font-size: 14px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .login-form-section {
    padding: 30px 16px;
  }

  .app-title {
    font-size: 28px;
  }

  .product-screenshot {
    padding: 16px;
  }

  .feature-intro {
    flex: 0 0 120px;
    padding: 16px;
  }

  .intro-title {
    font-size: 18px;
  }

  .intro-description {
    font-size: 13px;
  }
}


</style>