<template>
  <div class="case-detail-page">
    <!-- 全局加载状态 -->
    <div v-if="!isDataLoaded" class="page-loading">
      <div class="loading-content">
        <el-icon class="loading-spinner"><Loading /></el-icon>
        <p class="loading-text">正在加载案件数据...</p>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div v-else class="page-container">
      <!-- 顶部导航条 -->
      <header class="page-header">
        <div class="header-content">
          <!-- 分析标签 -->
          <div class="header-tabs">
            <el-tabs v-model="activeTab" class="analysis-tabs">
              <el-tab-pane
                v-for="tab in visibleTabs"
                :key="tab.name"
                :label="tab.label"
                :name="tab.name"
              />
            </el-tabs>
          </div>

          <h1 class="case-title">案号：{{ caseInfo.caseName || '未命名案件' }}</h1>

          <div class="header-actions">
            <el-button
              type="primary"
              @click="handleBack"
              class="back-btn"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>
      </header>

      <!-- 主工作区 -->
      <main class="page-main">
        <!-- 左侧文件面板 -->
        <aside class="file-panel" :class="{ 'panel-collapsed': !fileListVisible }">
          <!-- 折叠状态下的展开按钮 -->
          <div class="expand-btn" v-if="!fileListVisible" @click="toggleFileList" title="展开文件列表">
            <el-icon>
              <DArrowRight />
            </el-icon>
          </div>

          <!-- 文件列表内容 -->
          <div class="panel-content" v-if="fileListVisible">
            <!-- 搜索框和操作按钮 -->
            <div class="search-section">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索文件名..."
                size="default"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="action-buttons">
                <button
                  v-if="highlightedFileIds.length > 0"
                  class="clear-highlight-btn"
                  @click="resetHighlight"
                  title="清除高亮"
                >
                  <el-icon><Close /></el-icon>
                </button>
                <button class="collapse-btn" @click="toggleFileList" title="折叠文件列表">
                  <el-icon>
                    <DArrowLeft />
                  </el-icon>
                </button>
              </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list">
              <div v-if="filteredFiles.length === 0" class="empty-state">
                <el-empty
                  description="暂无文件"
                  :image-size="80"
                />
              </div>

              <!-- 文件夹和文件的紧凑布局 -->
              <div v-else class="file-tree">
                <!-- 遍历文件夹 -->
                <div
                  v-for="folder in organizedFiles"
                  :key="folder.name"
                  class="folder-group"
                >
                  <!-- 文件夹头部 -->
                  <div
                    class="folder-header"
                    @click="toggleFolder(folder.name)"
                  >
                    <el-icon class="folder-icon">
                      <Folder v-if="!folder.expanded" />
                      <FolderOpened v-else />
                    </el-icon>
                    <span class="folder-name">{{ folder.name }}</span>
                    <span class="file-count">({{ folder.files.length }})</span>
                    <el-icon class="expand-icon">
                      <ArrowRight v-if="!folder.expanded" />
                      <ArrowDown v-else />
                    </el-icon>
                  </div>

                  <!-- 文件夹内容 -->
                  <div v-show="folder.expanded" class="folder-content">
                    <div
                      v-for="file in folder.files"
                      :key="file.id"
                      :data-file-id="file.id"
                      class="file-item compact"
                      :class="{
                        'item-active': selectedFile?.id === file.id,
                        'item-highlighted': highlightedFileIds.includes(file.id)
                      }"
                      @click="handleFileSelect(file)"
                      @mouseenter="handleFileHover(file, true)"
                      @mouseleave="handleFileHover(file, false)"
                    >
                      <div class="file-icon">
                        <img :src="getFileIcon(file.name)" alt="file" />
                      </div>
                      <div class="file-details">
                        <div class="file-name" :title="file.name">{{ file.name }}</div>
                      </div>
                      <div class="file-actions">
                        <el-button
                          v-show="shouldShowDeleteButton(file)"
                          type="danger"
                          size="small"
                          text
                          @click.stop="handleDeleteFile(file)"
                          class="delete-btn"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            <!-- 浮动按钮容器 -->
            <div class="float-buttons-container">
              <!-- 上传按钮 -->
              <el-button
                type="primary"
                circle
                size="large"
                @click="showFileUpload"
                title="上传文件"
                class="upload-float-btn"
              >
                <el-icon><Plus /></el-icon>
              </el-button>

              <!-- 重新生成按钮 -->
              <el-button
                type="warning"
                circle
                size="large"
                @click="openRegenerateDialog"
                title="重新生成"
                class="regenerate-float-btn"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>

              <!-- 回收站按钮 -->
              <el-button
                type="danger"
                circle
                size="large"
                @click="toggleRecycleBin"
                :title="recycleBinVisible ? '关闭回收站' : '打开回收站'"
                class="recycle-bin-float-btn"
                :class="{ 'active': recycleBinVisible }"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </aside>

        <!-- 右侧主内容区 -->
        <section class="main-content-section">
          <!-- 文档预览区 -->
          <div class="document-section" @click="handleDocumentSectionClick">
            <!-- 证据选择模式标题 -->
            <div v-if="evidenceSelectionMode" class="evidence-selection-header">
              <span class="selection-title">
                📄 证据选择模式 - {{ currentSelectingSide === 'plaintiff' ? '诉方' : '辩方' }}
              </span>
              <div class="selection-actions">
                <span class="selection-tip">点击文档中的文本选择证据</span>
                <el-button size="small" @click="handleExitEvidenceSelectionMode">退出选择</el-button>
              </div>
            </div>
            <div class="document-viewer-container" :class="{ 'selection-mode': evidenceSelectionMode }">
              <div v-if="documentLoading" class="document-loading">
                <el-icon class="loading-spinner"><Loading /></el-icon>
                <p>正在加载文档...</p>
              </div>
              <!-- OCR处理中状态 -->
              <div v-if="ocrProcessing && !documentLoading" class="ocr-processing">
                <div class="processing-content">
                  <div class="processing-icon-wrapper">
                    <el-icon class="processing-icon">
                      <Loading />
                    </el-icon>
                  </div>
                  <div class="processing-text">
                    <h3 class="processing-title">OCR识别处理中</h3>
                    <p class="processing-message">文档正在进行OCR识别，请稍后再次点击查看结果</p>
                  </div>
                </div>
                <div class="processing-actions">
                  <el-button
                    @click="handleFileSelect(selectedFile)"
                    type="primary"
                    class="refresh-button"
                  >
                    <el-icon class="refresh-icon">
                      <Refresh />
                    </el-icon>
                    刷新查看
                  </el-button>
                </div>
              </div>
              <div v-if="documentError && documentError !== 'ocr_missing' && !documentLoading && !ocrProcessing" class="document-error-modern">
                <div class="error-content">
                  <div class="error-icon-wrapper">
                    <el-icon class="error-icon-modern">
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 8v4m0 4h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                      </svg>
                    </el-icon>
                  </div>
                  <div class="error-text">
                    <h3 class="error-title">文档预览暂不可用</h3>
                    <p class="error-message">{{ documentError }}</p>
                  </div>
                </div>
                <div class="error-actions">
                  <el-button
                    @click="retryLoadDocument"
                    type="primary"
                    :loading="ocrRetrying"
                    :disabled="ocrRetrying"
                    class="retry-button"
                  >
                    <el-icon v-if="!ocrRetrying" class="retry-icon">
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 4v6h6m16 10v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </el-icon>
                    {{ ocrRetrying ? '处理中...' : (documentError === '文档识别失败，请重新生成' ? '重新生成' : '重新加载') }}
                  </el-button>
                </div>
              </div>
              <div v-if="!selectedFile && !documentLoading && !documentError" class="document-empty">
                <el-empty
                  description="请从左侧选择文件进行预览"
                  :image-size="120"
                />
              </div>
              <!-- PDF预览器 - 用于QT类型文档 -->
              <div v-if="shouldShowPdfViewer && !documentLoading && !documentError && !ocrProcessing && selectedFile" class="pdf-preview-area">
                <VuePdfEmbed
                  v-if="selectedFile.pdfContent"
                  ref="pdfViewerRef"
                  :source="selectedFile.pdfContent"
                  :textLayer="true"
                  :allPages="true"
                  :scale="1.0"
                  :width="800"
                  class="pdf-embed"
                  @text-selected="handlePdfTextSelected"
                  @rendered="handlePdfRendered"
                  @loading-failed="handlePdfError"
                />
                <div v-else class="pdf-error">
                  <el-icon class="error-icon"><Warning /></el-icon>
                  <p>PDF内容加载失败</p>
                </div>
              </div>

              <!-- 图片预览器 - 用于其他文档类型 -->
              <DocumentViewer
                v-if="shouldShowImageViewer && !documentLoading && !documentError && !ocrProcessing && selectedFile"
                ref="documentViewerRef"
                :pages="displayPages"
                :container-width="dynamicContainerSize.width"
                :container-height="dynamicContainerSize.height"
                :default-scale="1.0"
                :show-controls="true"
                :show-ocr-controls="false"
                :show-search-panel="true"
                :enable-text-selection="true"
                :external-highlights="currentExternalHighlights"
                :enable-fuzzy-match="true"
                :fuzzy-match-threshold="0.7"
                @text-block-click="handleTextBlockClick"
                @text-block-hover="handleTextBlockHover"
                @text-selection="handleTextSelection"
                @page-change="handleDocumentPageChange"
                @image-load="handleDocumentImageLoad"
                @image-error="handleDocumentImageError"
              />

              <!-- 无预览内容时的提示 -->
              <div v-if="!shouldShowPdfViewer && !shouldShowImageViewer && selectedFile && !documentLoading && !documentError" class="no-preview-content">
                <el-empty
                  description="暂无可预览的内容"
                  :image-size="120"
                />
                <p class="preview-tip">
                  文档类型：{{ selectedFile?.documentType || '未知' }}
                </p>
              </div>
            </div>
          </div>

          <!-- 分析内容区 -->
          <div class="analysis-content">
            <!-- 分析组件 -->
              <DisputePoints
                v-if="activeTab === 'focus'"
                ref="disputePointsRef"
                :fileList="fileList"
                :case-import-id="String(decrypt(route.params.id as string))"
                :selectedTextData="selectedTextData"
                :evidenceSelectionMode="evidenceSelectionMode"
                :currentSelectingSide="currentSelectingSide"
                @detailHighlight="handleHighlight"
                @clear-selection="handleClearSelection"
                @enterEvidenceSelectionMode="handleEnterEvidenceSelectionMode"
                @exitEvidenceSelectionMode="handleExitEvidenceSelectionMode"
              />
              <EvidenceFacts
                v-else-if="activeTab === 'evidenceFacts'"
                ref="evidenceFactsRef"
                :caseImportId="String(decrypt(route.params.id as string))"
                :fileList="fileList"
                :caseInfo="caseInfo"
                @detailHighlight="handleHighlight"
                @detailFileJump="handleFileJump"
              />
              <ContradictionRecognition
                v-else-if="activeTab === 'contradiction'"
                ref="contradictionRef"
                :caseImportId="String(decrypt(route.params.id as string))"
                :fileList="fileList"
                @detailHighlight="handleHighlight"
              />
              <DocumentGeneration
                v-else-if="activeTab === 'documentGeneration'"
                :case-import-id="String(decrypt(route.params.id as string))"
                :has-counterclaim="hasCounterclaim"
                :case-info="caseInfo"
              />
          </div>
        </section>
      </main>
    </div>



    <!-- 回收站模态框 -->
    <el-dialog
      v-model="recycleBinVisible"
      title="回收站"
      width="800px"
      :close-on-click-modal="false"
      class="recycle-bin-dialog"
    >
      <RecycleBin
        :caseImportId="decrypt(route.params.id as string)"
        :visible="recycleBinVisible"
        @close="closeRecycleBin"
        @fileRestored="handleFileRestored"
        :show-header="false"
      />
    </el-dialog>

    <!-- 文件上传模态框 -->
    <el-dialog
      v-model="fileUploadVisible"
      width="700px"
      :close-on-click-modal="false"
      :show-close="true"
      class="file-upload-dialog"
    >
      <FileUpload
        :caseImportId="decrypt(route.params.id as string)"
        :visible="fileUploadVisible"
        @close="closeFileUpload"
        @fileUploaded="handleFileUploaded"
        :show-header="false"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeft,
  Delete,
  Loading,
  DArrowLeft,
  DArrowRight,
  Search,
  Document,
  Warning,
  Plus,
  Refresh,
  ZoomIn,
  ZoomOut,
  Folder,
  FolderOpened,
  ArrowRight,
  ArrowDown
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DisputePoints from './components/DisputePoints.vue'
import EvidenceFacts from './components/EvidenceFacts.vue'
import ContradictionRecognition from './components/ContradictionRecognition.vue'
import RecycleBin from './components/RecycleBin.vue'
import FileUpload from './components/FileUpload.vue'
import DocumentGeneration from './components/DocumentGeneration.vue'
import { decrypt } from '@/utils/crypto';
import { DocumentViewer } from '@/components/ImageViewer'
import VuePdfEmbed from '@/components/VuePdfEmbed'
import PdfViewer from '@/components/PdfViewer'

import {
  caseApi,
  disputeFocusApi,
  type CaseImportRecord,
  type CaseTypeInfo
} from '@/api'
import { API_ENDPOINTS } from '@/api/config'

// 导入文件夹配置
import { getFolderConfig } from '@/constants/fileFolder'

// 导入文档类型工具函数
import { shouldUsePdfViewer, shouldUseImageViewer, getViewerType } from '@/utils/documentType'

// 导入文件图标
import wordIcon from '@/assets/word.svg'
import pdfIcon from '@/assets/pdf.svg'
import txtIcon from '@/assets/txt.svg'
import wpsIcon from '@/assets/wps.svg'

// 路由相关
const route = useRoute()
const router = useRouter()

// ==================== 基础状态管理 ====================
// 页面加载状态
const isDataLoaded = ref(false)

// 当前激活的分析标签
const activeTab = ref('focus')

// 文件面板可见性
const fileListVisible = ref(true)

// 文档预览相关状态
const documentLoading = ref(false)
const documentError = ref<string>('')
const currentPage = ref(1)
const totalPages = ref(1)
const ocrRetrying = ref(false)
const ocrProcessing = ref(false)

// PDF预览器引用
const pdfViewerRef = ref<any>(null)

// DocumentViewer 引用
const documentViewerRef = ref<any>(null)

// 高亮文件ID列表
const highlightedFileIds = ref<number[]>([])

// ==================== 数据状态 ====================
// 案件信息
const caseInfo = ref<CaseImportRecord>({
  id: 0,
  caseName: '',
  caseCode: null,
  statusText: '',
  importStatus: 0,
  importTime: '',
  updateTime: '',
  importerName: null,
  errorMessage: null,
  totalCount: 0,
  successCount: 0,
  failCount: 0,
  progress: 0,
  relationAnalysisStatus: 1,
  relationAnalysisTime: '',
  timelineAnalysisStatus: 1,
  timelineAnalysisTime: '',
  disputeAnalysisStatus: 1,
  disputeAnalysisTime: '',
  contradictionAnalysisStatus: 1,
  contradictionAnalysisTime: '',
  disputeFocuseReasoningAnalysisStatus: 1,
  disputeFocuseReasoningAnalysisTime: '',
  caseType: undefined // 补充类型声明，防止ts报错
})

// 案件类型信息
const caseTypeInfo = ref<CaseTypeInfo>({
  ajlxdm: '',
  caseType: '',
  caseTypeCode: '',
  isSecondInstance: false
})

// 文件相关数据
interface FileItem {
  id: number
  name: string
  fileName: string
  documentType: string
  content?: string
  uploadTime: string
  text?: string
  extractedText?: string
  ocrData?: any
  ocrPreviews?: any[]
  ocrResult?: any[]
  imageWidth?: number
  imageHeight?: number
  totalPages?: number
  currentPage?: number
  fileSize?: number
  fileType?: string
  status?: number
  // PDF相关字段
  pdfPath?: string
  pdfContent?: string | ArrayBuffer
}

const fileList = ref<FileItem[]>([])
const selectedFile = ref<FileItem | null>(null)
const searchKeyword = ref('')

// 反诉相关状态
const hasCounterclaim = ref(false)

// 文件夹相关数据
interface FolderGroup {
  name: string
  files: FileItem[]
  expanded: boolean
}

// 文件夹状态管理 - 动态初始化
const folderStates = ref<Record<string, boolean>>({})

// 动态文件分类映射 - 基于案件类型和documentType
const folderMapping = computed(() => {
  return getFolderConfig(caseTypeInfo.value.ajlxdm)
})

// 初始化文件夹状态
const initializeFolderStates = () => {
  const config = getFolderConfig(caseTypeInfo.value.ajlxdm)
  const newStates: Record<string, boolean> = {}

  Object.keys(config).forEach(folderName => {
    newStates[folderName] = false // 默认关闭所有文件夹
  })

  folderStates.value = newStates
}

// 文本选择数据
const selectedTextData = ref<any>(null)

// 证据选择模式
const evidenceSelectionMode = ref(false)
const currentSelectingSide = ref<'plaintiff' | 'defendant' | null>(null)

// 当前外部高亮数据
const currentExternalHighlights = ref<Array<{
  pageNumber: number
  text: string
  locations?: Array<{
    x: number
    y: number
    width: number
    height: number
  }>
}>>([])

// 当前高亮的证据数据
const currentHighlightEvidence = ref<Array<{
  fileId: number
  fileName: string
  pageNumber?: number
  highlight?: string
}>>([])

// 组件挂载状态标志
const isMounted = ref(false)

// 窗口宽度（响应式）
const windowWidth = ref(window.innerWidth)



// 回收站相关
const recycleBinVisible = ref(false)

// 文件上传相关
const fileUploadVisible = ref(false)

// 文件悬浮状态管理
const hoveredFileId = ref<number | null>(null)



// ==================== 计算属性 ====================
// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (!searchKeyword.value.trim()) return fileList.value
  return fileList.value.filter(file =>
    file.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 组织后的文件夹结构
const organizedFiles = computed(() => {
  const folders: FolderGroup[] = []
  const usedFiles = new Set<number>()

  // 为每个文件夹分类创建文件夹
  Object.entries(folderMapping.value).forEach(([folderName, documentTypes]) => {
    const folderFiles: FileItem[] = []

    // 根据documentType匹配文件
    filteredFiles.value.forEach(file => {
      if (file.documentType && documentTypes.includes(file.documentType)) {
        folderFiles.push(file)
        usedFiles.add(file.id)
      }
    })

    // 始终显示所有文件夹，即使没有文件
    folders.push({
      name: folderName,
      files: folderFiles,
      expanded: folderStates.value[folderName] ?? false
    })
  })

  // 添加未分类的文件到"其他材料"（如果有的话）
  const uncategorizedFiles = filteredFiles.value.filter(file =>
    !usedFiles.has(file.id) || !file.documentType
  )
  if (uncategorizedFiles.length > 0) {
    // 查找"其他材料"文件夹并添加文件
    const otherFolder = folders.find(folder => folder.name === '其他材料')
    if (otherFolder) {
      otherFolder.files.push(...uncategorizedFiles)
    }
  }

  return folders
})

// 是否可以切换到上一个文件
const canGoPrev = computed(() => {
  if (!selectedFile.value || fileList.value.length === 0) return false
  const currentIndex = fileList.value.findIndex(file => file.id === selectedFile.value?.id)
  return currentIndex > 0
})

// 是否可以切换到下一个文件
const canGoNext = computed(() => {
  if (!selectedFile.value || fileList.value.length === 0) return false
  const currentIndex = fileList.value.findIndex(file => file.id === selectedFile.value?.id)
  return currentIndex < fileList.value.length - 1
})



// 容器尺寸计算
const dynamicContainerSize = computed(() => {
  if (!selectedFile.value) {
    return { width: 730, height: 1000 }
  }

  let imageWidth = 1191
  let imageHeight = 1684

  // 从OCR预览数据获取图片尺寸
  if (selectedFile.value.ocrPreviews && selectedFile.value.ocrPreviews.length > 0) {
    const firstPreview = selectedFile.value.ocrPreviews[0]
    imageWidth = firstPreview.inputImageWidth || firstPreview.ocrImageWidth || firstPreview.docImageWidth || 1191
    imageHeight = firstPreview.inputImageHeight || firstPreview.ocrImageHeight || firstPreview.docImageHeight || 1684
  } else if (selectedFile.value.imageWidth && selectedFile.value.imageHeight) {
    imageWidth = selectedFile.value.imageWidth
    imageHeight = selectedFile.value.imageHeight
  }

  // 根据文件面板折叠状态动态计算可用宽度
  // 文件面板展开时：350px，折叠时：48px
  // 分析面板固定：约40%的宽度
  // 间距和边距：约40px
  const filePanelWidth = fileListVisible.value ? 350 : 48
  const analysisPanelWidth = windowWidth.value * 0.4 // 分析面板约占40%
  const margins = 40 // 间距和边距

  // 计算文档预览区域的可用宽度
  const availableWidth = windowWidth.value - filePanelWidth - analysisPanelWidth - margins

  // 设置最小和最大宽度限制
  const minWidth = 500
  const maxWidth = 1000
  const documentWidth = Math.max(minWidth, Math.min(maxWidth, availableWidth - 40)) // 减去内边距

  // 根据比例计算高度
  const calculatedHeight = Math.round(documentWidth / (imageWidth / imageHeight))

  return {
    width: documentWidth,
    height: calculatedHeight
  }
})

// 显示的页面数据
const displayPages = computed(() => {
  if (!selectedFile.value) return []

  // 多页面模式：如果有OCR预览数据
  if (selectedFile.value.ocrPreviews && selectedFile.value.ocrPreviews.length > 0) {
    totalPages.value = selectedFile.value.ocrPreviews.length
    return selectedFile.value.ocrPreviews.map((preview: any, index: number) => {
      const imageUrl = preview.ocrImage || preview.docPreprocessingImage || preview.inputImage
      const originalWidth = preview.inputImageWidth || preview.ocrImageWidth || preview.docImageWidth || 1191
      const originalHeight = preview.inputImageHeight || preview.ocrImageHeight || preview.docImageHeight || 1684

      // 直接传递对应页面的OCR结果
      const pageOcrResult = selectedFile.value?.ocrResult?.[index]

      console.log(`页面 ${index} OCR数据:`, pageOcrResult)

      return {
        imageUrl,
        originalWidth,
        originalHeight,
        ocrResult: pageOcrResult
      }
    })
  }

  // 单页面模式
  if (selectedFile.value.content) {
    totalPages.value = 1
    return [{
      imageUrl: selectedFile.value.content,
      originalWidth: selectedFile.value.imageWidth || 1191,
      originalHeight: selectedFile.value.imageHeight || 1684,
      ocrResult: selectedFile.value.ocrResult?.[0],
      loading: false,
      error: undefined
    }]
  }

  return []
})
// 统一使用图片预览，不再根据QT类型判断
const shouldShowPdfViewer = computed(() => {
  return false // 始终使用图片预览
})

// 当前预览器类型（用于显示）
const currentViewerType = computed(() => {
  return shouldShowPdfViewer.value ? 'pdf' : 'image'
})

// 是否应该使用图片预览器
const shouldShowImageViewer = computed(() => {
  return currentViewerType.value === 'image' && displayPages.value.length > 0
})





// ==================== 状态映射 ====================
const statusMap = {
  focus: 'disputeAnalysisStatus',
  evidenceFacts: 'evidenceFactsStatus'
} as const

// ==================== 核心方法 ====================
// 返回列表页
const handleBack = () => {
  router.push('/case')
}

// 切换文件夹展开/收起状态
const toggleFolder = (folderName: string) => {
  folderStates.value[folderName] = !folderStates.value[folderName]
}

// 按照目录顺序查找第一个有文件的目录的第一个文件
const findFirstFileInFirstFolder = (): FileItem | null => {
  try {
    // 按照文件夹配置的顺序遍历
    const folderNames = Object.keys(folderMapping.value)

    for (const folderName of folderNames) {
      const documentTypes = folderMapping.value[folderName]

      // 查找属于当前文件夹的文件
      const folderFiles = fileList.value.filter(file =>
        file.documentType && documentTypes.includes(file.documentType)
      )

      if (folderFiles.length > 0) {
        // 返回该文件夹的第一个文件
        return folderFiles[0]
      }
    }

    // 如果所有配置的文件夹都没有文件，检查"其他材料"
    const uncategorizedFiles = fileList.value.filter(file => {
      const isInAnyFolder = Object.values(folderMapping.value).some(documentTypes =>
        file.documentType && documentTypes.includes(file.documentType)
      )
      return !isInAnyFolder || !file.documentType
    })

    if (uncategorizedFiles.length > 0) {
      return uncategorizedFiles[0]
    }

    // 如果都没有，返回第一个文件作为兜底
    return fileList.value.length > 0 ? fileList.value[0] : null

  } catch (error) {
    console.error('查找第一个文件失败:', error)
    // 兜底逻辑：返回第一个文件
    return fileList.value.length > 0 ? fileList.value[0] : null
  }
}

// 自动展开包含指定文件的文件夹
const autoExpandFoldersForFiles = (targetFiles: FileItem[]) => {
  try {
    // 遍历所有文件夹映射，找到包含目标文件的文件夹
    Object.entries(folderMapping.value).forEach(([folderName, documentTypes]) => {
      const hasTargetFile = targetFiles.some(file =>
        file.documentType && documentTypes.includes(file.documentType)
      )

      if (hasTargetFile) {
        // 展开包含目标文件的文件夹
        folderStates.value[folderName] = true
      }
    })

    // 检查是否有文件在"其他材料"中
    const uncategorizedTargetFiles = targetFiles.filter(file => {
      // 检查文件是否不属于任何已定义的分类
      const isInAnyFolder = Object.values(folderMapping.value).some(documentTypes =>
        file.documentType && documentTypes.includes(file.documentType)
      )
      return !isInAnyFolder || !file.documentType
    })

    if (uncategorizedTargetFiles.length > 0) {
      folderStates.value['其他材料'] = true
    }

  } catch (error) {
    console.error('自动展开文件夹失败:', error)
  }
}

// 切换文件面板可见性
const toggleFileList = () => {
  fileListVisible.value = !fileListVisible.value
}

// 切换回收站显示状态
const toggleRecycleBin = () => {
  recycleBinVisible.value = !recycleBinVisible.value
}

// 显示回收站
const showRecycleBin = () => {
  recycleBinVisible.value = true
}

// 关闭回收站
const closeRecycleBin = () => {
  recycleBinVisible.value = false
}

// 文件恢复后的处理
const handleFileRestored = () => {
  // 重新获取文件列表
  getFileList()
}

// 显示文件上传
const showFileUpload = () => {
  fileUploadVisible.value = true
}

// 关闭文件上传
const closeFileUpload = () => {
  fileUploadVisible.value = false
}

// 文件上传后的处理
const handleFileUploaded = () => {
  // 重新获取文件列表
  getFileList()
}

// 处理文件悬浮状态
const handleFileHover = (file: FileItem, isHover: boolean) => {
  hoveredFileId.value = isHover ? file.id : null
}

// 判断是否显示删除按钮
const shouldShowDeleteButton = (file: FileItem) => {
  // 当文件被选中或鼠标悬浮时显示删除按钮
  return selectedFile.value?.id === file.id || hoveredFileId.value === file.id
}

// 获取当前标签名称
const getCurrentTabName = () => {
  const tabNames = {
    focus: '争议焦点',
    evidenceFacts: '证据事实',
    contradiction: '矛盾识别'
  }
  return tabNames[activeTab.value as keyof typeof tabNames] || ''
}

// 直接重新生成当前标签
const openRegenerateDialog = () => {
  const currentTabName = getCurrentTabName()
  ElMessageBox.confirm(
    `确定要重新生成${currentTabName}的所有模型任务吗？这将覆盖当前所有相关数据。`,
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    await handleRegenerateCurrent()
  }).catch(() => {})
}

// 处理重新生成当前标签（简化后的主要逻辑）
const handleRegenerateCurrent = async () => {
  // 检查当前标签的分析状态
  const statusKey = statusMap[activeTab.value as keyof typeof statusMap]
  if (caseInfo.value[statusKey] === 0) {
    ElMessage.warning(`${getCurrentTabName()}正在分析中，请稍后再试`)
    return
  }

  try {
    await handleRegenerateCurrentAnalysis()
    // 移除全局成功提示，让各个模块自己处理状态显示
  } catch (error) {
    // 全局已处理错误提示
    console.error('重新生成当前标签失败:', error)
  }
}







// 重新生成当前标签分析
const handleRegenerateCurrentAnalysis = async () => {
  const currentTab = activeTab.value

  // 清空当前标签数据
  clearCurrentTabData(currentTab)

  // 调用对应的分析接口
  const caseImportId = route.params.id

  switch (currentTab) {
    case 'focus':
      // 争议焦点标签：调用子组件的重新生成方法，会自动触发TaskGeneratingStatus
      if (disputePointsRef.value?.regenerateFromParent) {
        await disputePointsRef.value.regenerateFromParent()
      }
      caseInfo.value.disputeAnalysisStatus = 0
      // 不再启动旧的轮询机制，TaskGeneratingStatus组件会处理状态监控
      break
    case 'contradiction':
      // 矛盾识别标签：调用子组件的重新生成方法
      if (contradictionRef.value?.regenerateFromParent) {
        await contradictionRef.value.regenerateFromParent()
      }
      break
    case 'evidenceFacts':
      // 证据事实重新生成逻辑：调用子组件的重新生成方法，会自动触发TaskGeneratingStatus
      if (evidenceFactsRef.value?.regenerateAnalysis) {
        await evidenceFactsRef.value.regenerateAnalysis()
      }
      break
  }
}

// 清空当前标签数据
const clearCurrentTabData = (tabName: string) => {
  // 根据标签名清空对应的数据
  switch (tabName) {
    case 'focus':
      if (disputePointsRef.value?.clearData) {
        disputePointsRef.value.clearData()
      }
      break
    case 'contradiction':
      if (contradictionRef.value?.clearData) {
        contradictionRef.value.clearData()
      }
      break
    case 'evidenceFacts':
      if (evidenceFactsRef.value?.clearData) {
        evidenceFactsRef.value.clearData()
      }
      break
  }
}

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'docx':
    case 'doc':
      return wordIcon
    case 'wps':
      return wpsIcon
    case 'pdf':
      return pdfIcon
    case 'txt':
      return txtIcon
    default:
      return txtIcon
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return ''
  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTimeStr
  }
}

// 重试加载图片
const retryLoadImage = () => {
  if (selectedFile.value) {
    handleFileSelect(selectedFile.value)
  }
}

// 上一个文件
const prevFile = () => {
  if (!canGoPrev.value) return
  const currentIndex = fileList.value.findIndex(file => file.id === selectedFile.value?.id)
  if (currentIndex > 0) {
    handleFileSelect(fileList.value[currentIndex - 1])
  }
}

// 下一个文件
const nextFile = () => {
  if (!canGoNext.value) return
  const currentIndex = fileList.value.findIndex(file => file.id === selectedFile.value?.id)
  if (currentIndex < fileList.value.length - 1) {
    handleFileSelect(fileList.value[currentIndex + 1])
  }
}



// ==================== 数据获取方法 ====================
// 获取案件详情
const getCaseDetail = async () => {
  try {
    const caseImportId =decrypt(route.params.id as string)
    const result = await caseApi.getDetail(caseImportId as string)
    if (result) {
      caseInfo.value = result as CaseImportRecord

      // TaskGeneratingStatus组件会自动检测正在运行的任务，不需要手动启动轮询
    }
  } catch (error: any) {
    // 检查是否是权限错误
    if (error?.message?.includes('无权限访问该案件')) {
      router.push('/case')
      return
    }
    // 其他错误由全局错误处理器处理
  }
}

// 获取案件类型信息
const getCaseTypeInfo = async () => {
  try {
    const caseImportId =decrypt(route.params.id as string)
    const result = await caseApi.getCaseType(caseImportId as string)
    if (result) {
      caseTypeInfo.value = result
      console.log('获取案件类型信息成功:', result)

      // 初始化文件夹状态
      initializeFolderStates()
    }
  } catch (error) {
    console.error('获取案件类型信息失败:', error)
    // 使用默认的一审配置
    caseTypeInfo.value = {
      ajlxdm: '0301',
      caseType: '一审',
      caseTypeCode: 'MSYS',
      isSecondInstance: false
    }
    initializeFolderStates()
  }
}

// 获取文件列表
const getFileList = async () => {
  try {
    const caseImportId =decrypt(route.params.id as string)
    const result = await caseApi.getCaseFileList(caseImportId as string)

    if (result && Array.isArray(result)) {
      fileList.value = result.map((item: any) => ({
        id: item.id,
        name: item.fileName,
        fileName: item.fileName,
        documentType: item.documentType,
        uploadTime: item.uploadTime,
        fileSize: item.fileSize,
        fileType: item.fileType,
        status: item.status,
        extractedText: item.extractedText,
        ocrPreviews: item.ocrPreviews,
        ocrResult: item.ocrResult,
        content: undefined
      }))

      // 按照目录顺序选中第一个有文件的目录的第一个文件
      if (fileList.value.length > 0 && !selectedFile.value) {
        const firstFileInFirstFolder = findFirstFileInFirstFolder()
        if (firstFileInFirstFolder) {
          // 自动展开包含该文件的文件夹
          autoExpandFoldersForFiles([firstFileInFirstFolder])
          await handleFileSelect(firstFileInFirstFolder)
        }
      }

      // 检查是否包含反诉文件（documentType = 'FSZ'）
      hasCounterclaim.value = fileList.value.some(file => file.documentType === 'FSZ')
      console.log('检查反诉文件:', hasCounterclaim.value)

    } else {
      fileList.value = []
      selectedFile.value = null
      hasCounterclaim.value = false
    }
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取文件列表失败:', error)
    fileList.value = []
    selectedFile.value = null
    hasCounterclaim.value = false
  }
}

// 文件选择处理
const handleFileSelect = async (file: FileItem) => {
  if (selectedFile.value?.id === file.id) return

  try {
    documentLoading.value = true
    documentError.value = ''
    ocrProcessing.value = false
    selectedTextData.value = null
    currentPage.value = 1

    // 统一使用图片预览，不再根据文档类型判断
    let ocrResult = null
    let ocrPreviews = null
    let pdfContent = null

    // 统一使用图片预览逻辑 - 获取OCR数据
    try {
      const [previews, result] = await Promise.allSettled([
        caseApi.getFileOcrPreviews(file.id),
        caseApi.getFileOcrResult(file.id)
      ])

      if (previews.status === 'fulfilled') {
        ocrPreviews = previews.value
      } else {
        console.warn('OCR预览获取失败:', previews.reason)
      }

      if (result.status === 'fulfilled') {
        ocrResult = result.value
      } else {
        console.warn('OCR结果获取失败:', result.reason)
      }

      // 根据ocrPreviews状态判断OCR处理状态
      const ocrStatus = getOcrStatus(ocrPreviews)

      if (ocrStatus === 'processing') {
        // OCR处理中，显示处理中状态
        documentError.value = ''
        documentLoading.value = false
        ocrProcessing.value = true
        // 不要return，继续更新selectedFile
      } else if (ocrStatus === 'failed' || ocrStatus === 'not_started' || (!ocrPreviews?.length && !ocrResult?.length)) {
        // OCR失败或无数据，显示重新生成按钮
        documentError.value = '文档识别失败，请重新生成'
        ocrProcessing.value = false
      } else {
        // OCR完成，清除处理中状态
        ocrProcessing.value = false
      }
    } catch (error) {
      console.error('OCR数据获取失败:', error)
      documentError.value = '文档数据获取失败'
    }

    // 更新选中文件
    selectedFile.value = {
      ...file,
      ocrResult,
      ocrPreviews,
      pdfContent,
      totalPages: ocrPreviews ? ocrPreviews.length : 1,
      currentPage: 1
    }


    // 更新当前文件的高亮数据
    await nextTick()
    updateCurrentFileHighlights()

  } catch (error) {
    console.error('文件加载失败:', error)
    documentError.value = '文件加载失败，请重试'
    ElMessage.error('文件加载失败')
  } finally {
    documentLoading.value = false
  }
}

// PDF预览器事件处理
const handlePdfLoaded = () => {
  console.log('PDF加载完成')
  documentLoading.value = false
  documentError.value = ''
}

const handlePdfRendered = () => {
  console.log('PDF渲染完成')
  // PDF渲染完成后可以进行其他操作
}

const handlePdfError = (error: string) => {
  console.error('PDF预览错误:', error)
  documentError.value = error
  documentLoading.value = false
}

// 删除文件
const handleDeleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      '确定要将此文件移入回收站吗？文件将可以在回收站中恢复。',
      '移入回收站',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除API（移入回收站）
    const result = await caseApi.deleteFile(file.id, '当前用户') // TODO: 获取真实用户信息

    if (result) {
      // 从列表中移除
      const index = fileList.value.findIndex(f => f.id === file.id)
      if (index > -1) {
        fileList.value.splice(index, 1)
      }

      // 如果删除的是当前选中文件，选择下一个文件
      if (selectedFile.value?.id === file.id) {
        if (fileList.value.length > 0) {
          const nextIndex = Math.min(index, fileList.value.length - 1)
          await handleFileSelect(fileList.value[nextIndex])
        } else {
          selectedFile.value = null
        }
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('删除文件失败:', error)
    }
  }
}
// ==================== 高亮处理方法 ====================
// 重置高亮状态
const resetHighlight = () => {
  try {
    // 立即清空，不使用 nextTick
    highlightedFileIds.value = []
    currentExternalHighlights.value = []
    currentHighlightEvidence.value = []
  } catch (error) {
    console.error('重置高亮状态失败:', error)
  }
}

// 重置文件状态
const resetFile = () => {
  selectedFile.value = null
  resetHighlight()
}

// 处理高亮显示
const handleHighlight = async (evidence: Array<{
  fileId: number
  fileName: string
  pageNumber?: number
  highlight?: string
}>) => {
  try {
    // 检查组件是否还在挂载状态
    if (!isMounted.value) {
      return
    }

    // 如果没有证据数据，显示提示
    if (!evidence || evidence.length === 0) {
      ElMessage.warning('未找到相关信息')
      return
    }

    // 重置高亮状态
    resetHighlight()

    // 立即保存当前高亮证据数据（在重置之后）
    currentHighlightEvidence.value = [...evidence] // 使用浅拷贝避免响应式问题

    // 获取需要高亮的文件ID
    const fileIds = evidence.map(e => Number(e.fileId))
    highlightedFileIds.value = [...fileIds] // 使用浅拷贝

    // 查找目标文件
    const targetFiles = fileList.value.filter(file => fileIds.includes(file.id))

    if (targetFiles.length === 0) {
      ElMessage.warning('未找到对应的文件')
      return
    }

    // 自动展开包含高亮文件的文件夹
    autoExpandFoldersForFiles(targetFiles)

    // 选择第一个目标文件
    if (!selectedFile.value || !fileIds.includes(selectedFile.value.id)) {
      await handleFileSelect(targetFiles[0])
    }

    // 使用 nextTick 确保文件数据已完全更新
    await nextTick()
    updateCurrentFileHighlights()

  } catch (error) {
    console.error('处理高亮失败:', error)
    ElMessage.error('高亮处理失败')
  }
}

// 处理文件跳转和页码跳转（不涉及高亮信息）
const handleFileJump = async (jumpData: Array<{
  fileId: number
  fileName: string
  pageNumber?: number
}>) => {
  console.log('handleFileJump', jumpData)
  try {
    // 检查组件是否还在挂载状态
    if (!isMounted.value) {
      return
    }

    // 如果没有跳转数据，显示提示
    if (!jumpData || jumpData.length === 0) {
      ElMessage.warning('未找到相关信息')
      return
    }

    // 获取需要跳转的文件ID
    const fileIds = jumpData.map(item => Number(item.fileId))
    
    // 查找目标文件
    const targetFiles = fileList.value.filter(file => fileIds.includes(file.id))

    if (targetFiles.length === 0) {
      ElMessage.warning('未找到对应的文件')
      return
    }

    // 自动展开包含目标文件的文件夹
    autoExpandFoldersForFiles(targetFiles)

    // 选择第一个目标文件
    if (!selectedFile.value || !fileIds.includes(selectedFile.value.id)) {
      await handleFileSelect(targetFiles[0])
    }

    // 使用 nextTick 确保文件数据已完全更新
    await nextTick()

    // 如果有指定页码，跳转到指定页面
    const firstJumpData = jumpData[0]
    if (firstJumpData.pageNumber && firstJumpData.pageNumber > 0) {
      // 计算页面索引（页码从1开始，索引从0开始）
      const targetPageIndex = Math.min(firstJumpData.pageNumber - 1, totalPages.value - 1)
      
      // 更新当前页码
      currentPage.value = targetPageIndex + 1
      
      console.log(`跳转到文件 ${firstJumpData.fileName} 第 ${firstJumpData.pageNumber} 页`)
      
      // 如果使用 DocumentViewer，通过 ref 调用其方法跳转页面
      if (documentViewerRef.value && documentViewerRef.value.jumpToPage) {
        // 等待下一个 tick 确保组件已更新
        await nextTick()
        documentViewerRef.value.jumpToPage(targetPageIndex)
      }
    }

  } catch (error) {
    console.error('文件跳转失败:', error)
    ElMessage.error('文件跳转失败')
  }
}


// 更新当前文件的高亮数据
const updateCurrentFileHighlights = () => {
  try {
    // 检查组件是否还在挂载状态
    if (!isMounted.value) {
      return
    }

    // 检查数据有效性
    if (!selectedFile.value || !currentHighlightEvidence.value || currentHighlightEvidence.value.length === 0) {
      currentExternalHighlights.value = []
      return
    }

    // 筛选出当前文件的高亮数据
    const currentFileEvidence = currentHighlightEvidence.value.filter(
      evidence => evidence && evidence.fileId === selectedFile.value?.id
    )

    if (currentFileEvidence.length === 0) {
      currentExternalHighlights.value = []
      return
    }

    // 转换为 DocumentViewer 需要的格式
    const highlights = currentFileEvidence.map(evidence => {
      const originalText = evidence.highlight || ''
      const cleanedText = originalText.replace(/\s+/g, '') // 去除所有空格
      return {
        // 不传递页码，使用全文搜索提高匹配成功率
        // pageNumber: evidence.pageNumber || 1,
        text: cleanedText,
        originalText: originalText, // 保留原始文本用于调试
        // 暂时不提供具体位置信息，让 DocumentViewer 通过文本搜索来定位
        locations: undefined
      }
    }).filter(highlight => {
      return highlight && highlight.text && highlight.text.trim() !== ''
    })

    // 直接更新响应式数据
    currentExternalHighlights.value = [...highlights] // 使用浅拷贝

  } catch (error) {
    console.error('updateCurrentFileHighlights 执行失败:', error)
    // 发生错误时清空高亮数据
    currentExternalHighlights.value = []
  }
}



// ==================== 辅助函数 ====================





// 清除选择
const handleClearSelection = () => {
  selectedTextData.value = null
}

// 文字块点击
const handleTextBlockClick = (block: any, page: any, pageIndex: number) => {
  selectedTextData.value = {
    text: block.text,
    coordinate: block,
    pageIndex
  }
}

// 判断OCR状态
const getOcrStatus = (ocrPreviews: any[]): 'not_started' | 'processing' | 'completed' | 'failed' => {
  if (!ocrPreviews) {
    return 'not_started'
  } else if (ocrPreviews.length === 0) {
    return 'not_started'  // 空数组表示没有数据，需要重新生成
  } else if (ocrPreviews.length === 1 &&
            (!ocrPreviews[0].ocrImage && !ocrPreviews[0].docPreprocessingImage && !ocrPreviews[0].inputImage)) {
    return 'processing'  // 有一个空对象表示处理中
  } else {
    return 'completed'   // 有有效数据表示完成
  }
}

// 重试加载文档
const retryLoadDocument = async () => {
  if (!selectedFile.value) return

  // 检查是否是OCR数据缺失的情况
  const isQTType = selectedFile.value.documentType === 'QT' ||
                   selectedFile.value.documentType === null ||
                   selectedFile.value.documentType === undefined

  if (!isQTType && documentError.value === '文档识别失败，请重新生成') {
    // 非QT类型且是OCR数据缺失，启动重新OCR
    console.log('开始重新生成OCR，设置处理中状态')

    // 立即清除所有状态并显示处理中状态
    documentError.value = ''
    documentLoading.value = false
    ocrRetrying.value = true
    ocrProcessing.value = true

    console.log('状态设置完成:', {
      ocrRetrying: ocrRetrying.value,
      documentError: documentError.value,
      documentLoading: documentLoading.value,
      ocrProcessing: ocrProcessing.value
    })

    // 确保UI立即更新
    await nextTick()

    try {
      // 调用重新OCR接口
      await caseApi.retryFileOcr(selectedFile.value.id)

      // API已经显示成功提示，这里不需要重复提示

    } catch (error) {
      console.error('重新OCR失败:', error)
      // 如果接口调用失败，恢复错误状态
      documentError.value = '重新生成失败，请重试'
      ocrProcessing.value = false
    } finally {
      ocrRetrying.value = false
    }
  } else {
    // 其他情况，普通重试或重新获取数据
    handleFileSelect(selectedFile.value)
  }
}




// DocumentViewer 事件处理
const handleTextBlockHover = (block: any, isHover: boolean) => {
  // 处理文字块悬浮
}

// 处理文字选择
const handleTextSelection = (selection: any) => {
  console.log('文字选择事件:', selection, '证据选择模式:', evidenceSelectionMode.value)

  // 如果是null或undefined，表示取消选择
  if (!selection) {
    // 只在非证据选择模式下清空选择数据
    if (!evidenceSelectionMode.value) {
      selectedTextData.value = null
    }
    console.log('选择被清空，证据选择模式:', evidenceSelectionMode.value)
    return
  }

  let selectedText = ''
  let pageNumber = 1

  // 处理段落选择
  if (selection.type === 'paragraph') {
    selectedText = selection.paragraphContent || ''
    pageNumber = selection.pageIndex + 1
    console.log('段落选择:', selectedText, '页码:', pageNumber)
  }
  // 处理OCR行选择
  else if (selection.type === 'ocr-lines') {
    selectedText = selection.combinedText || ''
    pageNumber = selection.selectedLines.length > 0 ? selection.selectedLines[0].pageIndex + 1 : 1
    console.log('OCR行选择:', selectedText, '页码:', pageNumber)
  }
  // 处理旧版本的selectedBlocks格式（兼容性）
  else if (selection.selectedBlocks && selection.selectedBlocks.length > 0) {
    selectedText = selection.selectedBlocks.map((blockKey: string) => {
      const [pageIndex, blockIndex] = blockKey.split('-').map(Number)
      const page = displayPages.value[pageIndex]
      if (page?.ocrResult?.ocrBlocks?.[blockIndex]) {
        return page.ocrResult.ocrBlocks[blockIndex].text
      }
      return ''
    }).filter(text => text).join('\n')
    pageNumber = selection.selectedBlocks.length > 0 ?
      parseInt(selection.selectedBlocks[0].split('-')[0]) + 1 : 1
    console.log('旧版本选择:', selectedText, '页码:', pageNumber)
  }

  // 只有在有有效文本时才更新选择数据
  if (selectedText && selectedText.trim()) {
    selectedTextData.value = {
      text: selectedText.trim(),
      selection: selection,
      fileId: selectedFile.value?.id,
      fileName: selectedFile.value?.name,
      pageNumber: pageNumber
    }

    console.log('✅ 有效文字选择:', selectedTextData.value)

    // 在证据选择模式下，给用户反馈
    if (evidenceSelectionMode.value) {
      console.log('🎯 证据选择模式下的文本选择，将自动添加为证据')
    }
  } else {
    console.log('❌ 选择的文本为空或无效:', selectedText)
    // 只在非证据选择模式下清空选择数据
    if (!evidenceSelectionMode.value) {
      selectedTextData.value = null
    }
  }
}

const handleDocumentPageChange = (pageIndex: number) => {
  currentPage.value = pageIndex + 1
  console.log(`当前查看文档第 ${pageIndex + 1} 页`)
}

const handleDocumentImageLoad = (pageIndex: number, event: Event) => {
  console.log(`文档第${pageIndex + 1}页图片加载完成`)
  if (pageIndex === 0) {
    documentLoading.value = false
    documentError.value = ''
  }
}

const handleDocumentImageError = (pageIndex: number, event: Event) => {
  console.error(`文档第${pageIndex + 1}页图片加载失败:`, event)
  if (pageIndex === 0) {
    documentLoading.value = false
    documentError.value = `第${pageIndex + 1}页图片加载失败`
  }
}

// ==================== PDF预览器事件处理 ====================
const handlePdfTextSelected = (data: { text: string; pageNumber: number }) => {
  console.log('PDF文本选择:', data)

  // 更新选择数据
  if (data.text && data.text.trim()) {
    selectedTextData.value = {
      text: data.text.trim(),
      selection: null, // PDF选择没有selection对象
      fileId: selectedFile.value?.id,
      fileName: selectedFile.value?.name,
      pageNumber: data.pageNumber
    }

    console.log('✅ PDF文字选择:', selectedTextData.value)

    // 在证据选择模式下，给用户反馈
    if (evidenceSelectionMode.value) {
      console.log('🎯 证据选择模式下的PDF文本选择，将自动添加为证据')
    }
  } else {
    console.log('❌ PDF选择的文本为空或无效:', data.text)
    if (!evidenceSelectionMode.value) {
      selectedTextData.value = null
    }
  }
}

const handlePdfPageChange = (page: number) => {
  currentPage.value = page
  console.log(`当前查看PDF第 ${page} 页`)
}

// ==================== 证据选择模式处理 ====================
// 进入证据选择模式
const handleEnterEvidenceSelectionMode = (data: { side: 'plaintiff' | 'defendant' }) => {
  evidenceSelectionMode.value = true
  currentSelectingSide.value = data.side

  if (currentSelectingSide.value !== data.side) {
    console.log(`切换证据选择方：从${currentSelectingSide.value || '无'}到${data.side}`)
  } else {
    console.log(`进入证据选择模式，当前选择方：${data.side}`)
  }
}

// 退出证据选择模式
const handleExitEvidenceSelectionMode = () => {
  evidenceSelectionMode.value = false
  currentSelectingSide.value = null
  console.log('退出证据选择模式')
}





// ==================== 组件引用 ====================
const disputePointsRef = ref()
const evidenceFactsRef = ref()
const contradictionRef = ref()



// ==================== 状态轮询 ====================
let pollingTimer: NodeJS.Timeout | null = null

const startStatusPolling = (statusKey: string) => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
  }

  pollingTimer = setInterval(async () => {
    await getCaseDetail()
    if (caseInfo.value[statusKey as keyof typeof caseInfo.value] === 1) {
      clearInterval(pollingTimer!)
      pollingTimer = null
    }
  }, 3000)
}

const stopStatusPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

// ==================== 监听器 ====================
// 监听标签页切换
watch(activeTab, async (newTab) => {
  console.log('切换到标签页:', newTab)
  resetHighlight()

  // TaskGeneratingStatus组件会自动检测当前标签的任务状态
})

// 监听数据加载状态
watch(isDataLoaded, (loaded) => {
  console.log('数据加载状态:', loaded)
})

// 窗口大小变化处理函数
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
const id = computed(() => {
  const encryptedId = route.params.id as string;
  return decrypt(encryptedId);
});
// ==================== 生命周期 ====================
onMounted(async () => {
  console.log('页面开始加载...')
  isMounted.value = true

  try {
    // 并行加载数据，先获取案件类型信息，再获取其他数据
    await Promise.allSettled([
      getCaseTypeInfo(),
      getCaseDetail(),
      getFileList()
    ])
  } catch (error) {
    // 全局已处理错误提示
    console.error('页面数据加载失败:', error)
  } finally {
    isDataLoaded.value = true
    console.log('页面数据加载完成')
  }

  // 添加窗口大小变化监听器，用于动态调整文档尺寸
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  console.log('页面卸载，清理资源...')
  isMounted.value = false

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)

  // 停止轮询
  stopStatusPolling()

  // 清理blob URL
  if (selectedFile.value?.pdfContent && selectedFile.value.pdfContent.startsWith('blob:')) {
    URL.revokeObjectURL(selectedFile.value.pdfContent)
  }

  // 清理高亮数据
  try {
    highlightedFileIds.value = []
    currentExternalHighlights.value = []
    currentHighlightEvidence.value = []
  } catch (error) {
    console.error('清理高亮数据失败:', error)
  }
})

// ==================== TaskGeneratingStatus 相关方法 ====================
// 获取当前标签对应的任务类型
const getTaskTypeForCurrentTab = () => {
  switch (activeTab.value) {
    case 'focus':
      return TaskType.DISPUTE_ANALYSIS
    case 'evidenceFacts':
      return TaskType.EVIDENCE_FACTS
    default:
      return TaskType.DISPUTE_ANALYSIS
  }
}

// 任务完成处理
const handleTaskCompleted = () => {
  const currentTabName = getCurrentTabName()
  ElMessage.success(`${currentTabName}分析完成`)

  // 刷新案件详情数据
  getCaseDetail()

  // 根据当前标签刷新对应的组件数据
  if (activeTab.value === 'focus' && disputePointsRef.value?.refreshData) {
    disputePointsRef.value.refreshData()
  } else if (activeTab.value === 'contradiction' && contradictionRef.value?.refreshData) {
    contradictionRef.value.refreshData()
  } else if (activeTab.value === 'evidenceFacts' && evidenceFactsRef.value?.refreshData) {
    evidenceFactsRef.value.refreshData()
  }
}

// 任务失败处理
const handleTaskFailed = (event: any) => {
  console.error('分析任务失败:', event)
  ElMessage.error(`分析任务失败: ${event.errorMessage || '未知错误'}`)
}

// tab动态渲染
const visibleTabs = computed(() => {
  if (caseInfo.value.caseType === '0' || caseInfo.value.caseType === 0) {
    return [
      // { label: '文书生成', name: 'documentGeneration' }
    ]
  }
  return [
    { label: '证据事实', name: 'evidenceFacts' },
    { label: '争议焦点', name: 'focus' },
    { label: '矛盾识别', name: 'contradiction' },
    // { label: '文书生成', name: 'documentGeneration' }
  ]
})

// 自动切换到第一个可见tab
watch(
  () => caseInfo.value.caseType,
  (val) => {
    if (visibleTabs.value.length > 0) {
      activeTab.value = visibleTabs.value[0].name
    }
  },
  { immediate: true }
)

</script>

<style scoped>
.case-detail-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f2f2f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  font-size: 32px;
  color: #007AFF;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #86868B;
  margin: 0;
}

.page-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  width: 100%;
  background: #ffffff;
  border-bottom: 1px solid #e5e5ea;
  flex-shrink: 0;
  z-index: 100;
  height: 50px !important;
  max-height: 50px !important;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  max-width: 100%;
  gap: 16px;
  position: relative;
  height: 50px !important;
  max-height: 50px !important;
  min-height: 50px !important;
}

.case-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f63ff;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: -0.01em;
}

.header-tabs {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
}

.header-tabs .analysis-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: none;
  }

  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }

  :deep(.el-tabs__nav) {
    border: none;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 3px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.5);
    margin-bottom: 0;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  :deep(.el-tabs__item) {
    border: none !important;
    padding: 6px 14px;
    font-size: 13px;
    font-weight: 500;
    color: #8E8E93;
    background: transparent;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    letter-spacing: -0.01em;

    &:hover {
      color: #1f63ff;
      background: rgba(31, 99, 255, 0.08);
      transform: scale(1.02);
    }

    &.is-active {
      color: #1f63ff;
      background: rgba(255, 255, 255, 0.95);
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(31, 99, 255, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
      transform: scale(1.02);
    }
  }

  :deep(.el-tabs__active-bar) {
    display: none;
  }
}

.header-actions {
  flex-shrink: 0;
}

.back-btn {
  background: #007AFF !important;
  border: none !important;
  color: white !important;
  font-weight: 500;
  border-radius: 8px !important;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #0056CC !important;
  transform: translateY(-1px);
}

.page-main {
  width: 100%;
  flex: 1;
  display: flex;
  gap: 12px;
  padding: 6px 12px 12px 12px;
  min-height: 0;
}

.file-panel {
  flex: 0 0 320px; /* 从300px增加到350px */
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.file-panel.panel-collapsed {
  flex: 0 0 48px;
}

.expand-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e5e5ea;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.expand-btn:hover {
  background: #f8f9fa;
  border-color: #007AFF;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.expand-btn .el-icon {
  font-size: 14px;
  color: #86868B;
  transition: color 0.3s ease;
}

.expand-btn:hover .el-icon {
  color: #007AFF;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-section {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-section .search-input {
  flex: 1;

  :deep(.el-input__wrapper) {
    border-radius: 10px;
    border: 1px solid #e5e5ea;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      border-color: #007AFF;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    }

    &.is-focus {
      border-color: #007AFF;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }
}

.collapse-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e5e5ea;
  border-radius: 8px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.collapse-btn:hover {
  background: #f8f9fa;
  border-color: #007AFF;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.collapse-btn .el-icon {
  font-size: 14px;
  color: #86868B;
  transition: color 0.3s ease;
}

.collapse-btn:hover .el-icon {
  color: #007AFF;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.clear-highlight-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e5e5ea;
  border-radius: 8px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.clear-highlight-btn:hover {
  background: rgba(255, 149, 0, 0.1);
  border-color: #FF9500;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.2);
}

.clear-highlight-btn .el-icon {
  font-size: 14px;
  color: #FF9500;
  transition: color 0.3s ease;
}

.clear-highlight-btn:hover .el-icon {
  color: #FF6B35;
}

.file-list {
  flex: 1;
  padding: 8px 12px 16px;
  overflow-y: auto;
  min-height: 0;
  position: relative;
}

.file-list::-webkit-scrollbar {
  width: 4px;
}

.file-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.file-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  transition: background 0.2s ease;
}

.file-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 浮动按钮容器样式 */
.file-panel .float-buttons-container {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24px; /* 稍微增加按钮间距 */
  z-index: 10;
}

/* 浮动按钮样式优化 */
.file-panel .float-buttons-container .el-button {
  backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px) scale(1.02);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

/* 上传按钮样式 */
.file-panel .upload-float-btn {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%); /* 改为蓝色系，与页面主色调协调 */
}





/* 按钮图标样式 */
.file-panel .float-buttons-container .el-button .el-icon {
  font-size: 16px;
}

/* 文件面板折叠时的按钮样式 */
.file-panel.panel-collapsed .float-buttons-container {
  bottom: 12px;
  gap: 18px;
}

.file-panel.panel-collapsed .float-buttons-container .el-button {
  width: 32px !important;
  height: 32px !important;
}

.file-panel.panel-collapsed .float-buttons-container .el-button .el-icon {
  font-size: 14px;
}

/* 回收站模态框样式 */
:deep(.recycle-bin-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.recycle-bin-dialog .el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.recycle-bin-dialog .el-dialog__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e5e5ea;
  padding: 16px 20px;
}

:deep(.recycle-bin-dialog .el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

:deep(.recycle-bin-dialog .recycle-bin) {
  border-radius: 0;
  height: 500px;
}

:deep(.recycle-bin-dialog .recycle-bin-content) {
  max-height: 450px;
}

/* 文件上传模态框样式 - 与导入组件完全一致 */
.file-upload-dialog {
  :deep(.el-overlay) {
    background: rgba(0, 0, 0, 0.4) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    background: rgba(255, 255, 255, 0.6) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-radius: 16px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    min-height: 450px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
  }

  :deep(.el-dialog__header) {
    display: none !important;
  }

  :deep(.el-dialog__body) {
    padding: 40px 40px 20px 40px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
  }

  :deep(.el-dialog__close) {
    color: #6b7280 !important;
    font-size: 18px !important;

    &:hover {
      color: #374151 !important;
    }
  }
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  background: #fafafa;
  border-radius: 12px;
  margin: 20px 12px;
  border: 2px dashed #e5e5ea;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 14px 12px;
  margin: 4px 0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  background: #f8f9fa;
  border-color: #e5e5ea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.file-item.item-active {
  background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
  border-color: #007AFF;
  box-shadow: 0 3px 12px rgba(0, 122, 255, 0.15);
  transform: translateY(-1px);
}

.file-item.item-highlighted::before {
  content: '';
  position: absolute;
  right: 8px;
  top: 8px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #FF9500, #FF6B35);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
}

.file-icon {
  width: 36px;
  height: 36px;
  margin-right: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  flex-shrink: 0;
}

.file-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.file-actions {
  opacity: 0;
  transition: all 0.25s ease;
  transform: translateX(8px);
}

.file-item:hover .file-actions {
  opacity: 1;
  transform: translateX(0);
}

.file-actions .delete-btn {
  color: #8e8e93;
  border-radius: 6px;
  padding: 4px;
  transition: all 0.2s ease;
}

.file-actions .delete-btn:hover {
  color: #ff3b30;
  background: rgba(255, 59, 48, 0.1);
}

/* ==================== 文件夹和紧凑布局样式 ==================== */
.file-tree {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-group {
  margin-bottom: 6px;
}

.folder-header {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，适应多行文本 */
  min-height: 32px; /* 稍微增加最小高度 */
  height: auto; /* 允许高度自适应 */
  padding: 6px 8px; /* 增加上下内边距 */
  background: #f5f7fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  gap: 6px; /* 添加元素间距 */
}

.folder-header:hover {
  background: #e8f4fd;
  border-color: #b3d8ff;
}

.folder-icon {
  font-size: 16px;
  color: #409eff;
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-top: 2px; /* 稍微向下偏移，与文本对齐 */
  transition: transform 0.2s ease;
}

.folder-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4; /* 增加行高，让多行文本更好看 */
  word-break: break-all; /* 允许在任意字符处换行 */
  min-width: 0; /* 确保flex子元素可以收缩 */
}

.file-count {
  font-size: 11px;
  color: #909399;
  flex-shrink: 0; /* 防止文件数量被压缩 */
  white-space: nowrap;
}

.expand-icon {
  font-size: 12px;
  color: #909399;
  flex-shrink: 0; /* 防止展开图标被压缩 */
  margin-top: 2px; /* 稍微向下偏移，与文本对齐 */
  transition: transform 0.2s ease;
}

.folder-content {
  margin-left: 8px;
  margin-top: 2px;
}

/* 紧凑文件项样式 */
.file-item.compact {
  height: 26px;
  padding: 2px 8px 2px 16px;
  margin: 1px 0;
  border-radius: 4px;
  background: #ffffff;
  border: 1px solid transparent;
  box-shadow: none;
}

.file-item.compact:hover {
  background: #f8f9fa;
  border-color: #e5e5ea;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  transform: none;
}

.file-item.compact.item-active {
  background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
  border-color: #007AFF;
  box-shadow: 0 1px 6px rgba(0, 122, 255, 0.12);
  transform: none;
}

.file-item.compact.item-highlighted::before {
  right: 6px;
  top: 6px;
  width: 6px;
  height: 6px;
}

.file-item.compact .file-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background: transparent;
  border-radius: 4px;
}

.file-item.compact .file-icon img {
  width: 16px;
  height: 16px;
}

.file-item.compact .file-name {
  font-size: 13px;
  font-weight: 400;
  line-height: 1.2;
}

.file-item.compact .file-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  transform: none;
}

.file-item.compact:hover .file-actions {
  opacity: 1;
  transform: none;
}

.file-item.compact .delete-btn {
  padding: 2px;
  font-size: 12px;
}

.file-item.compact .delete-btn .el-icon {
  font-size: 12px;
}

.main-content-section {
  flex: 1;
  min-width: 500px;
  display: flex;
  gap: 12px;
}

.document-section {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 400px;
}

/* 证据选择模式样式 */
.evidence-selection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
  border-bottom: 2px solid #67c23a;
  animation: slideDown 0.3s ease-out;

  .selection-title {
    font-size: 15px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .selection-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .selection-tip {
      font-size: 13px;
      color: #67c23a;
      font-weight: 500;
    }
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.document-viewer-container.selection-mode {
  border: 2px dashed #9c27b0;
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(233, 30, 99, 0.05) 100%);
}



.document-viewer-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f8f9fa;
}

.document-loading, .document-error, .document-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: #f8f9fa;
  z-index: 10;
}

.document-loading .loading-spinner {
  font-size: 32px;
  color: #007AFF;
  animation: spin 1s linear infinite;
}

.document-error .error-icon {
  font-size: 32px;
  color: #FF3B30;
}

.document-loading p, .document-error p, .document-empty p {
  font-size: 16px;
  color: #86868B;
  margin: 0;
}

/* 现代化错误提示样式 */
.document-error-modern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
  padding: 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.document-error-modern .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  text-align: center;
}

.document-error-modern .error-icon-wrapper {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.15);
}

.document-error-modern .error-icon-wrapper::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  z-index: -1;
  opacity: 0.1;
}

.document-error-modern .error-icon-modern {
  font-size: 16px;
  color: #ef4444;
  filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.2));
}

.document-error-modern .error-text {
  max-width: 400px;
}

.document-error-modern .error-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
}

.document-error-modern .error-message {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.document-error-modern .error-actions {
  display: flex;
  gap: 12px;
}

.document-error-modern .retry-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transition: all 0.2s ease;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.document-error-modern .retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
}

.document-error-modern .retry-button:active {
  transform: translateY(0);
}

.document-error-modern .retry-button.is-loading {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

.document-error-modern .retry-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* OCR处理中样式 */
.ocr-processing {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  z-index: 10;
  padding: 40px;
}

.ocr-processing .processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  text-align: center;
}

.ocr-processing .processing-icon-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  animation: pulse-processing 2s ease-in-out infinite;
}

.ocr-processing .processing-icon {
  font-size: 16px;
  color: white;
  animation: spin 2s linear infinite;
}

.ocr-processing .processing-text {
  max-width: 400px;
}

.ocr-processing .processing-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
}

.ocr-processing .processing-message {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.ocr-processing .processing-actions {
  display: flex;
  gap: 12px;
}

.ocr-processing .refresh-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transition: all 0.2s ease;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.ocr-processing .refresh-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
}

.ocr-processing .refresh-button:active {
  transform: translateY(0);
}

.ocr-processing .refresh-icon {
  margin-right: 6px;
  font-size: 14px;
}

@keyframes pulse-processing {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 无预览内容样式 */
.no-preview-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: #f8f9fa;
  z-index: 10;
}

.no-preview-content .preview-tip {
  font-size: 14px;
  color: #86868B;
  text-align: center;
  line-height: 1.5;
  margin: 0;
}



.analysis-content {
  flex: 1;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 改为自动滚动，让内容可以滚动显示 */
  border: 1px solid rgba(229, 229, 234, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  min-width: 400px;
  padding: 24px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(250, 251, 252, 0.8) 100%);
    z-index: -1;
  }
}













.loading-spinner {
  font-size: 24px;
  color: #3b82f6;
}

.loading-spinner .el-icon {
  animation: spin 1s linear infinite;
}

@keyframes titlePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1440px) {
  .case-detail-page .page-main .file-panel {
    flex: 0 0 280px;
  }

  .case-detail-page .page-main .analysis-panel {
    flex: 0 0 38%;
    min-width: 350px;
  }
}

@media (max-width: 1200px) {
  .case-detail-page .page-main .file-panel {
    flex: 0 0 260px;
  }

  .case-detail-page .page-main .file-panel.panel-collapsed {
    flex: 0 0 44px;
  }

  .case-detail-page .page-main .pdf-section {
    min-width: 400px;
  }

  .case-detail-page .page-main .analysis-panel {
    flex: 0 0 35%;
    min-width: 320px;
  }
}

/* PDF预览区域样式 */
.pdf-preview-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.pdf-embed {
  width: 100%;
  height: 100%;
}

/* 全局样式覆盖 Element Plus tabs - 强制垂直居中 */
.case-detail-page .page-header .header-tabs {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  height: 50px !important;
}

.case-detail-page .page-header .analysis-tabs {
  display: flex !important;
  align-items: center !important;
  height: auto !important;
}

.case-detail-page .page-header .analysis-tabs .el-tabs__header {
  height: auto !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
}

.case-detail-page .page-header .analysis-tabs .el-tabs__nav-wrap {
  height: auto !important;
  display: flex !important;
  align-items: center !important;
}

.case-detail-page .page-header .analysis-tabs .el-tabs__nav-wrap::after {
  display: none !important;
}

.case-detail-page .page-header .analysis-tabs .el-tabs__nav {
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  padding: 3px !important;
}

.case-detail-page .page-header .analysis-tabs .el-tabs__item {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 2px !important;
}

</style>