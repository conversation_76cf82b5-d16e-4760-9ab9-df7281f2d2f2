<template>
  <div class="case-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" class="search-form">
        <el-row :gutter="32" justify="center">
          <el-col :span="6">
            <el-form-item label="案件">
              <el-input
                v-model="searchForm.casename"
                placeholder=""
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="操作人">
              <el-input
                v-model="searchForm.operator"
                placeholder=""
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>

          <el-col :span="7">
            <el-form-item label="操作时间">
              <el-date-picker
                v-model="searchForm.operateTime"
                type="daterange"
                range-separator="至"
                start-placeholder=""
                end-placeholder=""
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 1, 1, 23, 59, 59)
                ]"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="3">
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.status"
                placeholder=""
                style="width: 100%"
              >
                <el-option label="全部" value="" />
                <el-option label="已完成" value="1" />
                <el-option label="分析中" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="1">
            <div class="search-actions">
              <el-button type="primary" :loading="loading" @click="handleSearch">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 结果展示区域 -->
    <div class="result-container">
      <!-- 操作栏 -->
      <div class="result-header">
        <el-button type="success" @click="showImportWizard">
          <el-icon><Upload /></el-icon>
          导入
        </el-button>
      </div>

      <div class="case-list-container">
        <!-- 案件列表 -->
        <el-row :gutter="24" v-if="caseList.length > 0">
          <el-col :span="6" v-for="item in caseList" :key="item.id">
            <el-card class="case-card" shadow="hover" @click="handleView(item)">
              <template #header>
                <div class="case-header">
                  <div class="case-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="case-title">{{ item.caseName }}</div>
                  <div class="case-status" :class="getStatusClass(item.importStatus)">
                    {{ getStatusText(item.importStatus) }}
                  </div>
                </div>
              </template>

              <div class="case-content">
                <div class="case-meta">
                  <div class="meta-item">
                    <span class="meta-label">操作人：</span>
                    <span class="meta-value">{{ item.importerName}}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">操作时间：</span>
                    <span class="meta-value">{{ item.importTime }}</span>
                  </div>
                </div>

                <div class="case-actions">
                  <el-button type="primary" link size="small" class="action-view" @click.stop="handleView(item)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button type="danger" link size="small" class="action-delete" @click.stop="handleDelete(item)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <el-empty
          v-else-if="!loading && caseList.length === 0"
          description="暂无案件数据"
          class="case-empty-state"
        >
          <template #image>
            <el-icon class="empty-icon"><Document /></el-icon>
          </template>
          <template #description>
            <p class="empty-description">暂无案件数据</p>
            <p class="empty-tip">您可以点击左上方"导入"按钮添加案件文件</p>
          </template>
        </el-empty>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 36, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>

    <!-- 导入模态框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入文件"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-upload
        class="upload-area"
        drag
        multiple
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        :show-file-list="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持任意格式文件上传
          </div>
        </template>
      </el-upload>

      <!-- 上传进度列表 -->
      <div class="upload-progress-list" v-if="fileList.length > 0">
        <div v-for="file in fileList" :key="file.uid" class="upload-progress-item">
          <div class="file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSizeLocal(file.size) }}</span>
          </div>
          <el-progress 
            :percentage="file.percentage || 0"
            :status="file.status === 'success' ? 'success' : file.status === 'fail' ? 'exception' : ''"
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">
            开始上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新的导入向导 -->
    <CaseImportWizard
      v-model="importWizardVisible"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Document, Search, Upload, UploadFilled, View, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadFile, UploadFiles, UploadStatus } from 'element-plus'
import { useRouter } from 'vue-router'
import { CaseImportWizard } from '@/components/import'
import { caseApi, type CaseImportRecord, type CaseImportSearchDTO, type PageResult } from '@/api'
import { confirmDelete, formatFileSize } from '@/utils/api-helper'
import { encrypt } from '@/utils/crypto';
// 使用统一的类型定义
type CaseItem = CaseImportRecord

// 加载状态
const loading = ref(false)

// 搜索表单数据
const searchForm = reactive({
  casename: '',
  operator: '',
  status: '',
  operateTime: null
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 案件列表数据
const caseList = ref<CaseItem[]>([])

// 导入相关
const importDialogVisible = ref(false)
const fileList = ref<UploadFiles>([])
const uploading = ref(false)

// 新的导入向导
const importWizardVisible = ref(false)

const router = useRouter()

// 处理搜索
const handleSearch = async () => {
  const params: CaseImportSearchDTO & { page: number; pageSize: number } = {
    page: currentPage.value,
    pageSize: pageSize.value,
    caseName: searchForm.casename,
    operator: searchForm.operator, // 如果后端支持这个字段，可以取消注释
    startTime: searchForm.operateTime?.[0],
    endTime: searchForm.operateTime?.[1],
    status: searchForm.status
  }

  try {
    // 使用全局loading和成功提示
    const result = await caseApi.getImportList(params, {
      loading: true,
      showSuccess: true,
      successMessage: '查询成功'
    })

    caseList.value = result.records
    total.value = result.total
  } catch (error) {
    // 全局已处理错误提示
    console.error('查询失败:', error)
  }
}

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 显示新的导入向导
const showImportWizard = () => {
  importWizardVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  handleSearch() // 刷新列表
}

// 处理文件变化
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  file.percentage = 0
  file.status = 'ready' as UploadStatus
  fileList.value = files
}

// 使用统一的文件大小格式化函数
const formatFileSizeLocal = (size: number | undefined) => {
  return formatFileSize(size || 0)
}

// 处理上传
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  try {
    // 获取所有文件对象
    const files = fileList.value.map(file => file.raw as File)

    // 调用统一的上传API（全局处理loading和提示）
    await caseApi.uploadBatch(files, {
      loading: true,
      showSuccess: true,
      successMessage: '上传成功,后台分析中，请稍后刷新页面查看结果'
    })

    importDialogVisible.value = false
    // 上传成功后刷新列表
    handleSearch()
  } catch (error) {
    // 全局已处理错误提示，这里只处理UI状态
    fileList.value.forEach(file => {
      file.status = 'fail' as UploadStatus
    })
  }
}

// 处理查看
const handleView = async (item: CaseItem) => {
  try {
    // 先查询最新的案件状态
    const statusResult = await caseApi.getCaseStatus(item.id)

    // 检查文件状态
    if (statusResult.fileStatus === 0) {
      ElMessage.warning('案件文件正在后台处理中，请稍后再试')
      return
    }

    // 状态正常，跳转到详情页
    const encryptedBmsah = encrypt(item.id + '');
    router.push(`/case/detail/${encryptedBmsah}`)
  } catch (error) {
    // 如果查询状态失败，使用本地状态判断
    console.warn('查询案件状态失败，使用本地状态:', error)
    if (item.fileStatus === 0) {
      ElMessage.warning('案件文件正在后台处理中，请稍后再试')
      return
    }

    const encryptedBmsah = encrypt(item.id + '');
    router.push(`/case/detail/${encryptedBmsah}`)
  }
}

// 处理删除
const handleDelete = async (item: CaseItem) => {
  try {
    await confirmDelete()
    await caseApi.delete(item.id)
    // 删除成功后刷新列表
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('删除失败:', error)
    }
  }
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'status-success'  // 已完成
    default:
      return 'status-warning'  // 分析中
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '已完成'
    default:
      return '分析中'
  }
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

// 组件挂载时加载数据
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.case-container {
  padding: 20px;
  background: transparent;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .search-container {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;

    .search-form {
      flex: 1;
      
      :deep(.el-row) {
        align-items: center;
      }
      
      .search-actions {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 100%;
        padding-bottom: 0;
      }
    }
    
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
    
    :deep(.el-date-editor) {
      width: 100%;
    }

    :deep(.el-form-item__label) {
      color: #374151;
      font-weight: 500;
      font-size: 14px;
    }

    :deep(.el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &.is-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    :deep(.el-select .el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &.is-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    :deep(.el-date-editor .el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &.is-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    :deep(.el-button--primary) {
      background: #3b82f6;
      border-color: #3b82f6;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        border-color: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }



  .result-container {
    background: #ffffff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .result-header {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 20px;

      .el-button {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        &.el-button--success {
          background: #10b981;
          border-color: #10b981;

          &:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    // 案件列表容器
    .case-list-container {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 8px;
      padding-top: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      // 空状态样式
      .case-empty-state {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 400px;

        :deep(.el-empty__image) {
          margin-bottom: 24px;

          .empty-icon {
            font-size: 80px;
            color: #d1d5db;
          }
        }

        :deep(.el-empty__description) {
          margin-bottom: 24px;

          .empty-description {
            font-size: 18px;
            color: #6b7280;
            font-weight: 500;
            margin: 0 0 8px 0;
          }

          .empty-tip {
            font-size: 14px;
            color: #9ca3af;
            margin: 0;
          }
        }

        :deep(.el-button) {
          border-radius: 6px;
          font-weight: 500;
          padding: 12px 24px;
          font-size: 14px;
          transition: all 0.2s ease;

          &.el-button--primary {
            background: #3b82f6;
            border-color: #3b82f6;

            &:hover {
              background: #2563eb;
              border-color: #2563eb;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }

    :deep(.case-card) {
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 20px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
      }

      .el-card__header {
        padding: 18px;
        border-bottom: 1px solid #f3f4f6;
      }

      .el-card__body {
        padding: 18px;
      }

      .case-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .case-icon {
          width: 48px;
          height: 48px;
          background: #3b82f6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          flex-shrink: 0;
        }

        .case-title {
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.4;
          margin: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 16px;
        }

        .case-status {
          padding: 6px 12px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          flex-shrink: 0;

          &.status-success {
            background: #dcfce7;
            color: #166534;
          }

          &.status-warning {
            background: #fef3c7;
            color: #92400e;
          }

          &.status-error {
            background: #fee2e2;
            color: #991b1b;
          }
        }
      }

      .case-content {
        .case-meta {
          margin-bottom: 18px;

          .meta-item {
            display: flex;
            margin-bottom: 12px;
            font-size: 14px;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              color: #6b7280;
              min-width: 80px;
              font-weight: 500;
            }

            .meta-value {
              color: #374151;
              flex: 1;
            }
          }
        }

        .case-actions {
          display: flex;
          justify-content: space-between;
          gap: 12px;
          padding-top: 16px;
          border-top: 1px solid #f3f4f6;

          .el-button {
            flex: 1;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            &.action-view {
              background: rgba(59, 130, 246, 0.1);
              color: #3b82f6;
              border: 1px solid rgba(59, 130, 246, 0.2);

              &:hover {
                background: rgba(59, 130, 246, 0.15);
                color: #2563eb;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
              }

              .el-icon {
                font-size: 16px;
              }
            }

            &.action-delete {
              background: rgba(239, 68, 68, 0.1);
              color: #ef4444;
              border: 1px solid rgba(239, 68, 68, 0.2);

              &:hover {
                background: rgba(239, 68, 68, 0.15);
                color: #dc2626;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
              }

              .el-icon {
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    .pagination-container {
      margin-top: 24px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #f3f4f6;
      padding-top: 20px;
    }
  }

  // 分页样式
  :deep(.el-pagination) {
    .el-pager li {
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin: 0 4px;
      transition: all 0.2s ease;
      color: #6b7280;
      font-weight: 500;

      &:hover {
        background: #f9fafb;
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &.is-active {
        background: #3b82f6;
        border-color: #3b82f6;
        color: white;
      }
    }

    .btn-prev,
    .btn-next {
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      transition: all 0.2s ease;
      color: #6b7280;
      font-weight: 500;

      &:hover {
        background: #f9fafb;
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:disabled {
        background: #f9fafb;
        border-color: #e5e7eb;
        color: #d1d5db;
      }
    }

    .el-pagination__total,
    .el-pagination__jump {
      color: #6b7280;
      font-weight: 500;
    }

    .el-select .el-input__wrapper {
      border-radius: 6px;
      border: 1px solid #e5e7eb;

      &:hover {
        border-color: #3b82f6;
      }
    }
  }
}

.upload-area {
  width: 100%;
  
  :deep(.el-upload-dragger) {
    width: 100%;
  }
}

.upload-progress-list {
  margin-top: 20px;
  
  .upload-progress-item {
    margin-bottom: 15px;
    
    .file-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      
      .file-name {
        color: #606266;
        font-size: 14px;
      }
      
      .file-size {
        color: #909399;
        font-size: 12px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

</style>
