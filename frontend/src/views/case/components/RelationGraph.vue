<template>
  <div class="relation-graph">
    <div ref="graphRef" class="graph-container">
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-if="error" class="error-message">
        {{ error }}
        <el-button type="primary" link @click="fetchGraphData">重试</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as d3 from 'd3'
import { relationGraphApi } from '@/api'

const props = defineProps<{
  id: string | number
}>()

const emit = defineEmits<{
  (e: 'detailHighlight', highlight: Array<{
    fileId: number
    fileName: string
    pageNumber?: number // 页码变为可选，支持全文搜索
    highlight: string
    locations?: Array<{
      pageNumber?: number
      x: number
      y: number
      width: number
      height: number
      text: string
      similarity: number
      pageWidth: number
      pageHeight: number
    }>
  }>): void
}>()

// 数据状态
const graphData = ref<{nodes: Node[], links: Link[]}>({ nodes: [], links: [] })
const loading = ref(false)
const error = ref<string | null>(null)
const dialogVisible = ref(false)



// 验证数据格式
const validateGraphData = (data: any): data is {nodes: Node[], links: Link[]} => {
  return (
    data &&
    Array.isArray(data.nodes) &&
    Array.isArray(data.links) &&
    data.nodes.every((node: any) => 
      node.eid && 
      typeof node.eid === 'string' &&
      node.name &&
      typeof node.name === 'string'
    ) &&
    data.links.every((link: any) =>
      link.rid &&
      typeof link.rid === 'string' &&
      link.source &&
      link.target &&
      link.type
    )
  )
}

// 修复类型问题
interface Node extends d3.SimulationNodeDatum {
  eid: string
  name: string
  type: string
  category: string
  icon?: string
  weight?: number
  attributes: Record<string, any>
  evidence?: Array<{
    fileId: number
    fileName: string
    pageNumber: number
    highlight: string
    locations: Array<{
      pageNumber: number
      x: number
      y: number
      width: number
      height: number
      text: string
      similarity: number
      pageWidth: number
      pageHeight: number
    }>
  }>
  isFixed?: boolean
}

interface Link extends d3.SimulationLinkDatum<Node> {
  rid: string
  source: string | Node
  target: string | Node
  type: string
  value: number
  direction?: 'single' | 'double'
  evidence?: Array<{
    fileId: number
    fileName: string
    pageNumber: number
    highlight: string
    locations: Array<{
      pageNumber: number
      x: number
      y: number
      width: number
      height: number
      text: string
      similarity: number
      pageWidth: number
      pageHeight: number
    }>
  }>
}

const graphRef = ref<HTMLElement>()

const simulation = ref<any>(null)

// 获取节点大小
const getNodeRadius = (node: Node) => {
  const baseRadius = 45
  const weight = node.weight || 1
  return baseRadius * Math.sqrt(weight)
}

// 获取节点图标
const getNodeIcon = (category: string) => {
  switch (category) {
    case '自然人':
      return '🧑'
    case '法人':
      return '🏢'
    case '房产':
      return '🏠'
    case '车辆':
      return '🚗'
    default:
      return '📄'
  }
}

// 获取关系图数据
const fetchGraphData = async () => {
  error.value = null

  try {
    console.log('获取关系图数据，ID:', props.id)

    // 使用全局loading
    const result = await relationGraphApi.get(props.id, { loading: true })
    console.log('API Response:', result)

    if (!result) {
      throw new Error('接口返回数据为空')
    }

    if (!validateGraphData(result)) {
      // 数据校验失败时清空图形
      graphData.value = null
      if (simulation.value) {
        simulation.value.stop()
        simulation.value = null
      }
      throw new Error('接口返回数据格式不正确')
    }

    graphData.value = result
    await nextTick()
    initGraph() // 获取数据后初始化图表
  } catch (e) {
    // 全局已处理错误提示
    error.value = e instanceof Error ? e.message : '获取数据失败'
    console.error('获取数据错误:', e)
  }
}

// 修改高亮处理函数
const handleHighlight = (evidence: any) => {
  if (!evidence) {
    ElMessage.warning('没有可用的来源信息')
    return
  }
  // 确保 evidence 是数组
  const Evidence = Array.isArray(evidence) ? evidence : [evidence]
  //根据evidence中的fileId升序
  Evidence.sort((a, b) => Number(a.fileId) - Number(b.fileId))
  emit('detailHighlight', Evidence)
}

// 清除所有节点和连线的高亮状态
const clearAllHighlights = () => {
  // 清除所有节点的高亮
  d3.selectAll('.node-circle').classed('highlighted', false)
  // 清除所有连线的高亮
  d3.selectAll('.link-line').classed('highlighted', false)
  // 清除所有关系文本的高亮
  d3.selectAll('.relation-label').classed('highlighted', false)
  // 清除所有节点名称的高亮
  d3.selectAll('.node-name').classed('highlighted', false)
  // 清除所有身份标签的高亮
  d3.selectAll('.identity-label').classed('highlighted', false)
}

// 高亮节点及其相关元素
const highlightNode = (nodeElement: Element) => {
  const nodeGroup = d3.select(nodeElement.parentElement)
  nodeGroup.select('.node-circle').classed('highlighted', true)
  nodeGroup.select('.node-name').classed('highlighted', true)
}

// 高亮身份标签
const highlightIdentity = (identityElement: Element) => {
  d3.select(identityElement).classed('highlighted', true)
}

// 初始化D3图表
const initGraph = () => {
  nextTick(() => {
    // 严格的空值检查
    if (!graphRef.value) {
      console.warn('图表容器未初始化')
      return
    }

    if (!graphData.value || !Array.isArray(graphData.value.nodes) || !graphData.value.nodes.length) {
      d3.select(graphRef.value).selectAll("*").remove()
      console.warn('没有可用的图表数据')
      return
    }

    // 清除现有的SVG
    d3.select(graphRef.value).selectAll("*").remove()

    // 创建SVG容器
    const width = graphRef.value.clientWidth
    const height = graphRef.value.clientHeight

    // 确保容器尺寸正确
    if (!width || !height) {
      console.warn('图表容器尺寸无效')
      return
    }

    const svg = d3.select(graphRef.value)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .on("click", (event) => {
        // 如果点击的是SVG容器本身（而不是节点或连线），则清除高亮
        if (event.target === event.currentTarget) {
          emit('detailHighlight', [])
        }
      })

    // 创建箭头标记
    svg.append("defs").append("marker")
      .attr("id", "arrow")
      .attr("viewBox", "0 -5 10 10")
      .attr("refX", 20)
      .attr("refY", 0)
      .attr("markerWidth", 6)
      .attr("markerHeight", 6)
      .attr("orient", "auto")
      .append("path")
      .attr("d", "M0,-5L10,0L0,5")
      .attr("fill", "#999")

    // 创建容器组
    const g = svg.append("g")

    // 创建缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.2, 4])
      .on("zoom", (event) => {
        g.attr("transform", event.transform)
      })

    svg.call(zoom as any)

    // 创建力导向图布局
    simulation.value = d3.forceSimulation(graphData.value.nodes)
      .force("link", d3.forceLink(graphData.value.links)
        .id((d: any) => d.eid)
        .distance(250)
        .strength(0.5))
      .force("charge", d3.forceManyBody()
        .strength(-1200)
        .distanceMax(500))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide()
        .radius(d => getNodeRadius(d as Node) + 20)
        .strength(0.8))
      .force("x", d3.forceX(width / 2).strength(0.1))
      .force("y", d3.forceY(height / 2).strength(0.1))

    // 创建节点组
    const nodes = g.append("g")
      .selectAll("g")
      .data(graphData.value.nodes)
      .enter()
      .append("g")
      .call(d3.drag<SVGGElement, Node>()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended))
      .on("dblclick", (event: MouseEvent, d: Node) => {
        event.stopPropagation()
        // 切换固定状态
        d.isFixed = !d.isFixed
        if (!d.isFixed) {
          d.fx = null
          d.fy = null
        } else {
          d.fx = d.x
          d.fy = d.y
        }
        // 更新节点样式
        const targetElement = (event.target as Element).closest('g')
        if (targetElement) {
          d3.select(targetElement)
            .select("circle")
            .classed("fixed", d.isFixed)
        }
      })

    // 添加节点圆形
    nodes.append("circle")
      .attr("r", (d: any) => getNodeRadius(d))
      .attr("fill", (d: any) => getNodeColor(d.type))
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .classed("fixed", (d: any) => d.isFixed)
      .classed("node-circle", true)
      .on("click", (event: MouseEvent, d: any) => {
        event.stopPropagation()
        clearAllHighlights()
        highlightNode(event.currentTarget as Element)
        handleHighlight(d.evidence)
      })

    // 添加节点图标
    nodes.append("text")
      .attr("class", "node-icon")
      .attr("text-anchor", "middle")
      .attr("dy", 0)
      .attr("font-size", "20px")
      .text((d: any) => getNodeIcon(d.type))

    // 添加节点名称（在圆圈内）并添加点击事件
    nodes.append("text")
      .text((d: any) => d.name)
      .attr("text-anchor", "middle")
      .attr("dy", 30)
      .attr("fill", "#fff")
      .attr("font-size", (d: any) => {
        return d.name.length > 4 ? "16px" : "18px"
      })
      .attr("font-weight", "bold")
      .attr("class", "node-name clickable")
      .on("click", (event: MouseEvent, d: any) => {
        event.stopPropagation()
        clearAllHighlights()
        highlightNode(event.currentTarget as Element)
        handleHighlight(d.evidence)
      })

    // 添加身份标签（在圆圈下方）并添加点击事件
    nodes.append("text")
      .text((d: any) => d.ssdw || d.type)
      .attr("text-anchor", "middle")
      .attr("dy", 55)
      .attr("fill", "#666")
      .attr("font-size", "16px")
      .attr("class", "identity-label clickable")
      .on("click", (event: MouseEvent, d: any) => {
        event.stopPropagation()
        clearAllHighlights()
        highlightIdentity(event.currentTarget as Element)
        handleHighlight(d.evidence)
      })

    // 创建连线组
    const linkGroup = g.append("g")
      .selectAll("g")
      .data(graphData.value.links)
      .enter()
      .append("g")

    // 添加连线
    linkGroup.append("line")
      .attr("stroke", "#999")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", (d: any) => d.value * 2)
      .attr("marker-end", "url(#arrow)")
      .attr("class", "link-line")
      .on("click", (event: MouseEvent, d: any) => {
        event.stopPropagation()
        clearAllHighlights()
        d3.select(event.currentTarget as Element).classed('highlighted', true)
        handleHighlight(d.evidence)
      })

    // 添加关系文本（在连线上）并添加点击事件
    const linkLabels = linkGroup.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", -5)
      .attr("fill", "#303133")
      .attr("font-size", "14px")
      .attr("class", "relation-label clickable")
      .text((d: any) => d.type)
      .on("click", (event: MouseEvent, d: any) => {
        event.stopPropagation()
        clearAllHighlights()
        d3.select(event.currentTarget as Element).classed('highlighted', true)
        handleHighlight(d.evidence)
      })

    // 为关系文本添加背景
    linkLabels.each(function() {
      const bbox = (this as SVGTextElement).getBBox()
      const padding = 2
      const parent = (this as SVGElement).parentElement
      if (parent) {
        d3.select(parent)
          .insert("rect", "text")
          .attr("x", bbox.x - padding)
          .attr("y", bbox.y - padding)
          .attr("width", bbox.width + 2 * padding)
          .attr("height", bbox.height + 2 * padding)
          .attr("fill", "white")
          .attr("fill-opacity", 0.8)
          .attr("rx", 2)
          .attr("ry", 2)
      }
    })

    // 更新力导向图的tick函数
    simulation.value
      .on("tick", () => {
        // 更新连线位置
        linkGroup.selectAll("line")
          .attr("x1", (d: any) => d.source.x)
          .attr("y1", (d: any) => d.source.y)
          .attr("x2", (d: any) => d.target.x)
          .attr("y2", (d: any) => d.target.y)

        // 更新节点组位置
        nodes.attr("transform", (d: any) => `translate(${d.x},${d.y})`)

        // 更新关系文本和背景位置
        linkGroup.each(function(d: any) {
          const g = d3.select(this)
          const x = (d.source.x + d.target.x) / 2
          const y = (d.source.y + d.target.y) / 2
          
          const text = g.select("text")
          const rect = g.select("rect")
          const bbox = (text.node() as SVGTextElement)?.getBBox()
          
          if (bbox) {
            text.attr("x", x).attr("y", y)
            rect
              .attr("x", x - bbox.width / 2 - 2)
              .attr("y", y - bbox.height - 2)
          }
        })
      })
  })
}

// 拖拽相关函数
const dragstarted = (event: d3.D3DragEvent<any, any, any>) => {
  if (!event.active) simulation.value.alphaTarget(0.3).restart()
  event.subject.fx = event.subject.x
  event.subject.fy = event.subject.y
}

const dragged = (event: d3.D3DragEvent<any, any, any>) => {
  event.subject.fx = event.x
  event.subject.fy = event.y
}

const dragended = (event: d3.D3DragEvent<any, any, any>) => {
  if (!event.active) simulation.value.alphaTarget(0)
  // 默认固定节点位置
  event.subject.isFixed = true
  const targetElement = event.sourceEvent.target.closest('g')
  if (targetElement) {
    d3.select(targetElement)
      .select("circle")
      .classed("fixed", true)
  }
}

// 获取节点颜色
const getNodeColor = (category: string) => {
  switch (category) {
    case '自然人':
      return '#409EFF'
    case '法人':
      return '#67C23A'
    case '房产':
      return '#E6A23C'
    case '车辆':
      return '#F56C6C'
    default:
      return '#909399'
  }
}

// 监听容器大小变化
const handleResize = () => {
  if (graphRef.value && graphData.value?.nodes?.length) {
    initGraph()
  }
}

// 生命周期钩子
onMounted(async () => {
  await fetchGraphData() // 组件挂载时获取数据
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (simulation.value) {
    simulation.value.stop()
  }
})


// 添加监听器来追踪 dialogVisible 的变化
watch(dialogVisible, (newVal, oldVal) => {
  console.log('dialogVisible 变化:', oldVal, '->', newVal)
})

// 暴露方法给父组件
defineExpose({
  fetchGraphData
})
</script>

<style scoped lang="scss">
.relation-graph {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fff;
  display: flex;
  flex-direction: column;
  
  .graph-container {
    flex: 1;
    position: relative;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1;

      .loading-icon {
        animation: rotate 1s linear infinite;
      }

      span {
        margin-top: 8px;
        color: #909399;
      }
    }

    .error-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #f56c6c;
      
      .el-button {
        margin-left: 8px;
      }
    }
    
    :deep(svg) {
      width: 100%;
      height: 100%;

      .node-circle {
        transition: all 0.3s ease;
        
        &.highlighted {
          stroke: #e6a23c;
          stroke-width: 4px;
          filter: drop-shadow(0 0 4px rgba(230, 162, 60, 0.5));
        }
      }

      .node-name {
        transition: all 0.3s ease;
        
        &.highlighted {
          fill: #e6a23c;
          filter: drop-shadow(0 0 2px rgba(230, 162, 60, 0.5));
        }
      }

      .identity-label {
        transition: all 0.3s ease;
        
        &.highlighted {
          fill: #e6a23c;
          font-weight: bold;
          filter: drop-shadow(0 0 2px rgba(230, 162, 60, 0.5));
        }
      }

      .link-line {
        transition: all 0.3s ease;
        
        &.highlighted {
          stroke: #e6a23c;
          stroke-width: 4px !important;
          stroke-opacity: 1 !important;
        }
      }

      .relation-label {
        transition: all 0.3s ease;
        
        &.highlighted {
          fill: #e6a23c;
          font-weight: bold;
        }
      }

      .clickable {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
          text-decoration: underline;
        }
      }

      .node-icon {
        pointer-events: none;
        user-select: none;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.regenerate-dialog) {
  width: 600px !important;
  
  .el-message-box__input {
    textarea {
      height: 150px !important;
      font-size: 14px;
      padding: 12px;
      resize: none;
    }
  }
  
  .el-message-box__header {
    padding: 20px;
    
    .el-message-box__title {
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .el-message-box__content {
    padding: 20px;
    padding-top: 0;
  }
  
  .el-message-box__btns {
    padding: 20px;
    padding-top: 0;
    
    button {
      min-width: 100px;
      padding: 12px 20px;
      font-size: 14px;
    }
  }
}
</style> 