<template>
  <div class="evidence-item">
    <div class="evidence-content">
      <el-button 
        type="info" 
        size="small" 
        plain 
        @click="$emit('evidence-click')" 
        class="evidence-tag"
      >
        {{ evidence.fileName }} {{ evidence.location }} 第{{ evidence.pageNumber }}页
      </el-button>
      <el-button
        v-if="evidence.highlight"
        type="primary"
        size="small"
        text
        class="toggle-highlight"
        @click="$emit('toggle-highlight', evidenceKey)"
      >
        {{ expandedHighlights[evidenceKey] ? '收起' : '查看原文' }}
        <el-icon class="el-icon--right">
          <component :is="expandedHighlights[evidenceKey] ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
      </el-button>
    </div>
    <div v-if="evidence.highlight && expandedHighlights[evidenceKey]" class="highlight-content">
      <div class="highlight-text">{{ getHighlightText() }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

interface Evidence {
  fileId: number;
  fileName: string;
  location?: string;
  highlight: string | string[];
  pageNumber: number;
  locations?: any[];
}

const props = defineProps<{
  evidence: Evidence;
  evidenceKey: string;
  expandedHighlights: Record<string, boolean>;
}>()

const emit = defineEmits<{
  'evidence-click': [];
  'toggle-highlight': [key: string];
}>()

const getHighlightText = () => {
  const highlight = props.evidence.highlight
  return Array.isArray(highlight) ? highlight[0] : highlight
}
</script>

<style scoped lang="scss">
.evidence-item {
  .evidence-content {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 2px;
    
    .evidence-tag {
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: normal;
      font-size: 13px;
      padding: 2px 8px;
      border-radius: 4px;
      background: #f5f7fa;
      
      &:hover {
        color: var(--el-color-primary);
        background: #ecf5ff;
      }
    }
    
    .toggle-highlight {
      padding: 2px 6px;
      font-size: 12px;
      
      .el-icon {
        transition: transform 0.3s ease;
      }
    }
  }
  
  .highlight-content {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px;
    margin-top: 2px;
    margin-bottom: 4px;
    
    .highlight-text {
      font-size: 13px;
      line-height: 1.6;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-word;
      background: #f0f2f5;
      padding: 8px;
      border-radius: 4px;
    }
  }
}
</style> 