<template>
  <div class="recycle-bin">
    <!-- 回收站头部 -->
    <div v-if="props.showHeader !== false" class="recycle-bin-header">
      <div class="header-left">
        <h3>回收站</h3>
        <span class="file-count">{{ recycleBinFiles.length }} 个文件</span>
      </div>
      <div class="header-actions">
        <el-button
          v-if="recycleBinFiles.length > 0"
          type="danger"
          size="small"
          @click="handleClearAll"
        >
          清空回收站
        </el-button>
        <el-button size="small" @click="handleClose">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 回收站内容 -->
    <div class="recycle-bin-content" v-loading="loading">
      <!-- 模态框模式下的操作栏 -->
      <div v-if="props.showHeader === false && recycleBinFiles.length > 0" class="modal-actions">
        <div class="file-count-info">{{ recycleBinFiles.length }} 个文件</div>
        <el-button
          type="danger"
          size="small"
          @click="handleClearAll"
        >
          清空回收站
        </el-button>
      </div>

      <!-- 空状态 -->
      <div v-if="recycleBinFiles.length === 0" class="empty-state">
        <el-empty
          description="回收站为空"
          :image-size="80"
        />
      </div>

      <!-- 文件列表 -->
      <div v-else class="recycle-file-list">
        <div
          v-for="file in recycleBinFiles"
          :key="file.id"
          class="recycle-file-item"
        >
          <div class="file-icon">
            <img :src="getFileIcon(file.fileName)" alt="file" />
          </div>
          <div class="file-details">
            <div class="file-name" :title="file.fileName">{{ file.fileName }}</div>
            <div class="file-meta">
              <span class="delete-time">删除时间：{{ formatDateTime(file.deletedTime) }}</span>
              <span v-if="file.deletedBy" class="deleted-by">删除人：{{ file.deletedBy }}</span>
            </div>
          </div>
          <div class="file-actions">
            <el-button
              type="primary"
              size="small"
              @click="handleRestore(file)"
            >
              恢复
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handlePermanentDelete(file)"
            >
              永久删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { caseApi } from '@/api'

// 导入文件图标
import wordIcon from '@/assets/word.svg'
import pdfIcon from '@/assets/pdf.svg'
import txtIcon from '@/assets/txt.svg'
import wpsIcon from '@/assets/wps.svg'

interface RecycleBinFile {
  id: number
  fileName: string
  fileSize: number
  fileType: string
  deletedTime: string
  deletedBy?: string
}

const props = defineProps<{
  caseImportId: string | number
  showHeader?: boolean
  visible?: boolean
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'fileRestored'): void
}>()

const loading = ref(false)
const recycleBinFiles = ref<RecycleBinFile[]>([])

// 获取回收站文件列表
const getRecycleBinFiles = async () => {
  loading.value = true
  try {
    const result = await caseApi.getRecycleBinFiles(props.caseImportId)
    recycleBinFiles.value = result || []
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取回收站文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 恢复文件
const handleRestore = async (file: RecycleBinFile) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复文件"${file.fileName}"吗？`,
      '恢复文件',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    const result = await caseApi.restoreFile(file.id)
    if (result) {
      // 从回收站列表中移除
      const index = recycleBinFiles.value.findIndex(f => f.id === file.id)
      if (index > -1) {
        recycleBinFiles.value.splice(index, 1)
      }
      emit('fileRestored')
    }
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('恢复文件失败:', error)
    }
  }
}

// 永久删除文件
const handlePermanentDelete = async (file: RecycleBinFile) => {
  try {
    await ElMessageBox.confirm(
      `确定要永久删除文件"${file.fileName}"吗？此操作无法撤销！`,
      '永久删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    // 使用全局成功提示
    const result = await caseApi.permanentDeleteFile(file.id, {
      showSuccess: true,
      successMessage: '文件已永久删除'
    })
    if (result) {
      // 从回收站列表中移除
      const index = recycleBinFiles.value.findIndex(f => f.id === file.id)
      if (index > -1) {
        recycleBinFiles.value.splice(index, 1)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('永久删除文件失败:', error)
    }
  }
}

// 清空回收站
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空回收站吗？所有文件将被永久删除，此操作无法撤销！',
      '清空回收站',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    // 调用批量清空接口（使用全局成功提示）
    await caseApi.clearRecycleBin(props.caseImportId, {
      showSuccess: true,
      successMessage: '回收站已清空'
    })

    // 清空本地列表
    recycleBinFiles.value = []
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('清空回收站失败:', error)
    }
  }
}

// 关闭回收站
const handleClose = () => {
  emit('close')
}

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'docx':
    case 'doc':
      return wordIcon
    case 'wps':
      return wpsIcon
    case 'pdf':
      return pdfIcon
    case 'txt':
      return txtIcon
    default:
      return txtIcon
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return ''
  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTimeStr
  }
}

// 监听模态框显示状态
watch(() => props.visible, (newVisible, oldVisible) => {
  // 当模态框从隐藏变为显示时，重新获取数据
  if (newVisible && !oldVisible) {
    console.log('回收站模态框打开，重新获取数据')
    getRecycleBinFiles()
  }
})

onMounted(() => {
  getRecycleBinFiles()
})
</script>

<style scoped>
.recycle-bin {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
}

.recycle-bin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5ea;
  background: #f8f9fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.file-count {
  font-size: 14px;
  color: #86868B;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.recycle-bin-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.modal-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #e5e5ea;
}

.file-count-info {
  font-size: 14px;
  color: #86868B;
  font-weight: 500;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.recycle-file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recycle-file-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e5e5ea;
  transition: all 0.2s ease;
}

.recycle-file-item:hover {
  background: #f0f0f0;
  border-color: #d1d1d6;
}

.file-icon {
  width: 36px;
  height: 36px;
  margin-right: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 8px;
  flex-shrink: 0;
}

.file-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #86868B;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}
</style>
