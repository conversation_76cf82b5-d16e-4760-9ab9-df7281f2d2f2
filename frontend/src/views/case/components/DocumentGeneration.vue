<template>
  <div class="doc-gen-page">
    <!-- 文书内容展示区域 -->
    <div class="doc-content-area" v-if="showDocumentContent && currentDocument">
      <div class="doc-content-header">
        <div class="doc-actions">
          <el-button type="success" icon="Download" size="small" @click="downloadDocument">下载</el-button>
          <el-button size="small" @click="backToMain">返回</el-button>
        </div>
      </div>
      
      <div class="doc-content-body">
        <!-- OnlyOffice 编辑器 -->
        <div v-if="showOnlyOffice" class="onlyoffice-editor-wrapper">
          <div id="onlyoffice-editor" style="width:100%;height:100%;background:#fff;"></div>

          <!-- 书签导航 -->
          <div class="bookmark-navigation" :class="{ 'expanded': showBookmarkNav }">
            <div class="bookmark-toggle" @click="showBookmarkNav = !showBookmarkNav">
              <el-icon>
                <Menu />
              </el-icon>
              <span v-if="showBookmarkNav">书签导航</span>
            </div>

            <div v-if="showBookmarkNav" class="bookmark-list">
              <div class="bookmark-header">
                <span>文书结构</span>
              </div>
              <div
                v-for="bookmark in bookmarkList"
                :key="bookmark.name"
                class="bookmark-item"
                @click="gotoBookmark(bookmark.displayName)"
              >
                <el-icon class="bookmark-icon">
                  <Document />
                </el-icon>
                <span class="bookmark-text">{{ bookmark.displayName }}</span>
              </div>
              <div v-if="bookmarkList.length === 0" class="no-bookmarks">
                <span>暂无书签</span>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 文书生成编辑器 -->
    <DocumentGenerationEditor 
      v-else-if="showEditor"
      :case-import-id="caseImportId"
      :case-type="editorParams.caseType"
      :document-type="editorParams.documentType"
      :has-counterclaim="hasCounterclaim"
      :case-info="props.caseInfo"
      @close="closeEditor"
    />

    <!-- 主页面，展示文书列表 -->
    <template v-else>
      <div class="records-main-area">
        <!-- 筛选区域 -->
        <div class="records-filter-header">
          <el-form :model="filterParams" class="records-filter-form">
            <!-- 第一行：文书类型和按钮 -->
            <div class="filter-row">
              <el-form-item label="文书类型:">
                <el-select v-model="filterParams.docType" placeholder="请选择文书类型" style="width: auto; min-width: 200px">
                  <el-option v-for="item in docTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchDocuments" :loading="searchLoading" size="small">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
                <el-button @click="resetFilter" size="small">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </div>
            
            <!-- 第二行：时间选择器 -->
            <div class="filter-row time-row">
              <el-form-item label="开始时间:" class="time-form-item">
                <el-date-picker
                  v-model="filterParams.startTime"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 180px"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item label="结束时间:" class="time-form-item">
                <el-date-picker
                  v-model="filterParams.endTime"
                  type="datetime"
                  placeholder="选择结束时间"
                  style="width: 180px"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>

        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="filteredDocs.length > 0" class="records-table-container">
          <!-- 批量操作区域 -->
          <div class="batch-actions">
            <el-button 
              type="danger" 
              size="small" 
              circle
              @click="batchDeleteDocuments"
              :disabled="selectedDocs.length === 0"
              title="批量删除"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
            <span v-if="selectedDocs.length > 0" class="selected-count">
              已选择 {{ selectedDocs.length }} 项
            </span>
          </div>

          <el-table
            :data="filteredDocs"
            style="width: 100%"
            stripe
            border
            height="calc(100vh - 520px)"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            table-layout="auto"
            :row-class-name="getRowClassName"
          >

            <el-table-column type="selection" width="55" align="center" header-align="center"/>
            <el-table-column label="文书类型" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                {{ getDocumentTypeLabel(row.documentType) }}
              </template>
            </el-table-column>

            <el-table-column prop="createTime" label="生成时间" min-width="180" show-overflow-tooltip />

            <el-table-column label="操作" width="120" fixed="right" align="center">
              <template #default="scope">
                <div class="operation-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    circle
                    @click="viewDocument(scope.row)"
                    title="查看"
                  >
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="deleteDocument(scope.row)"
                    title="删除"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
        <div v-else class="empty-state-container">
          <el-empty description="暂无数据" />
        </div>
      </div>
      
      <!-- 底部操作 -->
      <div class="doc-gen-footer">
        <el-button type="primary" @click="showGenerateDialog">写文书</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </template>
    
    <!-- 文书生成对话框 -->
    <el-dialog
      v-model="showDialog"
      title="选择需要生成的文书"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="案件类型:" required>
          <el-radio-group v-model="dialogCaseType">
            <el-radio v-for="item in caseTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文书类型:" required>
          <el-select v-model="dialogDocType" placeholder="请选择文书类型" style="width: 300px">
            <el-option v-for="item in dialogDocTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Delete, Edit, Download, Search, Refresh, Menu, Document } from '@element-plus/icons-vue'
import { documentGenerationApi, type DocumentGenerationInfo, caseApi } from '@/api'
import type { CaseTypeInfo } from '@/api/case'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import DocumentGenerationEditor from './DocumentGenerationEditor.vue'

// 定义 props
interface Props {
  caseImportId: string
  hasCounterclaim?: boolean
  caseInfo?: any // 案件信息，包含案号等
}

const props = withDefaults(defineProps<Props>(), {
  hasCounterclaim: false
})

// 获取路由参数
const route = useRoute()
const router = useRouter()
const caseImportId = computed(() => Number(props.caseImportId))

// 案件类型数据管理
const caseTypeInfo = ref<CaseTypeInfo | null>(null)
const caseTypeLoading = ref(false)

// 下拉选项配置
const caseTypeOptions = [
  { label: '民事一审', value: 'civil1' },
  { label: '民事二审', value: 'civil2' }
]
const docTypeOptions = [
  { label: '民事判决书（一审普通程序用）', value: 'civil_judgement_1' },
  { label: '民事判决书(驳回上诉，维持原判)', value: 'civil_judgement_2_reject' },
  { label: '民事判决书(二审改判)', value: 'civil_judgement_2_overrule' },
  { label: '民事判决书(部分改判)', value: 'civil_judgement_2_partial_overrule' }
]
const filterParams = reactive({
  caseType: '',
  docType: '',
  startTime: '',
  endTime: ''
})

// 文书数据管理
const docs = ref<DocumentGenerationInfo[]>([])
const filteredDocs = ref<DocumentGenerationInfo[]>([])
const loading = ref(false)
const searchLoading = ref(false)
const currentDocument = ref<DocumentGenerationInfo | null>(null)

// 分页相关
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 批量操作相关
const selectedDocs = ref<DocumentGenerationInfo[]>([])

// 界面状态管理
const showDocumentContent = ref(false)
const showDialog = ref(false)
const showOnlyOffice = ref(false)
const showEditor = ref(false)

// 编辑器参数
const editorParams = ref({
  caseType: '',
  documentType: ''
})

// 表单数据
const dialogCaseType = ref('')
const dialogDocType = ref('')
const editContent = ref('')

// 书签相关状态
const bookmarkList = ref<Array<{name: string, displayName: string}>>([])
const showBookmarkNav = ref(false)

const dialogDocTypeOptions = computed(() => {
  // 文书类型选项完全基于用户当前选择的案件类型
  if (dialogCaseType.value === 'civil1') {
    return [{ label: '民事判决书（一审普通程序用）', value: 'civil_judgement_1' }]
  } else if (dialogCaseType.value === 'civil2') {
    return [
      { label: '民事判决书(驳回上诉，维持原判)', value: 'civil_judgement_2_reject' },
      { label: '民事判决书(二审改判)', value: 'civil_judgement_2_overrule' },
      { label: '民事判决书(部分改判)', value: 'civil_judgement_2_partial_overrule' }
    ]
  }
  return []
})


/**
 * 根据英文文书类型获取中文标签
 */
function getDocumentTypeLabel(documentType: string): string {
  const docTypeMap: Record<string, string> = {
    'civil_judgement_1': '民事判决书（一审普通程序用）',
    'civil_judgement_2_reject': '民事判决书(驳回上诉，维持原判)',
    'civil_judgement_2_overrule': '民事判决书(二审改判)',
    'civil_judgement_2_partial_overrule': '民事判决书(部分改判)'
  }
  return docTypeMap[documentType] || documentType
}

/**
 * 跳转到指定书签
 */
async function gotoBookmark(bookmarkName: string) {
  try {
    // @ts-ignore
    if (window.docEditor?.serviceCommand) {
      // 搜索书签标记
      const bookmarkMarker = `[BOOKMARK:${bookmarkName}]`

      // @ts-ignore
      window.docEditor.serviceCommand('GotoBookmark', {
        name: bookmarkMarker
      })

      console.log(`跳转到书签: ${bookmarkName}`)
    } else {
      ElMessage.warning('编辑器未准备就绪，请稍后再试')
    }
  } catch (error) {
    console.error(`跳转书签失败: ${bookmarkName}`, error)
  }
}

// OnlyOffice相关函数
/**
 * 判断是否为Word文件
 */
function isWordFile(url?: string): boolean {
  if (!url || typeof url !== 'string') return false
  return url.endsWith('.docx') || url.endsWith('.doc') || url.includes('.docx?') || url.includes('.doc?')
}

/**
 * 格式化时间为无空格字符串
 */
function formatDateTime(date: string | number | Date): string {
  const d = new Date(date)
  const pad = (n: number) => n < 10 ? '0' + n : n
  return (
    d.getFullYear().toString() +
    pad(d.getMonth() + 1) +
    pad(d.getDate()) +
    pad(d.getHours()) +
    pad(d.getMinutes()) +
    pad(d.getSeconds())
  )
}

function loadOnlyOfficeAPI(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    // @ts-ignore
    if (window.DocsAPI) {
      console.log('OnlyOffice API已加载')
      resolve()
      return
    }

    // 获取OnlyOffice URL
    const scriptSrc = `${import.meta.env.VITE_ONLYOFFICE_URL}/web-apps/apps/api/documents/api.js`

    console.log('开始加载OnlyOffice API:', scriptSrc)

    // 创建script标签
    const script = document.createElement('script')
    script.src = scriptSrc
    script.async = true

    script.onload = () => {
      console.log('OnlyOffice API加载成功')
      resolve()
    }
    script.onerror = (error) => {
      console.error('OnlyOffice API加载失败:', error)
      reject(new Error('无法加载OnlyOffice API'))
    }

    // 添加到head
    document.head.appendChild(script)



  })
}

/**
 * 初始化OnlyOffice编辑器
 */
async function initOnlyOfficeEditor(fileUrl: string) {
  console.log('初始化OnlyOffice编辑器')
  await loadOnlyOfficeAPI()

  // documentKey使用时间戳生成
  const docKey = String(caseImportId.value) + '_' + formatDateTime(Date.now())

  // 解析书签数据
  if (currentDocument.value?.bookmark) {
    try {
      // 如果是字符串格式，尝试直接显示
      if (typeof currentDocument.value.bookmark === 'string') {
        const bookmarkNames = currentDocument.value.bookmark.split('；').filter(Boolean)

        bookmarkList.value = bookmarkNames.map(name => {
        const match = name.match(/<bookmark>(.*?)<\/bookmark>/)
          return {
            name: name.trim(),
            displayName: match ? match[1] : name.trim()
          }
        })
      }
    } catch (error) {
      console.error('解析书签数据失败:', error)
    }
  } else {
    bookmarkList.value = []
  }
  
  const config = {
    document: {
      fileType: 'docx',
      key: docKey,
      title: '文书在线编辑',
      url: fileUrl,
      permissions: {
        edit: true,
        download: true,
        print: true,
        review: true,
        comment: true
      }
    },
    documentType: 'word',
    editorConfig: {
      mode: 'edit',
      lang: 'zh-CN',
      user: {
        id: 'user-001',
        name: '当前用户'
      },
      coEditing: {
        mode: "strict",
        change: false,
      },
      callbackUrl: import.meta.env.VITE_API_BASE_URL + '/api/onlyoffice/callback?id=' + currentDocument.value?.id,
      // 社区版支持的有限配置（大部分customization需要商业许可证）
      customization: {
        autosave: true,
        // crtls +s 立即调用回调接口，否则关闭编辑器才会保存
        forcesave: "true",
        // 社区版支持的基本设置
        compactHeader: true,
        compactToolbar: true,
        zoom: 80,
        unit: 'cm', // 设置默认单位，但无法完全隐藏（需要商业版）
        // 尝试隐藏文件菜单中的高级设置（可能包含单位设置）
        layout: {
          toolbar: {
            file: {
              settings: false // 尝试隐藏高级设置选项
            }
          }
        },
        features: {
          spellcheck: {
            mode: false
          }
        }
        // 注意：logo.visible、customer、feedback等配置在社区版中无效
      }
    },
    height: '100%',
    width: '100%'
  }

  // 销毁旧实例
  // @ts-ignore
  if (window.DocsAPI?.DocEditor?.destroyEditor) {
    // @ts-ignore
    window.DocsAPI.DocEditor.destroyEditor('onlyoffice-editor')
  }
  
  // 动态等待OnlyOffice脚本加载
  function tryInitEditor() {
    // @ts-ignore
    if (window.DocsAPI?.DocEditor) {
      // @ts-ignore
      window.docEditor = new window.DocsAPI.DocEditor('onlyoffice-editor', config)
    } else {
      setTimeout(tryInitEditor, 200)
    }
  }
  tryInitEditor()
}

/**
 * 返回上一页
 */
function goBack() {
  router.back()
}

// 数据管理函数
/**
 * 加载案件类型信息
 */
async function loadCaseTypeInfo() {
  if (!caseImportId.value) {
    console.log('案件ID为空，无法加载案件类型信息')
    return
  }
  
  console.log('开始加载案件类型信息，案件ID:', caseImportId.value)
  caseTypeLoading.value = true
  
  try {
    const response = await caseApi.getCaseType(caseImportId.value)
    caseTypeInfo.value = response
    console.log('案件类型信息加载成功:', response)
  } catch (error) {
    console.error('加载案件类型信息失败:', error)
    ElMessage.error('加载案件类型信息失败')
  } finally {
    caseTypeLoading.value = false
  }
}

/**
 * 加载文书数据
 */
async function loadDocuments() {
  if (!caseImportId.value) {
    console.log('案件ID为空，无法加载文书数据')
    return
  }
  
  console.log('开始加载文书数据，案件ID:', caseImportId.value)
  loading.value = true
  
  try {
    // 使用分页查询接口
    const params = {
      current: pagination.current,
      size: pagination.size,
      caseImportId: caseImportId.value
    }
    
    const response = await documentGenerationApi.getPage(params)
    docs.value = response.records || []
    pagination.total = response.total
    filteredDocs.value = docs.value // 直接使用查询结果
  } catch (error) {
    console.error('加载文书数据失败:', error)
  } finally {
    loading.value = false
  }
}



/**
 * 查询文书数据
 */
async function searchDocuments() {
  if (!caseImportId.value) {
    console.log('案件ID为空，无法查询文书数据')
    return
  }
  
  console.log('开始查询文书数据，案件ID:', caseImportId.value, '筛选条件:', filterParams)
  searchLoading.value = true
  
  try {
    // 构建查询参数
    const params: any = {
      current: pagination.current,
      size: pagination.size,
      caseImportId: caseImportId.value
    }
    
    // 添加文书类型筛选 - 直接传递英文值
    if (filterParams.docType) {
      params.documentType = filterParams.docType
    }
    
    // 添加时间范围筛选
    if (filterParams.startTime) {
      params.startTime = filterParams.startTime
    }
    if (filterParams.endTime) {
      params.endTime = filterParams.endTime
    }
    
    // 调用分页查询接口
    const response = await documentGenerationApi.getPage(params)
    docs.value = response.records || []
    pagination.total = response.total
    filteredDocs.value = docs.value // 直接使用查询结果
  } catch (error) {
    console.error('查询文书数据失败:', error)
    ElMessage.error('查询失败，请稍后重试')
  } finally {
    searchLoading.value = false
  }
}

/**
 * 重置筛选条件
 */
function resetFilter() {
  filterParams.caseType = ''
  filterParams.docType = ''
  filterParams.startTime = ''
  filterParams.endTime = ''
  
  // 重置分页
  pagination.current = 1
  
  // 重新加载所有数据
  loadDocuments()
}

// 文书操作函数
/**
 * 查看文书
 */
function viewDocument(doc: DocumentGenerationInfo) {
  currentDocument.value = doc
  showDocumentContent.value = true
  showOnlyOffice.value = true
}


/**
 * 处理表格选择变化
 */
function handleSelectionChange(selection: DocumentGenerationInfo[]) {
  selectedDocs.value = selection
}

/**
 * 处理表格行点击事件
 */
function handleRowClick(row: DocumentGenerationInfo) {
  console.log('点击文书行:', row)
  viewDocument(row)
}

/**
 * 获取表格行的类名
 */
function getRowClassName({ row }: { row: DocumentGenerationInfo }) {
  return 'clickable-row'
}

/**
 * 处理分页变化
 */
function handlePageChange(page: number) {
  pagination.current = page
  loadDocuments()
}

/**
 * 处理每页大小变化
 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  loadDocuments()
}

/**
 * 批量删除文书
 */
async function batchDeleteDocuments() {
  if (selectedDocs.value.length === 0) {
    ElMessage.warning('请先选择要删除的文书')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocs.value.length} 个文书吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 提取要删除的文书ID列表
      const documentIds = selectedDocs.value
        .map(doc => doc.id)
        .filter(id => id !== undefined) as number[]

      if (documentIds.length === 0) {
        ElMessage.warning('没有有效的文书ID')
        return
      }

      // 调用批量删除API
      const deletedCount = await documentGenerationApi.batchDelete(documentIds)

      // 清空选中项
      selectedDocs.value = []
      
      // 重新加载当前页数据
      await loadDocuments()
    } catch (deleteError) {
      console.error('批量删除文书失败:', deleteError)
      ElMessage.error('批量删除失败，请稍后重试')
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除操作错误:', error)
    }
  }
}

/**
 * 删除文书
 */
async function deleteDocument(doc: DocumentGenerationInfo) {
  if (!doc.id) return
  
  try {
    await ElMessageBox.confirm('确定要删除这个文书吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    try {
      await documentGenerationApi.delete(doc.id)
      
      // 重新加载当前页数据
      await loadDocuments()
    } catch (deleteError) {
      console.error('删除文书失败:', deleteError)
      ElMessage.error('删除失败，请稍后重试')
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除操作错误:', error)
    }
  }
}

/**
 * 下载文书
 */
function downloadDocument() {
  if (currentDocument.value?.documentUrl) {
    window.open(currentDocument.value.documentUrl, '_blank')
  } else {
    ElMessage.warning('文书地址不可用')
  }
}

/**
 * 返回主页
 */
async function backToMain() {
  showDocumentContent.value = false
  currentDocument.value = null

  // 刷新文书列表
  await loadDocuments()
}

// 对话框操作函数
/**
 * 显示生成对话框
 */
function showGenerateDialog() {
  if (!caseImportId.value) {
    ElMessage.warning('案件ID不能为空')
    return
  }
  
  showDialog.value = true
  
  // 根据案件类型信息设置默认值，如果信息未加载则默认为一审
  if (caseTypeInfo.value) {
    const isSecondInstance = caseTypeInfo.value.isSecondInstance
    dialogCaseType.value = isSecondInstance ? 'civil2' : 'civil1'
  } else {
    dialogCaseType.value = 'civil1' // 默认为一审
  }
  
  dialogDocType.value = ''
}

/**
 * 确认生成文书 - 显示文书生成编辑器
 */
async function onConfirm() {
  if (!caseImportId.value) {
    ElMessage.warning('案件ID不能为空')
    return
  }
  if (!dialogCaseType.value || !dialogDocType.value) {
    ElMessage.warning('请选择案件类型和文书类型')
    return
  }

  // 关闭对话框
  showDialog.value = false
  
  // 设置编辑器参数并显示编辑器
  editorParams.value = {
    caseType: dialogCaseType.value,
    documentType: dialogDocType.value
  }
  showEditor.value = true
}

/**
 * 关闭编辑器
 */
function closeEditor() {
  showEditor.value = false
  editorParams.value = {
    caseType: '',
    documentType: ''
  }
}

/**
 * 保存编辑内容
 */


// 工具函数

// 监听器
watch(dialogCaseType, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    dialogDocType.value = ''
  }
})

// 监听案件类型信息变化，仅在对话框首次打开时设置默认值
// 后续用户可以自由修改，不会被后端数据覆盖

watch(
  () => [showOnlyOffice.value, currentDocument.value?.documentUrl],
  async ([show, url]) => {
    const urlStr = String(url)
    if (show && isWordFile(urlStr)) {
      await nextTick()
      initOnlyOfficeEditor(urlStr)
    }
  }
)

// 生命周期---加载时
onMounted(async () => {
  // 组件挂载时不再尝试隐藏logo，交由watch处理
  if (!caseImportId.value) {
    ElMessage.error('案件ID无效，无法加载数据')
    return
  }
  
  // 并行加载案件类型信息和文书数据
  await Promise.all([
    loadCaseTypeInfo(),
    loadDocuments()
  ])
  
  // 检查是否有文书记录，如果没有则自动弹出生成对话框
  if (docs.value.length === 0) {
    console.log('该案件暂无文书记录，自动弹出生成对话框')
    showGenerateDialog()
  } else {
    console.log(`该案件已有 ${docs.value.length} 条文书记录，显示文书列表`)
  }
})
</script>

<style scoped>

.doc-gen-page {
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 120px);
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
}

.records-main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow-y: auto;
}

.records-filter-header {
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

.records-filter-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.time-row {
  gap: 4px !important; /* 强制减少时间选择器之间的间距 */
}

/* 时间选择器间距修正 */
.time-row .time-form-item {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.time-row .time-form-item + .time-form-item {
  margin-left: 4px !important;
}

.filter-row .el-form-item:last-child {
  margin-left: auto;
  flex-shrink: 0;
  min-width: fit-content;
}

/* 统一筛选区域组件样式 */
.records-filter-form .el-form-item__label {
  font-size: 13px !important;
  font-weight: 500;
  color: #303133;
  line-height: 28px;
}

/* 统一输入框和下拉框高度 */
.records-filter-form .el-input__wrapper,
.records-filter-form .el-select .el-input__wrapper,
.records-filter-form .el-date-editor .el-input__wrapper {
  height: 28px !important;
  line-height: 28px !important;
}

/* 统一输入框和下拉框字体大小 */
.records-filter-form .el-input__inner,
.records-filter-form .el-select .el-input__inner,
.records-filter-form .el-date-editor .el-input__inner {
  font-size: 13px !important;
  line-height: 28px !important;
}

/* 统一按钮高度和字体大小 */
.records-filter-form .el-button {
  height: 28px !important;
  line-height: 28px !important;
  font-size: 13px !important;
  padding: 0 12px !important;
}

/* 文书类型下拉框样式 */
.records-filter-form .el-select {
  width: auto !important;
  min-width: 200px;
}

.records-filter-form .el-select .el-input__wrapper {
  width: auto;
  min-width: 200px;
}

/* 确保下拉选项能够完整显示 */
:deep(.el-select-dropdown) {
  min-width: 200px;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.records-table-container {
  flex: 1;
  min-height: 0; /* flex布局下内容溢出问题 */
  width: 100%;
  max-width: 100%;
  overflow-x: auto; /* 添加横向滚动支持 */
}

/* 表格自适应样式 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: auto !important; /* 自动调整列宽 */
}

/* 确保表格内容不会被截断 */
:deep(.el-table__body) {
  width: 100% !important;
}

/* 表格单元格内容样式 */
:deep(.el-table .cell) {
  word-break: break-word;
  line-height: 1.5;
  padding: 8px 12px;
}

.batch-actions {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.batch-actions .el-button {
  min-width: 25px;
  height: 25px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.batch-actions .el-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.batch-actions .el-button .el-icon {
  font-size: 12px;
}

.selected-count {
  font-size: 13px;
  color: #606266;
  margin-left: 8px;
}

.empty-state-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px; /* 确保空状态有足够的高度 */
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
  margin-bottom: 20px;
}

.loading-container {
  padding: 20px;
}

/* 文书内容展示样式 */
.doc-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.doc-content-header {
  border-bottom: 1px solid #e6e6e6;
  background: #fff;
  padding: 10px 20px;
}

.doc-content-header h3 {
  margin: 0 0 10px 0;
  color: #333;
}



.doc-meta span {
  margin-right: 20px;
}

.doc-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  z-index: 2;
}

.doc-actions .el-button {
  min-width: 80px;
}

.doc-content-body {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  padding: 10px;
  overflow-y: auto;
}



.preview-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.preview-img {
  width: 60px;
  height: 80px;
  object-fit: contain;
  border-radius: 4px;
  background: #f5f5f5;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-img:hover {
  transform: scale(1.1);
}

.opinion-text {
  word-break: break-all;
  line-height: 1.4;
}

.doc-gen-footer {
  position: sticky; /* 改为sticky使其在滚动时固定在底部 */
  bottom: -20px;
  z-index: 100;
  display: flex;
  justify-content: center;
  gap: 32px;
  border-top: 1px solid #ebeef5;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
}

#onlyoffice-editor {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.08);
  margin: 0 auto;
  max-width: 900px;
  min-height: 600px;
  box-sizing: border-box;
  overflow: auto;
}

/* 自定义弹窗样式 (如果不再使用，可以移除) */
:global(.no-document-dialog) {
  border-radius: 8px;
}

:global(.no-document-dialog .el-message-box__message) {
  font-size: 16px;
  line-height: 1.6;
  color: #666;
  text-align: center;
  padding: 20px 0;
}

:global(.no-document-dialog .el-message-box__btns) {
  padding: 20px 0 10px 0;
  text-align: center;
}

:global(.no-document-dialog .el-message-box__btns .el-button) {
  margin: 0 10px;
  padding: 10px 20px;
  border-radius: 6px;
}

/* ==================== 统一输入框和文本框hover样式 ==================== */

/* 输入框hover效果 */
:deep(.el-input__wrapper:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

:deep(.el-input__inner:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* 文本域hover效果 */
:deep(.el-textarea__inner:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* 下拉框hover效果 */
:deep(.el-select .el-input__wrapper:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

:deep(.el-select .el-input__inner:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  transition: all 0.2s ease !important;
}

/* 日期选择器hover效果 */
:deep(.el-date-editor .el-input__wrapper:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

:deep(.el-date-editor .el-input__inner:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  transition: all 0.2s ease !important;
}

/* 筛选区域输入框特殊hover效果 */
.records-filter-form :deep(.el-input__wrapper:hover),
.records-filter-form :deep(.el-select .el-input__wrapper:hover),
.records-filter-form :deep(.el-date-editor .el-input__wrapper:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

.records-filter-form :deep(.el-input__inner:hover),
.records-filter-form :deep(.el-select .el-input__inner:hover),
.records-filter-form :deep(.el-date-editor .el-input__inner:hover) {
  background-color: #fff !important;
  border-color: #fff !important;
  transition: all 0.2s ease !important;
}

/* 按钮hover效果统一 */
:deep(.el-button:hover) {
  background-color: rgba(64, 158, 255, 0.05) !important;
  border-color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* 保持原有按钮类型的颜色，但添加统一的hover效果 */
:deep(.el-button--primary:hover) {
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
}

:deep(.el-button--success:hover) {
  background-color: #85ce61 !important;
  border-color: #85ce61 !important;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.15) !important;
}

:deep(.el-button--danger:hover) {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.15) !important;
}

/* ==================== 结束统一样式 ==================== */

/* 操作按钮样式 - 参考 DocumentGenerationEditor.vue */
.operation-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  width: 100%;
  height: 100%;
  min-height: 32px;
  position: relative;
}

.operation-buttons .el-button {
  min-width: 25px;
  height: 25px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.operation-buttons .el-button + .el-button {
  margin-left: 0;
}

.operation-buttons .el-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.operation-buttons .el-button .el-icon {
  font-size: 12px;
}

/* 确保操作列表格单元格垂直居中 */
:deep(.el-table__body .el-table__row .el-table__cell:has(.operation-buttons)) {
  vertical-align: middle !important;
  padding: 0 !important;
}

/* 兼容性写法：通过表格列宽度识别操作列 */
:deep(.el-table__body .el-table__row td[width="120"]) {
  vertical-align: middle !important;
  padding: 0 !important;
}

/* OnlyOffice编辑器容器样式 */
.onlyoffice-editor-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 书签导航样式 */
.bookmark-navigation {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 0 8px 8px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  width: 50px;
  overflow: hidden;
}

.bookmark-navigation.expanded {
  width: 280px;
}

.bookmark-toggle {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s;
}

.bookmark-toggle .el-icon {
  font-size: 18px;
  margin-right: 8px;
}

.bookmark-toggle span {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
}

.bookmark-list {
  max-height: 400px;
  overflow-y: auto;
}

.bookmark-header {
  padding: 12px 16px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #909399;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bookmark-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.bookmark-icon {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
  flex-shrink: 0;
}

.bookmark-text {
  font-size: 13px;
  color: #303133;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-bookmarks {
  padding: 20px 16px;
  text-align: center;
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 表格中书签列样式 */
.bookmark-text {
  font-size: 13px;
  color: #303133;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* 可点击表格行样式 */
:deep(.clickable-row) {
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.clickable-row:hover) {
  background-color: #f5f7fa !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.clickable-row:active) {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式表格样式 */
@media (max-width: 1200px) {
  .records-table-container {
    overflow-x: auto;
  }

  :deep(.el-table) {
    min-width: 800px; /* 设置最小宽度防止过度压缩 */
  }
}

@media (max-width: 768px) {
  .records-main-area {
    padding: 10px;
  }

  .records-filter-form {
    gap: 8px;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-row .el-form-item:last-child {
    margin-left: 0;
    align-self: flex-end;
  }

  :deep(.el-table) {
    min-width: 600px;
    font-size: 12px;
  }

  :deep(.el-table .cell) {
    padding: 6px 8px;
  }

  .operation-buttons .el-button {
    min-width: 22px;
    height: 22px;
  }

  .operation-buttons .el-button .el-icon {
    font-size: 10px;
  }
}
</style>