<template>
  <div class="timeline-container">
    <div class="timeline-header">
      <h3>案件时序链</h3>
      <el-button type="primary" @click="handleAddEvent">
        <el-icon>
          <Plus />
        </el-icon>
        添加事件
      </el-button>
    </div>
    <el-timeline v-loading="loading">
      <el-timeline-item v-for="(event, index) in events" :key="index" :color="'#67C23A'">
        <div class="timeline-content">
          <div class="timeline-card" @click="handleCardClick(event)">
            <div class="card-header">
              <h4>{{ formatEventTime(event.eventTime) }}</h4>
              <div class="header-actions">
                <el-button type="primary" link @click="handleEditEvent(event, index)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </el-button>
                <el-button type="danger" link @click="handleDeleteEvent(index)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <div class="card-body">
              <div class="event-title">
                <el-tag size="small" effect="plain" class="event-label">{{ event.eventResume }}</el-tag>
                <span>{{ event.eventDescription }}</span>
              </div>
              <div v-if="event.evidence" class="evidence">
                <div class="evidence-header" @click="(e) => event.id && toggleEvidence(event.id, e)">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span class="evidence-title">信息来源（{{ event.evidence.length }}）</span>
                  <el-icon class="expand-icon" :class="{ 'is-expanded': event.id && expandedEvents.has(event.id) }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div class="evidence-content" v-show="event.id && expandedEvents.has(event.id)" v-for="evidence in event.evidence" @click.stop="handleEvidenceClick(evidence)">
                  <div class="evidence-source">
                    <span class="file-name">{{ evidence.fileName }}</span>
                    <span class="location">{{ evidence.pageNumber ? `第${evidence.pageNumber}页` : '某处' }}</span>
                  </div>
                  <div class="evidence-highlight">
                    <div class="highlight-item">
                      <span class="quote-mark">"</span>
                      {{ evidence.highlight }}
                      <span class="quote-mark">"</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>

    <!-- 编辑事件对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑事件" width="600px" destroy-on-close class="timeline-dialog"
      modal-class="timeline-dialog-modal" :modal="false" :draggable="true" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :model="editForm" label-width="100px" class="timeline-form">
        <el-form-item label="时间" required>
          <el-date-picker v-model="editForm.eventTime" type="datetime" placeholder="选择日期时间" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
        </el-form-item>
        <el-form-item label="事件简介" required>
          <el-input v-model="editForm.eventResume" placeholder="请输入事件简介" />
        </el-form-item>
        <el-form-item label="事件详情" required>
          <el-input v-model="editForm.eventDescription" type="textarea" :rows="3" placeholder="请输入事件详情" />
        </el-form-item>
        <el-form-item label="信息来源">
          <div class="evidence-form" v-for="(evidence, index) in editForm.evidence" :key="index">
            <div class="evidence-form-header">
              <span class="evidence-title">信息来源 #{{ index + 1 }}</span>
              <el-button type="danger" link @click="removeEvidence(index)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
            <div class="form-item">
              <span class="label required">文件：</span>
              <el-select v-model="evidence.fileId" placeholder="请选择文件" class="w-full"
                @change="(val) => handleFileChange(val, index)" clearable>
                <el-option v-for="file in fileList" :key="file.id" :label="file.name" :value="file.id" />
              </el-select>
            </div>
            <div class="form-item">
              <span class="label required">位置：</span>
              <el-input-number v-model="evidence.pageNumber" :min="1" />
              <span class="location-text">&emsp;页</span>
            </div>
            <div class="form-item">
              <span class="label required">引用文本：</span>
              <el-input v-model="evidence.highlight" type="textarea" :rows="3" placeholder="请输入引用文本（每行一个）" />
            </div>
          </div>
          <div class="add-evidence">
            <el-button type="primary" link @click="addNewEvidence">
              <el-icon>
                <Plus />
              </el-icon>
              添加信息来源
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加事件对话框 -->
    <el-dialog v-model="addDialogVisible" title="添加事件" width="600px" destroy-on-close class="timeline-dialog"
      modal-class="timeline-dialog-modal" :modal="false" :draggable="true" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :model="addForm" label-width="100px" class="timeline-form">
        <el-form-item label="时间" required>
          <el-date-picker v-model="addForm.eventTime" type="datetime" placeholder="选择日期时间" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
        </el-form-item>
        <el-form-item label="事件简介" required>
          <el-input v-model="addForm.eventResume" placeholder="请输入事件简介" />
        </el-form-item>
        <el-form-item label="事件详情" required>
          <el-input v-model="addForm.eventDescription" type="textarea" :rows="3" placeholder="请输入事件详情" />
        </el-form-item>
        <el-form-item label="消息来源">
          <div class="evidence-form" v-for="(evidence, index) in addForm.evidence" :key="index">
            <div class="evidence-form-header">
              <span class="evidence-title">信息来源 #{{ index + 1 }}</span>
              <el-button type="danger" link @click="removeAddEvidence(index)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
            <div class="form-item">
              <span class="label required">文件：</span>
              <el-select v-model="evidence.fileId" placeholder="请选择文件" class="w-full"
                @change="(val) => handleAddFileChange(val, index)" clearable>
                <el-option v-for="file in fileList" :key="file.id" :label="file.name" :value="file.id" />
              </el-select>
            </div>
            <div class="form-item">
              <span class="label required">位置：</span>
              <el-input-number v-model="evidence.pageNumber" :min="1" />
              <span class="location-text">&emsp;页</span>
            </div>
            <div class="form-item">
              <span class="label required">引用文本：</span>
              <el-input v-model="evidence.highlight" type="textarea" :rows="3" placeholder="请输入引用文本（每行一个）" />
            </div>
          </div>
          <div class="add-evidence">
            <el-button type="primary" link @click="addNewAddEvidence">
              <el-icon>
                <Plus />
              </el-icon>
              添加信息来源
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAdd">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Edit, Delete, Plus, Document, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { timelineApi } from '@/api'

import { Evidence, TimelineEvent } from '@/api/timeline'

// 组件挂载时获取数据
onMounted(() => {
  fetchTimelineData()
})

const props = defineProps<{
  caseImportId: string
  fileList: Array<{
    id: number
    name: string
    content?: string
    uploadTime: string
    text?: string
  }>
}>()

const events = ref<Omit<TimelineEvent, 'createTime' | 'updateTime'>[]>([])
const loading = ref(false)
const expandedEvents = ref<Set<number>>(new Set())

// 获取时序链数据
const fetchTimelineData = async () => {
  if (!props.caseImportId) {
    // 保留参数验证警告提示
    ElMessage.warning('案件ID不能为空')
    return
  }

  loading.value = true
  try {
    const result = await timelineApi.getList(props.caseImportId)
    if (result && Array.isArray(result)) {
      // 转换后端数据格式为前端所需格式
      events.value = result.map((item: TimelineEvent) => ({
        id: item.id,
        eventTime: item.eventTime,
        eventDescription: item.eventDescription,
        eventResume: item.eventResume,
        evidence: item.evidence,
        caseImportId: item.caseImportId,
        createTime: item.createTime,
        updateTime: item.updateTime
      }))
    } else {
      // 保留业务逻辑警告提示
      ElMessage.warning('获取时序链数据失败')
    }
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取时序链数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 编辑事件对话框
const editDialogVisible = ref(false)
const currentEditEvent = ref<Omit<TimelineEvent, 'id' | 'createTime' | 'updateTime'> | null>(null)
const currentEditIndex = ref(-1)

// 编辑表单
const editForm = ref<Partial<TimelineEvent>>({
  eventTime: '',
  eventDescription: '',
  eventResume: '',
  evidence: []
})

// 处理编辑事件
const handleEditEvent = (event: Omit<TimelineEvent, 'createTime' | 'updateTime'>, index: number) => {
  currentEditEvent.value = event
  currentEditIndex.value = index
  editForm.value = {
    ...event,
    evidence: event.evidence.map(evidence => ({
      ...evidence
    }))
  }
  editDialogVisible.value = true
}

// 处理删除事件
const handleDeleteEvent = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除该事件吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })

    const event = events.value[index]
    await timelineApi.delete(props.caseImportId, event.id!!)
    events.value.splice(index, 1)
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('删除失败:', error)
    }
  }
}

// 按时间排序事件
const sortEventsByTime = () => {
  events.value.sort((a, b) => new Date(a.eventTime).getTime() - new Date(b.eventTime).getTime())
}

// 保存编辑
const handleSaveEdit = async () => {
  if (currentEditIndex.value > -1 && currentEditEvent.value) {
    try {
      // 验证信息来源
      if (!validateEvidence(editForm.value.evidence)) {
        return
      }

      const event = events.value[currentEditIndex.value]
      await timelineApi.update(props.caseImportId, event.id!!, {
        eventTime: editForm.value?.eventTime,
        eventResume: editForm.value?.eventResume,
        eventDescription: editForm.value?.eventDescription,
        evidence: editForm.value?.evidence
      })

      // 更新本地数据
      events.value[currentEditIndex.value] = {
        ...event,
        eventTime: editForm.value.eventTime,
        eventResume: editForm.value.eventResume,
        eventDescription: editForm.value.eventDescription,
        evidence: editForm.value.evidence
      }
      // 按时间排序
      sortEventsByTime()
      editDialogVisible.value = false
    } catch (error) {
      // 全局已处理错误提示
      console.error('保存失败:', error)
    }
  }
}

// 添加事件对话框
const addDialogVisible = ref(false)
const addForm = ref<Partial<TimelineEvent>>({
  eventTime: '',
  eventDescription: '',
  eventResume: '',
  evidence: [],
})

// 处理添加事件
const handleAddEvent = () => {
  addForm.value = {
    eventTime: '',
    eventDescription: '',
    eventResume: '',
    evidence: [{
      fileId: undefined,
      fileName: undefined,
      pageNumber: undefined,
      highlight: undefined
    }]
  }
  addDialogVisible.value = true
}

// 保存添加的事件
const handleSaveAdd = async () => {
  try {
    // 表单验证
    if (!addForm.value.eventTime) {
      ElMessage.warning('请选择事件时间')
      return
    }
    if (!addForm.value.eventResume) {
      ElMessage.warning('请输入事件简介')
      return
    }
    if (!addForm.value.eventDescription) {
      ElMessage.warning('请输入事件详情')
      return
    }

    // 验证信息来源
    if (!validateEvidence(addForm.value.evidence)) {
      return
    }

    const newEvent = await timelineApi.create(props.caseImportId, {
      eventTime: addForm.value.eventTime,
      eventResume: addForm.value.eventResume,
      eventDescription: addForm.value.eventDescription,
      evidence: addForm.value.evidence,
      caseImportId: Number(props.caseImportId)
    })

    // 添加新事件到列表
    events.value.push({
      id: newEvent.id,
      eventTime: addForm.value.eventTime,
      eventDescription: addForm.value.eventDescription,
      eventResume: addForm.value.eventResume,
      evidence: addForm.value.evidence,
      caseImportId: Number(props.caseImportId)
    })
    // 按时间排序
    sortEventsByTime()
    addDialogVisible.value = false
  } catch (error) {
    // 全局已处理错误提示
    console.error('添加失败:', error)
  }
}


const emit = defineEmits<{
  (e: 'detailHighlight', highlight: Array<{
    fileId: number
    fileName: string
    pageNumber: number
    highlight: string
  }>): void
}>()

// Add handleCardClick function before onMounted
const handleCardClick = (event: Omit<TimelineEvent, 'createTime' | 'updateTime'>) => {
  if (event.evidence) {
    const validEvidence = event.evidence.filter(e =>
      e.fileId && e.fileName
    ).map(e => ({
      fileId: e.fileId!,
      fileName: e.fileName!,
      // 不传递页码，使用全文搜索提高匹配成功率
      // pageNumber: e.pageNumber!,
      highlight: e.highlight!,
    }))
    emit('detailHighlight', validEvidence);
  }
}

const formatEventTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const seconds = date.getSeconds()

  // 如果时间是00:00:00，只返回日期
  if (hours === 0 && minutes === 0 && seconds === 0) {
    return timeStr.split(' ')[0]
  }

  return timeStr
}

// 处理文件选择处理函数
const handleFileChange = (fileId: number, index: number) => {
  const selectedFile = props.fileList.find(file => file.id == fileId)
  if (selectedFile) {
    editForm.value.evidence[index].fileName = selectedFile.name
  }
}

const removeEvidence = (index: number) => {
  editForm.value.evidence.splice(index, 1)
}

const addNewEvidence = () => {
  editForm.value.evidence.push({
    fileId: undefined,
    fileName: undefined,
    pageNumber: undefined,
    highlight: undefined
  })
}

const removeAddEvidence = (index: number) => {
  addForm.value.evidence.splice(index, 1)
}

const addNewAddEvidence = () => {
  addForm.value.evidence.push({
    fileId: undefined,
    fileName: undefined,
    pageNumber: undefined,
    highlight: undefined
  })
}

const handleAddFileChange = (fileId: number, index: number) => {
  const selectedFile = props.fileList.find(file => file.id == fileId)
  if (selectedFile) {
    addForm.value.evidence[index].fileName = selectedFile.name
  }
}

// 添加验证函数
const validateEvidence = (evidence: Evidence[]): boolean => {
  for (let i = 0; i < evidence.length; i++) {
    const item = evidence[i]
    if (!item.fileId) {
      ElMessage.warning(`信息来源 #${i + 1} 请选择文件名`)
      return false
    }
    if (item.pageNumber === undefined || item.pageNumber === null) {
      ElMessage.warning(`信息来源 #${i + 1} 请输入位置`)
      return false
    }
    if (!item.highlight) {
      ElMessage.warning(`信息来源 #${i + 1} 请输入引用文本`)
      return false
    }
  }
  return true
}

// 切换信息来源的展开/收缩状态
const toggleEvidence = (eventId: number | undefined, event: Event) => {
  if (eventId === undefined) return
  event.stopPropagation() // 阻止事件冒泡
  const id = eventId as number
  if (expandedEvents.value.has(id)) {
    expandedEvents.value.delete(id)
  } else {
    expandedEvents.value.add(id)
  }
}

// Add handleEvidenceClick function
const handleEvidenceClick = (evidence: Evidence) => {
  if (evidence.fileId && evidence.fileName) {
    emit('detailHighlight', [{
      fileId: evidence.fileId,
      fileName: evidence.fileName,
      // 不传递页码，使用全文搜索提高匹配成功率
      // pageNumber: evidence.pageNumber,
      highlight: evidence.highlight || '',
    }]);
  }
}

defineExpose({ fetchTimelineData })
</script>

<style scoped lang="scss">
.timeline-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  :deep(.el-timeline-item) {
    .el-timeline-item__node {
      margin-top: 12px;
    }

    .el-timeline-item__tail {
      top: 12px;
    }
  }

  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  .timeline-content {
    .timeline-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      margin-bottom: 8px;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
        background-color: #f5f7fa;
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .card-header {
        padding: 10px 16px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 16px;
          color: #303133;
          font-weight: 600;
        }

        .header-actions {
          display: flex;
          gap: 8px;

          .el-button {
            padding: 4px;
          }
        }
      }

      .card-body {
        padding: 10px;

        .event-title {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          font-size: 15px;
          font-weight: 500;
          color: #303133;

          .event-label {
            margin-right: 8px;
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #909399;
          }
        }

        .event-content {
          margin-bottom: 12px;

          p {
            margin: 0;
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
          }
        }

        .evidence {
          font-size: 13px;
          color: #909399;
          padding: 10px;
          margin-top: 10px;
          border-top: 1px dashed #ebeef5;
          background-color: #f8f9fa;
          border-radius: 4px;

          .evidence-header {
            display: flex;
            align-items: center;
            color: #606266;
            cursor: pointer;
            user-select: none;

            .el-icon {
              margin-right: 4px;
              font-size: 16px;
            }

            .evidence-title {
              font-weight: 500;
            }

            .expand-icon {
              margin-left: auto;
              transition: transform 0.3s ease;
              
              &.is-expanded {
                transform: rotate(180deg);
              }
            }
          }

          .evidence-content {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #ebeef5;

            .evidence-source {
              margin-top: 2px;
              margin-bottom: 2px;
              font-size: 12px;
              color: #909399;
              cursor: pointer;
              padding: 4px 8px;
              border-radius: 4px;
              transition: background-color 0.3s ease;

              &:hover {
                background-color: #ecf5ff;
              }

              .file-name {
                font-weight: 500;
                color: #606266;
                margin-right: 8px;
              }

              .location {
                color: #909399;
              }
            }

            .evidence-highlight {
              margin-bottom: 12px;
              padding: 0 10px;

              .highlight-item {
                position: relative;
                padding: 10px 12px;
                background-color: #fff;
                border-left: 3px solid #409EFF;
                border-radius: 0 4px 4px 0;
                font-size: 13px;
                line-height: 1.6;
                color: #606266;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

                .quote-mark {
                  color: #909399;
                  font-size: 16px;
                  font-family: serif;
                  margin: 0 2px;
                }
              }
            }

            &:last-child {
              .evidence-highlight {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }




}

:deep(.timeline-dialog) {
  pointer-events: auto;
}
:deep(.timeline-dialog-modal) {
  pointer-events: none;
}

.timeline-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 0 !important;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      cursor: move;
      user-select: none;
      padding: 16px 20px;
      margin: 0;
      border-bottom: 1px solid #ebeef5;
      background-color: #f5f7fa;
      border-radius: 8px 8px 0 0;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .el-dialog__headerbtn {
        top: 16px;
        right: 16px;
      }
    }

    .el-dialog__body {
      padding: 20px;
      max-height: calc(90vh - 120px);
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
      background-color: #f5f7fa;
      border-radius: 0 0 8px 8px;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 30px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__footer) {
    padding: 20px 30px;
    border-top: 1px solid #ebeef5;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  .timeline-form {
    .w-full {
      width: 100%;
    }
  }

  .evidence-form {
    background-color: #f8f9fa;
    padding: 6px 10px;
    border-radius: 4px;
    width: 100%;
    margin-bottom: 12px;

    .evidence-form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 10px;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px dashed #dcdfe6;

      .evidence-title {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
      }
    }

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      width: 100%;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        flex: 0 0 100px;
        line-height: 32px;
        color: #606266;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      :deep(.el-input),
      :deep(.el-textarea) {
        flex: 1;
        width: 100%;
      }
    }
  }

  .add-evidence {
    display: flex;
    align-content: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>