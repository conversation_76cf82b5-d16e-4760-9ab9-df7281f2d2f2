<template>
  <div class="evidence-card">
    <div class="evidence-header">
      <div class="evidence-meta">
        <el-icon class="file-icon"><Document /></el-icon>
        <span class="file-name">{{ evidence.fileName || '未选择文件' }}</span>
        <el-tag
          v-if="evidence.pageNumber"
          size="small"
          :type="side === 'plaintiff' ? 'primary' : 'danger'"
          class="page-tag"
        >
          第{{ evidence.pageNumber }}页
        </el-tag>
      </div>
      <el-button
        size="small"
        type="danger"
        text
        @click="$emit('remove')"
        class="delete-btn"
      >
        <el-icon><Close /></el-icon>
      </el-button>
    </div>

    <div class="evidence-content">
      <el-input
        :model-value="evidence.highlight"
        @update:model-value="updateHighlight"
        type="textarea"
        :rows="3"
        placeholder="请编辑证据内容..."
        class="content-textarea"
        resize="none"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document, Close } from '@element-plus/icons-vue'

interface Evidence {
  fileId: number;
  fileName: string;
  pageNumber?: number; // 页码变为可选，支持全文搜索
  highlight: string;
}

const props = defineProps<{
  evidence: Evidence;
  side: 'plaintiff' | 'defendant';
}>()

const emit = defineEmits<{
  remove: [];
  update: [evidence: Evidence];
}>()

const updateHighlight = (value: string) => {
  const updatedEvidence = {
    ...props.evidence,
    highlight: value
  }
  emit('update', updatedEvidence)
}
</script>

<style scoped lang="scss">
.evidence-card {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: #c0c4cc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .evidence-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;

    .evidence-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .file-icon {
        color: #409eff;
        font-size: 16px;
      }

      .file-name {
        font-size: 13px;
        color: #303133;
        font-weight: 500;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .page-tag {
        font-size: 12px;
        height: 20px;
        line-height: 18px;
      }
    }

    .delete-btn {
      color: #f56c6c;

      &:hover {
        background: #fef0f0;
      }
    }
  }

  .evidence-content {
    padding: 12px 16px;

    .content-textarea {
      :deep(.el-textarea__inner) {
        border: none;
        box-shadow: none;
        background: transparent;
        resize: none;
        font-size: 13px;
        line-height: 1.5;

        &:focus {
          border: 1px solid #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }

        &::placeholder {
          color: #c0c4cc;
        }
      }
    }
  }
}
</style> 