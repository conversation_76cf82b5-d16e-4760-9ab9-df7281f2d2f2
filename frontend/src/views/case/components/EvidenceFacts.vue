<template>
  <div class="evidence-facts-container">

    <!-- 证据情况区域 -->
    <div class="evidence-section">
      <div class="section-header">
        <h3>证据情况</h3>
        <div class="header-actions">
          <el-button
            type="warning"
            circle
            @click="handleEvidenceRegenerateClick"
            :loading="false"
            :disabled="regeneratingEvidence"
            title="重新生成"
          >
            <template v-if="regeneratingEvidence">
              <el-icon class="is-loading"><Loading /></el-icon>
            </template>
            <template v-else>
              <el-icon><Refresh /></el-icon>
            </template>
          </el-button>
        </div>
      </div>
      <div class="section-content">
        <EvidenceOverview
          ref="evidenceOverviewRef"
          :caseImportId="Number(caseImportId)"
          @detailHighlight="handleEvidenceOverviewHighlight"
          @detailFileJump="handleEvidenceOverviewFileJump"
        />
      </div>
    </div>

    <!-- 质证情况区域 -->
    <div class="evidence-section">
      <div class="section-header">
        <h3>质证情况</h3>
        <div class="header-actions">
          <el-button
            type="warning"
            circle
            @click="handleVerificationRegenerateClick"
            :loading="generateLoading"
            title="重新生成"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="section-content">
        <VerificationEvidence
          ref="verificationEvidenceRef"
          :caseImportId="Number(caseImportId)"
          :fileList="fileList"
          @detailHighlight="handleDetailHighlight"
        />
      </div>
    </div>

    <!-- 无争议事实输入框 - 暂时注释 -->
    <!-- <div class="evidence-section" style="position: relative; min-height: 200px;">
      <TaskGeneratingStatus
        ref="undisputedFactsTaskRef"
        :case-import-id="caseImportId"
        :task-type="TaskType.UNDISPUTED_FACTS"
        :overlay="false"
        :position="'absolute'"
        :show-progress="true"
        :show-estimated-time="true"
        @task-completed="handleUndisputedFactsTaskCompleted"
        @task-failed="handleUndisputedFactsTaskFailed"
      />

      <div class="section-header">
        <h3 class="section-title">无争议事实</h3>
        <div class="header-right">
          <div class="section-actions">
            <el-button
              type="primary"
              size="small"
              @click="generateUndisputedFacts"
              :loading="undisputedFactsGenerating"
              :disabled="undisputedFactsGenerating"
              title="重新生成无争议事实"
            >
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
          </div>
        </div>
      </div>
      <div class="section-content undisputed-section-content">
        <el-input
          type="textarea"
          :rows="getDynamicRows(undisputedText)"
          v-model="undisputedText"
          :autosize="{ minRows: 8, maxRows: 50 }"
          resize="vertical"
          @blur="saveUndisputedFact"
          placeholder="请输入无争议事实..."
        />
        <div class="section-footer">
          <div class="ai-warning-text">
            本内容由AI生成，请注意甄别！
          </div>
          <div v-if="saving" class="saving-indicator">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在保存...
          </div>
        </div>
      </div>
    </div> -->

    <!-- 有争议事实输入框 0703先注释掉-->
<!--    <div class="evidence-section">-->
<!--      <div class="section-header">-->
<!--        <h3 class="section-title">有争议事实</h3>-->
<!--        <div class="section-actions" :class="{ 'editing-actions': editing.disputed }">-->
<!--          <template v-if="editing.disputed">-->
<!--            <el-button circle class="save-btn" @click="saveDisputedFact">-->
<!--              <el-icon><Check /></el-icon>-->
<!--            </el-button>-->
<!--            <el-button circle class="cancel-btn" @click="cancelDisputedFact">-->
<!--              <el-icon><Close /></el-icon>-->
<!--            </el-button>-->
<!--          </template>-->
<!--          <template v-else>-->
<!--            <el-button text circle @click="editBox('disputed')">-->
<!--              <el-icon><Edit /></el-icon>-->
<!--            </el-button>-->
<!--            <el-button text circle @click="refreshBox('disputed')">-->
<!--              <el-icon><Refresh /></el-icon>-->
<!--            </el-button>-->
<!--          </template>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="section-content disputed-section-content">-->
<!--        <el-input-->
<!--          type="textarea"-->
<!--          :rows="7"-->
<!--          v-model="disputedText"-->
<!--          :readonly="!editing.disputed"-->
<!--        />-->
<!--        <div class="ai-warning-text">-->
<!--          本内容由AI生成，请注意甄别！-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

    <!-- 法官意见输入框 - 暂时注释 -->
    <!-- <div class="evidence-section">
      <div class="section-header">
        <h3 class="section-title">法官意见</h3>
      </div>
      <div class="section-content">
        <el-input
          type="textarea"
          :rows="getDynamicRows(judgeText)"
          v-model="judgeText"
          placeholder="请输入法官意见"
          :autosize="{ minRows: 8, maxRows: 50 }"
          resize="vertical"
          @blur="saveJudgeFact"
        />
        <div v-if="saving" class="saving-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在保存...
        </div>
      </div>
    </div> -->

    <!-- 认定事实输入框 -->
    <div class="evidence-section" style="position: relative; min-height: 200px;">
      <!-- 任务生成状态组件 - 局部覆盖 -->
      <TaskGeneratingStatus
        ref="determineFactsTaskRef"
        :case-import-id="caseImportId"
        :task-type="TaskType.DETERMINE_FACTS"
        :overlay="false"
        :position="'absolute'"
        :show-progress="true"
        :show-estimated-time="true"
        @task-completed="handleDetermineFactsTaskCompleted"
        @task-failed="handleDetermineFactsTaskFailed"
      />

      <div class="section-header">
        <h3>认定事实</h3>
        <div class="header-actions">
          <!-- 二审案件显示两个独立按钮 -->
          <template v-if="isCivilSecondInstance">
            <div style="position: relative; display: inline-block; margin-right: 8px;">
              <el-button
                  type="primary"
                  size="small"
                  style="min-width: auto; white-space: nowrap;"
                  @click="handleDetermineFactsCommand('confirmFirstInstance')"
                  :loading="determineFactsGenerating"
                  :disabled="determineFactsGenerating"
                  title="确认一审认定事实"
              >
                确认一审事实
              </el-button>
            </div>
            <div style="position: relative; display: inline-block;">
              <el-button
                type="warning"
                size="small"
                style="min-width: auto; white-space: nowrap;"
                @click="handleDetermineFactsCommand('generateSecondInstance')"
                :loading="determineFactsGenerating"
                :disabled="determineFactsGenerating"
                title="另查明事实"
              >
                另查明事实
              </el-button>
              <RedDot
                :case-import-id="Number(caseImportId)"
                module-code="EVIDENCE_OVERVIEW"
                operated-module="DETERMINE_FACTS"
                :enable-event-listener="true"
                size="small"
                style="position: absolute; top: -2px; left: -2px;"
                ref="evidenceOverviewRedDotRef"
              />
            </div>
          </template>

          <!-- 一审案件显示普通按钮 -->
          <div v-else style="position: relative; display: inline-block;">
            <el-button
              type="warning"
              circle
              @click="generateDetermineFacts"
              :loading="determineFactsGenerating"
              :disabled="determineFactsGenerating"
              title="重新生成认定事实"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
            <RedDot
              :case-import-id="Number(caseImportId)"
              module-code="EVIDENCE_OVERVIEW"
              operated-module="DETERMINE_FACTS"
              :enable-event-listener="true"
              size="small"
              style="position: absolute; top: -2px; left: -2px;"
              ref="evidenceOverviewRedDotRef"
            />
          </div>
        </div>
      </div>
      <div class="section-content final-section-content">
        <el-input
          type="textarea"
          :rows="getDynamicRows(finalText)"
          v-model="finalText"
          :autosize="{ minRows: 8, maxRows: 50 }"
          placeholder="请输入认定事实..."
          resize="vertical"
          @blur="saveFinalFact"
        />
        <div class="section-footer">
          <div v-if="isShowingAIGenerated" class="ai-warning-text">
            本内容由AI生成，请注意甄别！
          </div>
          <div v-if="saving" class="saving-indicator">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在保存...
          </div>
        </div>
      </div>
    </div>





  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Refresh, Folder, ArrowDown, Loading, Check } from '@element-plus/icons-vue'
import { casePartyApi } from '@/api/caseParty'
import VerificationEvidence from './VerificationEvidence.vue'
import EvidenceOverview from './EvidenceOverview.vue'
import RedDot from '@/components/RedDot.vue'
import {evidenceFactsApi, EvidenceFactsDetails, evidenceOverviewApi} from "@/api";
import { taskStatusApi, TaskType } from '@/api/taskStatus'
import { TaskGeneratingStatus } from '@/components/task-generating'
import { eventBus } from '@/utils/eventBus'


// Props
interface Props {
  caseImportId: string | number
  fileList?: any[]
  caseInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  fileList: () => [],
  caseInfo: () => ({})
})

// 计算属性：判断是否为民事二审案件
const isCivilSecondInstance = computed(() => {
  return props.caseInfo?.ajlxdm === '0302'
})

// Emits
const emit = defineEmits<{
  detailHighlight: [evidence: Array<{
    fileId: number
    fileName: string
    pageNumber?: number
    highlight?: string
    position?: any
  }>]
  detailFileJump: [evidence: Array<{
    fileId: number
    fileName: string
    pageNumber?: number
  }>]
}>()

// 组件引用
const verificationEvidenceRef = ref()
const evidenceOverviewRef = ref<InstanceType<typeof EvidenceOverview> | null>(null)
// const undisputedFactsTaskRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null) // 暂时注释
const determineFactsTaskRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null)
// RedDot组件引用
const evidenceOverviewRedDotRef = ref()

// 响应式数据
const refreshing = ref(false)
const regeneratingEvidence = ref(false)
const undisputedFactsLoading = ref(false)
const refreshingUndisputed = ref(false)
const disputedFactsLoading = ref(false)
const refreshingDisputed = ref(false)
const generateLoading = ref(false)

// 三次点击检测相关
const clickCount = ref(0)
const clickTimer = ref<number | null>(null)
const CLICK_THRESHOLD = 500 // 500ms内点击3次

// 三次点击检测函数
const handleTripleClick = (tripleClickCallback: () => void, singleClickCallback: () => void) => {
  clickCount.value++
  
  // 清除之前的定时器
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
  }
  
  // 设置新的定时器
  clickTimer.value = setTimeout(() => {
    // 如果定时器触发时点击次数为1，执行单击回调
    if (clickCount.value === 1) {
      singleClickCallback()
    }
    clickCount.value = 0
  }, CLICK_THRESHOLD)
  
  // 如果点击次数达到3次，执行三次点击回调
  if (clickCount.value === 3) {
    clickCount.value = 0
    if (clickTimer.value) {
      clearTimeout(clickTimer.value)
    }
    tripleClickCallback()
  }
}

// 认定事实相关状态
const determineFactsGenerating = ref(false)

// 定时器相关
const evidenceTaskTimer = ref<number | null>(null)
const verificationTaskTimer = ref<number | null>(null)
const taskPollingInterval = 3000 // 3秒检查一次任务状态

// 清理定时器
const clearEvidenceTaskTimer = () => {
  if (evidenceTaskTimer.value) {
    clearInterval(evidenceTaskTimer.value)
    evidenceTaskTimer.value = null
  }
}

const clearVerificationTaskTimer = () => {
  if (verificationTaskTimer.value) {
    clearInterval(verificationTaskTimer.value)
    verificationTaskTimer.value = null
  }
}

// 清理所有定时器
const clearAllTaskTimers = () => {
  clearEvidenceTaskTimer()
  clearVerificationTaskTimer()
}

// 检查初始任务状态
const checkInitialTaskStatus = async () => {
  try {
    // 检查证据情况任务状态
    const evidenceRunning = await taskStatusApi.isTaskRunning(props.caseImportId, TaskType.EVIDENCE_OVERVIEW)
    if (evidenceRunning) {
      regeneratingEvidence.value = true
      startEvidenceTaskPolling()
      console.log('检测到证据情况任务正在运行，已启动定时检查')
    }

    // 检查质证情况任务状态
    const verificationRunning = await taskStatusApi.isTaskRunning(props.caseImportId, TaskType.VERIFICATION_EVIDENCE)
    if (verificationRunning) {
      generateLoading.value = true
      startVerificationTaskPolling()
      console.log('检测到质证情况任务正在运行，已启动定时检查')
    }
  } catch (error) {
    console.error('检查初始任务状态失败:', error)
  }
}

// 开始定时检查证据情况任务状态
const startEvidenceTaskPolling = () => {
  // 先清理之前的定时器
  clearEvidenceTaskTimer()

  evidenceTaskTimer.value = setInterval(async () => {
    try {
      // 检查任务是否还在运行
      const isRunning = await taskStatusApi.isTaskRunning(props.caseImportId, TaskType.EVIDENCE_OVERVIEW)

      if (isRunning) {
        // 任务还在 running，且还没刷新过，刷新一次证据情况列表
        if (evidenceOverviewRef.value?.refreshData) {
          await evidenceOverviewRef.value.refreshData()
        }
      }

      if (!isRunning) {
        // 任务完成，停止定时器并刷新数据
        clearEvidenceTaskTimer()
        regeneratingEvidence.value = false

        // 刷新证据情况数据
        if (evidenceOverviewRef.value?.refreshData) {
          await evidenceOverviewRef.value.refreshData()
        }

        ElMessage.success('证据情况生成完成')
      }
    } catch (error) {
      console.error('检查证据情况任务状态失败:', error)
      // 出错时也停止定时器
      clearEvidenceTaskTimer()
      regeneratingEvidence.value = false
    }
  }, taskPollingInterval)
}

// 开始定时检查质证情况任务状态
const startVerificationTaskPolling = () => {
  // 先清理之前的定时器
  clearVerificationTaskTimer()

  verificationTaskTimer.value = setInterval(async () => {
    try {
      // 检查任务是否还在运行
      const isRunning = await taskStatusApi.isTaskRunning(props.caseImportId, TaskType.VERIFICATION_EVIDENCE)

      if (!isRunning) {
        // 任务完成，停止定时器并刷新数据
        clearVerificationTaskTimer()
        generateLoading.value = false

        // 刷新质证情况数据
        // if (verificationEvidenceRef.value?.refreshData) {
        //   await verificationEvidenceRef.value.refreshData()
        // }

        ElMessage.success('质证情况生成完成')
      }
    } catch (error) {
      console.error('检查质证情况任务状态失败:', error)
      // 出错时也停止定时器
      clearVerificationTaskTimer()
      generateLoading.value = false
    }
  }, taskPollingInterval)
}

// 事实数据
interface Evidence {
  fileId: number
  fileName: string
  pageNumber?: number // 页码变为可选，支持全文搜索
  highlight: string
}

interface Fact {
  content: string
  evidence?: Evidence[]
  disputeReason?: string
}

const undisputedFacts = ref<Fact[]>([])
const disputedFacts = ref<Fact[]>([])

const undisputedText = ref('')
const disputedText = ref('')
const judgeText = ref('')
const finalText = ref('')

const facts = ref<EvidenceFactsDetails | null>(null)

// 认定事实相关状态
const isShowingAIGenerated = ref(false) // 当前显示的是否为AI生成的内容

// 无争议事实生成状态
const undisputedFactsGenerating = ref(false)

// 保存状态
const saving = ref(false)
let saveTimer: number | null = null

// 记录原始值，用于比较是否有变动
const originalValues = ref({
  undisputedText: '',
  judgeText: '',
  finalText: ''
})

// 处理质证情况的详情高亮
const handleDetailHighlight = (evidence: any) => {
  emit('detailHighlight', evidence)
}

// 处理证据高亮
const handleEvidenceOverviewHighlight = (evidence: Array<{
    fileId: number
    fileName: string
    pageNumber?: number
    highlight?: string
}>) => {
  emit('detailHighlight', evidence)
}

// 处理证据文件跳转
const handleEvidenceOverviewFileJump = (evidence: Array<{
    fileId: number
    fileName: string
    pageNumber?: number
}>) => {
  emit('detailFileJump', evidence)
}

// 处理证据点击
const handleEvidenceClick = (evidence: Evidence) => {
  const evidenceData = [{
    fileId: Number(evidence.fileId),
    fileName: evidence.fileName,
    // 不传递页码，使用全文搜索提高匹配成功率
    // pageNumber: evidence.pageNumber || 1,
    highlight: evidence.highlight || ''
  }]

  emit('detailHighlight', evidenceData)
  // 保留业务逻辑提示（文件定位反馈）
  console.log(`正在定位到文件: ${evidence.fileName}`)
}

// 刷新质证情况
const handleRefreshVerification = async () => {
  refreshing.value = true
  try {
    if (verificationEvidenceRef.value?.refreshData) {
      await verificationEvidenceRef.value.refreshData()
    }
    // 保留业务逻辑成功提示
    ElMessage.success('质证情况刷新成功')
  } catch (error) {
    // 保留业务逻辑错误提示
    console.error('刷新质证情况失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 重新生成当事人确认弹窗
const regeneratingParties = ref(false)
const showRegeneratePartiesConfirm = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重新生成当事人信息吗？这将覆盖当前所有当事人数据。',
      '重新生成当事人确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    regeneratingParties.value = true
    const success = await casePartyApi.regenerate(props.caseImportId)
    if (success) {
      ElMessage.success('当事人信息重新生成任务已启动')
    } else {
      ElMessage.error('当事人信息重新生成失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('重新生成当事人信息失败:', error)
    ElMessage.error('重新生成当事人信息失败')
  } finally {
    regeneratingParties.value = false
  }
}

// 证据情况重新生成点击处理
const handleEvidenceRegenerateClick = () => {
  handleTripleClick(() => {
    showRegeneratePartiesConfirm()
  }, () => {
    handleRegenerateEvidenceOverview()
  })
}

// 质证情况重新生成点击处理
const handleVerificationRegenerateClick = () => {
  handleTripleClick(() => {
    showRegeneratePartiesConfirm()
  }, () => {
    handleGenerateVerification()
  })
}

// 重新生成证据情况
const handleRegenerateEvidenceOverview = async () => {
  if (!props.caseImportId) {
    ElMessage.warning('案件ID不存在，无法重新生成证据情况')
    return
  }

  // 检查是否正在生成中
  if (regeneratingEvidence.value) {
    ElMessage.warning('证据情况正在生成中，请稍后再试')
    return
  }

  try {
    // 显示确认提示弹框
    await ElMessageBox.confirm(
      '确定要重新生成证据情况吗？这将覆盖当前内容。',
      '重新生成确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    regeneratingEvidence.value = true

    // 调用重新生成接口
    await evidenceOverviewApi.reanalyze(props.caseImportId)

    ElMessage.success('证据情况重新生成任务已启动，正在生成中...')

    // 开始定时检查任务状态
    startEvidenceTaskPolling()
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('重新生成证据情况失败:', error)
    ElMessage.error('重新生成失败，请稍后重试')

    // 出错时清理定时器和状态
    clearEvidenceTaskTimer()
    regeneratingEvidence.value = false
  }
}

// 生成质证情况
const handleGenerateVerification = () => {
  ElMessageBox.confirm(
    '确定要重新生成质证情况吗？这将覆盖当前所有质证内容。',
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      generateLoading.value = true

      // 清空本地数据
      if (verificationEvidenceRef.value?.clearLocalData) {
        verificationEvidenceRef.value.clearLocalData()
      }

      if (verificationEvidenceRef.value?.generateVerificationEvidence) {
        await verificationEvidenceRef.value.generateVerificationEvidence()
        // 由子组件内部统一启动任务监控，避免重复触发
        // 启动任务监控（通过子组件的引用）
        // const taskGeneratingRef = verificationEvidenceRef.value.taskGeneratingRef
        // if (taskGeneratingRef) {
        //   taskGeneratingRef.startTaskExecution()
        //   setTimeout(() => {
        //     taskGeneratingRef.startTaskMonitoring()
        //   }, 2000)
        // }
        ElMessage.success('质证情况重新生成任务已启动，正在生成中...')
      }
    } catch (error) {
      console.error('生成质证情况失败:', error)
      ElMessage.error('生成失败')
    } finally {
      generateLoading.value = false
    }
  }).catch(() => {})
}



// 组件挂载时的初始化
// 加载证据事实
async function loadFacts() {
  try {
    const res = await evidenceFactsApi.get(props.caseImportId)
    facts.value = res
    // undisputedText.value = res?.undisputedFacts || '' // 暂时注释
    disputedText.value = res?.controversialFacts || ''
    // judgeText.value = res?.dictum || '' // 暂时注释

    // 认定事实显示逻辑：优先显示AI生成的内容，如果为空则显示从文书提取的内容
    if (res?.determineFacts) {
      finalText.value = res.determineFacts
      isShowingAIGenerated.value = true
    } else if (res?.determineFactsExtract) {
      finalText.value = res.determineFactsExtract
      isShowingAIGenerated.value = false
    } else {
      finalText.value = ''
      isShowingAIGenerated.value = false
    }

    // 更新原始值，用于比较是否有变动
    originalValues.value = {
      undisputedText: undisputedText.value,
      judgeText: judgeText.value,
      finalText: finalText.value
    }
  } catch (e) {
    // 移除错误弹窗，静默处理
    console.warn('证据事实加载失败:', e)
  }
}

// 保存数据
async function saveFacts() {
  try {
    const payload: EvidenceFactsDetails = {
      id: facts.value?.id,
      caseImportId: props.caseImportId,
      undisputedFacts: undisputedText.value,
      controversialFacts: disputedText.value,
      dictum: judgeText.value,
      // 根据当前显示的内容类型保存到对应字段
      determineFacts: isShowingAIGenerated.value ? finalText.value : (facts.value?.determineFacts || ''),
      determineFactsExtract: !isShowingAIGenerated.value ? finalText.value : (facts.value?.determineFactsExtract || '')
    };
    await evidenceFactsApi.saveOrUpdate(payload);
    ElMessage.success('保存成功');
    await loadFacts();
    
    // 保存成功后更新原始值
    originalValues.value = {
      undisputedText: undisputedText.value,
      judgeText: judgeText.value,
      finalText: finalText.value
    }
  } catch (e) {
    ElMessage.error('保存失败');
  }
}

// 刷新证据事实
async function refreshFacts() {
  await loadFacts()
  ElMessage.success('刷新成功')
}



// 防抖保存函数
const debouncedSave = () => {
  if (saveTimer) {
    clearTimeout(saveTimer)
  }
  
  saveTimer = window.setTimeout(async () => {
    if (saving.value) return // 如果正在保存中，跳过
    
    saving.value = true
    try {
      await saveFacts()
    } catch (error) {
      console.error('自动保存失败:', error)
    } finally {
      saving.value = false
    }
  }, 1000) // 1秒防抖
}

// async function saveUndisputedFact() {
//   // 检查内容是否有变动
//   if (undisputedText.value !== originalValues.value.undisputedText) {
//     debouncedSave()
//   }
// }

// async function saveJudgeFact() {
//   // 检查内容是否有变动
//   if (judgeText.value !== originalValues.value.judgeText) {
//     debouncedSave()
//   }
// }

async function saveFinalFact() {
  // 检查内容是否有变动
  if (finalText.value !== originalValues.value.finalText) {
    debouncedSave()
  }
}

// 刷新按钮调用刷新
function refreshBox(type: 'undisputed' | 'disputed' | 'judge' | 'final') {
  refreshFacts()
}

// 生成无争议事实 - 暂时注释
// const generateUndisputedFacts = () => {
//   if (!props.caseImportId) {
//     ElMessage.warning('案件ID不存在，无法生成无争议事实')
//     return
//   }

//   // 检查是否正在生成中
//   if (undisputedFactsGenerating.value) {
//     ElMessage.warning('无争议事实正在生成中，请稍后再试')
//     return
//   }

//   // 显示确认提示弹框
//   ElMessageBox.confirm(
//     '确定要重新生成无争议事实吗？这将覆盖当前内容。',
//     '重新生成确认',
//     {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning'
//     }
//   ).then(async () => {
//     // 用户确认后执行生成
//     undisputedFactsGenerating.value = true
//     try {
//       // 启动生成任务
//       await evidenceFactsApi.generateUndisputedFacts(props.caseImportId)

//       // 如果成功启动，立即清空前端内容，但不更新原始值（如果失败，用户还能看到之前的内容）
//       undisputedText.value = ''

//       ElMessage.success('无争议事实生成任务已启动')

//       // 通知 TaskGeneratingStatus 组件开始监控任务状态
//       // undisputedFactsTaskRef.value?.startTaskExecution()
//       // setTimeout(() => {
//       //   undisputedFactsTaskRef.value?.startTaskMonitoring()
//       // }, 2000)

//     } catch (error) {
//       console.error('重新生成无争议事实失败:', error)
//       ElMessage.error('生成无争议事实失败，请稍后重试')
//     } finally {
//       undisputedFactsGenerating.value = false
//     }
//   }).catch(() => {
//     // 用户取消，不做任何操作
//     console.log('用户取消了重新生成操作')
//   })
// }

// 根据文本内容长度计算动态行数
function getDynamicRows(text: string): number {
  if (!text || text.trim() === '') {
    return 3 // 最小行数
  }
  
  // 计算文本长度和换行符数量
  const textLength = text.length
  const lineBreaks = (text.match(/\n/g) || []).length
  
  // 根据文本长度和换行符计算所需行数
  // 假设每行平均40个字符
  const estimatedLines = Math.ceil(textLength / 40) + lineBreaks
  
  // 限制在合理范围内
  return Math.max(3, Math.min(20, estimatedLines))
}

// 组件挂载时初始化
onMounted(async () => {
  await loadFacts()
  // 检查证据情况任务是否正在运行
  await checkInitialTaskStatus()
  console.log('证据事实页面已加载')
})

// 组件销毁前清理定时器
onBeforeUnmount(() => {
  clearEvidenceTaskTimer()
})

// ==================== 认定事实相关方法 ====================

// 处理认定事实下拉菜单命令
const handleDetermineFactsCommand = (command: string) => {
  // 发布红点重置事件，传递证据情况模块作为源模块，认定事实作为操作的模块
  eventBus.emit('red-dot:reset', {
    caseImportId: Number(props.caseImportId),
    sourceModule: 'EVIDENCE_OVERVIEW',
    operatedModule: 'DETERMINE_FACTS'
  })
  
  if (command === 'confirmFirstInstance') {
    confirmFirstInstanceFacts()
  } else if (command === 'generateSecondInstance') {
    generateDetermineFacts()
  }
}

// 确认一审认定事实
const confirmFirstInstanceFacts = async () => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '确认一审认定事实将设置固定内容，是否继续？',
      '确认一审认定事实',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    // 设置固定文本
    finalText.value = '本院审理，一审查明的事实清楚，本院依法予以确认。'
    isShowingAIGenerated.value = true

    // 保存到后端
    await saveFacts()

    ElMessage.success('一审认定事实确认成功')
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('确认一审认定事实失败:', error)
    ElMessage.error('确认一审认定事实失败')
  }
}

// 生成认定事实
async function generateDetermineFacts() {
  try {
    // 检查是否正在生成中
    if (determineFactsGenerating.value) {
      ElMessage.warning('认定事实正在生成中，请稍后再试')
      return
    }

    // 显示确认对话框
    await ElMessageBox.confirm(
      '重新生成将清空当前内容，是否继续？',
      '确认重新生成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    determineFactsGenerating.value = true

    // 清空当前内容，但不更新原始值（如果失败，用户还能看到之前的内容）
    finalText.value = ''
    isShowingAIGenerated.value = true

    await evidenceFactsApi.generateDetermineFacts(props.caseImportId)
    ElMessage.success('认定事实生成任务已启动，请稍后查看结果')

    // 通知 TaskGeneratingStatus 组件开始监控任务状态
    determineFactsTaskRef.value?.startTaskExecution()
    setTimeout(() => {
      determineFactsTaskRef.value?.startTaskMonitoring()
    }, 2000)
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('生成认定事实失败:', error)
  } finally {
    determineFactsGenerating.value = false
  }
}

// TaskGeneratingStatus 事件处理方法
// const handleUndisputedFactsTaskCompleted = () => {
//   ElMessage.success('无争议事实生成完成')
//   // 重新加载数据
//   loadFacts()
// }

// const handleUndisputedFactsTaskFailed = (event: any) => {
//   ElMessage.error(`无争议事实生成失败: ${event.errorMessage}`)
// }

const handleDetermineFactsTaskCompleted = () => {
  ElMessage.success('认定事实生成完成')
  // 重新加载数据
  loadFacts()
}

const handleDetermineFactsTaskFailed = (event: any) => {
  //内容框里有报错信息，不需要弹窗提醒了
  // ElMessage.error(`认定事实生成失败: ${event.errorMessage}`)
}







// 重新生成证据事实分析（供父组件调用）
const regenerateAnalysis = async () => {
  try {
    // 重新生成无争议事实 - 暂时注释
    // await generateUndisputedFacts()

    // 认定事实现在不依赖无争议事实和法官意见，用户可以手动生成
    console.log('证据事实重新生成功能暂时禁用')
    ElMessage.info('证据事实重新生成功能暂时禁用')
  } catch (error) {
    console.error('重新生成证据事实失败:', error)
    ElMessage.error('重新生成证据事实失败')
  }
}

// 清空数据（供父组件调用）
const clearData = () => {
  // undisputedText.value = '' // 暂时注释
  disputedText.value = ''
  finalText.value = ''
  // judgeText.value = '' // 暂时注释
  isShowingAIGenerated.value = false

  // 同步更新原始值
  originalValues.value = {
    undisputedText: '', // 保留原始值结构
    judgeText: '', // 保留原始值结构
    finalText: ''
  }
}

// 暴露方法供父组件调用
defineExpose({
  refreshVerification: handleRefreshVerification,
  regenerateEvidenceOverview: handleRegenerateEvidenceOverview,
  regenerateAnalysis,
  clearData,
})
</script>

<style scoped>
.evidence-facts-container {
  padding: 12px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;

  > * {
    flex-shrink: 0;
  }
}

.evidence-section {
  background: #fff;
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.evidence-section:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #ebeef5;
  position: relative;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
    padding-left: 10px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 6px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 14px;
      background: linear-gradient(to bottom, #67c23a, #e6a23c);
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .el-icon {
      font-size: 16px;
      margin-left: 6px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .status-display {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;

    .status-processing {
      color: #409eff;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .status-success {
      color: #67c23a;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .status-failed {
      color: #f56c6c;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .rotating {
      animation: rotate 1s linear infinite;
    }
  }
}

.section-actions {
  display: flex;
  gap: 6px;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 4px;

  .el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px) scale(1.05);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
    }

    .el-icon {
      font-size: 12px;
    }

    /* 圆形按钮的特殊样式 */
    &.is-circle {
      width: 24px;
      height: 24px;
      padding: 0;
      border: none;
      color: white;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.5s;
      }

      &:hover::before {
        left: 100%;
      }
    }
  }
}



.section-actions .el-button .el-icon {
  font-size: 12px;
  margin-right: 3px;
}

.section-content {
  padding: 7px 8px 8px 7px;
  flex: 1;

  .el-input {
    width: 100%;

    :deep(.el-textarea__inner) {
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;
      min-height: 200px;
      max-height: 500px;
      overflow-y: auto;

      &:hover {
        border-color: #c0c4cc;
      }

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

/* 质证情况区域特殊样式 */
.evidence-section:first-child .section-content {
  padding: 0;
  min-height: 400px;
}

/* 证据情况区域特殊样式 */
.evidence-section:nth-child(2) .section-content {
  padding: 0;
  min-height: 300px;
}

.loading-state {
  padding: 16px;
}

.empty-state {
  padding: 32px 16px;
  text-align: center;
}

.facts-list {
  padding: 16px;
}

.fact-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  margin-bottom: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
  transition: all 0.2s ease;
}

.fact-item.disputed {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.fact-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.fact-number {
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  background: #67c23a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
}

.fact-item.disputed .fact-number {
  background: #e6a23c;
}

.fact-content {
  flex: 1;
}

.fact-text {
  margin: 0 0 8px 0;
  line-height: 1.5;
  color: #303133;
  font-size: 13px;
}

.dispute-reason {
  margin-bottom: 8px;
  padding: 6px 10px;
  background: rgba(230, 162, 60, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.reason-label {
  color: #e6a23c;
  font-weight: 600;
}

.reason-text {
  color: #606266;
}

.fact-evidence {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.evidence-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
}

.evidence-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .evidence-facts-container {
    padding: 8px;
    gap: 6px;
  }
  .section-title {
    font-size: 13px;
  }
  .section-actions .el-button {
    padding: 3px 6px;
    font-size: 11px;
  }
  .fact-text {
    font-size: 12px;
  }
  .dispute-reason {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .evidence-facts-container {
    padding: 6px;
    gap: 4px;
  }
  
  .section-header {
    padding: 6px 10px;
  }

  .section-title {
    font-size: 11px;
  }

  .section-actions .el-button {
    padding: 2px 3px;
    font-size: 9px;
  }
  
  .facts-list {
    padding: 8px;
  }
  
  .fact-item {
    padding: 8px;
    margin-bottom: 8px;
  }

  .fact-text {
    font-size: 10px;
  }
  
  .dispute-reason {
    font-size: 9px;
    padding: 3px 6px;
  }

  .evidence-tag {
    font-size: 9px;
  }
}



.section-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 4px 7px 0px 7px;
  min-height: 24px;
}

.ai-warning-text {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 500;
  margin: 0;
}

.saving-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  
  .el-icon {
    font-size: 14px;
  }
  
  .is-loading {
    animation: rotate 1s linear infinite;
  }
}
.undisputed-section-content,
.disputed-section-content,
.final-section-content {
  position: relative;
}

/* 状态显示样式 - 与争议焦点说理保持一致 */
.status-display {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
}

.status-display .status-processing {
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-display .status-success {
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-display .status-failed {
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-display .rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

/* 修复 el-button loading 图标在圆形按钮中未居中问题 */
.el-button.is-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.el-button.is-loading .el-icon.is-loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
}
</style> 