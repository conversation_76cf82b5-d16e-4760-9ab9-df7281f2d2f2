<template>
  <div class="dispute-focus">
    <div class="section-header">
      <h3>
        争议焦点
        <span v-if="lawProgress.total > 0 && lawProgress.completed < lawProgress.total" class="law-progress">
          <i
            :class="[
              'law-progress-icon',
              lawProgress.completed >= lawProgress.total ? 'completed' : 'loading'
            ]"
          ></i>
          智推法条中 {{ lawProgress.completed }}/{{ lawProgress.total }}
        </span>
      </h3>
      <div class="header-actions">
        <el-button
          v-if="!showAddForm"
          type="primary"
          circle
          @click="showAddFocusForm"
          title="添加争议焦点"
        >
          <el-icon><Plus /></el-icon>
        </el-button>
        <el-button
          v-else
          type="info"
          circle
          @click="cancelAddFocus"
          title="取消添加"
        >
          <el-icon><Close /></el-icon>
        </el-button>
        <el-button type="warning" circle @click="$emit('regenerate-all-focus')" title="重新生成">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="focus-list">
      <!-- 任务生成状态组件容器 -->
      <div v-show="isTaskRunning" class="task-status-container">
        <TaskGeneratingStatus
          ref="taskGeneratingRef"
          :case-import-id="caseImportId"
          :task-type="TaskType.DISPUTE_FOCUS"
          :overlay="false"
          :position="'absolute'"
          :show-progress="true"
          :show-estimated-time="true"
          @task-started="isTaskRunning = true"
          @task-completed="handleTaskCompleted"
          @task-failed="handleTaskFailed"
        />
      </div>
      <!-- 新增争议焦点表单 -->
      <div v-if="showAddForm" class="focus-item">
        <div class="focus-card add-form-card">
          <!-- 争议焦点问题 -->
          <div class="focus-question-section">
            <div class="question-row">
              <span class="question-number">{{ focusList.length + 1 }}.</span>
              <div class="question-input">
                <el-input
                  v-model="newFocus.description"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 5 }"
                  placeholder="请输入争议焦点问题..."
                  @keyup.enter="saveNewFocus"
                />
              </div>
              <div class="question-actions">
                <el-button type="success" @click="saveNewFocus" title="保存" size="small" circle>
                  <el-icon><Check /></el-icon>
                </el-button>
                <el-button type="info" @click="cancelAddFocus" title="取消" size="small" circle>
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 结论选择 -->
          <div class="conclusion-section">
            <div class="conclusion-row">
              <div class="conclusion-label">结论</div>
              <div class="conclusion-options">
                <div class="conclusion-buttons">
                  <el-radio-group v-model="newFocus.conclusion" class="conclusion-radio-group">
                    <el-radio-button value="yes" class="conclusion-radio-btn success">是</el-radio-button>
                    <el-radio-button value="no" class="conclusion-radio-btn danger">否</el-radio-button>
                    <el-radio-button value="other" class="conclusion-radio-btn warning">其他</el-radio-button>
                  </el-radio-group>

                  <transition name="fade">
                    <el-input
                      v-if="newFocus.conclusion === 'other'"
                      v-model="newFocus.otherContent"
                      type="textarea"
                      :autosize="{ minRows: 1, maxRows: 3 }"
                      placeholder="请输入具体说明..."
                      size="default"
                      clearable
                      class="other-input"
                      maxlength="500"
                    />
                  </transition>
                </div>
              </div>
            </div>
          </div>

          <!-- 相关法条部分 -->
          <div class="laws-section">
            <legal-provision-selector
              v-model="newFocus.legalProvisions"
            />
          </div>
        </div>
      </div>

      <!-- 现有争议焦点列表 -->
      <div v-for="(focus, index) in focusList" :key="`focus-${index}`" class="focus-item">
        <!-- 争议焦点卡片 -->
        <div class="focus-card">
          <!-- 争议焦点问题 -->
          <div class="focus-question-section">
            <div class="question-row">
              <span class="question-number">{{ index + 1 }}.</span>
              <div class="question-input">
                <el-input
                  :model-value="(unsavedChanges[focus.id.toString()]?.description ?? focus.description) || ''"
                  @update:model-value="updateFocusDescription(focus, $event)"
                  @blur="autoSaveFocusChanges(focus)"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 5 }"
                  placeholder="请输入争议焦点问题..."
                />
              </div>
              <div class="question-actions">
                <el-button type="danger" size="small" circle @click="$emit('delete-focus', focus)" title="删除">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 结论部分 -->
          <div class="conclusion-section">
            <div class="conclusion-row">
              <div class="conclusion-label">结论</div>
              <div class="conclusion-options">
                <div class="conclusion-buttons">
                  <el-radio-group
                    :model-value="getConclusionRadioValue(focus)"
                    @update:model-value="updateFocusConclusion(focus, $event)"
                    @change="handleConclusionChange(focus, $event)"
                    class="conclusion-radio-group"
                  >
                    <el-radio-button value="yes" class="conclusion-radio-btn success">是</el-radio-button>
                    <el-radio-button value="no" class="conclusion-radio-btn danger">否</el-radio-button>
                    <el-radio-button value="other" class="conclusion-radio-btn warning">其他</el-radio-button>
                  </el-radio-group>
                  <!-- 其他说明输入框 -->
                  <transition name="fade">
                    <el-input
                      v-if="getConclusionRadioValue(focus) === 'other'"
                      :model-value="getConclusionOtherValue(focus)"
                      @update:model-value="updateFocusOtherContent(focus, $event)"
                      @blur="autoSaveFocusChanges(focus)"
                      type="textarea"
                      :autosize="{ minRows: 2, maxRows: 6 }"
                      placeholder="请输入具体说明..."
                      size="default"
                      clearable
                      class="other-input"
                      maxlength="500"
                    />
                  </transition>
                  <!-- 移除结论部分的保存按钮，统一使用描述部分的保存按钮 -->
                </div>
              </div>
            </div>
          </div>

          <!-- 相关法条部分 -->
          <div class="laws-section">
            <legal-provision-selector
              :model-value="getLegalProvisionsValue(focus)"
              @update:model-value="updateFocusLegalProvisions(focus, $event); autoSaveFocusChanges(focus)"
            />
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Plus, Delete, Refresh, Check, Close } from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import type { LegalProvisionRecord } from '@/api/legalProvision'
import LegalProvisionSelector from '@/components/LegalProvisionSelector/LegalProvisionSelector.vue'
import { disputeFocusApi, type DisputeFocus as ApiDisputeFocus } from '@/api/disputeFocus'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'
import { analysisTaskApi, type AgentTaskInfo, type AgentSubTaskResult } from '@/api/analysisTask'
import { TaskStatus } from '@/api/taskStatus'

// 直接使用 API 接口定义的类型，扩展一些前端需要的字段
interface DisputeFocus extends ApiDisputeFocus {
  // 前端扩展字段
  otherExplanation?: string;
  legalProvisions?: LegalProvisionRecord[];
  // 兼容旧数据格式
  plaintiffClaim?: string;
  defendantClaim?: string;
  laws?: string[] | null;
  lawSummaries?: string[] | null;
  plaintiffEvidence?: any[];
  defendantEvidence?: any[];
}

const props = defineProps<{
  focusList: DisputeFocus[];
  caseImportId: number;
}>()

const emit = defineEmits<{
  'add-focus': [focus: DisputeFocus];
  'regenerate-all-focus': [];
  'delete-all-focus': [];
  'edit-focus': [focus: DisputeFocus];
  'delete-focus': [focus: DisputeFocus];
  'update-focus': [focus: DisputeFocus];
  'task-completed': [];
}>()

// 组件引用
const taskGeneratingRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null)

// 控制任务生成状态组件的显示
const isTaskRunning = ref(false)

// 智推法条进度状态
const lawProgress = ref({
  total: 0,
  completed: 0
})

// 智推法条轮询定时器
const lawProgressTimer = ref<NodeJS.Timeout | null>(null)

// 任务完成处理
const handleTaskCompleted = () => {
  ElMessage.success('争议焦点分析完成')
  // 隐藏任务状态组件
  isTaskRunning.value = false
  // 开始轮询智推法条进度
  startLawProgressPolling()
  // 通知父组件任务完成，让父组件刷新数据
  emit('task-completed')
}

// 任务失败处理
const handleTaskFailed = (event: any) => {
  ElMessage.error(`争议焦点分析失败: ${event.errorMessage}`)
  // 隐藏任务状态组件
  isTaskRunning.value = false
}

// 获取结论标签类型
const getConclusionTagType = (conclusion?: string) => {
  switch (conclusion) {
    case 'yes':
      return 'success'
    case 'no':
      return 'danger'
    default:
      return conclusion ? 'warning' : 'info'
  }
}

// 通用的字段更新函数
const updateFocusField = (focus: DisputeFocus, field: string, value: any) => {
  const focusKey = focus.id.toString() // 统一使用字符串作为 key
  if (!unsavedChanges.value[focusKey]) {
    unsavedChanges.value[focusKey] = { ...focus }
  }
  unsavedChanges.value[focusKey][field] = value
}

// 更新争议焦点的描述（暂存到未保存更改中）
const updateFocusDescription = (focus: DisputeFocus, description: string) => {
  updateFocusField(focus, 'description', description)
}

// 获取结论的单选按钮值（直接使用接口数据）
const getConclusionRadioValue = (focus: DisputeFocus): string => {
  const currentConclusion = unsavedChanges.value[focus.id.toString()]?.conclusion ?? focus.conclusion

  // 直接返回接口的值，如果为空则返回空字符串
  if (currentConclusion === 'yes' || currentConclusion === 'no') {
    return currentConclusion
  }

  // 如果是 other_placeholder 或者有其他内容，都视为"其他"
  if (currentConclusion === 'other_placeholder' || (currentConclusion && currentConclusion !== 'yes' && currentConclusion !== 'no')) {
    return 'other'
  }

  return ''
}

// 获取"其他"选项的具体内容
const getConclusionOtherValue = (focus: DisputeFocus): string => {
  const currentConclusion = unsavedChanges.value[focus.id.toString()]?.conclusion ?? focus.conclusion

  // 如果是 other_placeholder，返回空字符串（显示占位符）
  if (currentConclusion === 'other_placeholder') {
    return ''
  }

  // 如果不是标准值，返回具体内容
  if (currentConclusion && currentConclusion !== 'yes' && currentConclusion !== 'no') {
    return currentConclusion
  }
  return ''
}

// 更新争议焦点的结论选择（暂存到未保存更改中）
const updateFocusConclusion = (focus: DisputeFocus, radioValue: string | number | boolean) => {
  const stringValue = String(radioValue) // 确保转换为字符串类型
  if (stringValue === 'yes' || stringValue === 'no') {
    // 直接保存选择的值
    updateFocusField(focus, 'conclusion', stringValue)
  } else if (stringValue === 'other') {
    // 如果选择"其他"，需要确保有一个值来触发输入框显示
    const currentConclusion = unsavedChanges.value[focus.id.toString()]?.conclusion ?? focus.conclusion
    if (currentConclusion === 'yes' || currentConclusion === 'no' || !currentConclusion) {
      // 设置一个特殊标记，表示用户选择了"其他"但还没有输入具体内容
      updateFocusField(focus, 'conclusion', 'other_placeholder')
    }
    // 如果已经有具体内容，保持不变
  }
}

// 处理结论选择变化
const handleConclusionChange = (focus: DisputeFocus, value: string | number | boolean) => {
  const stringValue = String(value)

  // 如果选择的是"是"或"否"，立即自动保存
  if (stringValue === 'yes' || stringValue === 'no') {
    autoSaveFocusChanges(focus)
  }
  // 如果选择的是"其他"，不自动保存，等待用户在输入框中输入内容后失焦时保存
}

// 更新"其他"选项的具体内容（暂存到未保存更改中）
const updateFocusOtherContent = (focus: DisputeFocus, otherContent: string) => {
  // 如果用户输入了内容，保存实际内容；如果清空了，保持特殊标记以显示输入框
  if (otherContent.trim()) {
    updateFocusField(focus, 'conclusion', otherContent)
  } else {
    updateFocusField(focus, 'conclusion', 'other_placeholder')
  }
}

// 更新争议焦点的法条（暂存到未保存更改中）
const updateFocusLegalProvisions = (focus: DisputeFocus, provisions: LegalProvisionRecord[]) => {
  // 使用统一的字段更新函数
  updateFocusField(focus, 'legalProvisions', provisions)

  // 同时更新旧格式以保持兼容性
  const laws = provisions.map(provision => provision.provisionNumber)
  const lawSummaries = provisions.map(provision => provision.content)

  updateFocusField(focus, 'laws', laws)
  updateFocusField(focus, 'lawSummaries', lawSummaries)
}

// 新增争议焦点相关状态
const showAddForm = ref(false)
const newFocus = ref({
  id: '',
  description: '',
  conclusion: '',
  otherContent: '', // 当选择"其他"时的具体内容
  legalProvisions: [] as LegalProvisionRecord[]
})

// 跟踪未保存的更改
const unsavedChanges = ref<Record<string, any>>({})

// 数据转换函数：组件数据转换为API数据
const convertToApiData = (focus: any) => {
  return {
    description: focus.description,
    conclusion: focus.conclusion === 'other_placeholder' ? '' : focus.conclusion,
    laws: focus.laws || focus.legalProvisions?.map((p: LegalProvisionRecord) => p.provisionNumber) || [],
    lawSummaries: focus.lawSummaries || focus.legalProvisions?.map((p: LegalProvisionRecord) => p.content) || []
  }
}

// 创建一个计算属性来实时检测每个争议焦点的真实变更状态
const focusChangeStatus = computed(() => {
  const status: Record<string, boolean> = {}

  Object.keys(unsavedChanges.value).forEach(focusId => {
    const focus = props.focusList.find(f => f.id.toString() === focusId)
    if (focus) {
      status[focusId] = hasRealChanges(focus, unsavedChanges.value[focusId])
    }
  })

  return status
})

// 检测是否有真实的变更（不包括复原到原始值的情况）
const hasRealChanges = (focus: DisputeFocus, changes: any): boolean => {
  if (!changes) return false

  // 检测描述是否真的有变更
  const hasDescriptionChanged = changes.description !== undefined && changes.description !== focus.description

  // 检测结论是否真的有变更
  const hasConclusionChanged = changes.conclusion !== undefined && changes.conclusion !== focus.conclusion

  // 检测法条是否真的有变更
  const hasLegalChanged = hasLegalProvisionsChanged(changes, focus)

  return hasDescriptionChanged || hasConclusionChanged || hasLegalChanged
}

// 重置新增表单
const resetNewFocus = () => {
  newFocus.value = {
    id: '',
    description: '',
    conclusion: '',
    otherContent: '',
    legalProvisions: []
  }
}

// 显示新增表单
const showAddFocusForm = () => {
  resetNewFocus()
  showAddForm.value = true
}

// 取消新增
const cancelAddFocus = () => {
  showAddForm.value = false
  resetNewFocus()
}

// 保存新增争议焦点
const saveNewFocus = async () => {
  if (!newFocus.value.description.trim()) {
    ElMessage.warning('请输入争议焦点问题')
    return
  }

  try {
    // 检查 caseImportId 是否有效
    if (!props.caseImportId || props.caseImportId === 0) {
      ElMessage.error('案件ID无效，无法保存争议焦点')
      return
    }

    // 根据选择决定保存的结论内容
    let finalConclusion = newFocus.value.conclusion
    if (newFocus.value.conclusion === 'other' && newFocus.value.otherContent.trim()) {
      finalConclusion = newFocus.value.otherContent.trim()
    } else if (newFocus.value.conclusion === 'other_placeholder') {
      finalConclusion = ''
    }

    // 准备新增数据 - 构造完整的 DisputeFocus 对象
    const createData = {
      id: null, // 新增时ID为null
      title: newFocus.value.description, // API可能需要title字段
      description: newFocus.value.description,
      category: '', // 默认分类
      caseImportId: props.caseImportId,
      conclusion: finalConclusion,
      createTime: '',
      updateTime: '',
      ...convertToApiData({
        ...newFocus.value,
        conclusion: finalConclusion
      })
    }

    // 调用统一的保存API（后端会根据ID判断新增还是更新）
    const savedFocus = await disputeFocusApi.save(createData)

    // 发送事件通知父组件刷新数据
    emit('add-focus', savedFocus)

    showAddForm.value = false
    resetNewFocus()

    // 显示新增成功提示
    ElMessage.success('争议焦点添加成功')
  } catch (error) {
    console.error('保存争议焦点失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 获取法条的当前值（优先使用未保存的更改）
const getLegalProvisionsValue = (focus: DisputeFocus): LegalProvisionRecord[] => {
  const changes = unsavedChanges.value[focus.id.toString()]
  if (changes && changes.legalProvisions) {
    return changes.legalProvisions
  }
  return convertToLegalProvisions(focus)
}

// 将新数据格式转换为 LegalProvisionRecord 格式
const convertToLegalProvisions = (focus: DisputeFocus): LegalProvisionRecord[] => {
  // 只使用原始数据，避免循环引用
  if (!focus.laws || !focus.lawSummaries) {
    return []
  }

  return focus.laws.map((law, index) => ({
    id: index + 1, // 临时ID，因为新格式没有ID
    provisionNumber: law,
    content: focus.lawSummaries?.[index] || ''
  }))
}



// 检测法条是否发生变更
const hasLegalProvisionsChanged = (changes: any, original: DisputeFocus): boolean => {
  // 如果 changes 中没有 legalProvisions 字段，说明没有修改过法条
  if (!changes.hasOwnProperty('legalProvisions')) {
    return false
  }

  const changedProvisions = changes.legalProvisions || []
  const originalProvisions = convertToLegalProvisions(original) || []

  // 比较数组长度
  if (changedProvisions.length !== originalProvisions.length) {
    return true
  }

  // 深度比较每个法条
  return changedProvisions.some((changed: any, index: number) => {
    const orig = originalProvisions[index]
    return (
      changed.provisionNumber !== orig?.provisionNumber ||
      changed.content !== orig?.content
    )
  })
}

// 统一的变更检测函数
const hasAnyUnsavedChanges = (focus: DisputeFocus): boolean => {
  return focusChangeStatus.value[focus.id.toString()] || false
}

// 检查是否有未保存的更改（保持向后兼容）
const hasUnsavedChanges = (focus: DisputeFocus): boolean => {
  return hasAnyUnsavedChanges(focus)
}

// 保存争议焦点的更改
const saveFocusChanges = async (focus: DisputeFocus, isAutoSave = false) => {
  const changes = unsavedChanges.value[focus.id.toString()]
  if (!changes) return

  try {
    // 处理特殊标记：如果结论是 'other_placeholder'，转换为空字符串
    const processedChanges = { ...changes }
    if (processedChanges.conclusion === 'other_placeholder') {
      processedChanges.conclusion = ''
    }

    // 准备更新数据 - 构造完整的 DisputeFocus 对象
    const updateData = {
      id: Number(focus.id), // 更新时必须传ID
      title: processedChanges.description || focus.description,
      description: processedChanges.description || focus.description,
      category: focus.category || '',
      caseImportId: focus.caseImportId, // 添加必需的 caseImportId 字段
      conclusion: processedChanges.conclusion,
      createTime: focus.createTime || '',
      updateTime: focus.updateTime || '',
      ...convertToApiData(processedChanges)
    }

    // 调用统一的保存API（后端会根据ID判断新增还是更新）
    const updatedFocus = await disputeFocusApi.save(updateData)

    // 更新本地数据：将保存的更改应用到原始的 focus 对象
    Object.assign(focus, {
      description: processedChanges.description || focus.description,
      conclusion: processedChanges.conclusion !== undefined ? processedChanges.conclusion : focus.conclusion,
      laws: processedChanges.laws || focus.laws,
      lawSummaries: processedChanges.lawSummaries || focus.lawSummaries,
      legalProvisions: processedChanges.legalProvisions || focus.legalProvisions
    })

    // 发送更新事件通知父组件刷新数据
    emit('update-focus', updatedFocus)

    // 清除未保存的更改（在更新本地数据之后）
    delete unsavedChanges.value[focus.id.toString()]

    // 根据保存类型显示不同的提示
    if (isAutoSave) {
      // 自动保存显示右上角通知，更不干扰用户
      ElNotification({
        title: '自动保存',
        message: '争议焦点已自动保存',
        type: 'success',
        duration: 2000,
        position: 'top-right'
      })
    } else {
      // 手动保存显示标准提示
      ElMessage.success('保存成功')
    }
  } catch (error) {
    console.error('保存争议焦点失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 自动保存争议焦点的更改（防抖处理）
const autoSaveTimers = ref<Record<string, NodeJS.Timeout>>({})

const autoSaveFocusChanges = (focus: DisputeFocus) => {
  const focusId = focus.id.toString()

  // 清除之前的定时器
  if (autoSaveTimers.value[focusId]) {
    clearTimeout(autoSaveTimers.value[focusId])
  }

  // 设置新的定时器，500ms后自动保存
  autoSaveTimers.value[focusId] = setTimeout(async () => {
    const changes = unsavedChanges.value[focusId]
    if (changes) {
      console.log('自动保存争议焦点:', focusId, changes)
      await saveFocusChanges(focus, true) // 传递 isAutoSave = true
    }
    delete autoSaveTimers.value[focusId]
  }, 500)
}

// 更新智推法条进度
const updateLawProgress = async () => {
  try {
    // 直接从争议焦点数据获取法条进度
    const progress = await disputeFocusApi.getLawProgress(props.caseImportId)

    const previousCompleted = lawProgress.value.completed

    lawProgress.value = {
      total: progress.total,
      completed: progress.completed
    }

    console.log('智推法条进度更新:', {
      total: lawProgress.value.total,
      completed: lawProgress.value.completed,
      previousCompleted
    })

    // 如果法条数有更新，重新查询争议焦点列表
    if (progress.completed > previousCompleted) {
      console.log('法条数更新，重新查询争议焦点列表')
      emit('refresh-focus-list')
    }

    // 如果所有争议焦点都完成了智推法条，停止轮询
    if (progress.completed >= progress.total && progress.completed > 0) {
      console.log('所有争议焦点智推法条已完成，停止轮询')
      stopLawProgressPolling()
    }

    return progress.completed
  } catch (error) {
    console.error('获取智推法条进度失败:', error)
    // 发生错误时重置进度为0
    lawProgress.value = {
      total: props.focusList.length,
      completed: 0
    }
    return 0
  }
}

// 开始智推法条进度轮询
const startLawProgressPolling = () => {
  console.log('开始智推法条进度轮询')
  // 先清除之前的定时器
  stopLawProgressPolling()

  // 立即执行一次
  updateLawProgress()

  // 每3秒轮询一次
  lawProgressTimer.value = setInterval(async () => {
    await updateLawProgress()
  }, 3000)
}

// 停止智推法条进度轮询
const stopLawProgressPolling = () => {
  if (lawProgressTimer.value) {
    console.log('停止智推法条进度轮询')
    clearInterval(lawProgressTimer.value)
    lawProgressTimer.value = null
  }
}

// 手动开始显示任务状态（供父组件调用）
const startTaskGenerating = () => {
  isTaskRunning.value = true
}

// 暴露方法给父组件
defineExpose({
  taskGeneratingRef,
  startTaskGenerating
})

// 组件卸载时清理定时器
onUnmounted(() => {
  // 清理所有自动保存定时器
  Object.values(autoSaveTimers.value).forEach(timer => {
    if (timer) {
      clearTimeout(timer)
    }
  })
  autoSaveTimers.value = {}
})

// 监听争议焦点列表变化，更新进度总数
watch(() => props.focusList, async (newList, oldList) => {
  lawProgress.value.total = newList.length
  // 当争议焦点列表变化时，重新获取进度
  await updateLawProgress()

  // 如果争议焦点列表从空变为有内容，检查是否需要开始轮询
  if (oldList && oldList.length === 0 && newList.length > 0) {
    console.log('争议焦点列表已加载，检查是否需要开始智推法条轮询')
    await checkAndStartLawProgressPolling()
  }
}, { immediate: true })

// 组件挂载时初始化进度并检查是否需要开始轮询
onMounted(async () => {
  await updateLawProgress()
  // 检查是否需要开始智推法条轮询
  await checkAndStartLawProgressPolling()
})

// 检查并开始智推法条轮询
const checkAndStartLawProgressPolling = async () => {
  try {
    // 如果有争议焦点，检查法条完成情况
    if (props.focusList.length > 0) {
      // 获取当前法条进度
      const progress = await disputeFocusApi.getLawProgress(props.caseImportId)

      // 如果还没有完成所有智推法条，开始轮询
      if (progress.completed < progress.total) {
        console.log('检测到智推法条未完成，开始轮询', {
          completed: progress.completed,
          total: progress.total
        })
        startLawProgressPolling()
      } else {
        console.log('智推法条已全部完成，无需轮询', {
          completed: progress.completed,
          total: progress.total
        })
      }
    } else {
      console.log('无争议焦点，无需轮询')
    }
  } catch (error) {
    console.error('检查智推法条轮询状态失败:', error)
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopLawProgressPolling()
})

</script>

<style scoped lang="scss">
.dispute-focus {
  background-color: #fff;
  border-radius: 10px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  // 移除 flex: 1 和 min-height，让高度自适应内容
  // 移除 overflow: hidden
  overflow: visible; // 确保内容不被裁切

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
    position: relative;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      position: relative;
      display: flex;
      align-items: center;
      gap: 8px;

      .law-progress {
        font-size: 12px;
        color: #909399;
        font-weight: normal;
        background: #f0f2f5;
        padding: 2px 8px;
        border-radius: 12px;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 4px;

        .law-progress-icon {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          display: inline-block;

          &.loading {
            border: 2px solid #e4e7ed;
            border-top: 2px solid #409eff;
            animation: law-progress-spin 1s linear infinite;
          }

          &.completed {
            background: #67c23a;
            position: relative;

            &::after {
              content: '✓';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-size: 8px;
              font-weight: bold;
            }
          }
        }
      }

      @keyframes law-progress-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #67c23a, #e6a23c);
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }

    .header-actions {
      display: flex;
      gap: 4px;

      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 0;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px) scale(1.05);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
        }

        .el-icon {
          font-size: 12px;
        }

        // 圆形按钮的特殊样式
        &.is-circle {
          border: none;
          color: white;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.5s;
          }

          &:hover {
            transform: translateY(-2px) scale(1.08);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(-1px) scale(1.02);
          }

          // 添加按钮 - 绿色
          &[type="primary"] {
            background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);

            &:hover {
              background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
              box-shadow: 0 3px 10px rgba(103, 194, 58, 0.3);
            }
          }

          // 重新生成按钮 - 橙色
          &[type="warning"] {
            background: linear-gradient(135deg, #e6a23c 0%, #f0a020 100%);

            &:hover {
              background: linear-gradient(135deg, #f0a020 0%, #e6a23c 100%);
              box-shadow: 0 3px 10px rgba(230, 162, 60, 0.3);
            }
          }

          // 删除按钮 - 红色
          &[type="danger"] {
            background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);

            &:hover {
              background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
              box-shadow: 0 3px 10px rgba(245, 108, 108, 0.3);
            }
          }
        }
      }
    }
  }

  .focus-list {
    // 移除 flex: 1 和 overflow-y: auto，让内容自然展开
    padding-right: 4px;
    overflow: visible; // 确保内容不被裁切
    min-height: 400px; // 设置合理的最小高度，避免内容区域太小

    // 任务状态容器样式
    .task-status-container {
      position: relative;
      min-height: 400px; // 设置合理的最小高度，确保内容不会显得太空
      margin-bottom: 16px; // 与其他内容的间距
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8fafc; // 添加浅色背景，避免看起来太空白
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      
      // 当任务不在运行时，不占用空间
      &:empty {
        display: none;
      }
    }

    .focus-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .focus-card {
      background: #ffffff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;

      &.add-form-card {
        border: 2px dashed #409eff;
        background: #f0f9ff;

        .focus-question-section {
          background: #f0f9ff;
        }

        .conclusion-section {
          background: #f0f9ff;
        }

        .laws-section {
          background: #f0f9ff;
        }

        .question-input {
          :deep(.el-input__wrapper) {
            background: white;
            border: 1px solid #409eff;
          }
        }

        .question-actions {
          .el-button {
            &.el-button--success {
              background: #67c23a;
              border-color: #67c23a;
            }

            &.el-button--info {
              background: #909399;
              border-color: #909399;
            }
          }
        }
      }
    }

    // 保存按钮样式
    .question-actions {
      .el-button {
        &.el-button--success {
          animation: pulse 2s infinite;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
      }
    }

    .focus-question-section {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;

      .question-row {
        display: flex;
        align-items: center;
        gap: 12px;

        .question-number {
          font-size: 16px;
          font-weight: 600;
          color: #1890ff;
          min-width: 24px;
          flex-shrink: 0;
        }

        .question-input {
          flex: 1;

          :deep(.el-input) {
            .el-input__wrapper {
              border: 1px solid #d1d5db;
              border-radius: 6px;

              &:hover {
                border-color: #9ca3af;
              }

              &.is-focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }

            .el-input__inner {
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }

        .question-actions {
          display: flex;
          gap: 8px;
          flex-shrink: 0;

          .el-button {
            // 使用 Element Plus 默认的 small 尺寸
          }
        }
      }
    }

    .conclusion-section {
      padding: 16px;
      background: #f8fafc;
      border-bottom: 1px solid #e4e7ed;

      .conclusion-row {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;

        .conclusion-label {
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          min-width: 40px;
          flex-shrink: 0;
        }

        .conclusion-options {
          flex: 1;

          .conclusion-buttons {
            display: flex;
            align-items: center;
            gap: 16px;

            .conclusion-radio-group {
              display: flex;
              gap: 8px; // 添加单选按钮之间的间距

              :deep(.el-radio-button) {
                .el-radio-button__inner {
                  border-radius: 6px; // 所有按钮都使用圆角
                  font-size: 14px;
                  font-weight: 500;
                  padding: 8px 16px;
                  min-width: 60px;
                  transition: all 0.3s ease;
                  // 覆盖默认的蓝色样式
                  border: 1px solid #dcdfe6 !important;
                  background-color: #ffffff !important;
                  color: #606266 !important;
                  box-shadow: none !important;
                  outline: none !important;

                  &:hover {
                    border-color: #c0c4cc !important;
                    background-color: #f5f7fa !important;
                    box-shadow: none !important;
                  }

                  &:focus {
                    border-color: #c0c4cc !important;
                    box-shadow: none !important;
                    outline: none !important;
                  }

                  &:active {
                    box-shadow: none !important;
                    outline: none !important;
                  }
                }

                // 移除默认的连接样式，让每个按钮都是独立的圆角
                &:first-child .el-radio-button__inner {
                  border-radius: 6px;
                }

                &:last-child .el-radio-button__inner {
                  border-radius: 6px;
                }

                &:only-child .el-radio-button__inner {
                  border-radius: 6px;
                }

                // 选中状态的颜色 - 完全覆盖默认蓝色
                &.is-active {
                  .el-radio-button__inner {
                    // 强制覆盖所有可能的蓝色样式
                    box-shadow: none !important;
                    outline: none !important;
                  }

                  &.success .el-radio-button__inner {
                    background-color: #67c23a !important;
                    border-color: #67c23a !important;
                    color: white !important;
                    box-shadow: none !important;
                  }

                  &.danger .el-radio-button__inner {
                    background-color: #f56c6c !important;
                    border-color: #f56c6c !important;
                    color: white !important;
                    box-shadow: none !important;
                  }

                  &.warning .el-radio-button__inner {
                    background-color: #e6a23c !important;
                    border-color: #e6a23c !important;
                    color: white !important;
                    box-shadow: none !important;
                  }
                }
              }
            }

            .other-input {
              min-width: 200px;
              max-width: 350px;

              :deep(.el-input__wrapper) {
                border-radius: 6px;
                border: 1px solid #fbbf24;

                &:hover {
                  border-color: #f59e0b;
                }

                &.is-focus {
                  border-color: #f59e0b;
                  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
                }
              }
            }

            // 过渡动画
            .fade-enter-active,
            .fade-leave-active {
              transition: all 0.3s ease;
            }

            .fade-enter-from,
            .fade-leave-to {
              opacity: 0;
              transform: translateX(-10px);
            }
          }
        }
      }


    }

    .laws-section {
      padding: 16px;
      background: #f9fafb;
    }




  }
}

// 响应式设计
@media (max-width: 768px) {
  .dispute-focus {
    .focus-list {
      .focus-header {
        flex-direction: column;
        gap: 12px;

        .focus-actions {
          margin-left: 0;
          align-self: flex-end;
        }
      }

      .conclusion-section {
        .conclusion-value {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}
</style>