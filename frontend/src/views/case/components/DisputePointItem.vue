<template>
  <div
    :id="id"
    class="point-item"
    :class="{
      'active': activePointId === point.id.toString(),
      'related': isRelatedPoint,
      'editable': true
    }"
    :data-point-id="point.id"
    @click="$emit('click')"
    @dblclick="handleDoubleClick"
  >
    <div class="point-content">
      <div class="point-number">{{ pointIndex + 1 }}.</div>
      <div class="point-text" v-if="!isEditing">{{ point.content }}</div>
      <div class="point-edit" v-else>
        <el-input
          ref="editInputRef"
          v-model="editContent"
          type="textarea"
          :rows="3"
          @blur="handleSaveEdit"
          @keydown.enter.ctrl="handleSaveEdit"
          @keydown.esc="handleCancelEdit"
          placeholder="请输入观点内容..."
          class="edit-textarea"
        />
        <div class="edit-actions">
          <el-button size="small" type="primary" @click="handleSaveEdit" :loading="saving">保存</el-button>
          <el-button size="small" @click="handleCancelEdit">取消</el-button>
        </div>
      </div>
    </div>
    <div class="point-actions" @click.stop>
      <el-button
        size="small"
        type="danger"
        text
        @click="$emit('delete')"
        class="delete-btn"
        title="删除观点"
      >
        <el-icon><Delete /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { litigationPointsApi } from '@/api/litigationPoints'
import { ElMessage } from 'element-plus'

interface DisputePoint {
  id: number;
  content: string;
  pointDate: string;
  sortOrder: number;
  partyName: string;
  evidence: any[];
  side: string;
  caseImportId: number;
  sourceId: number;
  pointId?: string;
  relatedPointId?: string;
}

const props = defineProps<{
  id?: string;
  point: DisputePoint;
  pointIndex: number;
  activePointId: string | null;
  relatedPointIds: string[];
  side: 'plaintiff' | 'defendant';
  isSecondInstance: boolean;
}>()

const emit = defineEmits<{
  click: [];
  edit: [];
  delete: [];
  update: [point: DisputePoint];
}>()

// 检查当前观点是否为相关点
const isRelatedPoint = computed(() => {
  if (!props.activePointId || !props.relatedPointIds || props.relatedPointIds.length === 0) return false

  return props.relatedPointIds.includes(props.point.id.toString())
})

// 编辑状态
const isEditing = ref(false)
const editContent = ref('')
const saving = ref(false)
const editInputRef = ref()

// 处理双击事件
const handleDoubleClick = () => {
  // 启动内联编辑
  startInlineEdit()
}

// 开始内联编辑
const startInlineEdit = async () => {
  isEditing.value = true
  editContent.value = props.point.content

  // 等待DOM更新后聚焦输入框
  await nextTick()
  if (editInputRef.value) {
    editInputRef.value.focus()
  }
}

// 保存编辑
const handleSaveEdit = async () => {
  if (saving.value) return

  const newContent = editContent.value.trim()
  if (!newContent) {
    ElMessage.warning('观点内容不能为空')
    return
  }

  if (newContent === props.point.content) {
    // 内容没有变化，直接取消编辑
    handleCancelEdit()
    return
  }

  try {
    saving.value = true

    // 调用API更新观点
    await litigationPointsApi.updatePoint(props.point.id, {
      content: newContent
    })

    // 更新成功，通知父组件
    const updatedPoint = { ...props.point, content: newContent }
    emit('update', updatedPoint)

    // 退出编辑模式
    isEditing.value = false

  } catch (error) {
    console.error('更新观点失败:', error)
    ElMessage.error('更新观点失败')
  } finally {
    saving.value = false
  }
}

// 取消编辑
const handleCancelEdit = () => {
  isEditing.value = false
  editContent.value = ''
}
</script>

<style scoped lang="scss">
.point-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  transition: all 0.3s ease;
  border-left: 2px solid transparent;
  cursor: pointer;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px) scale(1.005);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);

    .point-actions {
      opacity: 1;
    }
  }

  &.editable {
    cursor: pointer;
  }

  &.active {
    border-left-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  &.related {
    border-left-color: #ff9800;
    background: rgba(255, 152, 0, 0.15);
    box-shadow: 0 2px 12px rgba(255, 152, 0, 0.2);
    transform: translateY(-1px);

    .point-content .point-number {
      background-color: rgba(255, 152, 0, 0.2);
      color: #ff9800;
      font-weight: 700;
    }

    .point-content .point-text {
      color: #e65100;
      font-weight: 500;
    }
  }
  
  .point-content {
    display: flex;
    flex-direction: row;
    flex: 1;

    .point-number {
      flex-shrink: 0;
      margin-right: 8px;
      font-weight: 600;
      color: #606266;
      min-width: 16px;
      background-color: rgba(0, 0, 0, 0.04);
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 11px;
    }

    .point-text {
      flex: 1;
      line-height: 1.5;
      font-size: 12px;
      color: #303133;
      text-align: left;
      padding-right: 6px;
      position: relative;
    }

    .point-edit {
      flex: 1;
      padding-right: 6px;

      .edit-textarea {
        margin-bottom: 8px;

        :deep(.el-textarea__inner) {
          font-size: 12px;
          line-height: 1.5;
          border: 1px solid #409eff;
          border-radius: 4px;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }
      }

      .edit-actions {
        display: flex;
        gap: 6px;
        justify-content: flex-end;

        .el-button {
          padding: 4px 8px;
          font-size: 11px;
          height: 24px;
        }
      }
    }
  }

  .point-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-left: auto;
    flex-shrink: 0;

    .delete-btn {
      padding: 2px 4px;
      min-height: 20px;
      border-radius: 4px;

      :deep(.el-icon) {
        font-size: 12px;
      }

      &:hover {
        background-color: rgba(245, 108, 108, 0.1);
        color: #f56c6c;
      }
    }
  }
}

/* 移除闪动动画，改为固定的高亮样式 */

/* 小分辨率适配 */
@media (max-width: 1200px) {
  .point-item {
    padding: 4px 6px;
    margin-bottom: 3px;

    .point-content {
      .point-number {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        font-size: 10px;
      }

      .point-text {
        font-size: 11px;
        line-height: 1.4;
        padding-right: 4px;
      }
    }
  }
}

/* 超小分辨率适配 */
@media (max-width: 768px) {
  .point-item {
    padding: 3px 4px;

    .point-content {
      .point-number {
        width: 14px;
        height: 14px;
        margin-right: 4px;
        font-size: 9px;
      }

      .point-text {
        font-size: 10px;
        line-height: 1.3;
      }
    }
  }
}
</style> 