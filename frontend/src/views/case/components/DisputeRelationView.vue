<template>
  <div class="dispute-comparison">
    <!-- 诉辩关系图 -->
    <div class="relation-header">
      <h3>{{ relationTitle }}</h3>
      <div class="header-actions">
        <el-button
            type="primary"
            size="small"
            :icon="Plus"
            @click="$emit('add-point')"
            circle
            title="添加诉辩观点"
        />
        <el-button
          type="warning"
          size="small"
          :icon="Refresh"
          @click="handleRegenerate"
          circle
          title="重新生成诉辩关系"
        />
      </div>
    </div>

    <div ref="containerRef" class="dispute-relations">
      <!-- 任务生成状态组件 - 局部覆盖 -->
      <TaskGeneratingStatus
        ref="taskGeneratingRef"
        :case-import-id="caseImportId"
        :task-type="TaskType.LITIGATION_RELATION"
        :overlay="false"
        :position="'absolute'"
        :show-progress="true"
        :show-estimated-time="true"
        @task-completed="handleTaskCompleted"
        @task-failed="handleTaskFailed"
      />

      <!-- 无正面回应提示 -->
      <div v-if="showNoResponseMessage" class="no-response-message">
        <div class="no-response-content">
          <span class="no-response-text">{{ currentRelationType || '无正面回应' }}</span>
        </div>
      </div>

      <!-- 左侧(诉方) -->
      <div class="side plaintiff">
        <div class="side-header">
          <el-tag size="large" type="primary" effect="plain">{{ plaintiffTagText }}</el-tag>
        </div>

        <!-- 诉方当事人列表 -->
        <div class="parties-container">
          <div
            v-for="party in plaintiffParties"
            :key="`plaintiff-party-${party.id}`"
            class="party-section"
          >
            <div class="party-header">
              <div class="party-name">{{ party.name }}</div>
            </div>

            <!-- 该当事人的观点列表 -->
            <div class="points-list">
              <div v-for="(group, groupDate) in party.pointsByDate" :key="`plaintiff-${party.id}-${groupDate}`" class="points-group">
                <div v-if="Object.keys(party.pointsByDate).length > 1" class="date-header">
                  {{ formatDate(groupDate) }}
                </div>
                <dispute-point-item
                  v-for="(point, pointIndex) in group"
                  :key="`plaintiff-${point.id}`"
                  :id="`point-${point.pointId || point.id}`"
                  :point="point"
                  :point-index="pointIndex"
                  :active-point-id="activePointId"
                  :related-point-ids="relatedPointIds"
                  :is-second-instance="caseTypeInfo?.isSecondInstance || false"
                  side="plaintiff"
                  @click="$emit('point-click', point, 'plaintiff')"
                  @edit="$emit('edit-point', point)"
                  @delete="$emit('delete-point', point)"
                  @update="$emit('update-point', $event)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间区域 - 仅用于布局间距 -->
      <div class="relation-space"></div>

      <!-- 右侧(辩方) -->
      <div class="side defendant">
        <div class="side-header">
          <el-tag size="large" type="danger" effect="plain">{{ defendantTagText }}</el-tag>
        </div>

        <!-- 辩方当事人列表 -->
        <div class="parties-container">
          <div
            v-for="party in defendantParties"
            :key="`defendant-party-${party.id}`"
            class="party-section"
          >
            <div class="party-header">
              <div class="party-name">{{ party.name }}</div>
            </div>

            <!-- 该当事人的观点列表 -->
            <div class="points-list">
              <div v-for="(group, groupDate) in party.pointsByDate" :key="`defendant-${party.id}-${groupDate}`" class="points-group">
                <div v-if="Object.keys(party.pointsByDate).length > 1" class="date-header">
                  {{ formatDate(groupDate) }}
                </div>
                <dispute-point-item
                  v-for="(point, pointIndex) in group"
                  :key="`defendant-${point.id}`"
                  :id="`point-${point.pointId || point.id}`"
                  :point="point"
                  :point-index="pointIndex"
                  :active-point-id="activePointId"
                  :related-point-ids="relatedPointIds"
                  :is-second-instance="caseTypeInfo?.isSecondInstance || false"
                  side="defendant"
                  @click="$emit('point-click', point, 'defendant')"
                  @edit="$emit('edit-point', point)"
                  @delete="$emit('delete-point', point)"
                  @update="$emit('update-point', $event)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { litigationPointsApi } from '@/api/litigationPoints'
import { caseApi, type CaseTypeInfo } from '@/api'
import DisputePointItem from './DisputePointItem.vue'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'
// @ts-ignore
import { jsPlumb } from 'jsplumb'

interface DisputePoint {
  id: number;
  content: string;
  pointDate: string;
  sortOrder: number;
  partyName: string;
  evidence: any[];
  side: string;
  caseImportId: number;
  sourceId: number;
  pointId?: string;
  relatedPointId?: string;
}

interface PartyInfo {
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}

interface ActiveRelation {
  plaintiffPointId: string;
  defendantPointId: string;
  relationType: string;
}

interface RelationData {
  id?: number;
  plaintiffPointId: string;
  defendantPointId: string;
  relationType: string;
}

const props = defineProps<{
  plaintiffParties: PartyInfo[];
  defendantParties: PartyInfo[];
  activePointId: string | null;
  relatedPointIds: string[];
  activeRelation: ActiveRelation | null;
  caseImportId: string | number;
  relations?: RelationData[];
}>()

const emit = defineEmits<{
  'add-point': [];
  'point-click': [point: DisputePoint, side: 'plaintiff' | 'defendant'];
  'edit-point': [point: DisputePoint];
  'delete-point': [point: DisputePoint];
  'update-point': [point: DisputePoint];
  'regenerate': [];
  'relation-updated': [relation: { id?: number; relationType: string }];
}>()

// 组件引用
const taskGeneratingRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null)

// 案件类型相关状态
const caseTypeInfo = ref<CaseTypeInfo | null>(null)

// jsPlumb 实例
const jsPlumbInstance = ref<any>(null)
const containerRef = ref<HTMLElement | null>(null)

// 编辑关系状态
const editingConnectionId = ref<string | null>(null)
const relationTypeOptions = [
  '否认',
  '部分认可',
  '全部认可',
  '无正面回应'
]

// 控制"无正面回应"消息显示
const showNoResponseMessage = ref(false)
// 当前关系类型
const currentRelationType = ref('')

// 计算关系标题
const relationTitle = computed(() => {
  if (caseTypeInfo.value?.isSecondInstance) {
    return '上诉关系'
  }
  return '诉辩关系'
})

// 计算诉方标签文字
const plaintiffTagText = computed(() => {
  if (caseTypeInfo.value?.isSecondInstance) {
    return '判'
  }
  return '诉'
})

// 计算辩方标签文字
const defendantTagText = computed(() => {
  if (caseTypeInfo.value?.isSecondInstance) {
    return '诉'
  }
  return '辩'
})



// 日期格式化
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`
}

// 处理重新生成
const handleRegenerate = () => {
  ElMessageBox.confirm(
    '确定要重新生成诉辩关系吗？这将覆盖当前所有诉辩观点和关系数据。',
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await litigationPointsApi.regenerate(props.caseImportId)
      // 直接开始任务执行状态显示，不需要立即检查
      taskGeneratingRef.value?.startTaskExecution()
      // 延迟一段时间后开始轮询检查任务状态
      setTimeout(() => {
        taskGeneratingRef.value?.startTaskMonitoring()
      }, 2000) // 2秒后开始检查，给任务创建一些时间
      emit('regenerate')
    } catch (error) {
      console.error('重新生成诉辩关系失败:', error)
    }
  }).catch(() => {})
}

// 处理任务完成
const handleTaskCompleted = () => {
  ElMessage.success('诉辩关系分析完成')
  // 通知父组件刷新数据
  emit('regenerate')
  // 清除当前连线，等待用户重新点击观点
  setTimeout(() => {
    clearConnections()
  }, 1000)
}

// 处理任务失败
const handleTaskFailed = (errorMessage: string) => {
  ElMessage.error(`诉辩关系分析失败: ${errorMessage}`)
}

// 从外部调用的重新生成方法（不显示确认对话框）
const regenerateFromParent = async () => {
  try {
    await litigationPointsApi.regenerate(props.caseImportId)
    // 直接开始任务执行状态显示，不需要立即检查
    taskGeneratingRef.value?.startTaskExecution()
    // 延迟一段时间后开始轮询检查任务状态
    setTimeout(() => {
      taskGeneratingRef.value?.startTaskMonitoring()
    }, 2000) // 2秒后开始检查，给任务创建一些时间
    emit('regenerate')
  } catch (error) {
    console.error('重新生成诉辩关系失败:', error)
    throw error // 重新抛出错误，让父组件处理
  }
}

// 从外部触发TaskGeneratingStatus（不调用API）
const startTaskGeneratingFromParent = () => {
  taskGeneratingRef.value?.startTaskExecution()
  setTimeout(() => {
    taskGeneratingRef.value?.startTaskMonitoring()
  }, 2000)
}

// 获取案件类型信息
const getCaseTypeInfo = async () => {
  try {
    const result = await caseApi.getCaseType(props.caseImportId)
    caseTypeInfo.value = result
  } catch (error) {
    console.error('获取案件类型失败:', error)
    // 默认为一审
    caseTypeInfo.value = {
      ajlxdm: '',
      caseType: '一审',
      caseTypeCode: 'MSYS',
      isSecondInstance: false
    }
  }
}

// 初始化 jsPlumb
const initJsPlumb = () => {
  if (!containerRef.value) return

  // 创建 jsPlumb 实例
  jsPlumbInstance.value = jsPlumb.getInstance({
    Container: containerRef.value,
    Connector: ['Bezier', { curviness: 50 }],
    PaintStyle: {
      stroke: '#409eff',
      strokeWidth: 2
    },
    Endpoint: ['Dot', { radius: 6 }], // 适中的端点大小
    EndpointStyle: { fill: '#409eff' },
    Anchor: ['Left', 'Right'],
    DragOptions: { cursor: 'pointer', zIndex: 2000 },
    // 启用连接拖拽
    ConnectionsDetachable: true,
    ReattachConnections: true,
    // 设置拖拽限制
    BeforeDetach: (connection: any) => {
      // 允许拖拽分离
      return true
    },
    // 设置拖拽完成后的处理
    BeforeDrop: (params: any) => {
      console.log('BeforeDrop 被调用，参数:', params)
      return handleConnectionDrop(params)
    },
    Overlays: [
      ['Arrow', {
        location: 1,
        width: 10,
        length: 10,
        paintStyle: { fill: '#409eff' }
      }]
    ]
  })

  // 绑定连线点击事件，阻止冒泡
  jsPlumbInstance.value.bind('click', (connection: any, event: any) => {
    if (event) {
      event.stopPropagation()
    }
  })

  // 绑定连线双击删除事件
  jsPlumbInstance.value.bind('dblclick', (connection: any, event: any) => {
    if (event) {
      event.stopPropagation()
    }
    handleConnectionDoubleClick(connection)
  })

  // 绑定连线拖拽事件
  jsPlumbInstance.value.bind('connectionDrag', (connection: any) => {
    console.log('=== connectionDrag 事件触发 ===')
    console.log('开始拖拽连线:', connection)
    // 显示可连接的端点
    showDropTargetsForDrag(connection)
  })

  // 绑定端点拖拽开始事件
  jsPlumbInstance.value.bind('connectionDragStart', (connection: any) => {
    console.log('=== connectionDragStart 事件触发 ===')
    console.log('连线拖拽开始:', connection)
  })

  // 绑定端点拖拽事件
  jsPlumbInstance.value.bind('endpointDrag', (endpoint: any) => {
    console.log('=== endpointDrag 事件触发 ===')
    console.log('端点拖拽:', endpoint)
  })

  // 绑定连线拖拽停止事件
  jsPlumbInstance.value.bind('connectionDragStop', (connection: any) => {
    console.log('停止拖拽连线:', connection)
    // 隐藏拖拽端点
    hideDropTargets()
  })

  // 绑定连线分离事件
  jsPlumbInstance.value.bind('connectionDetached', (params: any) => {
    console.log('连线分离:', params)
    // 显示可连接的端点
    if (params.connection && params.connection.relationData) {
      showDropTargetsForDrag(params.connection)
    }
  })

  // 绑定连线重新连接事件
  jsPlumbInstance.value.bind('connectionMoved', (params: any) => {
    console.log('连线重新连接:', params)
    // 隐藏拖拽端点
    hideDropTargets()
    // 处理连接移动后的保存
    handleConnectionMoved(params)
  })

  // 绑定连接建立事件
  jsPlumbInstance.value.bind('connection', (info: any) => {
    console.log('=== 新连接建立 ===')
    console.log('连接信息:', info)
    console.log('源ID:', info.sourceId, '目标ID:', info.targetId)
    console.log('连接对象:', info.connection)

    // 为新建立的连接尝试附加关系数据
    if (info.connection && !info.connection.relationData) {
      console.log('新连接缺少关系数据，尝试查找...')

      // 查找可能的原始关系
      if (props.relations) {
        for (const relation of props.relations) {
          const relationSourceId = `point-${relation.plaintiffPointId}`
          const relationTargetId = `point-${relation.defendantPointId}`

          // 检查是否与现有关系相关
          if ((info.sourceId === relationSourceId && info.targetId === relationTargetId) ||
              (info.sourceId === relationTargetId && info.targetId === relationSourceId)) {
            console.log('为新连接附加关系数据:', relation)
            info.connection.relationData = relation
            break
          }
        }
      }
    }
  })

  // 不在初始化时添加端点，只有在显示连线时才添加
}

// 显示拖拽时的可连接端点
const showDropTargetsForDrag = (connection: any) => {
  console.log('showDropTargetsForDrag 被调用，连接:', connection)
  if (!jsPlumbInstance.value || !connection || !connection.relationData) {
    console.log('缺少必要数据，跳过显示端点')
    return
  }

  const originalRelation = connection.relationData
  console.log('原始关系数据:', originalRelation)
  console.log('连接的sourceId:', connection.sourceId, 'targetId:', connection.targetId)

  // 直接的解决方案：根据连接的方向和用户操作来判断
  let targetSide: string

  // 检查是否按住了Shift键来强制指定类型
  const isShiftPressed = window.event && (window.event as KeyboardEvent).shiftKey

  if (isShiftPressed) {
    // 按住Shift键时，强制显示诉方端点
    targetSide = 'plaintiff'
    console.log('检测到Shift键，强制显示诉方端点')
  } else {
    // 默认显示辩方端点
    targetSide = 'defendant'
    console.log('默认显示辩方端点')
  }

  // 为了满足您的要求，我们添加一个更明确的判断
  // 通过检查连接线的端点位置来判断
  const sourceElement = document.getElementById(connection.sourceId)
  const targetElement = document.getElementById(connection.targetId)

  if (sourceElement && targetElement) {
    const sourceRect = sourceElement.getBoundingClientRect()
    const targetRect = targetElement.getBoundingClientRect()

    // 获取鼠标当前位置（如果可用）
    const mouseX = window.event ? (window.event as MouseEvent).clientX : 0

    if (mouseX > 0) {
      // 根据鼠标位置判断：更接近哪个端点
      const sourceDistance = Math.abs(mouseX - (sourceRect.left + sourceRect.width))
      const targetDistance = Math.abs(mouseX - targetRect.left)

      if (sourceDistance < targetDistance) {
        targetSide = 'plaintiff' // 更接近诉方端点
        console.log('鼠标更接近诉方端点，显示诉方可连接端点')
      } else {
        targetSide = 'defendant' // 更接近辩方端点
        console.log('鼠标更接近辩方端点，显示辩方可连接端点')
      }
    }
  }

  console.log('最终目标方:', targetSide)

  // 为目标方的观点添加临时端点
  const pointElements = document.querySelectorAll('[id^="point-"]')
  pointElements.forEach(element => {
    const pointInfo = findPointByElementId(element.id)

    // 只为目标方的观点添加端点
    if (pointInfo && pointInfo.side === targetSide) {
      // 检查该元素是否已经有端点了，避免重复添加
      const existingEndpoints = jsPlumbInstance.value.getEndpoints(element)
      if (existingEndpoints && existingEndpoints.length > 0) {
        return // 已经有端点了，跳过
      }

      const isPlaintiff = pointInfo.side === 'plaintiff'

      if (isPlaintiff) {
        // 为诉方观点添加右侧端点
        jsPlumbInstance.value.addEndpoint(element, {
          anchor: 'Right',
          isSource: true,
          isTarget: true,
          maxConnections: -1,
          endpoint: ['Dot', { radius: 6 }], // 与默认端点大小一致
          paintStyle: {
            fill: '#409eff', // 使用诉方默认颜色（蓝色）
            stroke: '#ffffff',
            strokeWidth: 2
          },
          hoverPaintStyle: {
            fill: '#67c23a', // 悬停时变绿色
            stroke: '#ffffff',
            strokeWidth: 3
          },
          cssClass: 'drag-target-endpoint' // 添加CSS类用于识别
        })
      } else {
        // 为辩方观点添加左侧端点
        jsPlumbInstance.value.addEndpoint(element, {
          anchor: 'Left',
          isSource: true,
          isTarget: true,
          maxConnections: -1,
          endpoint: ['Dot', { radius: 6 }], // 与默认端点大小一致
          paintStyle: {
            fill: '#e6a23c', // 使用辩方默认颜色（橙色）
            stroke: '#ffffff',
            strokeWidth: 2
          },
          hoverPaintStyle: {
            fill: '#67c23a', // 悬停时变绿色
            stroke: '#ffffff',
            strokeWidth: 3
          },
          cssClass: 'drag-target-endpoint' // 添加CSS类用于识别
        })
      }
    }
  })
}

// 隐藏拖拽端点
const hideDropTargets = () => {
  if (!jsPlumbInstance.value) return

  // 查找所有带有拖拽标识的端点并删除
  const pointElements = document.querySelectorAll('[id^="point-"]')
  pointElements.forEach(element => {
    const endpoints = jsPlumbInstance.value.getEndpoints(element)
    if (endpoints) {
      endpoints.forEach((endpoint: any) => {
        if (endpoint.cssClass && endpoint.cssClass.includes('drag-target-endpoint')) {
          jsPlumbInstance.value.deleteEndpoint(endpoint)
        }
      })
    }
  })
}



// 根据元素ID查找观点信息
const findPointByElementId = (elementId: string) => {
  // 从 point-xxx 格式中提取ID
  const pointId = elementId.replace('point-', '')

  // 遍历所有当事人的观点
  for (const party of props.plaintiffParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          if (point.id.toString() === pointId || point.pointId === pointId) {
            return point
          }
        }
      }
    }
  }

  for (const party of props.defendantParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          if (point.id.toString() === pointId || point.pointId === pointId) {
            return point
          }
        }
      }
    }
  }

  return null
}

// 根据pointId查找观点信息
const findPointByPointId = (pointId: string) => {
  // 遍历所有当事人的观点
  for (const party of props.plaintiffParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          // 只通过 pointId 字段匹配，不使用数据库 ID
          if (point.pointId === pointId) {
            return point
          }
        }
      }
    }
  }

  for (const party of props.defendantParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          // 只通过 pointId 字段匹配，不使用数据库 ID
          if (point.pointId === pointId) {
            return point
          }
        }
      }
    }
  }

  return null
}



// 处理连线拖拽完成
const handleConnectionDrop = (params: any) => {
  console.log('handleConnectionDrop 被调用，参数:', params)
  const { sourceId, targetId, connection } = params

  // 获取源和目标观点的信息
  const sourcePoint = findPointByElementId(sourceId)
  const targetPoint = findPointByElementId(targetId)

  console.log('源观点:', sourcePoint, '目标观点:', targetPoint)

  if (!sourcePoint || !targetPoint) {
    ElMessage.error('无法找到对应的观点信息')
    return false
  }

  // 检查是否是有效的拖拽（必须有原始连线数据）
  console.log('连接对象:', connection, '关系数据:', connection?.relationData)
  if (!connection || !connection.relationData) {
    ElMessage.error('无效的连线拖拽')
    return false
  }

  // 获取原始关系数据
  const originalRelation = connection.relationData

  // 修正判断逻辑：通过比较原始连接和当前连接来判断拖拽的端点
  const originalSourceId = `point-${originalRelation.plaintiffPointId}`
  const originalTargetId = `point-${originalRelation.defendantPointId}`

  console.log('原始连接:', originalSourceId, '->', originalTargetId)
  console.log('当前连接:', sourceId, '->', targetId)

  let isDraggingPlaintiffEnd = false
  let isDraggingDefendantEnd = false

  // 如果源ID发生了变化，说明拖拽的是诉方端点
  if (sourceId !== originalSourceId) {
    isDraggingPlaintiffEnd = true
    console.log('检测到拖拽诉方端点（源ID变化）')
  }
  // 如果目标ID发生了变化，说明拖拽的是辩方端点
  else if (targetId !== originalTargetId) {
    isDraggingDefendantEnd = true
    console.log('检测到拖拽辩方端点（目标ID变化）')
  }
  // 如果都没变化，可能是在同一个观点上拖拽，允许连接
  else {
    console.log('连接没有变化，允许')
    return true
  }

  // 根据拖拽的端点类型，验证目标观点的合法性
  if (isDraggingDefendantEnd) {
    // 拖拽辩方端，只能连接到其他辩方观点
    if (targetPoint.side !== 'defendant') {
      ElMessage.error('辩方端点只能连接到其他辩方观点')
      return false
    }
  } else if (isDraggingPlaintiffEnd) {
    // 拖拽诉方端，只能连接到其他诉方观点
    if (targetPoint.side !== 'plaintiff') {
      ElMessage.error('诉方端点只能连接到其他诉方观点')
      return false
    }
  }

  // 确定新的连接关系
  let newPlaintiffPointId: string
  let newDefendantPointId: string

  if (isDraggingDefendantEnd) {
    // 拖拽辩方端，更新辩方观点ID
    newPlaintiffPointId = originalRelation.plaintiffPointId
    newDefendantPointId = targetPoint.pointId || targetPoint.id.toString()
  } else {
    // 拖拽诉方端，更新诉方观点ID
    newPlaintiffPointId = targetPoint.pointId || targetPoint.id.toString()
    newDefendantPointId = originalRelation.defendantPointId
  }

  // 检查是否真的有变化
  if (newPlaintiffPointId === originalRelation.plaintiffPointId &&
      newDefendantPointId === originalRelation.defendantPointId) {
    return true // 没有变化，允许连接
  }

  // 异步更新关系
  console.log('准备更新关系，从:', originalRelation.plaintiffPointId, '->', originalRelation.defendantPointId)
  console.log('更新为:', newPlaintiffPointId, '->', newDefendantPointId)
  updateConnectionRelation(originalRelation, newPlaintiffPointId, newDefendantPointId)

  console.log('BeforeDrop 返回 true，允许连接')
  return true // 允许连接
}

// 处理连接移动（拖拽完成）
const handleConnectionMoved = (params: any) => {
  console.log('handleConnectionMoved 被调用，参数:', params)

  if (!params.connection) {
    console.log('没有连接对象，跳过处理')
    return
  }

  // 如果连接没有关系数据，尝试从原始连接中获取
  if (!params.connection.relationData) {
    console.log('连接没有关系数据，尝试查找原始关系')

    // 查找可能的原始关系
    const sourceId = params.connection.sourceId
    const targetId = params.connection.targetId

    console.log('查找关系，源ID:', sourceId, '目标ID:', targetId)

    // 从现有关系中查找匹配的关系
    if (props.relations) {
      for (const relation of props.relations) {
        const relationSourceId = `point-${relation.plaintiffPointId}`
        const relationTargetId = `point-${relation.defendantPointId}`

        // 检查是否是这个关系的变更
        if ((sourceId === relationSourceId || targetId === relationTargetId) ||
            (sourceId === relationTargetId || targetId === relationSourceId)) {
          console.log('找到可能的原始关系:', relation)
          params.connection.relationData = relation
          break
        }
      }
    }

    if (!params.connection.relationData) {
      console.log('无法找到原始关系，跳过处理')
      return
    }
  }

  const connection = params.connection
  const originalRelation = connection.relationData

  // 获取新的源和目标ID
  const newSourceId = connection.sourceId
  const newTargetId = connection.targetId

  console.log('原始关系:', originalRelation)
  console.log('新的源ID:', newSourceId, '新的目标ID:', newTargetId)

  // 从ID中提取观点ID
  const newPlaintiffPointId = newSourceId.replace('point-', '')
  const newDefendantPointId = newTargetId.replace('point-', '')

  // 检查是否真的有变化
  if (newPlaintiffPointId === originalRelation.plaintiffPointId &&
      newDefendantPointId === originalRelation.defendantPointId) {
    console.log('连接没有变化，跳过更新')
    return
  }

  console.log('连接发生变化，准备更新:', {
    原诉方: originalRelation.plaintiffPointId,
    新诉方: newPlaintiffPointId,
    原辩方: originalRelation.defendantPointId,
    新辩方: newDefendantPointId
  })

  // 更新关系
  updateConnectionRelation(originalRelation, newPlaintiffPointId, newDefendantPointId)
}

// 更新连接关系
const updateConnectionRelation = async (originalRelation: any, newPlaintiffPointId: string, newDefendantPointId: string) => {
  try {
    // 调用API更新关系
    await litigationPointsApi.updateRelation(originalRelation.id!, {
      plaintiffPointId: newPlaintiffPointId,
      defendantPointId: newDefendantPointId,
      relationType: originalRelation.relationType
    })

    // 更新关系数据
    originalRelation.plaintiffPointId = newPlaintiffPointId
    originalRelation.defendantPointId = newDefendantPointId

    // 通知父组件更新数据
    emit('relation-updated', {
      id: originalRelation.id,
      relationType: originalRelation.relationType
    })

    ElMessage.success('关系连接更新成功')

    // 不要重新绘制连线，让jsPlumb保持当前的连接状态
    // 只有在必要时（比如数据刷新）才重新绘制
    console.log('连接关系更新完成，保持当前连线状态')
  } catch (error) {
    console.error('更新连接关系失败:', error)
    ElMessage.error('更新连接关系失败')
  }
}

// 绘制特定观点的连线
const drawConnectionForPoint = (searchId: string) => {
  console.log('drawConnectionForPoint 被调用，searchId:', searchId)
  console.log('当前关系数据:', props.relations)

  if (!jsPlumbInstance.value || !props.relations) {
    console.log('jsPlumb实例或关系数据不存在')
    return
  }

  // 清除现有连线和端点
  jsPlumbInstance.value.deleteEveryConnection()
  jsPlumbInstance.value.deleteEveryEndpoint()

  // 查找与该观点相关的关系，支持通过 pointId 或数据库 ID 查找
  const relatedRelations = props.relations.filter(relation => {
    console.log('检查关系:', relation, '与searchId:', searchId)

    // 首先尝试通过 pointId 匹配
    if (relation.plaintiffPointId === searchId || relation.defendantPointId === searchId) {
      console.log('通过pointId匹配成功')
      return true
    }

    // 如果没有匹配，尝试通过数据库 ID 匹配
    // 查找对应的观点，获取其 pointId
    const plaintiffPoint = findPointByPointId(relation.plaintiffPointId)
    const defendantPoint = findPointByPointId(relation.defendantPointId)

    console.log('查找到的观点:', {
      plaintiffPoint: plaintiffPoint ? { id: plaintiffPoint.id, pointId: plaintiffPoint.pointId } : null,
      defendantPoint: defendantPoint ? { id: defendantPoint.id, pointId: defendantPoint.pointId } : null
    })

    const matchByDbId = (plaintiffPoint?.id.toString() === searchId) ||
                       (defendantPoint?.id.toString() === searchId)

    if (matchByDbId) {
      console.log('通过数据库ID匹配成功')
    }

    return matchByDbId
  })

  console.log('找到的相关关系:', relatedRelations)

  // 检查是否有"无正面回应"的关系（一方为空）
  const noResponseRelation = relatedRelations.find(relation =>
    !relation.plaintiffPointId || !relation.defendantPointId ||
    relation.plaintiffPointId === '' || relation.defendantPointId === ''
  )

  if (noResponseRelation) {
    console.log('找到无正面回应关系:', noResponseRelation)
    showNoResponseMessage.value = true
    // 设置关系类型用于显示
    currentRelationType.value = noResponseRelation.relationType || '无正面回应'
    return
  }

  console.log('找到的相关关系:', relatedRelations)

  // 如果没有找到相关关系，显示"无正面回应"
  if (relatedRelations.length === 0) {
    console.log('没有找到相关关系，显示"无正面回应"')
    showNoResponseMessage.value = true
    return
  }

  // 隐藏"无正面回应"消息
  console.log('找到相关关系，隐藏"无正面回应"消息')
  showNoResponseMessage.value = false

  // 为相关关系绘制连线，jsPlumb会自动创建端点
  relatedRelations.forEach(relation => {
    const sourceId = `point-${relation.plaintiffPointId}`
    const targetId = `point-${relation.defendantPointId}`

    const sourceElement = document.getElementById(sourceId)
    const targetElement = document.getElementById(targetId)

    if (sourceElement && targetElement) {
      try {
        const connectionId = `connection-${relation.plaintiffPointId}-${relation.defendantPointId}`

        const connection = jsPlumbInstance.value.connect({
          source: sourceId,
          target: targetId,
          anchors: ['Right', 'Left'], // 诉方右侧连接，辩方左侧连接
          detachable: true,
          reattach: true,
          endpoint: ['Dot', { radius: 6 }],
          endpointStyles: [
            { fill: '#409eff', stroke: '#ffffff', strokeWidth: 2 }, // 诉方端点（蓝色）
            { fill: '#e6a23c', stroke: '#ffffff', strokeWidth: 2 }  // 辩方端点（橙色）
          ],
          paintStyle: {
            stroke: '#409eff',
            strokeWidth: 2
          },
          overlays: [
            ['Custom', {
              create: () => {
                return createEditableLabel(relation, connectionId)
              },
              location: 0.5,
              id: `label-${connectionId}`
            }],
            ['Arrow', {
              location: 1,
              width: 10,
              length: 10,
              paintStyle: { fill: '#409eff' }
            }]
          ]
        })

        // 在连线上存储关系数据和ID
        connection.relationData = relation
        connection.connectionId = connectionId
      } catch (error) {
        console.warn('连线创建失败:', error, { sourceId, targetId })
      }
    }
  })
}

// 清除所有连线和端点
const clearConnections = () => {
  if (jsPlumbInstance.value) {
    jsPlumbInstance.value.deleteEveryConnection()
    jsPlumbInstance.value.deleteEveryEndpoint()
  }
  // 隐藏"无正面回应"消息并重置关系类型
  showNoResponseMessage.value = false
  currentRelationType.value = ''
}

// 创建可编辑的标签
const createEditableLabel = (relation: any, connectionId: string) => {
  const labelDiv = document.createElement('div')
  labelDiv.className = 'editable-connection-label'
  labelDiv.style.cssText = `
    background: linear-gradient(135deg, #ff9800, #ff7043);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1000;
  `

  // 创建显示文本
  const textSpan = document.createElement('span')
  textSpan.textContent = relation.relationType
  textSpan.className = 'label-text'

  // 创建下拉选择器（初始隐藏）
  const select = document.createElement('select')
  select.className = 'label-select'
  select.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    background: white;
    border: 1px solid #409eff;
    border-radius: 12px;
    font-size: 10px;
    padding: 2px 8px;
    color: #333;
    cursor: pointer;
  `

  // 添加选项
  relationTypeOptions.forEach(option => {
    const optionElement = document.createElement('option')
    optionElement.value = option
    optionElement.textContent = option
    optionElement.selected = option === relation.relationType
    select.appendChild(optionElement)
  })

  // 点击事件：显示下拉选择器
  labelDiv.addEventListener('click', (e) => {
    e.stopPropagation()
    editingConnectionId.value = connectionId
    select.style.opacity = '1'
    textSpan.style.opacity = '0'
    select.focus()
  })

  // 选择改变事件
  select.addEventListener('change', async (e) => {
    const newRelationType = (e.target as HTMLSelectElement).value
    await updateRelationType(relation, newRelationType, connectionId)
    textSpan.textContent = newRelationType
    select.style.opacity = '0'
    textSpan.style.opacity = '1'
    editingConnectionId.value = null
  })

  // 失去焦点事件
  select.addEventListener('blur', () => {
    select.style.opacity = '0'
    textSpan.style.opacity = '1'
    editingConnectionId.value = null
  })

  labelDiv.appendChild(textSpan)
  labelDiv.appendChild(select)

  return labelDiv
}

// 更新关系类型
const updateRelationType = async (relation: any, newRelationType: string, connectionId: string) => {
  try {
    // 调用API更新关系
    await litigationPointsApi.updateRelation(relation.id!, {
      relationType: newRelationType
    })

    // 更新关系数据
    relation.relationType = newRelationType

    // 通知父组件更新数据
    emit('relation-updated', {
      id: relation.id,
      relationType: newRelationType
    })

    ElMessage.success('关系类型更新成功')
  } catch (error) {
    console.error('更新关系失败:', error)
    ElMessage.error('更新关系失败')
  }
}

// 处理连接线双击删除
const handleConnectionDoubleClick = async (connection: any) => {
  console.log('连接线双击事件:', connection)

  if (!connection || !connection.relationData) {
    ElMessage.error('无法获取连接关系信息')
    return
  }

  const relation = connection.relationData

  // 获取诉方和辩方观点信息
  const plaintiffPoint = findPointByPointId(relation.plaintiffPointId)
  const defendantPoint = findPointByPointId(relation.defendantPointId)

  if (!plaintiffPoint || !defendantPoint) {
    ElMessage.error('无法找到相关观点信息')
    return
  }

  // 构建确认消息
  const plaintiffLabel = caseTypeInfo.value?.isSecondInstance ? '判方' : '诉方'
  const defendantLabel = caseTypeInfo.value?.isSecondInstance ? '诉方' : '辩方'

  const confirmMessage = `确定要删除这条诉辩关系吗？

${plaintiffLabel}观点：${plaintiffPoint.content.substring(0, 50)}${plaintiffPoint.content.length > 50 ? '...' : ''}

${defendantLabel}观点：${defendantPoint.content.substring(0, 50)}${defendantPoint.content.length > 50 ? '...' : ''}

关系类型：${relation.relationType}`

  try {
    await ElMessageBox.confirm(
      confirmMessage,
      '删除诉辩关系',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 调用删除API
    await litigationPointsApi.deleteRelation(relation.id!)

    // 删除连接线
    jsPlumbInstance.value.deleteConnection(connection)

    // 通知父组件更新数据
    emit('relation-updated', { id: relation.id, relationType: relation.relationType })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除诉辩关系失败:', error)
      ElMessage.error('删除诉辩关系失败')
    }
  }
}

// 重新绘制连线
const redrawConnections = async () => {
  await nextTick()
  if (jsPlumbInstance.value) {
    jsPlumbInstance.value.repaintEverything()
    // 如果有激活的观点，重新绘制其连线（不添加端点）
    if (props.activePointId) {
      const activePoint = findPointByDomId(props.activePointId)
      if (activePoint) {
        const searchId = activePoint.pointId || activePoint.id.toString()
        drawConnectionForPoint(searchId)
      }
    }
  }
}

// 根据DOM ID查找观点
const findPointByDomId = (domId: string) => {
  // 遍历所有当事人的观点
  for (const party of props.plaintiffParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          if (point.id.toString() === domId) {
            return point
          }
        }
      }
    }
  }

  for (const party of props.defendantParties) {
    if (party.pointsByDate) {
      for (const dateGroup of Object.values(party.pointsByDate)) {
        for (const point of dateGroup) {
          if (point.id.toString() === domId) {
            return point
          }
        }
      }
    }
  }

  return null
}

// 组件挂载时获取案件类型
onMounted(async () => {
  await getCaseTypeInfo()
  await nextTick()
  initJsPlumb()
  // 不在挂载时绘制连线，等待用户点击观点
})

// 组件卸载时清理
onUnmounted(() => {
  if (jsPlumbInstance.value) {
    jsPlumbInstance.value.reset()
  }
})

// 监听激活观点变化
watch(() => props.activePointId, (newActivePointId, oldActivePointId) => {
  if (!jsPlumbInstance.value) return

  if (newActivePointId) {
    // 有新的激活观点，绘制其连线
    const activePoint = findPointByDomId(newActivePointId)
    console.log('找到的激活观点:', activePoint)
    if (activePoint) {
      // 使用 pointId 或者 id 作为查找关系的依据
      const searchId = activePoint.pointId || activePoint.id.toString()
      console.log('使用的searchId:', searchId, '(pointId:', activePoint.pointId, ', id:', activePoint.id, ')')
      drawConnectionForPoint(searchId)
    }
  } else {
    // 没有激活观点，清除所有连线
    clearConnections()
  }
})

// 监听关系数据变化
watch(() => props.relations, () => {
  // 只有在有激活观点时才重新绘制连线
  if (props.activePointId) {
    redrawConnections()
  }
}, { deep: true })

// 监听观点数据变化，如果有激活观点则重新绘制连线
watch(() => [props.plaintiffParties, props.defendantParties], () => {
  if (props.activePointId) {
    setTimeout(() => {
      redrawConnections()
    }, 100)
  }
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  regenerateFromParent,
  startTaskGeneratingFromParent,
  redrawConnections,
  clearConnections
})
</script>

<style scoped lang="scss">
.dispute-comparison {
  display: flex;
  flex-direction: column;
  /* 移除固定高度，让容器根据内容自适应 */
  min-height: 300px; /* 添加最小高度 */
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  background-color: #f5f6fa;
  background-image: linear-gradient(135deg, #f8faff 0%, #f5f6fa 100%);
  border-radius: 8px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.04);
  
  .relation-header {
    padding: 6px 10px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #f0f5ff, #f7f8fa, #fff5f5);

    h3 {
      margin: 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
      position: relative;
      padding-left: 10px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #409eff, #f56c6c);
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.dispute-relations {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 8px;
  padding: 8px;
  position: relative; /* 为TaskGeneratingStatus提供定位上下文 */
  /* 移除最大高度限制和滚动条，让内容完全显示 */
  min-height: 200px; /* 设置最小高度 */
  
  .side {
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 10px;
    /* 适中的最小高度，让没有数据时也有合适的显示空间 */
    min-height: 280px;

    &.plaintiff {
      background: linear-gradient(135deg, #ecf5ff 0%, #e6f1fc 100%);
      box-shadow: 0 3px 10px rgba(64, 158, 255, 0.12);
      border: 1px solid rgba(64, 158, 255, 0.12);
    }

    &.defendant {
      background: linear-gradient(135deg, #fef0f0 0%, #fde9e9 100%);
      box-shadow: 0 3px 10px rgba(245, 108, 108, 0.12);
      border: 1px solid rgba(245, 108, 108, 0.12);
    }

    .side-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      padding: 8px;

      .el-tag {
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        padding: 0 16px;
        height: 32px;
        line-height: 32px;
      }
    }

    .parties-container {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .party-section {
      background: rgba(255, 255, 255, 0.6);
      border-radius: 8px;
      padding: 12px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .party-header {
      margin-bottom: 8px;
      padding-bottom: 6px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      text-align: center;

      .party-name {
        font-weight: 600;
        font-size: 13px;
        color: #333;
      }
    }

    .points-list {
      .points-group {
        margin-bottom: 6px;

        .date-header {
          font-size: 10px;
          color: #666;
          background-color: rgba(0, 0, 0, 0.05);
          padding: 2px 6px;
          border-radius: 8px;
          margin-bottom: 4px;
          font-weight: 500;
          display: inline-block;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
        }
      }
    }
  }
}

.relation-space {
  width: 40px;
  min-width: 40px;
  position: relative;
  margin: 0 3px;
}

@keyframes scaleIn {
  from { transform: scale(0.7); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* jsPlumb 连线样式 */
:deep(.jtk-connector) {
  z-index: 1;
  pointer-events: auto;
}

:deep(.jtk-endpoint) {
  z-index: 2;
  pointer-events: auto;
}

:deep(.jtk-overlay) {
  z-index: 3;
  pointer-events: auto;
}

/* 可编辑连线标签样式 */
:deep(.editable-connection-label) {
  &:hover {
    background: linear-gradient(135deg, #ffb74d, #ff8a65) !important;
    box-shadow: 0 3px 8px rgba(255, 152, 0, 0.4) !important;
    transform: scale(1.05);
  }

  .label-text {
    transition: opacity 0.2s ease;
  }

  .label-select {
    transition: opacity 0.2s ease;
  }
}

/* 无正面回应消息样式 - 与连接线标签样式保持一致 */
.no-response-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  pointer-events: none;
}

.no-response-content {
  background: linear-gradient(135deg, #ff9800, #ff7043);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  white-space: nowrap;
  transition: all 0.2s ease;
}

.no-response-text {
  color: white;
  font-size: 10px;
  font-weight: 600;
  white-space: nowrap;
}



/* 小分辨率适配 */
@media (max-width: 1200px) {
  .dispute-relations {
    gap: 6px;
    padding: 6px;
    /* 保持内容自适应高度 */
    min-height: 180px;
  }

  .relation-space {
    width: 30px;
    min-width: 30px;
  }

  .side {
    padding: 8px;
    /* 小分辨率下稍微减少最小高度 */
    min-height: 250px;

    .side-header {
      height: 32px;
      margin-bottom: 6px;
    }

    .points-list {
      padding: 4px 2px;
    }
  }


}

/* 超小分辨率适配 */
@media (max-width: 768px) {
  .dispute-relations {
    gap: 4px;
    padding: 6px;
    /* 保持内容自适应高度 */
    min-height: 150px;
  }

  .relation-space {
    width: 20px;
    min-width: 20px;
    margin: 0 2px;
  }

  .side {
    padding: 6px;
    /* 移动端进一步减少最小高度 */
    min-height: 220px;

    .side-header {
      height: 28px;
    }
  }
}
</style> 