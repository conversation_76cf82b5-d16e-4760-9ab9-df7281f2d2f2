<template>
  <div class="timeline-container">
    <div class="timeline-header">
      <h3>矛盾识别</h3>
      <el-button type="primary" @click="handleAddEvent" v-if="false">
        <el-icon><Plus /></el-icon>
        添加
      </el-button>
    </div>

    <!-- 矛盾识别内容 -->
    <div class="contradiction-content" v-loading="loading">
      <div v-if="contradictionList.length === 0" class="empty-state">
        <el-empty description="暂无矛盾识别数据" />
      </div>
      <el-timeline v-else>
        <el-timeline-item
          v-for="(item, index) in contradictionList"
          :key="item.id"
          :hollow="false"
          :color="getSeverityColor(item.severity)"
        >
          <div class="timeline-content">
            <div class="timeline-card">
              <div class="card-header">
                <div class="header-left">
                  <h4>{{ item.contradictionPoint }}</h4>
                  <div class="card-tags">
                    <el-tag size="small" :type="getPartyTypeTag(item.partyType)">{{ item.partyType }}</el-tag>
                    <el-tag size="small" effect="plain">{{ item.partyName }}</el-tag>
                    <el-tag size="small" :type="getSeverityTag(item.severity)">{{ item.severity }}</el-tag>
                  </div>
                </div>
                <div class="header-actions">
                  <!-- 可以添加编辑、删除按钮 -->
                </div>
              </div>
              <div class="card-body">
                <div class="event-title">
                  <el-tag size="small" effect="plain" class="event-label">矛盾类型</el-tag>
                  <span>{{ item.contradictionType }}</span>
                </div>
                <div class="event-content">
                  <el-tag size="small" effect="plain" class="event-label">具体内容</el-tag>
                  <span>{{ item.content }}</span>
                </div>
                <div v-if="item.evidence?.length" class="evidence">
                  <div class="evidence-header" @click="toggleEvidence(item.id)">
                    <el-icon><Document /></el-icon>
                    <span class="evidence-title">证据信息（{{ item.evidence.length }}）</span>
                    <el-icon class="expand-icon" :class="{ 'is-expanded': expandedEvents.has(item.id) }">
                      <ArrowDown />
                    </el-icon>
                  </div>
                  <div class="evidence-content" v-show="expandedEvents.has(item.id)">
                    <div v-for="(evidence, evidenceIndex) in item.evidence" :key="evidenceIndex" class="evidence-item">
                      <div class="evidence-source" @click="handleFileClick([evidence])">
                        <span class="location">{{ evidence.fileName }} 第{{ evidence.pageNumber }}页</span>
                      </div>
                      <div class="evidence-highlight" @click="handleFileClick([evidence])">
                        <div class="highlight-item">
                          <span class="quote-mark">"</span>
                          {{ evidence.highlight }}
                          <span class="quote-mark">"</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 编辑事件对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑矛盾点"
      width="800px"
      destroy-on-close
      class="timeline-dialog"
      :modal="false"
      :draggable="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="editForm" label-width="100px" class="timeline-form">
        <el-form-item label="矛盾点" required>
          <el-input v-model="editForm.contradictionPoint" placeholder="请输入矛盾点" />
        </el-form-item>
        <el-form-item label="具体内容" required>
          <el-input 
            v-model="editForm.content" 
            type="textarea"
            :rows="3"
            placeholder="请输入具体内容"
          />
        </el-form-item>
        <el-form-item label="信息来源" required>
          <div class="evidence-form">
            <div class="form-item">
              <span class="label">来源：</span>
              <el-select 
                v-model="editForm.fileId" 
                placeholder="请选择文件"
                class="w-full"
                @change="handleFileChange"
                clearable
                multiple
              >
                <el-option
                  v-for="file in fileList"
                  :key="file.id"
                  :label="file.name"
                  :value="file.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加事件对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加矛盾点"
      width="800px"
      destroy-on-close
      class="timeline-dialog"
      :modal="false"
      :draggable="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="addForm" label-width="100px" class="timeline-form">
        <el-form-item label="矛盾点" required>
          <el-input v-model="addForm.contradictionPoint" placeholder="请输入矛盾点" />
        </el-form-item>
        <el-form-item label="具体内容" required>
          <el-input 
            v-model="addForm.content" 
            type="textarea"
            :rows="3"
            placeholder="请输入具体内容"
          />
        </el-form-item>
        <el-form-item label="信息来源">
          <div class="evidence-form">
            <div class="form-item">
              <span class="label">来源：</span>
              <el-select 
                v-model="addForm.fileId" 
                placeholder="请选择文件"
                class="w-full"
                @change="handleFileChange"
                clearable
                multiple
              >
                <el-option
                  v-for="file in fileList"
                  :key="file.id"
                  :label="file.name"
                  :value="file.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAdd">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Edit, Delete, Plus, Document, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { contradictionApi } from '@/api'


const props = defineProps<{
  caseImportId: string
  fileList: Array<{
    id: number
    name: string
    content?: string
    uploadTime: string
    text?: string
  }>
}>()

// 矛盾识别数据结构
interface Contradiction {
  id: number
  caseImportId: number
  contradictionPoint: string
  content: string
  partyType: string
  partyName: string
  contradictionType: string
  severity: string
  evidence: Evidence[]
  createTime: string
  updateTime: string
}
interface TextLocation {
  pageNumber: number;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  similarity: number;
  pageWidth: number;
  pageHeight: number;
}


interface Evidence {
  fileId: number
  fileName: string
  pageNumber: number
  highlight: string
}
const contradictionList = ref<Contradiction[]>([])
const loading = ref(false)
const groupedContradictions = ref<{ [key: string]: Contradiction[] }>({})

// 添加展开/收起状态管理
const expandedEvents = ref<Set<number>>(new Set())

// 切换证据展开/收起
const toggleEvidence = (eventId: number) => {
  if (expandedEvents.value.has(eventId)) {
    expandedEvents.value.delete(eventId)
  } else {
    expandedEvents.value.add(eventId)
  }
}

// 获取矛盾识别列表
const fetchContradictions = async () => {
  try {
    // 使用全局loading
    const result = await contradictionApi.getList(props.caseImportId, { loading: true })
    contradictionList.value = result || []
    console.log('矛盾识别数据:', contradictionList.value)
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取矛盾识别列表失败:', error)
    contradictionList.value = []
  }
}

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case '严重': return '#F56C6C'
    case '中等': return '#E6A23C'
    case '轻微': return '#67C23A'
    default: return '#909399'
  }
}

// 获取当事人类型标签
const getPartyTypeTag = (partyType: string) => {
  switch (partyType) {
    case '原告': return 'danger'
    case '被告': return 'warning'
    default: return 'info'
  }
}

// 获取严重程度标签
const getSeverityTag = (severity: string) => {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    case '轻微': return 'success'
    default: return 'info'
  }
}

// 编辑表单
const editForm = ref({
  id: 0,
  contradictionPoint: '',
  content: '',
  caseImportId: 0,
  fileId: 0
})

// 添加表单
const addForm = ref({
  contradictionPoint: '',
  content: '',
  caseImportId: 0,
  fileId: 0
})

// 编辑对话框
const editDialogVisible = ref(false)
const currentEditIndex = ref(-1)

// 添加对话框
const addDialogVisible = ref(false)

// 处理编辑事件
const handleEditEvent = (item: Contradiction, index: number) => {
  currentEditIndex.value = index
  editForm.value = {
    ...item,
    fileId: item.evidence?.[0]?.fileId || 0
  }
  editDialogVisible.value = true
}

// 处理删除事件
const handleDeleteEvent = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除该矛盾点吗？', '提示', {
      type: 'warning'
    })

    // 使用全局成功提示
    await contradictionApi.delete(id, {
      showSuccess: true,
      successMessage: '删除成功'
    })
    fetchContradictions()
  } catch (error) {
    if (error !== 'cancel') {
      // 全局已处理错误提示
      console.error('删除失败:', error)
    }
  }
}

// 处理添加事件
const handleAddEvent = () => {
  addForm.value = {
    contradictionPoint: '',
    content: '',
    caseImportId: Number(props.caseImportId),
    fileId: 0
  }
  addDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    // 使用全局成功提示
    await contradictionApi.update({
      ...editForm.value,
      fileId: editForm.value.fileId
    }, { showSuccess: true, successMessage: '保存成功' })

    editDialogVisible.value = false
    fetchContradictions()
  } catch (error) {
    // 全局已处理错误提示
    console.error('保存失败:', error)
  }
}

// 保存添加
const handleSaveAdd = async () => {
  try {
    // 这里应该调用添加API，暂时只刷新列表
    fetchContradictions()
    addDialogVisible.value = false
  } catch (error) {
    // 全局已处理错误提示
    console.error('添加失败:', error)
  }
}

// 添加文件点击处理函数
const handleFileClick = (evidence: Evidence[]) => {
  if (!evidence || evidence.length === 0) {
    return
  }

  // 转换为高亮信息格式
  const highlightInfo = evidence.map((item: Evidence) => ({
    fileId: item.fileId,
    fileName: item.fileName,
    // 不传递页码，使用全文搜索提高匹配成功率
    // pageNumber: item.pageNumber,
    highlight: item.highlight,
    locations: [] // 简化版本不包含位置信息
  }))

  console.log('点击证据信息:', highlightInfo)

  emit('detailHighlight', highlightInfo)
}

// 添加 emit 定义

const emit = defineEmits<{
  (e: 'detailHighlight', highlight: Array<{
    fileId: number
    fileName: string
    pageNumber?: number // 页码变为可选，支持全文搜索
    highlight: string
    locations?: Array<{
      pageNumber?: number
      x: number
      y: number
      width: number
      height: number
      text: string
      similarity: number
      pageWidth: number
      pageHeight: number
    }>
  }>): void
}>()

// 处理文件选择
const handleFileChange = (fileId: number) => {
  const selectedFile = props.fileList.find(file => file.id === fileId)
  if (selectedFile) {
    if (editDialogVisible.value) {
      editForm.value.fileId = fileId
    } else if (addDialogVisible.value) {
      addForm.value.fileId = fileId
    }
  }
}

defineExpose({ fetchContradictions })

// 组件挂载时获取数据
onMounted(() => {
  fetchContradictions()
})
</script>

<style scoped lang="scss">
.timeline-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  :deep(.el-timeline-item) {
    .el-timeline-item__node {
      margin-top: 12px;
    }

    .el-timeline-item__tail {
      top: 12px;
    }
  }

  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  .contradiction-content {
    min-height: 200px;
  }

  .timeline-content {
    .timeline-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      margin-bottom: 8px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
      }

      .card-header {
        padding: 12px 16px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .header-left {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #303133;
            font-weight: 600;
          }

          .card-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
          }
        }

        .header-actions {
          display: flex;
          gap: 8px;

          .el-button {
            padding: 4px;
          }
        }
      }

      .card-body {
        padding: 16px;

        .event-title, .event-content {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          font-size: 15px;
          font-weight: 500;
          color: #303133;

          .event-label {
            margin-right: 8px;
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #909399;
          }
        }

        .evidence {
          font-size: 13px;
          color: #909399;
          padding: 12px;
          margin-top: 12px;
          border-top: 1px dashed #ebeef5;
          background-color: #f8f9fa;
          border-radius: 4px;

          .evidence-header {
            display: flex;
            align-items: center;
            color: #606266;
            cursor: pointer;
            user-select: none;

            .el-icon {
              margin-right: 4px;
              font-size: 16px;
            }

            .evidence-title {
              font-weight: 500;
            }

            .expand-icon {
              margin-left: auto;
              transition: transform 0.3s ease;
              
              &.is-expanded {
                transform: rotate(180deg);
              }
            }
          }

          .evidence-content {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #ebeef5;

            .evidence-item {
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }
            }

            .evidence-source {
              margin-bottom: 4px;
              font-size: 12px;
              color: #909399;
              cursor: pointer;
              padding: 4px 8px;
              border-radius: 4px;
              transition: background-color 0.3s ease;

              &:hover {
                background-color: #ecf5ff;
              }

              .file-name {
                font-weight: 500;
                color: #606266;
                margin-right: 8px;
              }

              .location {
                color: #909399;
              }
            }

            .evidence-highlight {
              padding: 0 10px;

              .highlight-item {
                position: relative;
                padding: 10px 12px;
                background-color: #fff;
                border-left: 3px solid #409EFF;
                border-radius: 0 4px 4px 0;
                font-size: 13px;
                line-height: 1.6;
                color: #606266;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

                .quote-mark {
                  color: #909399;
                  font-size: 16px;
                  font-family: serif;
                  margin: 0 2px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.timeline-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 0 !important;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 30px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__footer) {
    padding: 20px 30px;
    border-top: 1px solid #ebeef5;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  .timeline-form {
    .w-full {
      width: 100%;
    }
  }

  .evidence-form {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    width: 100%;

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      width: 100%;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        flex: 0 0 100px;
        line-height: 32px;
        color: #606266;
      }

      :deep(.el-input),
      :deep(.el-textarea) {
        flex: 1;
        width: 100%;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style> 