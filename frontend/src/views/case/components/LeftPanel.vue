<template>
  <div class="left-panel">
    <!-- 文件列表区域 -->
    <div class="file-list">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文件名"
          prefix-icon="Search"
          clearable
        />
      </div>
      <div class="file-cards">
        <div
          v-for="file in filteredFiles"
          :key="file.id"
          class="file-card"
          :class="{ active: props.selectedFile?.id === file.id }"
          :data-file-id="file.id"
          @click="handleFileSelect(file)"
        >
          <div class="file-icon">
            <img :src="getFileIcon(file.name)" :alt="file.name" class="icon-img" />
          </div>
          <div class="file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-time">{{ formatDateTime(file.uploadTime) }}</span>
          </div>
          <el-icon class="delete-icon" @click.stop="handleDeleteFile(file)">
            <Delete />
          </el-icon>
        </div>
      </div>
      <div class="file-actions">
        <el-button
          v-if="props.analysisStatus === 0"
          type="primary"
          :loading="true"
          disabled
        >
          生成中，已用时{{ props.generationTime || '0秒' }}
        </el-button>
        <el-button v-else type="primary" @click="$emit('regenerate')">
          <el-icon><Refresh /></el-icon>
          重新生成
        </el-button>
      </div>
    </div>
    <!-- 文件内容预览区域 -->
    <div class="file-preview">
      <VuePdfEmbed
        v-if="props.selectedFile?.content"
        ref="pdfViewerRef"
        :source="props.selectedFile.content"
        :highlights="props.highlightInfo"
        :page="props.currentPage"
        :highlight-text="props.highlightText"
        :textLayer="true"
        :allPages="true"
        :scale="1.0"
        :width="800"
        class="pdf-embed"
        @text-selected="handleTextSelected"
        @rendered="handlePdfRendered"
        @loading-failed="handlePdfError"
      />
      <div v-else class="empty-preview">
        请选择文件查看内容
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, withDefaults } from 'vue'
import { Delete, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { caseApi } from '@/api'

import VuePdfEmbed from '@/components/VuePdfEmbed'
// 导入SVG图标
import wordIcon from '@/assets/word.svg'
import pdfIcon from '@/assets/pdf.svg'
import txtIcon from '@/assets/txt.svg'
import wpsIcon from '@/assets/wps.svg'

interface FileItem {
  id: number
  name: string
  content?: string
  uploadTime: string
  text?: string
}

interface Props {
  fileList: FileItem[]
  selectedFile: FileItem | null
  highlightInfo: Array<{
    pageNumber: number
    x: number
    y: number
    width: number
    height: number
    text: string
    similarity: number
    pageWidth: number
    pageHeight: number
  }>
  currentPage: number
  highlightText: string
  analysisStatus: number
  generationTime?: string
}

const props = withDefaults(defineProps<Props>(), {
  generationTime: '0秒'
})

const emit = defineEmits<{
  (e: 'update:fileList', value: FileItem[]): void
  (e: 'update:selectedFile', value: FileItem | null): void
  (e: 'regenerate'): void
  (e: 'fileSelect', file: FileItem): void
  (e: 'textSelected', data: { text: string; pageNumber: number }): void
}>()

const searchKeyword = ref('')
const pdfViewerRef = ref<InstanceType<typeof VuePdfEmbed> | null>(null)

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (!searchKeyword.value) return props.fileList
  return props.fileList.filter(file => 
    file.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 选择文件
const handleFileSelect = async (file: FileItem) => {
  emit('fileSelect', file)
}

// 处理文本选择
const handleTextSelected = (data: { text: string; pageNumber: number }) => {
  emit('textSelected', data)
}

// 处理PDF渲染完成事件
const handlePdfRendered = () => {
  console.log('PDF渲染完成')
}

// 处理PDF加载错误事件
const handlePdfError = (error: Error) => {
  console.error('PDF加载失败:', error)
}

// 删除文件
const handleDeleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm('确定要删除该文件吗？', '提示', {
      type: 'warning'
    })
    const newFileList = props.fileList.filter(f => f.id !== file.id)
    emit('update:fileList', newFileList)
    if (props.selectedFile?.id === file.id) {
      emit('update:selectedFile', null)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'docx':
    case 'doc':
      return wordIcon
    case 'wps':
      return wpsIcon
    case 'pdf':
      return pdfIcon
    case 'txt':
      return txtIcon
    default:
      return txtIcon
  }
}

// 日期格式化函数
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

// 组件卸载时清理 Blob URL
onUnmounted(() => {
  if (props.selectedFile?.content && props.selectedFile.content.startsWith('blob:')) {
    URL.revokeObjectURL(props.selectedFile.content)
  }
})
</script>

<style scoped lang="scss">
.left-panel {
  flex: 1;
  display: flex;
  gap: 20px;
  
  .file-list {
    flex: 0 0 320px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    
    .search-box {
      margin-bottom: 16px;
    }
    
    .file-cards {
      flex: 1;
      overflow-y: auto;
      
      .file-card {
        position: relative;
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 8px;
        background: #f5f7fa;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background: #ecf5ff;
        }
        
        &.active {
          background: #fff9e6;
          border: 1px solid #e6a23c;
        }

        &.highlight-border {
          &::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 8px;
            width: 6px;
            height: 6px;
            background-color: #e6a23c;
            border-radius: 50%;
          }
        }
        
        .file-icon {
          margin-right: 12px;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .icon-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .file-info {
          flex: 1;
          min-width: 0;
          
          .file-name {
            display: block;
            font-size: 14px;
            color: #303133;
            word-break: break-all;
            margin-bottom: 4px;
          }
          
          .file-time {
            display: block;
            font-size: 12px;
            color: #909399;
          }
        }
        
        .delete-icon {
          position: absolute;
          top: 8px;
          right: 8px;
          font-size: 16px;
          color: #909399;
          cursor: pointer;
          
          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
    
    .file-actions {
      margin-top: 16px;
      display: flex;
      gap: 8px;
      
      .el-button {
        flex: 1;
        justify-content: center;
      }
    }
  }
  
  .file-preview {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;

    // 美化滚动条
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .preview-content {
      white-space: pre-wrap;
      word-break: break-all;
    }

    .empty-preview {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style> 