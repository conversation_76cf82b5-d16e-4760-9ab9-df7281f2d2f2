<template>
  <div class="file-upload">
    <!-- 上传头部 -->
    <div v-if="props.showHeader !== false" class="upload-header">
      <div class="header-left">
        <h3>上传文件</h3>
        <span class="file-count">{{ fileList.length }} 个文件</span>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="handleClose">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="wizard-content">
      <div class="import-content">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="robot-container">
            <img src="@/assets/reboot.gif" alt="小天枢机器人" class="robot-gif" />
          </div>
          <h2 class="welcome-title">您好，我是小天枢</h2>
        </div>

        <!-- 上传区域 -->
        <div class="upload-section">
          <div class="upload-container">
        <el-upload
          ref="uploadRef"
          class="upload-area"
          :class="{ 'drag-over': isDragOver, 'has-files': fileList.length > 0 }"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          :show-file-list="false"
          accept=".doc,.docx,.pdf,.png,.jpg,.jpeg"
          :limit="10"
          multiple
          @dragover="isDragOver = true"
          @dragleave="isDragOver = false"
          @drop="isDragOver = false"
        >
          <div class="upload-placeholder">
            <el-icon class="upload-icon">
              <UploadFilled v-if="!isDragOver" />
              <FolderOpened v-else />
            </el-icon>
            <div class="upload-text">
              <span v-if="!isDragOver">
                小天枢为您提供智能阅卷服务，请拖拽文件到此处或点击上传
              </span>
              <span v-else class="drag-text">释放文件开始上传</span>
            </div>
            <div class="upload-hint">支持 word、pdf、png、jpg 格式</div>
          </div>
        </el-upload>

        <!-- 文件列表 -->
        <div v-if="fileList.length > 0" class="file-list">
          <div class="file-list-header">
            <span>已选择文件 ({{ fileList.length }})</span>
            <el-button type="text" size="small" @click="clearAllFiles">清空</el-button>
          </div>
          <div class="file-items">
            <div v-for="file in fileList" :key="file.uid" class="file-item" :class="{ error: file.status === 'fail' }">
              <div class="file-info">
                <el-icon class="file-icon">
                  <Document v-if="file.status !== 'fail'" />
                  <WarningFilled v-else />
                </el-icon>
                <div class="file-details">
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
                </div>
                <div class="file-actions">
                  <el-icon class="delete-icon" @click="removeFile(file)">
                    <Delete />
                  </el-icon>
                </div>
              </div>
              <div v-if="file.status === 'fail'" class="file-error">
                上传失败，请重试
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="wizard-footer">
      <div class="upload-actions">
        <!-- 默认显示上传按钮 -->
        <div v-if="!allFilesProcessed">
          <el-button
            type="primary"
            size="large"
            :loading="uploading"
            :disabled="uploading || fileList.length === 0"
            @click="startUpload"
            class="upload-btn"
          >
            <el-icon v-if="!uploading"><Upload /></el-icon>
            {{ uploading ? '上传中...' : (fileList.length > 0 ? `开始上传` : '开始上传') }}
          </el-button>
          <el-button @click="handleCancel" size="large">取消</el-button>
        </div>

        <!-- 完成后的操作按钮 -->
        <div v-else>
          <el-button @click="handleCancel" size="large">取消</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Document, Delete, Close, Service, FolderOpened, WarningFilled, Upload } from '@element-plus/icons-vue'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
import { caseApi } from '@/api'
import { formatFileSize } from '@/utils/api-helper'

const props = defineProps<{
  caseImportId: string | number
  showHeader?: boolean
  visible?: boolean
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'fileUploaded'): void
}>()

// 响应式数据
const fileList = ref<UploadFiles>([])
const uploading = ref(false)
const uploadRef = ref<UploadInstance>()
const isDragOver = ref(false)

// 计算属性
const hasUploadedFiles = computed(() => {
  return fileList.value.some(file => file.status === 'success' || file.status === 'fail')
})

const hasUnuploadedFiles = computed(() => {
  return fileList.value.some(file => !file.status || file.status === 'ready')
})

const unuploadedCount = computed(() => {
  return fileList.value.filter(file => !file.status || file.status === 'ready').length
})

const allFilesProcessed = computed(() => {
  return fileList.value.length > 0 &&
         fileList.value.every(file => file.status === 'success' || file.status === 'fail') &&
         !uploading.value
})

// 支持的文件类型
const allowedTypes = ['.doc', '.docx', '.pdf', '.png', '.jpg', '.jpeg']

// 触发文件选择
const triggerFileSelect = () => {
  const uploadElement = uploadRef.value?.$el?.querySelector('.el-upload')
  if (uploadElement) {
    uploadElement.click()
  }
}

// 处理文件变化
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  console.log('文件变化:', file, files)
  
  // 检查文件格式
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('只支持 word、pdf、png、jpg 格式文件')
    return
  }

  // 检查文件大小（限制为100MB）
  const maxSize = 2 * 1024 * 1024 * 1024 // 100MB
  if (file.size && file.size > maxSize) {
    ElMessage.error('文件大小不能超过2G')
    return
  }
  
  fileList.value = files
  console.log('文件列表更新:', fileList.value)
}

// 移除文件
const removeFile = (file: UploadFile) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 清空所有文件
const clearAllFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
}

// 开始上传
const startUpload = async () => {
  // 只获取未上传的文件
  const unuploadedFiles = fileList.value.filter(file => !file.status || file.status === 'ready')

  if (unuploadedFiles.length === 0) {
    ElMessage.warning('没有需要上传的文件')
    return
  }

  uploading.value = true
  try {
    // 更新未上传文件的状态
    unuploadedFiles.forEach(file => {
      file.status = 'uploading'
      file.percentage = 0
    })

    // 逐个上传未上传的文件
    for (const fileItem of unuploadedFiles) {
      const file = fileItem.raw as File

      try {
        // 模拟真实的上传进度
        let currentProgress = 0
        const progressInterval = setInterval(() => {
          if (currentProgress < 85) {
            // 前85%进度较快
            const increment = Math.random() * 15 + 5
            currentProgress = Math.min(currentProgress + increment, 85)
            fileItem.percentage = currentProgress
          } else if (currentProgress < 95) {
            // 85%-95%进度较慢
            const increment = Math.random() * 3 + 1
            currentProgress = Math.min(currentProgress + increment, 95)
            fileItem.percentage = currentProgress
          }
          // 95%-100%由API完成后设置
        }, 300)

        // 调用单文件上传API
        await caseApi.uploadSingleFile(props.caseImportId, file)

        // 清除进度模拟
        clearInterval(progressInterval)

        // 完成上传
        fileItem.status = 'success'
        fileItem.percentage = 100

        // 短暂延迟以显示完成状态
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        fileItem.status = 'fail'
        fileItem.percentage = 0
        console.error(`文件 ${file.name} 上传失败:`, error)
        // 全局已处理错误提示，这里只记录日志
      }
    }

    const successCount = fileList.value.filter(f => f.status === 'success').length
    const failedCount = fileList.value.filter(f => f.status === 'fail').length

    if (successCount > 0) {
      // 保留批量上传结果的汇总提示（业务逻辑反馈）
      if (failedCount > 0) {
        ElMessage.success(`成功上传 ${successCount} 个文件，${failedCount} 个文件失败`)
      } else {
        ElMessage.success(`成功上传 ${successCount} 个文件`)
      }
      emit('fileUploaded')
      
      // 如果全部上传成功，立即关闭模态框
      if (failedCount === 0) {
        emit('close')
      }
    }

  } catch (error) {
    // 全局已处理错误提示
    console.error('上传失败:', error)
    fileList.value.forEach(file => {
      if (file.status === 'uploading') {
        file.status = 'fail'
      }
    })
  } finally {
    uploading.value = false
  }
}

// 关闭上传面板
const handleClose = () => {
  emit('close')
}

// 无需重新生成
const handleNoRegenerate = () => {
  // TODO: 后续连接后端API
  console.log('无需重新生成')
  emit('close')
}

// 重新生成
const handleRegenerate = () => {
  // TODO: 后续连接后端API
  console.log('重新生成')
  emit('close')
}

// 取消
const handleCancel = () => {
  emit('close')
}

// 重置组件状态
const resetState = () => {
  fileList.value = []
  uploading.value = false

  // 重置上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 监听模态框显示状态
watch(() => props.visible, (newVisible, oldVisible) => {
  // 当模态框从隐藏变为显示时，重置状态
  if (newVisible && !oldVisible) {
    resetState()
  }
})

// 组件挂载时也重置一次状态
onMounted(() => {
  resetState()
})


</script>

<style scoped lang="scss">
.file-upload {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: transparent;
  border-radius: 0;
  overflow: visible;
}

.upload-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5ea;
  background: #f8f9fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.file-count {
  font-size: 14px;
  color: #86868B;
}

.header-actions {
  display: flex;
  gap: 8px;
}

:deep(.el-dialog__body) {
  padding: 32px 32px 20px 32px !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;

  @media (max-width: 768px) {
    padding: 24px 24px 16px 24px !important;
  }
}

:deep(.el-dialog__footer) {
  padding: 20px 32px 32px 32px !important;

  @media (max-width: 768px) {
    padding: 16px 24px 24px 24px !important;
  }
}

.wizard-content {
  flex: 1;
  display: flex;
  flex-direction: column;

  .import-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;

    // 欢迎区域样式
    .welcome-section {
      text-align: center;
      padding: 20px 0;

      .robot-container {
        margin-bottom: -20px;

        .robot-gif {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          object-fit: contain;
          animation: float 3s ease-in-out infinite;

          @media (max-width: 768px) {
            width: 80px;
            height: 80px;
          }
        }
      }

      .welcome-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 20px;
        }
      }

      .welcome-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    // 上传区域样式
    .upload-section {
      width: 100%;

      .upload-container {
        margin-bottom: 20px;
      }
    }
  }
}

.upload-area {
  margin-bottom: 24px;

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed #d1d5db;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(243, 244, 246, 0.9) 100%);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    @media (max-width: 768px) {
      height: 160px;
      border-radius: 12px;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: #3b82f6;
      background: linear-gradient(135deg, rgba(240, 249, 255, 0.9) 0%, rgba(219, 234, 254, 0.9) 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);

      &::before {
        opacity: 1;
      }
    }
  }

  &.drag-over :deep(.el-upload-dragger) {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(236, 253, 245, 0.9) 0%, rgba(209, 250, 229, 0.9) 100%);
    transform: scale(1.02);
    box-shadow: 0 12px 30px rgba(16, 185, 129, 0.2);
  }

  &.has-files :deep(.el-upload-dragger) {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(236, 253, 245, 0.9) 0%, rgba(209, 250, 229, 0.9) 100%);
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    position: relative;
    z-index: 1;
    padding: 20px;

    .upload-icon {
      font-size: 48px;
      color: #9ca3af;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      @media (max-width: 768px) {
        font-size: 40px;
        margin-bottom: 12px;
      }
    }

    .upload-text {
      font-size: 16px;
      color: #6b7280;
      font-weight: 500;
      margin-bottom: 8px;
      text-align: center;

      @media (max-width: 768px) {
        font-size: 14px;
      }

      .drag-text {
        color: #10b981;
        font-weight: 600;
      }
    }

    .upload-hint {
      font-size: 12px;
      color: #9ca3af;
      text-align: center;
    }
  }

  &:hover .upload-placeholder .upload-icon,
  &.drag-over .upload-placeholder .upload-icon {
    color: #3b82f6;
    transform: scale(1.1);
  }

  &.drag-over .upload-placeholder .upload-icon {
    color: #10b981;
  }
}

// 文件列表
.file-list {
  margin-top: 24px;

  .file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;

    span {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
  }

  .file-items {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
  }

  .file-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f3f4f6;
    }

    &.error {
      background: #fef2f2;
      border-color: #fecaca;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .file-icon {
        font-size: 20px;
        color: #3b82f6;
        flex-shrink: 0;

        &.error {
          color: #ef4444;
        }
      }

      .file-details {
        flex: 1;
        min-width: 0;

        .file-name {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          display: block;
          font-size: 12px;
          color: #6b7280;
          margin-top: 2px;
        }
      }

      .file-actions {
        .delete-icon {
          font-size: 16px;
          color: #ef4444;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            background: #fee2e2;
            color: #dc2626;
          }
        }
      }
    }

    .file-error {
      margin-top: 8px;
      font-size: 12px;
      color: #ef4444;
    }
  }
}

.wizard-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  min-height: 20px;
}

// 上传按钮
.upload-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    gap: 12px;
  }

  .el-button {
    border-radius: 12px;
    font-weight: 600;
    padding: 14px 32px;
    font-size: 16px;
    transition: all 0.3s ease;
    min-width: 120px;

    @media (max-width: 768px) {
      padding: 12px 24px;
      font-size: 14px;
      min-width: 100px;
    }

    &.el-button--default {
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(209, 213, 219, 0.8);
      color: #6b7280;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        border-color: #3b82f6;
        color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    justify-content: center;

    &:disabled {
      background: #f3f4f6 !important;
      color: #9ca3af !important;
      cursor: not-allowed !important;
      transform: none !important;
      box-shadow: none !important;
    }

    &:not(:disabled) {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      border: none;
      color: white;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
      }

      &:active {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }

    &.is-loading {
      pointer-events: none;
    }
  }

  .el-button--warning.upload-btn:not(:disabled) {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

    &:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
    }
  }
}


// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}


</style>
