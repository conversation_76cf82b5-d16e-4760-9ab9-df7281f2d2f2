<template>
  <div class="dispute-points">
    <!-- 诉辩关系图组件 -->
    <dispute-relation-view
      ref="disputeRelationViewRef"
      :plaintiff-parties="plaintiffParties"
      :defendant-parties="defendantParties"
      :active-point-id="activePointId"
      :related-point-ids="relatedPointIds"
      :active-relation="activeRelation"
      :relations="pointRelations"
      :case-import-id="caseImportId"
      @add-point="showAddPointDialog"
      @point-click="handlePointClick"
      @edit-point="handleEditPoint"
      @delete-point="handleDeletePoint"
      @update-point="handlePointInlineUpdate"
      @regenerate="handleRegenerateLitigationPoints"
      @relation-updated="handleRelationUpdated"
    />

    <!-- 争议焦点列表组件 -->
    <dispute-focus-list
      ref="disputeFocusListRef"
      :focus-list="disputeFocusList"
      :case-import-id="Number(caseImportId)"
      @add-focus="handleFocusAdded"
      @regenerate-all-focus="handleRegenerateAllFocus"
      @delete-all-focus="handleDeleteAllFocus"
      @focus-title-click="handleFocusTitleClick"
      @edit-focus="handleEditFocus"
      @delete-focus="handleDeleteFocus"
      @update-focus="handleUpdateFocus"
      @claim-click="handleClaimClick"
      @evidence-click="handleEvidenceClick"
      @law-click="handleLawClick"
      @task-completed="handleDisputeFocusTaskCompleted"
      @refresh-focus-list="handleRefreshFocusList"
    />

    <!-- 争议焦点说理组件 -->
    <dispute-reasoning />

    <!-- 添加/编辑诉辩观点对话框组件 -->
    <add-point-dialog
      v-model:visible="addPointDialogVisible"
      v-model:form="addPointForm"
      :saving="addPointSaving"
      :plaintiff-parties="plaintiffParties"
      :defendant-parties="defendantParties"
      :is-edit-mode="isEditMode"
      :editing-point="editingPoint"
      :case-import-id="caseImportId"
      @save="handleAddPointSave"
      @cancel="handleAddPointCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed, defineExpose, provide } from 'vue'
import { ElMessage, ElMessageBox, ElSelect } from 'element-plus'
import { disputeFocusApi, type DisputeFocus } from '@/api'
import { 
  litigationPointsApi, 
  type SaveDisputePointsRequest,
  type Evidence,
  type DisputePoint,
  type DisputePointsResponse
} from '@/api/litigationPoints'

import DisputeRelationView from './DisputeRelationView.vue'
import DisputeFocusList from './DisputeFocusList.vue'
import DisputeReasoning from './DisputeReasoning.vue'
import AddPointDialog from './AddPointDialog.vue'

const props = defineProps({
  caseImportId: {
    type: String,
    required: true
  },
  fileList: {
    type: Array as () => Array<{
      id: number;
      name: string;
      content?: string;
      uploadTime: string;
      text?: string;
    }>,
    default: () => []
  },

})

const emit = defineEmits(['evidence-click', 'law-click', 'locate-evidence', 'detailHighlight', 'add-focus', 'edit-focus', 'delete-focus', 'clear-selection'])

// 提供案件ID给子组件
provide('caseImportId', ref(props.caseImportId))

// 统一类型定义
interface TextLocation {
  pageNumber: number;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  similarity: number;
  pageWidth: number;
  pageHeight: number;
}

// 使用从API导入的类型，但扩展一些本地特有的字段
interface LocalDisputePoint extends DisputePoint {
  relatedPointId?: string; // 添加关联ID字段
}

interface LocalEvidence extends Evidence {
  location?: string;
  locations?: TextLocation[];
}

interface PartyInfo {
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}

interface DisputeFocus {
  id: number;
  caseImportId: number;
  description: string;
  plaintiffClaim: string;
  defendantClaim: string;
  amount: number | null;
  transactionDate: string | null;
  sortOrder: number;
  plaintiffEvidence: Evidence[];
  defendantEvidence: Evidence[];
  laws: string[] | null;
  lawSummaries: string[] | null;
  createTime: string;
  updateTime: string;
}

interface Relation {
  id: number;
  plaintiffPointId: number;
  defendantPointId: number;
  relationType: string;
  createTime: string;
  updateTime: string;
}

interface DisputePointsResponse {
  plaintiffs: PartyInfo[];
  defendants: PartyInfo[];
  disputes: DisputeFocus[];
  relations: Relation[];
}

// 当事人列表
const plaintiffParties = ref<{
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}[]>([])

const defendantParties = ref<{
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}[]>([])



// 当事人信息
const plaintiffInfo = ref<{
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}>({
  id: '',
  name: '',
  role: '诉方',
  date: '',
  pointsByDate: {}
})

const defendantInfo = ref<{
  id: string;
  name: string;
  role: string;
  date: string;
  pointsByDate?: Record<string, DisputePoint[]>;
}>({
  id: '',
  name: '',
  role: '辩方',
  date: '',
  pointsByDate: {}
})

// 诉辩观点和关系
const plaintiffPoints = ref<DisputePoint[]>([])
const defendantPoints = ref<DisputePoint[]>([])
const pointRelations = ref<{
  id?: number;
  plaintiffPointId: string;
  defendantPointId: string;
  relationType: string;
}[]>([])
const disputeFocusList = ref<{
  id: number; // 修改为 number 类型，与 API 接口一致
  title?: string;
  description: string;
  category?: string;
  caseImportId?: number;
  plaintiffClaim?: string;
  defendantClaim?: string;
  conclusion?: string; // 添加结论字段
  laws?: string[] | null;
  lawSummaries?: string[] | null;
  plaintiffEvidence?: Evidence[];
  defendantEvidence?: Evidence[];
  createTime?: string;
  updateTime?: string;
}[]>([])

// 当前选中的观点ID
const activePointId = ref<string | null>(null)

// 当前显示的关系
const activeRelation = ref<{
  plaintiffPointId: string;
  defendantPointId: string;
  relationType: string;
} | null>(null)

// 相关联的观点ID列表（支持多个）
const relatedPointIds = ref<string[]>([])

// 添加诉辩观点相关数据
const addPointDialogVisible = ref(false)
const addPointSaving = ref(false)
const isEditMode = ref(false)
const editingPoint = ref<DisputePoint | null>(null)

// 工具函数：转换 side 显示文本
const getSideDisplayText = (side: string): string => {
  switch (side) {
    case 'plaintiff':
      return '诉方'
    case 'defendant':
      return '辩方'
    default:
      return side
  }
}

// 工具函数：转换显示文本为 side 值
const getSideValue = (displayText: string): string => {
  switch (displayText) {
    case '诉方':
      return 'plaintiff'
    case '辩方':
      return 'defendant'
    default:
      return displayText
  }
}
const addPointForm = ref({
  plaintiff: {
    partyName: '',
    content: '',
    pointDate: new Date().toISOString().split('T')[0]
  },
  defendant: {
    partyName: '',
    content: '',
    pointDate: new Date().toISOString().split('T')[0]
  }
})

// 添加诉辩观点时的可用文件列表
const availableFiles = ref<{ id: number, name: string }[]>([])

// 连接点坐标
const connectionPoints = ref<{
  plaintiff?: { x: number, y: number },
  defendant?: { x: number, y: number },
  top?: number,
  height?: number
}>({})

// 检查一个观点是否为相关点
const isRelatedPoint = (pointId: string): boolean => {
  if (!activePointId.value || !activeRelation.value) return false
  
  return pointId === activeRelation.value.plaintiffPointId || 
         pointId === activeRelation.value.defendantPointId
}

// 更新连接线位置
const updateConnectionLine = () => {
  // 不再需要复杂的位置计算
}

// 获取连接线所需的DOM元素
const getConnectionElements = () => {
  // 找到当前激活的观点和相关联的观点的DOM元素
  const activePointElement = document.querySelector(`.point-item[data-point-id="${activePointId.value}"]`)
  const relatedPointElement = document.querySelector(`.point-item[data-point-id="${relatedPointId.value}"]`)
  
  if (!activePointElement || !relatedPointElement) {
    console.warn('未找到观点元素', activePointId.value, relatedPointId.value)
    return null
  }
  
  // 获取连接线容器
  const relationContainer = document.querySelector('.relation-lines-container')
  if (!relationContainer) {
    console.warn('未找到连接区域容器')
    return null
  }
  
  // 获取滚动容器
  const plaintiffList = document.querySelector('.plaintiff .points-list')
  const defendantList = document.querySelector('.defendant .points-list')
  
  return {
    activePointElement,
    relatedPointElement,
    relationContainer,
    plaintiffList,
    defendantList
  }
}

// 获取元素位置信息
const getElementPositions = (elements: any) => {
  const { activePointElement, relatedPointElement, relationContainer } = elements
  
  // 获取各个元素的位置信息
  const activeRect = activePointElement.getBoundingClientRect()
  const relatedRect = relatedPointElement.getBoundingClientRect()
  const containerRect = relationContainer.getBoundingClientRect()
  
  return {
    activeRect,
    relatedRect,
    containerRect
  }
}

// 计算连接点坐标
const calculateConnectionCoordinates = (
  isActiveOnLeft: boolean, 
  activeRect: DOMRect, 
  relatedRect: DOMRect, 
  containerRect: DOMRect, 
  plaintiffList: Element | null, 
  defendantList: Element | null
) => {
  // 计算元素中心点的垂直位置，考虑滚动位置
  let activeCenter, relatedCenter
  
  if (isActiveOnLeft) {
    // 活跃元素在左侧(诉方)
    const plaintiffScrollTop = plaintiffList ? (plaintiffList as HTMLElement).scrollTop : 0
    const defendantScrollTop = defendantList ? (defendantList as HTMLElement).scrollTop : 0
    
    // 计算诉方元素的中心点位置（相对于连接线容器的顶部）
    // 元素位置 = 元素在视口中的位置 - 连接容器在视口中的顶部位置 + 滚动容器的滚动位置
    activeCenter = (activeRect.top + activeRect.height/2) - containerRect.top + plaintiffScrollTop
    
    // 计算辩方元素的中心点位置
    relatedCenter = (relatedRect.top + relatedRect.height/2) - containerRect.top + defendantScrollTop
  } else {
    // 活跃元素在右侧(辩方)
    const plaintiffScrollTop = plaintiffList ? (plaintiffList as HTMLElement).scrollTop : 0
    const defendantScrollTop = defendantList ? (defendantList as HTMLElement).scrollTop : 0
    
    // 计算辩方元素的中心点位置
    activeCenter = (activeRect.top + activeRect.height/2) - containerRect.top + defendantScrollTop
    
    // 计算诉方元素的中心点位置
    relatedCenter = (relatedRect.top + relatedRect.height/2) - containerRect.top + plaintiffScrollTop
  }
  
  // 计算连接线的位置 - 相对于连接线容器
  const top = Math.min(activeCenter, relatedCenter)
  const height = Math.abs(activeCenter - relatedCenter)
  
  return {
    top,
    height
  }
}

// 添加节流函数优化滚动事件处理
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function(this: any, ...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

// 优化的滚动事件处理函数
const handleScroll = debounce(() => {
  if (activePointId.value && relatedPointId.value) {
    updateConnectionLine()
  }
}, 16) // 约等于60fps

// 设置事件监听器
const setupEventListeners = () => {
  // 不再需要监听滚动事件
}

// 移除事件监听器
const removeEventListeners = () => {
  // 不再需要移除滚动事件监听器
}

// 组件挂载时设置事件监听器
onMounted(() => {
  // 延迟加载数据
  setTimeout(() => {
    if (document.visibilityState !== 'hidden') {
      getDisputePoints()
    }
  }, 100)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  // 不再需要移除事件监听器
})

// 添加加载数据的方法，供外部调用
const loadData = () => {
  getDisputePoints()
}

// 从外部调用的重新生成方法（不显示确认对话框）
const regenerateFromParent = async () => {
  try {
    saving.value = true

    // 清空本地数据
    disputeFocusList.value = []

    // 调用重新生成争议焦点标签所有任务的API（包括诉辩关系和争议焦点）
    await disputeFocusApi.regenerateAll(props.caseImportId)

    // 等接口返回成功后，再显示TaskGeneratingStatus组件

    // 1. 触发诉辩关系的TaskGeneratingStatus（不调用API，只显示状态）
    if (disputeRelationViewRef.value?.startTaskGeneratingFromParent) {
      disputeRelationViewRef.value.startTaskGeneratingFromParent()
    }

    // 2. 触发争议焦点的TaskGeneratingStatus
    // 先显示任务状态容器
    if (disputeFocusListRef.value?.startTaskGenerating) {
      disputeFocusListRef.value.startTaskGenerating()
    }

    const focusTaskRef = disputeFocusListRef.value?.taskGeneratingRef
    if (focusTaskRef) {
      focusTaskRef.startTaskExecution(true) // 传入true表示手动开始，从0计时
      setTimeout(() => {
        focusTaskRef.startTaskMonitoring()
      }, 2000)
    }

  } catch (error) {
    console.error('重新生成所有争议焦点失败:', error)
    throw error // 重新抛出错误，让父组件处理
  } finally {
    saving.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  loadData,
  regenerateFromParent
})





// 计算按日期分组的诉方观点
const plaintiffPointsByDate = computed(() => {
  return groupPointsByDate(plaintiffPoints.value)
})

// 计算按日期分组的辩方观点
const defendantPointsByDate = computed(() => {
  return groupPointsByDate(defendantPoints.value)
})



// 按日期分组观点的工具函数
const groupPointsByDate = (points: DisputePoint[]): Record<string, DisputePoint[]> => {
  const groups: Record<string, DisputePoint[]> = {}
  
  // 按日期排序
  const sortedPoints = [...points].sort((a, b) => {
    const dateA = a.pointDate || '0'
    const dateB = b.pointDate || '0'
    return dateA.localeCompare(dateB)
  })
  
  // 按日期分组
  sortedPoints.forEach(point => {
    const date = point.pointDate || '未知日期'
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(point)
  })
  
  return groups
}

// 处理观点点击
const handlePointClick = (point: DisputePoint, side: 'plaintiff' | 'defendant') => {
  // 再次点击同一个观点，取消选中
  if (activePointId.value === point.id.toString()) {
    resetActivePoint()
    // 清除连线
    disputeRelationViewRef.value?.clearConnections?.()
    return
  }
  
  // 设置当前激活的观点
  setActivePoint(point, side)
  
  // 如果是诉方观点，检查对应的辩方观点是否在当前选中的辩方当事人下
  if (side === 'plaintiff') {
    const relatedDefendantPointId = findRelatedPointId('plaintiff', point.id)
    if (relatedDefendantPointId) {
      // 查找包含该观点的辩方当事人
      const targetDefendant = defendantParties.value.find(defendant => {
        if (defendant.pointsByDate) {
          return Object.values(defendant.pointsByDate).some(points => 
            points.some(p => p.id.toString() === relatedDefendantPointId)
          )
        }
        return false
      })
      
      // 滚动到对应观点
      if (targetDefendant) {
        // 如果已经是当前选中的当事人，直接滚动到对应观点
        scrollToRelatedPoint(relatedDefendantPointId, 'defendant')
      }
    }
  }
  
  // 如果有evidence信息，触发高亮显示
  if (point.evidence && point.evidence.length > 0) {
    emit('detailHighlight', point.evidence)
  }
}

// 重置激活的观点
const resetActivePoint = () => {
  activePointId.value = null
  activeRelation.value = null
  relatedPointIds.value = []
}

// 处理容器点击事件（点击空白区域清除高亮）


// 设置激活的观点
const setActivePoint = (point: DisputePoint, side: 'plaintiff' | 'defendant') => {
  activePointId.value = point.id.toString()

  // 查找所有与该观点相关的关系
  const relatedRelations = pointRelations.value.filter(rel => {
    if (side === 'plaintiff') {
      return rel.plaintiffPointId === point.pointId
    } else {
      return rel.defendantPointId === point.pointId
    }
  })

  // 收集所有相关观点的ID
  const relatedIds: string[] = []
  let primaryRelation: {
    plaintiffPointId: string
    defendantPointId: string
    relationType: string
  } | null = null

  relatedRelations.forEach(relation => {
    if (side === 'plaintiff') {
      // 找到对应的辩方观点ID
      const relatedDefendantPoint = defendantPoints.value.find(p => p.pointId === relation.defendantPointId)
      if (relatedDefendantPoint) {
        relatedIds.push(relatedDefendantPoint.id.toString())
      }
    } else {
      // 找到对应的诉方观点ID
      const relatedPlaintiffPoint = plaintiffPoints.value.find(p => p.pointId === relation.plaintiffPointId)
      if (relatedPlaintiffPoint) {
        relatedIds.push(relatedPlaintiffPoint.id.toString())
      }
    }

    // 使用第一个关系作为主要关系（用于向后兼容）
    if (!primaryRelation) {
      primaryRelation = relation
    }
  })

  activeRelation.value = primaryRelation
  relatedPointIds.value = relatedIds
}

// 处理诉方点击
const handlePlaintiffClick = () => {
  togglePartySelect('plaintiff')
}

// 处理辩方点击
const handleDefendantClick = () => {
  togglePartySelect('defendant')
}

// 切换当事人选择器
const togglePartySelect = (side: 'plaintiff' | 'defendant') => {
  const selectRef = side === 'plaintiff' ? plaintiffSelectRef.value : defendantSelectRef.value
  const selectEl = selectRef?.$el.querySelector('input')
  if (selectEl) {
    selectEl.click()
    selectRef?.focus()
  }
}



// 根据ID查找相关的观点ID
const findRelatedPointId = (side: 'plaintiff' | 'defendant', pointId: number): string | undefined => {
  const pointIdStr = pointId.toString()

  if (pointRelations.value && pointRelations.value.length > 0) {
    // 关系数据中的 plaintiffPointId/defendantPointId 对应观点数据中的 pointId 字段

    if (side === 'plaintiff') {
      // 查找诉方观点对应的辩方观点
      // 首先找到当前观点的 pointId
      const plaintiffPoint = plaintiffPoints.value.find(p => p.id.toString() === pointIdStr)
      if (plaintiffPoint && plaintiffPoint.pointId) {
        const relation = pointRelations.value.find(rel => rel.plaintiffPointId === plaintiffPoint.pointId)
        if (relation) {
          // 根据 defendantPointId 找到对应的辩方观点的数据库ID
          const defendantPoint = defendantPoints.value.find(p => p.pointId === relation.defendantPointId)
          return defendantPoint?.id.toString()
        }
      }
    } else {
      // 查找辩方观点对应的诉方观点
      const defendantPoint = defendantPoints.value.find(p => p.id.toString() === pointIdStr)
      if (defendantPoint && defendantPoint.pointId) {
        const relation = pointRelations.value.find(rel => rel.defendantPointId === defendantPoint.pointId)
        if (relation) {
          // 根据 plaintiffPointId 找到对应的诉方观点的数据库ID
          const plaintiffPoint = plaintiffPoints.value.find(p => p.pointId === relation.plaintiffPointId)
          return plaintiffPoint?.id.toString()
        }
      }
    }
  }
  return undefined
}

// 添加诉辩观点相关方法
const showAddPointDialog = () => {
  // 重置编辑状态
  isEditMode.value = false
  editingPoint.value = null
  // 重置表单
  resetAddPointForm()
  // 初始化数据
  initializeAddPointData()
  // 显示对话框
  addPointDialogVisible.value = true
}

// 编辑观点
const handleEditPoint = (point: DisputePoint) => {
  // 设置编辑模式
  isEditMode.value = true
  editingPoint.value = point

  // 重置表单
  resetAddPointForm()

  // 查找相关的观点对（通过pointId关系）
  const relatedPoints = findRelatedPointPair(point)

  // 预填充表单数据 - 显示双方观点
  if (relatedPoints.plaintiffPoint) {
    addPointForm.value.plaintiff = {
      partyName: relatedPoints.plaintiffPoint.partyName,
      content: relatedPoints.plaintiffPoint.content,
      pointDate: relatedPoints.plaintiffPoint.pointDate || new Date().toISOString().split('T')[0]
    }
  }

  if (relatedPoints.defendantPoint) {
    addPointForm.value.defendant = {
      partyName: relatedPoints.defendantPoint.partyName,
      content: relatedPoints.defendantPoint.content,
      pointDate: relatedPoints.defendantPoint.pointDate || new Date().toISOString().split('T')[0]
    }
  }

  // 初始化数据
  initializeAddPointData()
  // 显示对话框
  addPointDialogVisible.value = true
}

// 删除观点
const handleDeletePoint = async (point: DisputePoint) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个观点吗？\n\n"${point.content.substring(0, 50)}${point.content.length > 50 ? '...' : ''}"`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除API（API已配置自动显示成功消息）
    await litigationPointsApi.delete(point.id)

    // 重新加载数据
    getDisputePoints()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除观点失败:', error)
      ElMessage.error('删除观点失败')
    }
  }
}

// 处理观点内联更新
const handlePointInlineUpdate = (updatedPoint: DisputePoint) => {
  // 重新加载数据以确保数据同步
  getDisputePoints()
}

const resetAddPointForm = () => {
  addPointForm.value = {
    plaintiff: {
      partyName: '',
      content: '',
      pointDate: new Date().toISOString().split('T')[0]
    },
    defendant: {
      partyName: '',
      content: '',
      pointDate: new Date().toISOString().split('T')[0]
    }
  }
}

const initializeAddPointData = () => {
  // 从父组件获取可用文件列表
  availableFiles.value = props.fileList.map(file => ({
    id: file.id,
    name: file.name
  }))
}



const handleAddPointCancel = () => {
  addPointDialogVisible.value = false
  resetAddPointForm()
}

const handleAddPointSave = async () => {
  try {
    addPointSaving.value = true

    if (isEditMode.value && editingPoint.value) {
      // 编辑模式：更新现有观点
      await handleUpdatePoint()
    } else {
      // 添加模式：创建新观点
      await handleCreatePoint()
    }

  } catch (error) {
    // 全局已处理错误提示
    console.error('保存诉辩观点失败:', error)
  } finally {
    addPointSaving.value = false
  }
}

// 创建新观点
const handleCreatePoint = async () => {
  // 验证表单
  if (!validateAddPointForm()) {
    return
  }

  // 准备保存数据
  const saveData = prepareSaveData()

  // 调用保存API（API已配置自动显示成功消息）
  await saveDisputePoints(saveData)

  // 保存成功，关闭对话框并重置表单
  addPointDialogVisible.value = false
  resetAddPointForm()

  // 刷新数据
  await getDisputePoints()
}

// 更新现有观点
const handleUpdatePoint = async () => {
  if (!editingPoint.value) return

  // 验证表单（编辑模式下的验证）
  if (!validateEditPointForm()) {
    return
  }

  // 准备批量更新数据
  const updateData = prepareBatchUpdateData()

  // 调用批量更新API（API已配置自动显示成功消息）
  await litigationPointsApi.updatePointPair(updateData)

  // 更新成功，关闭对话框并重置状态
  addPointDialogVisible.value = false
  resetAddPointForm()
  isEditMode.value = false
  editingPoint.value = null

  // 刷新数据
  await getDisputePoints()
}

const validateAddPointForm = (): boolean => {
  const { plaintiff, defendant } = addPointForm.value

  // 至少需要填写一方的观点
  if (!plaintiff.content.trim() && !defendant.content.trim()) {
    ElMessage.warning('请至少填写一方的观点内容')
    return false
  }

  // 如果填写了观点内容，必须选择当事人
  if (plaintiff.content.trim() && !plaintiff.partyName) {
    ElMessage.warning('请选择诉方当事人')
    return false
  }

  if (defendant.content.trim() && !defendant.partyName) {
    ElMessage.warning('请选择辩方当事人')
    return false
  }

  return true
}

// 编辑模式表单验证
const validateEditPointForm = (): boolean => {
  const { plaintiff, defendant } = addPointForm.value

  // 编辑模式下，只需要验证填写的那一方
  const hasPlaintiffContent = plaintiff.content.trim()
  const hasDefendantContent = defendant.content.trim()

  if (!hasPlaintiffContent && !hasDefendantContent) {
    ElMessage.warning('请填写观点内容')
    return false
  }

  // 验证当事人选择
  if (hasPlaintiffContent && !plaintiff.partyName) {
    ElMessage.warning('请选择诉方当事人')
    return false
  }

  if (hasDefendantContent && !defendant.partyName) {
    ElMessage.warning('请选择辩方当事人')
    return false
  }

  return true
}

// 准备更新数据
const prepareUpdateData = () => {
  const { plaintiff, defendant } = addPointForm.value

  // 确定要更新的是哪一方的观点
  const hasPlaintiffContent = plaintiff.content.trim()
  const hasDefendantContent = defendant.content.trim()

  if (hasPlaintiffContent) {
    return {
      content: plaintiff.content,
      pointDate: plaintiff.pointDate || new Date().toISOString().split('T')[0]
    }
  } else if (hasDefendantContent) {
    return {
      content: defendant.content,
      pointDate: defendant.pointDate || new Date().toISOString().split('T')[0]
    }
  }

  return {}
}

// 准备批量更新数据
const prepareBatchUpdateData = () => {
  const { plaintiff, defendant } = addPointForm.value

  const updateData: any = {
    caseImportId: props.caseImportId
  }

  // 查找相关的观点对
  const relatedPoints = findRelatedPointPair(editingPoint.value!)

  // 准备诉方观点更新数据
  if (plaintiff.content.trim() && relatedPoints.plaintiffPoint) {
    updateData.plaintiffPoint = {
      id: relatedPoints.plaintiffPoint.id,
      partyName: plaintiff.partyName,
      content: plaintiff.content,
      pointDate: plaintiff.pointDate || new Date().toISOString().split('T')[0],
      needUpdate: true
    }
  }

  // 准备辩方观点更新数据
  if (defendant.content.trim() && relatedPoints.defendantPoint) {
    updateData.defendantPoint = {
      id: relatedPoints.defendantPoint.id,
      partyName: defendant.partyName,
      content: defendant.content,
      pointDate: defendant.pointDate || new Date().toISOString().split('T')[0],
      needUpdate: true
    }
  }

  return updateData
}

// 查找相关的观点对
const findRelatedPointPair = (clickedPoint: DisputePoint) => {
  const result = {
    plaintiffPoint: null as DisputePoint | null,
    defendantPoint: null as DisputePoint | null,
    relation: null as any
  }

  // 确定点击的观点是诉方还是辩方
  const clickedSide = clickedPoint.side === 'plaintiff' ? 'plaintiff' : 'defendant'

  // 设置点击的观点
  if (clickedSide === 'plaintiff') {
    result.plaintiffPoint = clickedPoint
  } else {
    result.defendantPoint = clickedPoint
  }

  // 查找相关的关系
  const relation = pointRelations.value.find(rel => {
    if (clickedSide === 'plaintiff') {
      return rel.plaintiffPointId === clickedPoint.pointId
    } else {
      return rel.defendantPointId === clickedPoint.pointId
    }
  })

  if (relation) {
    result.relation = relation

    // 根据关系查找对方的观点
    if (clickedSide === 'plaintiff' && relation.defendantPointId) {
      // 查找对应的辩方观点
      result.defendantPoint = defendantPoints.value.find(p => p.pointId === relation.defendantPointId) || null
    } else if (clickedSide === 'defendant' && relation.plaintiffPointId) {
      // 查找对应的诉方观点
      result.plaintiffPoint = plaintiffPoints.value.find(p => p.pointId === relation.plaintiffPointId) || null
    }
  }

  return result
}

const prepareSaveData = (): SaveDisputePointsRequest => {
  const { plaintiff, defendant } = addPointForm.value
  const saveData: SaveDisputePointsRequest = {
    caseImportId: props.caseImportId,
    points: []
  }

  // 添加诉方观点
  if (plaintiff.content.trim()) {
    saveData.points.push({
      side: 'plaintiff',
      partyName: plaintiff.partyName,
      content: plaintiff.content,
      pointDate: plaintiff.pointDate || new Date().toISOString().split('T')[0]
    })
  }

  // 添加辩方观点
  if (defendant.content.trim()) {
    saveData.points.push({
      side: 'defendant',
      partyName: defendant.partyName,
      content: defendant.content,
      pointDate: defendant.pointDate || new Date().toISOString().split('T')[0]
    })
  }

  return saveData
}

const saveDisputePoints = async (data: SaveDisputePointsRequest) => {
  try {
    const result = await litigationPointsApi.save(data)
    return result
  } catch (error) {
    console.error('保存诉辩观点失败:', error)
    throw error
  }
}

// 更新所有观点之间的关联关系
const updatePointRelations = () => {
  // 更新诉方观点的关联
  plaintiffPoints.value.forEach(point => {
    const relation = pointRelations.value.find(rel => rel.plaintiffPointId === point.id.toString())
    if (relation) {
      point.relatedPointId = relation.defendantPointId
    }
  })
  
  // 更新辩方观点的关联
  defendantPoints.value.forEach(point => {
    const relation = pointRelations.value.find(rel => rel.defendantPointId === point.id.toString())
    if (relation) {
      point.relatedPointId = relation.plaintiffPointId
    }
  })
}

// 组件引用
const disputeFocusListRef = ref()
const disputeRelationViewRef = ref()
const plaintiffSelectRef = ref<InstanceType<typeof ElSelect> | null>(null)
const defendantSelectRef = ref<InstanceType<typeof ElSelect> | null>(null)
const plaintiffNameRef = ref<HTMLElement | null>(null)
const defendantNameRef = ref<HTMLElement | null>(null)


// 滚动到相关观点的位置
const scrollToRelatedPoint = (pointId: string | null, side: 'plaintiff' | 'defendant') => {
  // 检查pointId是否为null
  if (!pointId) return
  
  // 找到相关观点的DOM元素
  const relatedElement = document.querySelector(`.${side} .point-item[data-point-id="${pointId}"]`)
  if (!relatedElement) {
    console.warn('未找到相关观点元素:', pointId, side)
    return
  }
  
  // 找到包含该元素的滚动容器
  const scrollContainer = document.querySelector(`.${side} .points-list`)
  if (!scrollContainer) return
  
  // 检查元素是否在可视区域内
  const containerRect = scrollContainer.getBoundingClientRect()
  const elementRect = relatedElement.getBoundingClientRect()
  
  const isInView = 
    elementRect.top >= containerRect.top && 
    elementRect.bottom <= containerRect.bottom
    
  // 如果不在视图中，滚动到该元素
  if (!isInView) {
    relatedElement.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest'
    })
  }

  // 不再使用闪烁效果，改为通过 relatedPointId 状态来控制固定高亮
  // relatedPointId 已经在 setActivePoint 中设置，会通过 props 传递给子组件
}

// 获取争议焦点数据
const getDisputePoints = async () => {
  try {
    // 同时调用两个接口
    const [disputeFocusResult, litigationPointsResult] = await Promise.allSettled([
      disputeFocusApi.get(props.caseImportId),
      litigationPointsApi.get(props.caseImportId)
    ])

    // 重置数据
    resetData()

    // 合并数据
    const mergedData: MergedDisputePointsResponse = {
      plaintiffs: [],
      defendants: [],
      disputes: [],
      relations: []
    }

    // 处理争议焦点数据
    if (disputeFocusResult.status === 'fulfilled' && disputeFocusResult.value) {
      const focusData = disputeFocusResult.value
      // disputeFocusApi.get() 直接返回 DisputeFocus[] 数组
      if (Array.isArray(focusData)) {
        mergedData.disputes = focusData
      }
    } else if (disputeFocusResult.status === 'rejected') {
      console.warn('获取争议焦点数据失败:', disputeFocusResult.reason)
    }

    // 处理诉辩观点数据
    if (litigationPointsResult.status === 'fulfilled' && litigationPointsResult.value) {
      const pointsData = litigationPointsResult.value
      if (pointsData.plaintiffs) {
        mergedData.plaintiffs = pointsData.plaintiffs
      }
      if (pointsData.defendants) {
        mergedData.defendants = pointsData.defendants
      }
      if (pointsData.relations) {
        mergedData.relations = pointsData.relations
      }
    } else if (litigationPointsResult.status === 'rejected') {
      console.warn('获取诉辩观点数据失败:', litigationPointsResult.reason)
    }

    // 处理合并后的数据
    processResponseData(mergedData)

    // 如果两个接口都失败了，显示错误信息
    if (disputeFocusResult.status === 'rejected' && litigationPointsResult.status === 'rejected') {
      ElMessage.warning('暂无争议焦点和诉辩观点数据')
    }

  } catch (error) {
    // 全局已处理错误提示
    console.error('获取数据失败:', error)
  }
}

// 重置数据
const resetData = () => {
  pointRelations.value = []
  plaintiffPoints.value = []
  defendantPoints.value = []
  disputeFocusList.value = []
  plaintiffParties.value = []
  defendantParties.value = []
}

// 优化数据处理函数
const processResponseData = (data: MergedDisputePointsResponse) => {
  try {
    // 1. 处理关系数据
    if (data.relations) {
      pointRelations.value = data.relations.map(relation => ({
        id: relation.id,
        plaintiffPointId: relation.plaintiffPointId ? relation.plaintiffPointId.toString() : '',
        defendantPointId: relation.defendantPointId ? relation.defendantPointId.toString() : '',
        relationType: relation.relationType || ''
      }));
    }
    
    // 2. 处理诉方数据
    plaintiffParties.value = [];
    plaintiffPoints.value = [];
    if (data.plaintiffs && data.plaintiffs.length > 0) {
      data.plaintiffs.forEach((plaintiff) => {
        // 使用原始ID
        plaintiffParties.value.push({
          id: plaintiff.name,
          name: plaintiff.name,
          role: 'plaintiff',
          date: '',
          pointsByDate: plaintiff.pointsByDate || {}
        });
        
        if (plaintiff.pointsByDate) {
          // 处理观点数据，添加关联ID字段
          Object.entries(plaintiff.pointsByDate).forEach(([_, points]) => {
            (points as DisputePoint[]).forEach((point: DisputePoint) => {
              const pointWithRelation: DisputePoint = {
                ...point,
                relatedPointId: findRelatedPointId('plaintiff', point.id) 
              };
              plaintiffPoints.value.push(pointWithRelation);
            });
          });
        }
      });
      
      if (plaintiffParties.value.length > 0) {
        plaintiffInfo.value = plaintiffParties.value[0];
      }
    }
    
    // 3. 处理辩方数据
    defendantParties.value = [];
    defendantPoints.value = [];
    if (data.defendants && data.defendants.length > 0) {
      data.defendants.forEach((defendant) => {
        // 使用原始ID
        defendantParties.value.push({
          id: defendant.name,
          name: defendant.name,
          role: 'defendant',
          date: '',
          pointsByDate: defendant.pointsByDate || {}
        });
        
        if (defendant.pointsByDate) {
          // 处理观点数据，添加关联ID字段
          Object.entries(defendant.pointsByDate).forEach(([_, points]) => {
            (points as DisputePoint[]).forEach((point: DisputePoint) => {
              const pointWithRelation: DisputePoint = {
                ...point,
                relatedPointId: findRelatedPointId('defendant', point.id)
              };
              defendantPoints.value.push(pointWithRelation);
            });
          });
        }
      });
      
      if (defendantParties.value.length > 0) {
        defendantInfo.value = defendantParties.value[0];
      }
    }
    
    // 4. 处理争议焦点
    if (data.disputes) {
      disputeFocusList.value = data.disputes.map(dispute => ({
        id: dispute.id, // 保持原始的 number 类型
        title: dispute.title || '',
        description: dispute.description,
        category: dispute.category || '',
        caseImportId: dispute.caseImportId,
        plaintiffClaim: dispute.plaintiffClaim,
        defendantClaim: dispute.defendantClaim,
        conclusion: dispute.conclusion, // 添加结论字段
        laws: dispute.laws || [],
        lawSummaries: dispute.lawSummaries || [],
        plaintiffEvidence: dispute.plaintiffEvidence || [],
        defendantEvidence: dispute.defendantEvidence || []
      }));
    }
    
    // 5. 更新关系连接
    updatePointRelations();
    
  } catch (error) {
    console.error('处理数据失败:', error);
    ElMessage.error('处理数据失败，请刷新重试');
  }
}



// 处理法条点击
const handleLawClick = (law: string) => {
  // 触发事件
  emit('law-click', law)
}

// 日期格式化
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`
}

// 处理证据点击
const handleEvidenceClick = (evidence: Evidence) => {
  if (!evidence) return

  // 过滤并转换证据数据
  const validEvidence = [evidence].filter(e =>
    e.fileId && e.fileName
  ).map(e => ({
    fileId: e.fileId!,
    fileName: e.fileName!,
    // 不传递页码，使用全文搜索提高匹配成功率
    // pageNumber: e.pageNumber!,
    highlight: Array.isArray(e.highlight) ? e.highlight[0] : e.highlight,
    // locations: e.locations!
  }))

  if (validEvidence.length > 0) {
    emit('detailHighlight', validEvidence)
  }
}

const expandedHighlights = ref<Record<string, boolean>>({})

const toggleHighlight = (key: string) => {
  expandedHighlights.value[key] = !expandedHighlights.value[key]
}

// 处理主张点击 - 点击某一方时显示双方数据进行编辑
const handleClaimClick = (focus: {
  id: string;
  description: string;
  plaintiffClaim: string;
  defendantClaim: string;
  laws: string[] | null;
  lawSummaries: string[] | null;
  plaintiffEvidence: Evidence[];
  defendantEvidence: Evidence[];
}, side: 'plaintiff' | 'defendant') => {
  // 点击某一方时，打开编辑对话框显示双方数据
  isFocusEditMode.value = true
  currentFocus.value = { ...focus }
  dialogVisible.value = true
}

// 处理争议焦点标题点击
const handleFocusTitleClick = (focus: {
  id: string;
  description: string;
  plaintiffClaim: string;
  defendantClaim: string;
  laws: string[] | null;
  lawSummaries: string[] | null;
  plaintiffEvidence: Evidence[];
  defendantEvidence: Evidence[];
}) => {
  // 使用 nextTick 确保在折叠面板状态改变后再触发证据显示
  nextTick(() => {
    if (focus.plaintiffEvidence && focus.plaintiffEvidence.length > 0) {
      // 获取第一个诉方证据
      const firstEvidence = focus.plaintiffEvidence[0]
      handleEvidenceClick(firstEvidence)
    }
  })
}

// 模态框相关状态
const dialogVisible = ref(false)
const isFocusEditMode = ref(false)
const currentFocus = ref<any>(null)
const saving = ref(false)

// 添加争议焦点（显示模态框）
const handleAddFocus = () => {
  isFocusEditMode.value = false
  currentFocus.value = {
    description: '',
    conclusion: '',
    otherExplanation: '',
    legalProvisions: []
  }
  dialogVisible.value = true
}

// 处理争议焦点添加完成（刷新数据）
const handleFocusAdded = (savedFocus: any) => {
  // 重新加载数据以确保同步
  getDisputePoints()
}

// 编辑争议焦点
const handleEditFocus = (focus: any) => {
  isFocusEditMode.value = true
  currentFocus.value = { ...focus }
  dialogVisible.value = true
}

// 删除争议焦点
const handleDeleteFocus = (focus: any) => {
  ElMessageBox.confirm(
    '确定要删除该争议焦点吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      saving.value = true
      await disputeFocusApi.delete(focus.id)
      // 重新加载数据
      getDisputePoints()
    } catch (error) {
      // 全局已处理错误提示
      console.error('删除争议焦点失败:', error)
    } finally {
      saving.value = false
    }
  }).catch(() => {})
}

// 重新生成争议焦点
const handleRegenerateFocus = (focus: any) => {
  ElMessageBox.confirm(
    '确定要重新生成该争议焦点吗？这将覆盖当前内容。',
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      saving.value = true
      ElMessage.info('重新生成功能暂未实现，仅前端展示')
      // TODO: 调用重新生成API
      // await disputeFocusApi.regenerate(focus.id)
      // 重新加载数据
      // getDisputePoints()
    } catch (error) {
      // 全局已处理错误提示
      console.error('重新生成争议焦点失败:', error)
    } finally {
      saving.value = false
    }
  }).catch(() => {})
}

// 重新生成诉辩关系
const handleRegenerateLitigationPoints = async () => {
  try {
    // 重新加载数据
    await getDisputePoints()
    // 清除连线，等待用户重新点击观点
    setTimeout(() => {
      disputeRelationViewRef.value?.clearConnections?.()
    }, 500)
  } catch (error) {
    console.error('重新获取诉辩观点数据失败:', error)
  }
}

// 处理关系更新
const handleRelationUpdated = (updatedRelation: { id?: number; relationType: string }) => {
  // 更新本地关系数据
  const relationIndex = pointRelations.value.findIndex(r => r.id === updatedRelation.id)
  if (relationIndex !== -1) {
    pointRelations.value[relationIndex].relationType = updatedRelation.relationType
  }
}

// 重新生成所有争议焦点
const handleRegenerateAllFocus = () => {
  ElMessageBox.confirm(
    '确定要重新生成所有争议焦点吗？这将覆盖当前所有内容。',
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      saving.value = true

      // 调用重新生成争议焦点API
      await disputeFocusApi.regenerate(props.caseImportId)

      // 清空本地数据
      disputeFocusList.value = []

      // 通知 TaskGeneratingStatus 组件开始监控任务状态
      // 先显示任务状态容器
      if (disputeFocusListRef.value?.startTaskGenerating) {
        disputeFocusListRef.value.startTaskGenerating()
      }
      
      const taskGeneratingRef = disputeFocusListRef.value?.taskGeneratingRef
      if (taskGeneratingRef) {
        taskGeneratingRef.startTaskExecution()
        setTimeout(() => {
          taskGeneratingRef.startTaskMonitoring()
        }, 2000)
      }

    } catch (error) {
      // 全局已处理错误提示
      console.error('重新生成所有争议焦点失败:', error)
    } finally {
      saving.value = false
    }
  }).catch(() => {})
}

// 争议焦点任务完成处理
const handleDisputeFocusTaskCompleted = () => {
  // 刷新争议焦点数据
  getDisputePoints()
}

// 刷新争议焦点列表处理（智推法条更新时）
const handleRefreshFocusList = () => {
  console.log('智推法条更新，刷新争议焦点列表')
  // 刷新争议焦点数据
  getDisputePoints()
}

// 删除所有争议焦点
const handleDeleteAllFocus = () => {
  if (disputeFocusList.value.length === 0) {
    ElMessage.warning('暂无争议焦点可删除')
    return
  }

  ElMessageBox.confirm(
    `确定要删除所有 ${disputeFocusList.value.length} 个争议焦点吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      saving.value = true

      // 调用删除所有争议焦点API
      await disputeFocusApi.deleteAll(props.caseImportId)

      // 清空本地数据
      disputeFocusList.value = []

      // 重新加载数据以确保同步
      await getDisputePoints()

    } catch (error) {
      // 全局已处理错误提示
      console.error('删除所有争议焦点失败:', error)
    } finally {
      saving.value = false
    }
  }).catch(() => {})
}

// 保存争议焦点
const handleSaveFocus = async (focusData: any) => {
  try {
    saving.value = true
    await disputeFocusApi.save({
      ...focusData,
      caseImportId: props.caseImportId
    })
    // 重新加载数据
    getDisputePoints()
    // 关闭模态框
    dialogVisible.value = false
  } catch (error) {
    // 全局已处理错误提示
    console.error('保存争议焦点失败:', error)
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  currentFocus.value = null
}

// 处理清空选择
const handleClearSelection = () => {
  emit('clear-selection')
}

// 处理争议焦点更新
const handleUpdateFocus = (updatedFocus: any) => {
  // 在本地更新数据
  const index = disputeFocusList.value.findIndex(focus => focus.id === updatedFocus.id)
  if (index !== -1) {
    disputeFocusList.value[index] = { ...disputeFocusList.value[index], ...updatedFocus }
  }

  // 这里可以添加自动保存逻辑，或者标记为需要保存
  console.log('争议焦点已更新:', updatedFocus)
}


</script>

<style scoped lang="scss">
.dispute-points {
  padding: 12px;
  // 移除 height: 100% 和 overflow-y: auto，让内容自适应高度
  display: flex;
  flex-direction: column;
  gap: 8px;

  > * {
    flex-shrink: 0;
  }
}

.dispute-comparison {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  background-color: #f5f6fa;
  background-image: linear-gradient(135deg, #f8faff 0%, #f5f6fa 100%);
  border-radius: 8px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.04);
  
  .relation-header {
    padding: 6px 10px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #f0f5ff, #f7f8fa, #fff5f5);

    h3 {
      margin: 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
      position: relative;
      padding-left: 10px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #409eff, #f56c6c);
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.dispute-relations {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 8px;
  flex: 1;
  overflow: hidden;
  padding: 8px;
  
  .side {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 10px;

    &.plaintiff {
      background: linear-gradient(135deg, #ecf5ff 0%, #e6f1fc 100%);
      box-shadow: 0 3px 10px rgba(64, 158, 255, 0.12);
      border: 1px solid rgba(64, 158, 255, 0.12);
    }

    &.defendant {
      background: linear-gradient(135deg, #fef0f0 0%, #fde9e9 100%);
      box-shadow: 0 3px 10px rgba(245, 108, 108, 0.12);
      border: 1px solid rgba(245, 108, 108, 0.12);
    }

    .side-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      position: relative;
      padding-right: 6px;
      height: 36px;
      
      .el-tag {
        flex-shrink: 0;
        position: absolute;
        left: 0;
        top: 8px;
        padding: 0 12px;
        height: 28px;
        line-height: 28px;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
      }

      .party-name {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 8px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        padding: 6px 14px;
        border-radius: 24px;
        min-width: 120px;
        text-align: center;
        color: #333;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(4px);
        
        &:hover {
          transform: translateX(-50%) translateY(-3px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .selector-icon {
          margin-left: 6px;
          font-size: 12px;
          opacity: 0.7;
          transition: all 0.3s;
        }

        &:hover .selector-icon {
          transform: rotate(180deg);
          opacity: 1;
        }
      }
      
      .person-select {
        position: absolute;
        z-index: -1;
        opacity: 0;
        pointer-events: auto;
        
        :deep(.el-input__wrapper) {
          width: 0;
        }
      }
    }

    .points-list {
      flex: 1;
      overflow-y: auto;
      padding-right: 6px;
      margin-top: 6px;
      scroll-behavior: smooth;
      position: relative;
      mask-image: linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%);
      padding: 6px 3px;

      /* 隐藏滚动条但保持滚动功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      .points-group {
        margin-bottom: 8px;
        position: relative;

        .date-header {
          font-size: 11px;
          color: #606266;
          background-color: rgba(0, 0, 0, 0.06);
          padding: 3px 8px;
          border-radius: 10px;
          margin-bottom: 6px;
          font-weight: 500;
          display: inline-block;
          text-align: left;
          position: relative;
          left: 0;
          margin-left: 0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
        }

        .point-item {
          display: flex;
          margin-bottom: 4px;
          padding: 6px 8px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 6px;
          transition: all 0.3s ease;
          border-left: 2px solid transparent;
          cursor: pointer;
          position: relative;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px) scale(1.005);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
          }

          &.active {
            border-left-color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }

          &.related {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.12);
          }
          
          .point-content {
            display: flex;
            flex-direction: row;
            flex: 1;

            .point-number {
              flex-shrink: 0;
              margin-right: 8px;
              font-weight: 600;
              color: #606266;
              min-width: 16px;
              background-color: rgba(0, 0, 0, 0.04);
              width: 18px;
              height: 18px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
              font-size: 11px;
            }

            .point-text {
              flex: 1;
              line-height: 1.5;
              font-size: 12px;
              color: #303133;
              text-align: left;
              padding-right: 6px;
            }
          }
        }
      }
    }
  }
}

.relation-lines-container {
  width: 80px;
  min-width: 80px;
  position: relative;
  background: linear-gradient(to right, rgba(236, 245, 255, 0.5), rgba(254, 240, 240, 0.5));
  border-radius: 8px;
  box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 3px;
}

.relation-line {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #409eff, #ff9800);
    transform: translateY(-50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  .relation-type {
    position: relative;
    padding: 3px 10px;
    background: linear-gradient(135deg, #ff9800, #ff7043);
    color: #fff;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    text-align: center;
    min-width: 60px;
    white-space: nowrap;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: scaleIn 0.4s ease-out;
  }
}

.dispute-focus {
  flex: 1;
  background-color: #fff;
  border-radius: 10px;
  padding: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
    position: relative;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #67c23a, #e6a23c);
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
    
    .header-actions {
      display: flex;
      gap: 4px;

      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 0;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px) scale(1.05);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
        }

        .el-icon {
          font-size: 12px;
        }

        // 圆形按钮的特殊样式
        &.is-circle {
          border: none;
          color: white;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.5s;
          }

          &:hover {
            transform: translateY(-2px) scale(1.08);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(-1px) scale(1.02);
          }

          // 添加按钮 - 绿色
          &[type="primary"] {
            background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);

            &:hover {
              background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
              box-shadow: 0 3px 10px rgba(103, 194, 58, 0.3);
            }
          }

          // 重新生成按钮 - 橙色
          &[type="warning"] {
            background: linear-gradient(135deg, #e6a23c 0%, #f0a020 100%);

            &:hover {
              background: linear-gradient(135deg, #f0a020 0%, #e6a23c 100%);
              box-shadow: 0 3px 10px rgba(230, 162, 60, 0.3);
            }
          }

          // 删除按钮 - 红色
          &[type="danger"] {
            background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);

            &:hover {
              background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
              box-shadow: 0 3px 10px rgba(245, 108, 108, 0.3);
            }
          }
        }
      }
    }
  }
  
  .focus-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 6px;
    mask-image: linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%);
    padding: 10px 6px 10px 0;
    
    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 3px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.25);
      }
    }

    :deep(.el-collapse) {
      border: none;
    }
    
    :deep(.el-collapse-item__header) {
      padding: 8px 12px;
      background: linear-gradient(to right, #f9fafc, #f5f7fa);
      border-radius: 8px;
      border: none;
      margin-bottom: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

      &:hover {
        background: linear-gradient(to right, #f5f7fa, #ecf5ff);
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
      }

      &.is-active {
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        background: linear-gradient(to right, #ecf5ff, #f5f7fa);
        
        .focus-title {
          .focus-index {
            background: rgba(64, 158, 255, 0.2);
            color: #1890ff;
            font-weight: 600;
          }
          
          .focus-desc {
            color: #1890ff;
            font-weight: 600;
          }
        }
        
        /* 移除黑框，改为左侧边框高亮 */
        border-left: 4px solid #409EFF;
        padding-left: 16px; /* 调整左侧内边距 */
      }
    }
    
    :deep(.el-collapse-item__wrap) {
      border: none;
      background-color: transparent;
    }
    
    :deep(.el-collapse-item__content) {
      padding: 4px 12px 16px;
    }

    /* 修改箭头样式 */
    :deep(.el-collapse-item__arrow) {
      transition: all 0.3s ease;
      color: #909399;
      font-weight: bold;
      font-size: 14px;

      &.is-active {
        color: #409EFF;
      }
    }

    .focus-title {
      display: flex;
      align-items: center;
      width: 100%;

      .focus-index {
        font-weight: bold;
        margin-right: 10px;
        color: #409EFF;
        position: relative;
        background: rgba(64, 158, 255, 0.1);
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(64, 158, 255, 0.15);
        font-size: 12px;
      }

      .focus-desc {
        flex: 1;
        font-weight: 500;
        color: #303133;
        font-size: 13px;
        transition: all 0.3s ease;
        font-size: 14px;
      }
      
      .focus-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: all 0.3s ease;
        
        .el-button {
          padding: 4px;

          .el-icon {
            font-size: 12px;
          }

          &:hover {
            transform: scale(1.1);
          }

          // 重新生成按钮样式
          &[type="warning"] {
            color: #e6a23c;

            &:hover {
              color: #cf9236;
              background-color: rgba(230, 162, 60, 0.1);
            }
          }
        }
      }
      
      &:hover .focus-actions {
        opacity: 1;
      }
    }
    
    .focus-details {
      background-color: #fff;
      border-radius: 0 0 10px 10px;
      
      .focus-content {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .focus-claims {
          display: flex;
          gap: 8px;

          .claim {
            flex: 1;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            
            &.plaintiff-claim {
              background: linear-gradient(135deg, #ecf5ff 0%, #e6f1fc 100%);
              border-left: 4px solid #409EFF;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
              
              &:hover {
                box-shadow: 0 6px 16px rgba(64, 158, 255, 0.2);
                transform: translateY(-3px);
              }
            }
            
            &.defendant-claim {
              background: linear-gradient(135deg, #fef0f0 0%, #fde9e9 100%);
              border-left: 4px solid #F56C6C;
              box-shadow: 0 4px 12px rgba(245, 108, 108, 0.15);
              
              &:hover {
                box-shadow: 0 6px 16px rgba(245, 108, 108, 0.2);
                transform: translateY(-3px);
              }
            }
            
            .claim-header {
              margin-bottom: 6px;
              display: flex;
              align-items: center;

              :deep(.el-tag) {
                font-size: 11px;
                padding: 2px 6px;
                font-weight: 600;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
              }
            }

            .claim-content {
              font-size: 12px;
              line-height: 1.5;
              color: #303133;
            }
          }
        }
        
        .focus-evidence {
          display: flex;
          gap: 8px;
          margin-top: 6px;
          
          .evidence-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
            
            &.plaintiff-evidence {
              .evidence-item {
                padding-left: 0;
              }
            }
            
            &.defendant-evidence {
              .evidence-item {
                padding-left: 0;
              }
            }
            
            .evidence-item {
              .evidence-content {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 2px;
                
                .evidence-tag {
                  cursor: pointer;
                  transition: all 0.3s ease;
                  font-weight: normal;
                  font-size: 13px;
                  padding: 2px 8px;
                  border-radius: 4px;
                  background: #f5f7fa;
                  
                  &:hover {
                    color: var(--el-color-primary);
                    background: #ecf5ff;
                  }
                }
                
                .toggle-highlight {
                  padding: 2px 6px;
                  font-size: 12px;
                  
                  .el-icon {
                    transition: transform 0.3s ease;
                  }
                }
              }
              
              .highlight-content {
                background: #f8f9fa;
                border-radius: 4px;
                padding: 8px;
                margin-top: 2px;
                margin-bottom: 4px;
                
                .highlight-text {
                  font-size: 13px;
                  line-height: 1.6;
                  color: #303133;
                  white-space: pre-wrap;
                  word-break: break-word;
                  background: #f0f2f5;
                  padding: 8px;
                  border-radius: 4px;
                }
              }
            }
          }
        }
        
        .focus-laws {
          margin-top: 10px;
          
          .laws-header {
            font-weight: 500;
            margin-bottom: 14px;
            color: #303133;
            background: rgba(103, 194, 58, 0.1);
            display: inline-block;
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 13px;
            box-shadow: 0 2px 5px rgba(103, 194, 58, 0.15);
          }
          
          .laws-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .law-item {
              .law-tag {
                cursor: pointer;
                transition: all 0.3s ease;
                padding: 4px 10px;
                border-radius: 6px;
                font-weight: 500;
                
                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }
              }
              
              .law-summary {
                margin-top: 10px;
                padding: 10px 12px;
                background-color: #f9f9f9;
                border-radius: 8px;
                font-size: 12px;
                color: #606266;
                line-height: 1.6;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
              }
            }
          }
        }
      }
    }
  }
}

/* 添加连接线动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.7); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 移除闪动动画，现在使用固定的 related 状态样式 */

/* 处理争议焦点编辑模态框的PDF交互问题 - 与时序链一致的配置 */
:deep(.focus-dialog) {
  pointer-events: auto;
}

:deep(.focus-dialog-modal) {
  pointer-events: none;
}

/* 控制模态框位置，显示在右半边 - 使用更强的选择器 */
:deep(.el-dialog__wrapper.focus-dialog-modal) {
  pointer-events: none;
  
  .el-dialog.focus-dialog {
    margin: 15vh 5% 15vh 55% !important;
    transform: none !important;
    pointer-events: auto;
  }
}

/* 小分辨率适配 */
@media (max-width: 1200px) {
  .dispute-points {
    padding: 8px;
    gap: 6px;
  }

  .dispute-comparison {
    border-radius: 6px;
  }

  .relation-lines-container {
    width: 60px;
    min-width: 60px;
  }

  .side {
    padding: 8px;

    .side-header {
      height: 32px;
      margin-bottom: 6px;

      h3 {
        font-size: 13px;
      }
    }

    .points-list {
      padding: 4px 2px;

      .points-group {
        margin-bottom: 6px;

        .date-header {
          font-size: 10px;
          padding: 2px 6px;
          margin-bottom: 4px;
        }

        .point-item {
          padding: 4px 6px;
          margin-bottom: 3px;

          .point-content {
            .point-number {
              width: 16px;
              height: 16px;
              margin-right: 6px;
              font-size: 10px;
            }

            .point-text {
              font-size: 11px;
              line-height: 1.4;
              padding-right: 4px;
            }
          }
        }
      }
    }
  }

  .relation-type {
    padding: 2px 8px;
    font-size: 10px;
    min-width: 50px;
    border-radius: 12px;
  }

  .dispute-focus {
    padding: 6px;
    border-radius: 8px;

    .section-header {
      margin-bottom: 6px;
      padding-bottom: 4px;

      h3 {
        font-size: 13px;
        padding-left: 8px;

        &::before {
          width: 2px;
          height: 12px;
        }
      }

      .header-actions {
        gap: 3px;

        .el-button {
          width: 20px;
          height: 20px;

          .el-icon {
            font-size: 11px;
          }
        }
      }
    }

    .focus-title {
      .focus-index {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        font-size: 11px;
      }

      .focus-desc {
        font-size: 12px;
      }
    }

    .focus-content {
      gap: 8px;

      .focus-claims {
        gap: 6px;

        .claim {
          padding: 6px;

          .claim-header {
            margin-bottom: 4px;

            :deep(.el-tag) {
              font-size: 10px;
              padding: 1px 4px;
            }
          }

          .claim-content {
            font-size: 11px;
            line-height: 1.4;
          }
        }
      }

      .focus-evidence {
        gap: 6px;
        margin-top: 4px;
      }
    }
  }
}

/* 超小分辨率适配 */
@media (max-width: 768px) {
  .dispute-points {
    padding: 6px;
    gap: 4px;
  }

  .dispute-relations {
    gap: 4px;
    padding: 6px;
  }

  .relation-lines-container {
    width: 40px;
    min-width: 40px;
    margin: 0 2px;
  }

  .relation-type {
    padding: 1px 6px;
    font-size: 9px;
    min-width: 40px;
    border-radius: 10px;
  }

  .side {
    padding: 6px;

    .side-header {
      height: 28px;

      h3 {
        font-size: 12px;
      }
    }

    .points-list {
      .point-item {
        padding: 3px 4px;

        .point-content {
          .point-number {
            width: 14px;
            height: 14px;
            margin-right: 4px;
            font-size: 9px;
          }

          .point-text {
            font-size: 10px;
            line-height: 1.3;
          }
        }
      }
    }
  }
}

/* 添加诉辩观点对话框样式 */
.add-point-dialog {
  .add-point-form {
    .point-pair-container {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;

      .point-section {
        flex: 1;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }

        &.plaintiff-section {
          background: linear-gradient(135deg, #ecf5ff 0%, #e6f1fc 100%);
          border-color: rgba(64, 158, 255, 0.2);

          h4 {
            color: #409eff;
          }
        }

        &.defendant-section {
          background: linear-gradient(135deg, #fef0f0 0%, #fde9e9 100%);
          border-color: rgba(245, 108, 108, 0.2);

          h4 {
            color: #f56c6c;
          }
        }

        .evidence-section {
          .evidence-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            align-items: center;
          }

          .selection-tip {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 12px;
            font-size: 13px;
            color: #1e40af;
            text-align: center;
          }

          .evidence-card {
            background: #ffffff;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .evidence-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px 16px;
              background: #f8f9fa;
              border-bottom: 1px solid #e4e7ed;

              .evidence-meta {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;

                .file-icon {
                  color: #409eff;
                  font-size: 16px;
                }

                .file-name {
                  font-size: 13px;
                  color: #303133;
                  font-weight: 500;
                  max-width: 150px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .page-tag {
                  font-size: 12px;
                  height: 20px;
                  line-height: 18px;
                }
              }

              .delete-btn {
                color: #f56c6c;

                &:hover {
                  background: #fef0f0;
                }
              }
            }

            .evidence-content {
              padding: 12px 16px;

              .content-textarea {
                .el-textarea__inner {
                  border: none;
                  box-shadow: none;
                  background: transparent;
                  resize: none;
                  font-size: 13px;
                  line-height: 1.5;

                  &:focus {
                    border: 1px solid #409eff;
                    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                  }

                  &::placeholder {
                    color: #c0c4cc;
                  }
                }
              }
            }
          }
        }
      }


    }

    .relation-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .el-radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .el-radio {
          margin-right: 0;
        }
      }
    }

    .form-tip {
      padding: 12px;
      background: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 6px;
      color: #1890ff;
      font-size: 13px;
      text-align: center;
    }
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .add-point-dialog {
    .add-point-form {
      .point-pair-container {
        flex-direction: column;
        gap: 16px;


      }
    }
  }
}
</style>