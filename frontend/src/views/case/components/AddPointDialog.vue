<template>
  <!-- 添加/编辑诉辩观点对话框 -->
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="95%"
    :before-close="handleCancel"
    class="add-point-dialog"
    top="3vh"
  >
    <div class="add-point-form" :class="{ 'edit-mode': isEditMode }">
      <div class="point-pair-container">
        <!-- 诉方观点 -->
        <div class="point-section plaintiff-section">
          <h4>{{ plaintiffSectionTitle }}</h4>

          <el-form :model="form.plaintiff" label-width="80px">
            <el-form-item :label="plaintiffPartyLabel">
              <el-select v-model="form.plaintiff.partyName" :placeholder="plaintiffPartyPlaceholder">
                <el-option
                  v-for="party in plaintiffParties"
                  :key="party.id"
                  :label="party.name"
                  :value="party.name"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="plaintiffContentLabel">
              <el-input
                v-model="form.plaintiff.content"
                type="textarea"
                :rows="4"
                :placeholder="plaintiffContentPlaceholder"
              />
            </el-form-item>

            <el-form-item :label="plaintiffTimeLabel">
              <el-date-picker
                v-model="form.plaintiff.pointDate"
                type="date"
                :placeholder="plaintiffTimePlaceholder"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>

          </el-form>
        </div>

        <!-- 辩方观点 -->
        <div class="point-section defendant-section">
          <h4>{{ defendantSectionTitle }}</h4>

          <el-form :model="form.defendant" label-width="80px">
            <el-form-item :label="defendantPartyLabel">
              <el-select v-model="form.defendant.partyName" :placeholder="defendantPartyPlaceholder">
                <el-option
                  v-for="party in defendantParties"
                  :key="party.id"
                  :label="party.name"
                  :value="party.name"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="defendantContentLabel">
              <el-input
                v-model="form.defendant.content"
                type="textarea"
                :rows="4"
                :placeholder="defendantContentPlaceholder"
              />
            </el-form-item>

            <el-form-item :label="defendantTimeLabel">
              <el-date-picker
                v-model="form.defendant.pointDate"
                type="date"
                :placeholder="defendantTimePlaceholder"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>

          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ saveButtonText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { watch, computed, onMounted, ref } from 'vue'
import { caseApi, type CaseTypeInfo } from '@/api'

interface PartyInfo {
  id: string;
  name: string;
  role: string;
  date: string;
}

interface AddPointForm {
  plaintiff: {
    partyName: string;
    content: string;
    pointDate: string;
  };
  defendant: {
    partyName: string;
    content: string;
    pointDate: string;
  };
}

const props = defineProps<{
  visible: boolean;
  saving: boolean;
  form: AddPointForm;
  plaintiffParties: PartyInfo[];
  defendantParties: PartyInfo[];
  isEditMode?: boolean;
  editingPoint?: any;
  caseImportId: string | number;
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'update:form': [value: AddPointForm];
  'save': [];
  'cancel': [];
}>()

// 案件类型信息
const caseTypeInfo = ref<CaseTypeInfo | null>(null)

// 获取案件类型信息
const getCaseTypeInfo = async () => {
  try {
    const result = await caseApi.getCaseType(props.caseImportId)
    caseTypeInfo.value = result
  } catch (error) {
    console.error('获取案件类型失败:', error)
    // 默认为一审
    caseTypeInfo.value = {
      ajlxdm: '',
      caseType: '一审',
      caseTypeCode: 'MSYS',
      isSecondInstance: false
    }
  }
}

// 计算对话框标题
const dialogTitle = computed(() => {
  const baseTitle = caseTypeInfo.value?.isSecondInstance ? '上诉关系' : '诉辩关系'
  return props.isEditMode ? `编辑${baseTitle}` : `添加${baseTitle}`
})

// 计算诉方区域标题
const plaintiffSectionTitle = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '判项' : '诉讼请求'
})

// 计算辩方区域标题
const defendantSectionTitle = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '上诉请求' : '答辩意见'
})

// 计算诉方内容标签
const plaintiffContentLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '判项内容' : '诉讼请求'
})

// 计算辩方内容标签
const defendantContentLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '上诉请求' : '答辩意见'
})

// 计算诉方时间标签
const plaintiffTimeLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '判决时间' : '诉讼时间'
})

// 计算辩方时间标签
const defendantTimeLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '上诉时间' : '答辩时间'
})

// 计算诉方内容占位符
const plaintiffContentPlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '请输入判项内容...' : '请输入诉讼请求内容...'
})

// 计算辩方内容占位符
const defendantContentPlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '请输入上诉请求内容...' : '请输入答辩意见内容...'
})

// 计算诉方时间占位符
const plaintiffTimePlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '选择判决时间' : '选择诉讼时间'
})

// 计算辩方时间占位符
const defendantTimePlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '选择上诉时间' : '选择答辩时间'
})

// 计算诉方当事人标签
const plaintiffPartyLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '法院' : '原告'
})

// 计算辩方当事人标签
const defendantPartyLabel = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '上诉人' : '被告'
})

// 计算诉方当事人占位符
const plaintiffPartyPlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '请选择法院' : '请选择原告'
})

// 计算辩方当事人占位符
const defendantPartyPlaceholder = computed(() => {
  return caseTypeInfo.value?.isSecondInstance ? '请选择上诉人' : '请选择被告'
})

// 计算保存按钮文字
const saveButtonText = computed(() => {
  const baseText = caseTypeInfo.value?.isSecondInstance ? '上诉关系' : '诉辩关系'
  return props.isEditMode ? `更新${baseText}` : `保存${baseText}`
})

const handleSave = () => {
  emit('save')
}

const handleCancel = () => {
  emit('cancel')
}

// 组件挂载时获取案件类型
onMounted(() => {
  getCaseTypeInfo()
})
</script>

<style scoped lang="scss">
/* 添加诉辩观点对话框样式 */
.add-point-dialog {
  .add-point-form {
    .point-pair-container {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      min-height: 0; /* 允许flex子项收缩 */

      .point-section {
        flex: 1;
        min-width: 0; /* 允许内容收缩 */
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }

        &.plaintiff-section {
          background: linear-gradient(135deg, #ecf5ff 0%, #e6f1fc 100%);
          border-color: rgba(64, 158, 255, 0.2);

          h4 {
            color: #409eff;
          }
        }

        &.defendant-section {
          background: linear-gradient(135deg, #fef0f0 0%, #fde9e9 100%);
          border-color: rgba(245, 108, 108, 0.2);

          h4 {
            color: #f56c6c;
          }
        }



        /* 表单项样式优化 */
        :deep(.el-form-item) {
          margin-bottom: 16px;

          .el-form-item__label {
            font-weight: 500;
            color: #606266;
            white-space: nowrap;
          }

          .el-form-item__content {
            min-width: 0; /* 允许内容收缩 */
          }

          .el-textarea__inner {
            min-height: 80px;
            resize: vertical;
          }

          .el-input__inner,
          .el-textarea__inner {
            width: 100%;
            box-sizing: border-box;
          }

          .el-select {
            width: 100%;
          }

          .el-date-editor {
            width: 100%;
          }
        }
      }
    }


  }
}

/* 响应式适配 - 只在小屏幕时垂直排列 */
@media (max-width: 1000px) {
  .add-point-dialog {
    .add-point-form {
      .point-pair-container {
        flex-direction: column;
        gap: 16px;
      }
    }
  }
}

/* 对话框整体样式优化 */
:deep(.el-dialog) {
  max-height: 90vh;
  max-width: 1400px;
  margin: 0 auto;

  .el-dialog__body {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
    padding: 20px;
  }

  .el-dialog__header {
    padding: 20px 20px 10px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
}
</style> 