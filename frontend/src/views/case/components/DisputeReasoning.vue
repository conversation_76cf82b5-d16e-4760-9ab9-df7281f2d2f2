<template>
  <div class="dispute-reasoning" style="position: relative; min-height: 280px;">
    <!-- 任务生成状态组件 - 局部覆盖 -->
    <TaskGeneratingStatus
      ref="taskGeneratingRef"
      :case-import-id="caseImportId"
      :task-type="TaskType.DISPUTE_FOCUS_REASONING"
      :overlay="false"
      :position="'absolute'"
      :show-progress="true"
      :show-estimated-time="true"
      @task-completed="handleTaskCompleted"
      @task-failed="handleTaskFailed"
    />

    <div class="section-header">
      <h3>争议焦点说理</h3>
      <div class="header-right">
        <div class="header-actions">
        <!-- 保存按钮 - 只在有未保存更改时显示 -->
        <el-button
          v-if="hasUnsavedChanges"
          type="success"
          circle
          @click="saveReasoningChanges"
          title="保存所有更改"
          size="small"
          :loading="saveLoading"
        >
          <el-icon><Check /></el-icon>
        </el-button>
        <el-button
          type="primary"
          circle
          @click="copyReasoningContent"
          title="复制内容"
          size="small"
        >
          <el-icon><DocumentCopy /></el-icon>
        </el-button>
        <el-button
          type="warning"
          circle
          @click="regenerateReasoning"
          title="重新生成"
          size="small"
          :loading="reasoningLoading"
          :disabled="reasoningLoading"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
        </div>
      </div>
    </div>
    <div class="reasoning-content">
      <el-input
        ref="textareaRef"
        v-model="reasoningText"
        type="textarea"
        :autosize="{ minRows: 8, maxRows: 50 }"
        placeholder="请输入..."
        class="reasoning-textarea"
        resize="vertical"
      />
    </div>
    <div class="ai-disclaimer">
      本内容由AI生成，请注意甄别！
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, watch } from 'vue'
import { DocumentCopy, Refresh, Check, Loading, Close } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDisputeReasoning, generateDisputeReasoning, updateDisputeReasoning } from '@/api/disputeReasoning'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'

// 获取案件ID
const caseImportId = inject('caseImportId')

// 组件引用
const textareaRef = ref()
const taskGeneratingRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null)

// 争议焦点说理相关状态
const reasoningText = ref('')
const reasoningLoading = ref(false)
const originalText = ref('') // 保存原始文本，用于检测变更
const hasUnsavedChanges = ref(false) // 是否有未保存的更改
const saveLoading = ref(false) // 保存加载状态

// 复制争议教导说理内容
const copyReasoningContent = async () => {
  if (!reasoningText.value.trim()) {
    ElMessage.warning('暂无内容可复制')
    return
  }

  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(reasoningText.value)
      ElMessage.success('内容已复制到剪贴板')
    } else {
      // 降级到传统方法
      const textArea = document.createElement('textarea')
      textArea.value = reasoningText.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      if (document.execCommand('copy')) {
        ElMessage.success('内容已复制到剪贴板')
      } else {
        ElMessage.error('复制失败，请手动复制')
      }

      document.body.removeChild(textArea)
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 加载争议焦点说理数据
const loadReasoningData = async () => {
  if (!caseImportId?.value) {
    console.warn('案件ID不存在，无法加载争议焦点说理')
    return
  }

  try {
    const result = await getDisputeReasoning(caseImportId.value)
    if (result && result.disputeFocuseReasoning) {
      reasoningText.value = result.disputeFocuseReasoning
      originalText.value = result.disputeFocuseReasoning // 保存原始文本
      hasUnsavedChanges.value = false // 重置未保存状态
    } else {
      reasoningText.value = ''
      originalText.value = ''
      hasUnsavedChanges.value = false
      console.log('未找到争议焦点说理数据')
    }
  } catch (error) {
    console.error('加载争议焦点说理失败:', error)
  }
}

// 重新生成争议焦点说理
const regenerateReasoning = () => {
  if (!caseImportId?.value) {
    ElMessage.warning('案件ID不存在，无法生成争议焦点说理')
    return
  }

  // 检查是否正在生成中
  if (reasoningLoading.value) {
    ElMessage.warning('争议焦点说理正在生成中，请稍后再试')
    return
  }

  // 根据是否有未保存更改显示不同的确认提示
  const confirmMessage = hasUnsavedChanges.value
    ? '确定要重新生成争议焦点说理吗？这将覆盖当前内容，包括您未保存的更改。'
    : '确定要重新生成争议焦点说理吗？这将覆盖当前内容。'

  // 显示确认提示弹框
  ElMessageBox.confirm(
    confirmMessage,
    '重新生成确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    // 用户确认后执行生成
    reasoningLoading.value = true
    try {
      // 启动生成任务
      await generateDisputeReasoning(caseImportId.value)

      // 如果成功启动，立即清空前端内容
      reasoningText.value = ''
      originalText.value = ''
      hasUnsavedChanges.value = false

      // 通知 TaskGeneratingStatus 组件开始监控任务状态
      taskGeneratingRef.value?.startTaskExecution()
      setTimeout(() => {
        taskGeneratingRef.value?.startTaskMonitoring()
      }, 2000)

    } catch (error) {
      console.error('重新生成失败:', error)
    } finally {
      reasoningLoading.value = false
    }
  }).catch(() => {
    // 用户取消，不做任何操作
    console.log('用户取消了重新生成操作')
  })
}

// 保存争议焦点说理
const saveReasoningChanges = async () => {
  if (!caseImportId?.value) {
    ElMessage.warning('案件ID不存在，无法保存争议焦点说理')
    return
  }

  if (!hasUnsavedChanges.value) {
    ElMessage.info('没有需要保存的更改')
    return
  }

  saveLoading.value = true
  try {
    await updateDisputeReasoning(caseImportId.value, reasoningText.value)
    originalText.value = reasoningText.value // 更新原始文本
    hasUnsavedChanges.value = false // 重置未保存状态
    // API已配置自动显示成功消息
  } catch (error) {
    console.error('保存争议焦点说理失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    saveLoading.value = false
  }
}

// 监听文本变化，检测是否有未保存的更改
watch(reasoningText, (newValue) => {
  hasUnsavedChanges.value = newValue !== originalText.value
}, { immediate: true })

// 任务完成处理
const handleTaskCompleted = () => {
  ElMessage.success('争议焦点说理分析完成')
  // 重新加载数据
  loadReasoningData()
}

// 任务失败处理
const handleTaskFailed = (event: any) => {
  ElMessage.error(`争议焦点说理分析失败: ${event.errorMessage}`)
}

// 组件挂载时加载数据
onMounted(() => {
  loadReasoningData()
})
</script>

<style scoped lang="scss">
.dispute-reasoning {
  background-color: #fff;
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
    position: relative;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #67c23a, #e6a23c);
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .status-display {
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;

      .status-processing {
        color: #409eff;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .status-success {
        color: #67c23a;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .status-failed {
        color: #f56c6c;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .rotating {
        animation: rotate 1s linear infinite;
      }
    }

    .header-actions {
      display: flex;
      gap: 4px;

      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 0;
        font-weight: 500;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 12px;
        }

        &:hover {
          transform: translateY(-1px) scale(1.05);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
        }

        // 保存按钮的脉冲动画
        &.el-button--success {
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .reasoning-content {
    padding: 7px 8px 8px 7px;
    flex: 1;

    .reasoning-textarea {
      width: 100%;

      :deep(.el-textarea__inner) {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        font-size: 14px;
        line-height: 1.6;
        resize: vertical;
        min-height: 200px;
        max-height: 500px;
        overflow-y: auto;

        &:hover {
          border-color: #c0c4cc;
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }

  .ai-disclaimer {
    text-align: right;
    padding: 4px 7px 0px 7px;
    font-size: 12px;
    color: #f56c6c;
    font-weight: 500;
    margin-top: 0;
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dispute-reasoning {
    .section-header {
      padding: 10px 12px;

      h3 {
        font-size: 13px;
        padding-left: 8px;

        &::before {
          width: 2px;
          height: 12px;
        }
      }

      .header-actions {
        gap: 6px;

        .el-button {
          width: 24px;
          height: 24px;

          .el-icon {
            font-size: 12px;
          }
        }
      }
    }

    .reasoning-content {
      padding: 12px;

      .reasoning-textarea {
        :deep(.el-textarea__inner) {
          font-size: 13px;
          min-height: 180px;
          max-height: 400px;
        }
      }

    }

    .ai-disclaimer {
      padding: 6px 12px;
      font-size: 11px;
    }
    }
  }

</style>
