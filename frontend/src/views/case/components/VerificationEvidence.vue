<template>
  <div class="verification-evidence-container">
    <!-- 任务生成状态组件 -->
    <TaskGeneratingStatus
      ref="taskGeneratingRef"
      :case-import-id="caseImportId"
      :task-type="TaskType.VERIFICATION_EVIDENCE"
      :overlay="false"
      :position="'absolute'"
      :show-progress="true"
      :show-estimated-time="true"
      @task-completed="handleTaskCompleted"
      @task-failed="handleTaskFailed"
    />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 质证情况内容 -->
    <div v-else-if="Object.keys(verificationData).length > 0 || partyList.length > 0" class="verification-content">
      <!-- 当事人TAB页 -->
      <el-tabs v-model="activePartyTab" type="card" class="party-tabs" @tab-change="handleTabChange">
        <el-tab-pane
          v-for="party in partyList"
          :key="party.key"
          :label="party.label"
          :name="party.key"
        >
          <!-- 质证表格 -->
          <div class="verification-table-container card-container">
            <!-- 表格工具栏 -->
            <div class="table-toolbar">
              <div class="toolbar-actions">
                <el-button
                  type="text"
                  :icon="null"
                  @click="handleAddVerification(party.key)"
                  :disabled="loading"
                >
                  <el-icon><Plus /></el-icon>
                  新增
                </el-button>
                <el-button
                  type="text"
                  :icon="null"
                  @click="handleBatchDelete(party.key)"
                  :disabled="getCurrentPartyData(party.key).length === 0"
                  style="color: #f56c6c;"
                >
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-button>
              </div>
            </div>
            
            <!-- 表格滚动容器 -->
            <div class="table-scroll-container verification-table">
              <el-table
                ref="verificationTableRef"
                :data="getCurrentPartyData(party.key)"
                stripe
                border
                style="width: 100%"
                :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
                @selection-change="handleSelectionChange"
                @row-click="handleRowClick"
              >
                <!-- 选择列 -->
                <el-table-column
                  type="selection"
                  width="50"
                  align="center"
                />
                
                <!-- 序号列 -->
                <el-table-column
                  label="序号"
                  type="index"
                  width="80"
                  align="center"
                  :index="getRowIndex"
                />
              
                <!-- 质证内容列 -->
                <el-table-column
                  label="质证内容"
                  prop="verificationContent"
                  min-width="300"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="verification-content-cell">
                      <!-- 编辑模式或新增行 -->
                      <div v-if="editingRows[row.id] || Number(row.id) < 0" class="edit-content">
                        <el-input
                          :model-value="getEditingStatement(row)"
                          @input="updateEditingContent(row, $event)"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入质证内容"
                          @blur="Number(row.id) < 0 ? undefined : handleContentEdit(row, getEditingStatement(row))"
                          @keyup.enter="Number(row.id) < 0 ? undefined : handleContentEdit(row, getEditingStatement(row))"
                        />
                      </div>
                      <!-- 显示模式 -->
                      <div v-else class="display-content">
                        <el-tooltip
                          effect="light"
                          placement="top"
                          :content="getTooltipContent(row)"
                          :disabled="!shouldShowTooltip(row)"
                          popper-class="verification-content-tooltip"
                          :show-after="200"
                          :hide-after="100"
                          :enterable="true"
                        >
                          <p 
                            class="content-text"
                            :class="{ 
                              'selectable': true, 
                              'has-tooltip': shouldShowTooltip(row),
                              'content-truncated': getTooltipContent(row).length > 30
                            }"
                          >
                            {{ getTooltipContent(row) }}
                          </p>
                        </el-tooltip>
                        <!-- 证据信息 (新增行不显示证据) -->
                        <div v-if="row.evidence && row.evidence.length > 0 && Number(row.id) > 0" class="evidence-tags">
                          <el-tag
                            v-for="(evidence, index) in row.evidence"
                            :key="index"
                            size="small"
                            type="info"
                            class="evidence-tag"
                            @click="handleEvidenceClick(evidence)"
                          >
                            {{ evidence.fileName }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                
                <!-- 是否采纳列 -->
                <el-table-column
                  label="是否采纳"
                  prop="isAdopt"
                  width="80"
                  align="center"
                >
                  <template #default="{ row }">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                      <el-switch
                        v-model="row.isAdopt"
                        :active-value="1"
                        :inactive-value="0"
                        width="60"
                        inline-prompt
                        active-text="采纳"
                        inactive-text="不采纳"
                        active-color="#409eff"
                        inactive-color="#c0c4cc"
                        @change="handleAdoptStatusChange(row)"
                      />
                    </div>
                  </template>
                </el-table-column>
                <!-- 操作列 -->
                <el-table-column
                  label="操作"
                  width="90"
                  align="center"
                  fixed="right"
                >
                  <template #default="{ row }">
                    <div class="row-actions">
                      <!-- 编辑模式下 -->
                      <template v-if="editingRows[row.id]">
                        <!-- 新增行的保存按钮 -->
                        <el-button
                          v-if="Number(row.id) < 0"
                          type="success"
                          size="small"
                          @click.stop="handleSaveNewVerification(activePartyTab, row)"
                          :disabled="!getEditingStatement(row).trim()"
                        >
                          保存
                        </el-button>
                        <!-- 编辑现有行的保存按钮 -->
                        <el-button
                          v-else
                          type="success"
                          size="small"
                          @click.stop="handleContentEdit(row, getEditingStatement(row))"
                        >
                          保存
                        </el-button>
                        <!-- 取消按钮 -->
                        <el-button
                          size="small"
                          @click.stop="Number(row.id) < 0 ? handleCancelAdd(activePartyTab, row) : (() => { editingRows[row.id] = false; editingContent[row.id] = undefined })()"
                        >
                          取消
                        </el-button>
                      </template>
                      <!-- 普通模式下 -->
                      <template v-else>
                        <el-button type="primary" size="small" text @click.stop="startEditRow(row)">
                          <el-icon><Edit /></el-icon>
                        </el-button>
                        <el-button type="danger" size="small" text @click.stop="handleDeleteRow(row)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </template>
                    </div>
                  </template>
                </el-table-column>
              
              
              </el-table>
            </div>
            
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty
        description="暂无质证情况信息"
        :image-size="120"
      >

      </el-empty>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star, Refresh, Delete, Edit, Plus } from '@element-plus/icons-vue'
import { verificationEvidenceApi, type VerificationEvidenceInfo, type Evidence, casePartyApi, type CasePartyTab } from '@/api'
import { PARTY_TYPE } from '@/constants/litigation'
import { TaskGeneratingStatus, TaskType } from '@/components/task-generating'

// Props
interface Props {
  caseImportId: number
  fileList?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  fileList: () => []
})

// Emits
const emit = defineEmits<{
  detailHighlight: [evidence: Array<{
    fileId: number
    fileName: string
    pageNumber?: number
    highlight?: string
    position?: any
  }>]
}>()

// 响应式数据
const loading = ref(false)
const generateLoading = ref(false)
const verificationData = ref<{ [partyKey: string]: VerificationEvidenceInfo[] }>({}) // 按当事人分组存储质证数据
const caseParties = ref<CasePartyTab[]>([])
const partyNameListMap = ref<{ [partyKey: string]: string[] }>({}) // 存储每个当事人的partyNameList
const activePartyTab = ref('')
const editMode = ref(false)
const selectedRows = ref<VerificationEvidenceInfo[]>([])
const editingRows = ref<{ [key: number]: boolean }>({})
const newRowData = ref<{ [key: string]: VerificationEvidenceInfo | null }>({})
const tempRowId = ref(0)
const editingContent = ref<{ [key: number]: string }>({})
const verificationTableRef = ref()
const taskGeneratingRef = ref<InstanceType<typeof TaskGeneratingStatus> | null>(null)
const isInitializing = ref(false) // 标记是否正在初始化，避免重复调用
const isRefreshingData = ref(false) // 标记是否正在刷新，避免重复触发
const partyLoadingMap = ref<{ [key: string]: boolean }>({}) // 单个当事人请求去重
const hasHandledTaskCompleted = ref(false) // 任务完成事件去重
const lastLoadedAtMap = ref<{ [key: string]: number }>({}) // 单个当事人上次加载时间
const lastLoadedSignatureMap = ref<{ [key: string]: string }>({}) // 单个当事人上次加载签名

// 当事人类型显示映射
const partyTypeDisplayMap: { [key: string]: string } = {
    '原告': '原告',
    '被告': '被告',
    '第三方证人': '第三方证人',
    '第三人': '第三人',
    '代理人': '代理人'
}


// 新增计算属性，避免重复计算
const getTooltipContent = (row: VerificationEvidenceInfo) => {
  return (row.verificationContent || '')
}

const shouldShowTooltip = (row: VerificationEvidenceInfo) => {
  const content = (row.verificationContent || '')
  return content.trim().length > 30 // 降低阈值，提高用户体验
}





// 处理行点击 - 选中行
const handleRowClick = (row: VerificationEvidenceInfo) => {
  // 行点击时选中该行
  const tableRef = verificationTableRef.value
  if (tableRef && typeof tableRef.toggleRowSelection === 'function') {
    tableRef.toggleRowSelection(row)
  } else {
    // 如果toggleRowSelection方法不存在，手动处理选择状态
    const currentSelection = selectedRows.value
    const isSelected = currentSelection.some(item => item.id === row.id)
    
    if (isSelected) {
      // 如果已选中，则取消选中
      selectedRows.value = currentSelection.filter(item => item.id !== row.id)
    } else {
      // 如果未选中，则选中
      selectedRows.value = [...currentSelection, row]
    }
  }
}

// 获取编辑状态下的文本内容
const getEditingStatement = (row: VerificationEvidenceInfo): string => {
  if (editingRows.value[row.id!] && editingContent.value[row.id!] !== undefined) {
    return editingContent.value[row.id!]
  }
  return (row.verificationContent || '')
}

// 开始编辑行
const startEditRow = (row: VerificationEvidenceInfo) => {
  editingRows.value[row.id!] = true
  editingContent.value[row.id!] = (row.verificationContent || '')
}

// 更新编辑中的内容
const updateEditingContent = (row: VerificationEvidenceInfo, newContent: string) => {
  editingContent.value[row.id!] = newContent
}

// 移除历史JSON重建逻辑，统一使用纯文本


//TODO 待优化  EvidenceFacts.vue页面一次执行,将 caseParties 传给证据情况页面(EvidenceFacts.vue)和质证情况(VerificationEvidence.vue)
// 计算属性 - 当事人列表
const partyList = computed(() => {
  console.log('VerificationEvidence.vue页面执行中')
  if (caseParties.value.length === 0) {
    return []
  }

  const parties: Array<{ key: string; label: string; sortOrder: number }> = []

  // 定义当事人类型优先级
  const partyTypeOrder: { [key: string]: number } = {
    '原告': 1,
    '被告': 2,
    '第三方证人': 3,
    '第三人': 4,
    '代理人': 5
  }

  // 根据当事人接口返回的数据生成tab列表
  caseParties.value.forEach((party, index) => {
    const displayType = partyTypeDisplayMap[party.partyType] || party.partyType
    const sortOrder = (partyTypeOrder[party.partyType] || 999) * 1000 + index + 1
    const partyKey = `${party.partyType}-${party.partyNameString}`

    parties.push({
      key: partyKey,
      label: `${displayType}：${party.partyNameString}`,
      sortOrder
    })
  })

  // 按优先级排序
  return parties.sort((a, b) => a.sortOrder - b.sortOrder)
})

/**
 * 获取当前当事人的质证数据
 * - 过滤 verificationContent 为空（含空白）或为 "无" 的记录
 */
const getCurrentPartyData = (partyKey: string) => {
  // 从按当事人分组的数据中获取
  const existingData = verificationData.value[partyKey] || []

  // 过滤 verificationContent 为空或为 "无" 的记录
  const filteredExisting = existingData.filter(item => {
    const content = (item.verificationContent || '').trim()
    return content !== '' && content !== '无'
  })

  // 如果有新增行数据，添加到列表末尾
  const newRow = newRowData.value[partyKey]
  if (newRow) {
    return [...filteredExisting, newRow]
  }

  return filteredExisting
}



// 获取行索引（从1开始）
const getRowIndex = (index: number) => {
  return index + 1
}

// 生成质证情况信息
const generateVerificationEvidence = async () => {
  try {
    generateLoading.value = true
    // 每次重新生成前，重置完成事件去重标记
    hasHandledTaskCompleted.value = false
    await verificationEvidenceApi.generate(props.caseImportId)

    // 启动任务监控
    taskGeneratingRef.value?.startTaskExecution()
    setTimeout(() => {
      taskGeneratingRef.value?.startTaskMonitoring()
    }, 2000)
  } catch (error) {
    console.error('生成质证情况失败:', error)
  } finally {
    generateLoading.value = false
  }
}

// 加载当事人数据
const loadCaseParties = async () => {
  try {
    const parties = await casePartyApi.listTab(props.caseImportId)
    caseParties.value = parties || []
    // 基于最新当事人数据构建 partyNameListMap（避免在计算属性内产生副作用）
    const map: { [key: string]: string[] } = {}
    caseParties.value.forEach((p) => {
      const key = `${p.partyType}-${p.partyNameString}`
      map[key] = (p.partyNameList || []).slice()
    })
    partyNameListMap.value = map
  } catch (error) {
    console.error('加载当事人数据失败:', error)
    ElMessage.error('加载当事人数据失败')
    caseParties.value = []
  }
}

// 加载指定当事人的质证数据
const loadVerificationDataForParty = async (partyKey: string) => {
  try {
    // 请求去重：同一当事人并发/重复触发时仅保留一次
    if (partyLoadingMap.value[partyKey]) {
      return
    }
    partyLoadingMap.value[partyKey] = true

    // 获取该当事人的partyNameList
    const partyNameListRaw = partyNameListMap.value[partyKey] || []
    const partyNameList = Array.from(new Set(partyNameListRaw.slice().sort()))

    if (partyNameList.length === 0) {
      console.warn(`当事人 ${partyKey} 的partyNameList为空`)
      verificationData.value[partyKey] = []
      return
    }

    // 节流：同一签名在短时间内重复请求则跳过
    const signature = `${props.caseImportId}_${partyNameList.join('|')}`
    const now = Date.now()
    const lastSig = lastLoadedSignatureMap.value[partyKey]
    const lastAt = lastLoadedAtMap.value[partyKey] || 0
    if (lastSig === signature && (now - lastAt) < 1200) {
      return
    }

    // 使用partyNameList调用API获取质证数据
    const responses = await verificationEvidenceApi.listByCaseImportIdAndPartyNames(props.caseImportId, partyNameList)
    if (responses && responses.length > 0) {
      verificationData.value[partyKey] = responses
    } else {
      verificationData.value[partyKey] = []
    }

    // 记录本次加载签名与时间
    lastLoadedSignatureMap.value[partyKey] = signature
    lastLoadedAtMap.value[partyKey] = now
  } catch (error) {
    console.error(`加载当事人 ${partyKey} 的质证数据失败:`, error)
    verificationData.value[partyKey] = []
  } finally {
    partyLoadingMap.value[partyKey] = false
  }
}

// 加载质证情况数据
const loadVerificationData = async () => {
  // 只加载当前激活的当事人数据
  if (activePartyTab.value) {
    await loadVerificationDataForParty(activePartyTab.value)
  }
}

// 初始化激活的tab
const initActiveTab = () => {
  if (partyList.value.length > 0 && !activePartyTab.value) {
    activePartyTab.value = partyList.value[0].key
  }
}

// 首次进入时加载当事人和质证情况
const initialLoad = async () => {
  isInitializing.value = true
  try {
    await loadCaseParties()
    initActiveTab()
    // 加载第一个当事人的质证数据
    if (activePartyTab.value) {
      await loadVerificationDataForParty(activePartyTab.value)
    }
  } finally {
    isInitializing.value = false
  }
}

// 处理tab切换
const handleTabChange = async (partyKey: string) => {
  console.log('切换到当事人:', partyKey)
  // 如果正在初始化，跳过处理，避免重复调用
  if (isInitializing.value) {
    console.log('正在初始化，跳过tab变化处理')
    return
  }
  // 切换TAB时清理所有临时数据并加载新数据
  clearAllTempData()
  if (partyKey) {
    await loadVerificationDataForParty(partyKey)
  }
}

// 刷新数据只刷新当前激活的当事人质证情况
const refreshData = async () => {
  if (isRefreshingData.value) return
  isRefreshingData.value = true
  try {
    // 若因清空本地数据导致 activePartyTab 被重置，此处补充初始化
    if (!activePartyTab.value) {
      await loadCaseParties()
      initActiveTab()
      await nextTick()
    }

    if (activePartyTab.value) {
      await loadVerificationDataForParty(activePartyTab.value)
    }
    clearAllTempData()
  } finally {
    isRefreshingData.value = false
  }
}

// 处理采纳状态变更
const handleAdoptStatusChange = async (row: VerificationEvidenceInfo) => {
  if (!row.id) return
  
  try {
    await verificationEvidenceApi.updateAdoptStatus(row.id, row.isAdopt)
  } catch (error) {
    console.error('更新采纳状态失败:', error)
    // 回滚状态
    row.isAdopt = row.isAdopt === 1 ? 0 : 1
    ElMessage.error('更新失败')
  }
}

// 处理证据点击
const handleEvidenceClick = (evidence: Evidence) => {
  console.log('点击证据:', evidence)
  
  // 验证证据数据的完整性
  if (!evidence.fileId || !evidence.fileName) {
    ElMessage.warning('证据信息不完整，无法高亮显示')
    console.error('证据数据不完整:', evidence)
    return
  }
  
  if (!evidence.highlight || evidence.highlight.trim() === '') {
    ElMessage.warning('证据没有高亮文本，无法定位')
    console.error('证据缺少高亮文本:', evidence)
    return
  }
  
  // 构造符合父组件期望的格式
  const evidenceData = [{
    fileId: Number(evidence.fileId), // 确保是数字类型
    fileName: evidence.fileName,
    // 不传递页码，使用全文搜索提高匹配成功率
    pageNumber: evidence.pageNumber || 1,
    highlight: evidence.highlight.trim() // 去除前后空格
  }]
  
  console.log('发送高亮数据:', evidenceData)
  
  // 发送高亮事件，父组件会切换到对应文件
  emit('detailHighlight', evidenceData)
  
  // 保留业务逻辑提示（文件定位反馈）
  console.log(`正在定位到文件: ${evidence.fileName}，页码: ${evidence.pageNumber}`)
}

// 处理编辑模式
const handleEditMode = () => {
  editMode.value = !editMode.value
  if (!editMode.value) {
    // 退出编辑模式时清空编辑状态
    editingRows.value = {}
    editingContent.value = {}
  }
}

// 处理批量删除
const handleBatchDelete = async (partyKey: string) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条质证情况信息吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 批量删除（并发）
    const ids = selectedRows.value
      .map(row => row.id as number)
      .filter(id => typeof id === 'number')
    await verificationEvidenceApi.batchDelete(ids)

    await loadVerificationData()
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 处理选择变化
const handleSelectionChange = (selection: VerificationEvidenceInfo[]) => {
  selectedRows.value = selection
}

// 处理内容编辑
const handleContentEdit = async (row: VerificationEvidenceInfo, newStatement: string) => {
  if (!row.id) return

  try {
    // 后端已改为直接存纯文本到 verificationContent 字段
    const newVerificationContent = newStatement
    const updatedRow = { ...row, verificationContent: newVerificationContent }
    
    // 使用全局成功提示
    await verificationEvidenceApi.update(updatedRow, {
      showSuccess: true,
      successMessage: '更新成功'
    })

    // 更新本地数据 - 需要在所有当事人的数据中查找并更新
    Object.keys(verificationData.value).forEach(partyKey => {
      const partyData = verificationData.value[partyKey]
      const index = partyData.findIndex(item => item.id === row.id)
      if (index > -1) {
        partyData[index].verificationContent = newVerificationContent
      }
    })

    // 清理编辑状态
    delete editingRows.value[row.id]
    delete editingContent.value[row.id]
  } catch (error) {
    // 全局已处理错误提示
    console.error('更新失败:', error)
  }
}

// 处理新增质证内容
const handleAddVerification = (partyKey: string) => {
  const [partyType, partyName] = partyKey.split('-')
  
  // 创建临时新行数据
  const newTempId = --tempRowId.value // 使用负数作为临时ID
  const newRow: VerificationEvidenceInfo = {
    id: newTempId,
    caseImportId: Number(props.caseImportId),
    partyType,
    partyName,
    verificationContent: '',
    isAdopt: 1 // 默认采纳
  }
  
  // 设置新行数据和编辑状态
  newRowData.value[partyKey] = newRow
  editingRows.value[newTempId] = true
}

// 保存新增的质证内容
const handleSaveNewVerification = async (partyKey: string, row: VerificationEvidenceInfo) => {
  const currentStatement = getEditingStatement(row)
  if (!currentStatement.trim()) {
    ElMessage.warning('请输入质证内容')
    return
  }

  try {

    const newVerification = {
      caseImportId: Number(props.caseImportId),
      partyType: row.partyType,
      partyName: row.partyName,
      // 后端已改为直接存纯文本到 verificationContent 字段
      verificationContent: currentStatement.trim(),
      isAdopt: row.isAdopt
    }

    await verificationEvidenceApi.create(newVerification as VerificationEvidenceInfo)
    
    // 清除新行数据
    delete newRowData.value[partyKey]
    delete editingRows.value[row.id!]
    delete editingContent.value[row.id!]

    // 重新加载当前当事人的数据
    await loadVerificationDataForParty(partyKey)
    
    // ElMessage.success('新增质证内容成功')
  } catch (error) {
    console.error('新增质证内容失败:', error)
    ElMessage.error('新增质证内容失败')
  }
}

// 取消新增
const handleCancelAdd = (partyKey: string, row: VerificationEvidenceInfo) => {
  delete newRowData.value[partyKey]
  delete editingRows.value[row.id!]
  delete editingContent.value[row.id!]
}

// 清理所有临时数据
const clearAllTempData = () => {
  newRowData.value = {}
  // 切换当事人时，统一取消所有行的编辑状态（含已有行与临时行）
  editingRows.value = {}
  editingContent.value = {}
  tempRowId.value = 0
}

// 新增删除行方法
function handleDeleteRow(row) {
  if (!row.id) return
  ElMessageBox.confirm('确定要删除该条质证内容吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await verificationEvidenceApi.delete(row.id)
    // 重新加载当前当事人的数据
    if (activePartyTab.value) {
      await loadVerificationDataForParty(activePartyTab.value)
    }
  }).catch(() => {})
}

// 使用 @tab-change 事件替代对 activePartyTab 的 watch

// 组件挂载时加载数据
onMounted(() => {
  initialLoad()
})

// 清空本地数据
const clearLocalData = () => {
  verificationData.value = {}
  // 只清空质证意见的数据
  // caseParties.value = []
  // partyNameListMap.value = {}
  clearAllTempData()
  // 重置激活的TAB
  activePartyTab.value = ''
  // 清空签名/时间/去重标记，避免跨案件残留
  lastLoadedSignatureMap.value = {}
  lastLoadedAtMap.value = {}
  partyLoadingMap.value = {}
}

// 处理任务完成
const handleTaskCompleted = () => {
  // 去重：防止任务完成事件在边界条件下触发两次
  if (hasHandledTaskCompleted.value) return
  hasHandledTaskCompleted.value = true
  ElMessage.success('质证情况分析完成')
  // 重新加载数据
  refreshData()
}

// 处理任务失败
const handleTaskFailed = (event: any) => {
  // ElMessage.error(`质证情况分析失败: ${event.errorMessage}`)
}

// 暴露方法供父组件调用
defineExpose({
  refreshData,
  generateVerificationEvidence,
  clearLocalData,
  taskGeneratingRef
})
</script>

<style scoped>
.verification-evidence-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative; /* 为TaskGeneratingStatus提供定位上下文 */
}

.loading-container {
  padding: 16px;
}

.verification-content {
  /* flex: 1; */
  overflow: hidden;
}

.party-tabs {
  /* height: 100%; */
}

.verification-table-container {
  display: flex;
  flex-direction: column;
  /* min-height: 520px; */
  /* flex: 1 1 0%; */
}

.table-scroll-container {
  /* flex: 1; */
  overflow-y: auto;
  max-height: 60vh;
}

.verification-content-cell {
  padding: 6px 0;
}

.content-text {
  margin: 0 0 6px 0;
  line-height: 1.5;
  color: #303133;
  font-size: 13px;
  transition: all 0.2s ease;
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 80px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  
  &.content-truncated {
    position: relative;
    
    &::after {
      content: '...';
      position: absolute;
      right: 0;
      bottom: 0;
      background: linear-gradient(to right, transparent, #fff 50%);
      padding-left: 20px;
      color: #999;
    }
  }
  
  &.has-tooltip {
    cursor: help;
    position: relative;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      border-radius: 4px;
      transition: background-color 0.2s ease;
    }
  }
}

.content-text.selectable {
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 6px;
  margin: -4px -6px 4px -6px;
  transition: all 0.2s ease;
}

.content-text.selectable:hover {
  background-color: #f0f9ff;
  border: 1px solid #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.edit-content {
  margin-bottom: 6px;
}

.edit-content :deep(.el-textarea__inner) {
  font-size: 13px;
  padding: 6px 8px;
  border-radius: 4px;
}

.table-toolbar {
  display: flex;
  justify-content: flex-start;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 10;
  flex-shrink: 0;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-actions .el-button {
  padding: 4px 8px;
  font-size: 14px;
}

.evidence-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.evidence-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  padding: 2px 6px;
}

.evidence-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 20px;
}

/* 表格样式优化 */
.verification-table-container :deep(.el-table) {
  border-radius: 0 0 6px 6px;
  overflow: hidden;
  font-size: 13px;
}

.verification-table-container :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.verification-table-container :deep(.el-table__header-wrapper) {
  border-radius: 0;
}

.verification-table-container :deep(.el-table__header th) {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
  font-size: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.verification-table-container :deep(.el-table__body td) {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.verification-table-container :deep(.el-table__row:hover) {
  background: #f8f9fa;
}

.verification-table-container :deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
}

.verification-table-container :deep(.el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #c0c4cc;
  transform: scale(0.9);
}

/* 新增质证内容按钮容器 */
.add-verification-container {
  display: flex;
  justify-content: center;
  padding: 6px 0;
  margin-top: 2px;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
  background: #fff;
}

.add-verification-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
  transition: all 0.2s ease !important;
  width: 40px !important;
  height: 40px !important;
}

.add-verification-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
}

.add-verification-btn:active {
  transform: translateY(0) !important;
}

.add-verification-btn .el-icon {
  font-size: 16px !important;
}



.card-container {
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-container .table-toolbar {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.card-container .add-verification-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 12px 12px;
}

.verification-table :deep(.el-table) {
  font-size: 13px;
  border: none;
}

.verification-table :deep(.el-table .cell) {
  padding: 6px 8px;
  line-height: 1.4;
}

.verification-table :deep(.el-table th .cell) {
  font-weight: 600;
  padding: 8px 8px;
}

.verification-table :deep(.el-table__header) {
  border-radius: 0;
}

.verification-table :deep(.el-table__body) {
  border-radius: 0;
}

.verification-table :deep(.el-table::before) {
  display: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .verification-table-container :deep(.el-table) {
    font-size: 12px;
  }
  
  .content-text {
    font-size: 12px;
  }
  
  .table-toolbar {
    padding: 4px 8px;
  }
  
  .toolbar-actions .el-button {
    padding: 3px 6px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .verification-evidence-container {
    border-radius: 4px;
  }
  
  .loading-container {
    padding: 12px;
  }
  
  .party-tabs :deep(.el-tabs__header) {
    padding: 0 12px;
  }
  
  .party-tabs :deep(.el-tabs__item) {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .verification-table-container :deep(.el-table) {
    font-size: 11px;
  }
  
  .content-text {
    font-size: 11px;
    line-height: 1.4;
  }
  
  .table-toolbar {
    padding: 3px 6px;
  }
  
  .toolbar-actions {
    gap: 4px;
  }
  
  .toolbar-actions .el-button {
    padding: 2px 4px;
    font-size: 10px;
  }
  
  .evidence-tags {
    gap: 3px;
  }
  
  .evidence-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .add-verification-container {
    padding: 4px 0;
  }
  
  .add-verification-btn {
    width: 36px !important;
    height: 36px !important;
  }
  
  .add-verification-btn .el-icon {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .verification-table-container :deep(.el-table__header th) {
    padding: 6px 0;
    font-size: 11px;
  }
  
  .verification-table-container :deep(.el-table__body td) {
    padding: 4px 0;
  }
  
  .party-tabs :deep(.el-tabs__nav-scroll) {
    overflow-x: auto;
  }
  
  .party-tabs :deep(.el-tabs__item) {
    white-space: nowrap;
    min-width: 80px;
  }
}

/* 紧凑表格行样式 */
.verification-table-container :deep(.el-table__row),
.verification-table-container :deep(.el-table__body td),
.verification-table-container :deep(.el-table__cell) {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
  font-size: 12px !important;
  line-height: 1.3 !important;
}

.verification-table-container :deep(.el-table__header th) {
  padding: 6px 0 !important;
  font-size: 12px !important;
}

/* 让质证情况新增按钮hover效果与证据情况一致 */
.verification-evidence-container .el-button.is-text:not(.is-disabled):hover {
  background-color: #f5f7fa !important;
  color: #409eff !important;
}

.row-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.row-actions .el-button {
  margin: 0;
}

.row-actions .el-button + .el-button {
  margin-left: 4px;
}

</style>

<!-- 全局样式 -->
<style>
/* 质证内容 tooltip 样式 */
.verification-content-tooltip {
  max-width: 500px !important;
  background: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 6px !important;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12) !important;
  padding: 12px !important;
}

.verification-content-tooltip .el-tooltip__content {
  color: #303133 !important;
  line-height: 1.5 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  font-size: 12px !important;
  max-height: 250px !important;
  overflow-y: auto !important;
}

.verification-content-tooltip:before {
  border-bottom-color: #e4e7ed !important;
}

.verification-content-tooltip[data-popper-placement^="top"] > .el-popper__arrow:before {
  border-top-color: #e4e7ed !important;
}

.verification-content-tooltip[data-popper-placement^="bottom"] > .el-popper__arrow:before {
  border-bottom-color: #e4e7ed !important;
}

.verification-content-tooltip[data-popper-placement^="left"] > .el-popper__arrow:before {
  border-left-color: #e4e7ed !important;
}

.verification-content-tooltip[data-popper-placement^="right"] > .el-popper__arrow:before {
  border-right-color: #e4e7ed !important;
}

/* 响应式 tooltip */
@media (max-width: 768px) {
  .verification-content-tooltip {
    max-width: 300px !important;
    padding: 8px !important;
  }
  
  .verification-content-tooltip .el-tooltip__content {
    font-size: 11px !important;
    max-height: 200px !important;
  }
}
</style> 