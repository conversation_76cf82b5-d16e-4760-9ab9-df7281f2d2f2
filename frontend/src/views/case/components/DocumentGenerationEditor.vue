<template>
  <div class="document-generation-editor">
    <!-- OnlyOffice 编辑器 -->
    <div v-if="showOnlyOffice" class="onlyoffice-container">
      <div class="onlyoffice-header">
        <div class="header-left">
          <h4>{{ documentTitle }}</h4>
          <!-- 生成步骤状态显示 -->
          <div v-if="isGenerating || currentGenerationStatus" class="generation-status">
            <el-icon class="status-icon">
              <Loading />
            </el-icon>
            <span class="status-text">{{ currentGenerationStatus }}</span>
    </div>
        </div>
        <div class="onlyoffice-actions">
          <el-button @click="closeOnlyOffice">返回编辑</el-button>
        </div>
      </div>
      <div class="onlyoffice-editor-wrapper">
        <div id="onlyoffice-editor" class="onlyoffice-editor"></div>

        <!-- 书签导航 -->
        <div class="bookmark-navigation" :class="{ 'expanded': showBookmarkNav }">
          <div class="bookmark-toggle" @click="showBookmarkNav = !showBookmarkNav">
            <el-icon>
              <Menu />
            </el-icon>
            <span v-if="showBookmarkNav">书签导航</span>
          </div>

          <div v-if="showBookmarkNav" class="bookmark-list">
            <div class="bookmark-header">
              <span>文书结构</span>
            </div>
            <div
              v-for="bookmark in bookmarkList"
              :key="bookmark.name"
              class="bookmark-item"
              @click="gotoBookmark(bookmark.name)"
            >
              <el-icon class="bookmark-icon">
                <Document />
              </el-icon>
              <span class="bookmark-text">{{ bookmark.displayName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- 内容编辑区域 -->
    <div v-else class="document-editor-workspace">

      <!-- 页面头部 -->
      <div class="workspace-header">
        <h4>{{ documentTitle }}</h4>
      </div>

      <!-- 民事一审判决书内容 -->
      <template class="civil_judgement_1" v-if="isCivilFirstInstance">
        <!-- 判项情况 -->
        <div class="judgment-module">
          <div v-if="!hasCounterclaim" class="no-counterclaim">
            <div class="section-header">
              <h3>判项情况</h3>
            </div>
            <div class="judgment-toolbar">
              <div class="judgment-type-controls">
                <el-button-group>
                  <el-button
                      :type="selectedJudgmentType === 'all_support' ? 'primary' : 'default'"
                      @click="setJudgmentType('all_support')"
                  >
                    全部支持
                  </el-button>
                  <el-button
                      :type="selectedJudgmentType === 'partial_support' ? 'primary' : 'default'"
                      @click="setJudgmentType('partial_support')"
                  >
                    部分支持
                  </el-button>
                  <el-button
                      :type="selectedJudgmentType === 'all_reject' ? 'primary' : 'default'"
                      @click="setJudgmentType('all_reject')"
                  >
                    全部驳回
                  </el-button>
                </el-button-group>
              </div>

              <div class="action-buttons">
                <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="batchDeleteJudgmentItems"
                    :disabled="!hasSelectedJudgmentItems"
                    title="批量删除"
                >
                  <el-icon>
                    <Delete/>
                  </el-icon>
                </el-button>
              </div>
            </div>

            <!-- 判项表格和按钮容器 -->
            <div class="table-wrapper">

              <div class="data-table-container">
                <el-table
                    v-loading="loadingJudgmentItems"
                    :data="judgmentItems"
                    style="width: 100%"
                    empty-text="暂无判决结果"
                    class="custom-table responsive-table"
                    border
                    :table-layout="'fixed'"
                    @selection-change="handleJudgmentSelectionChange"
                >
                  <el-table-column type="selection" width="55" align="center" header-align="center"/>
                  <el-table-column
                      prop="sequence"
                      label="序号"
                      width="80"
                      align="center"
                      header-align="center"
                  />
                  <el-table-column
                      prop="content"
                      label="判决结果"
                      min-width="300"
                      align="left"
                      header-align="center"
                      class-name="content-column"
                  >
                    <template #default="{ row }">
                      <div class="content-cell">
                        <el-popover placement="top" :width="400" trigger="hover"
                                    :disabled="!shouldShowHover(row.content, 'wide')">
                          <template #reference>
                            <el-input
                                v-model="row.content"
                                placeholder="请输入判决结果"
                                size="default"
                                @input="markJudgmentAsChanged(row)"
                            />
                          </template>
                          <div class="popover-content">
                            {{ row.content }}
                          </div>
                        </el-popover>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                      prop="result"
                      label="结果"
                      width="140"
                      align="center"
                      header-align="center"
                  >
                    <template #default="{ row }">
                      <div style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                        <el-switch
                            v-model="row.isSupported"
                            :active-value="true"
                            :inactive-value="false"
                            width="60"
                            inline-prompt
                            active-text="支持"
                            inactive-text="不支持"
                            active-color="#409eff"
                            inactive-color="#c0c4cc"
                            @change="() => handleJudgmentSupportChange(row)"
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                      label="操作"
                      width="95"
                      align="center"
                      header-align="center"
                  >
                    <template #default="{ row, $index }">
                      <div class="operation-buttons">
                        <el-button
                            v-if="row.contentChanged"
                            type="success"
                            size="small"
                            circle
                            @click="saveJudgmentItem(row, $index)"
                            title="保存"
                        >
                          <el-icon>
                            <Check/>
                          </el-icon>
                        </el-button>
                        <el-button
                            type="danger"
                            size="small"
                            circle
                            @click="deleteJudgmentItem($index)"
                            title="删除"
                        >
                          <el-icon>
                            <Delete/>
                          </el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 添加判决项按钮 -->
              <div class="add-row-container">
                <el-button
                    type="primary"
                    :icon="Plus"
                    circle
                    size="small"
                    @click="addNewJudgmentItem"
                />
              </div>
            </div>
          </div>

          <!-- 若案件存在反诉，则判项情况展示如下 -->
          <div v-if="hasCounterclaim" class="counterclaim-note">

            <!-- 本诉原告部分 -->
            <div class="plaintiff">
              <div class="section-header">
                <h3>{{ plaintiffPartyInfo.ssdw }}：{{ plaintiffPartyInfo.dsrxm }}</h3>
              </div>
              <div class="judgment-toolbar">
                <div class="judgment-type-controls">
                  <el-button-group>
                    <el-button
                        :type="selectedPlaintiffJudgmentType === 'all_support' ? 'primary' : 'default'"
                        @click="setPlaintiffJudgmentType('all_support')"
                    >
                      全部支持
                    </el-button>
                    <el-button
                        :type="selectedPlaintiffJudgmentType === 'partial_support' ? 'primary' : 'default'"
                        @click="setPlaintiffJudgmentType('partial_support')"
                    >
                      部分支持
                    </el-button>
                    <el-button
                        :type="selectedPlaintiffJudgmentType === 'all_reject' ? 'primary' : 'default'"
                        @click="setPlaintiffJudgmentType('all_reject')"
                    >
                      全部驳回
                    </el-button>
                  </el-button-group>
                </div>
                <div class="action-buttons">
                  <el-button
                      type="danger"
                      size="small"
                      circle
                      @click="batchDeletePlaintiffJudgmentItems"
                      :disabled="!hasSelectedPlaintiffJudgmentItems"
                      title="批量删除"
                  >
                    <el-icon>
                      <Delete/>
                    </el-icon>
                  </el-button>
                </div>
              </div>
              <div class="table-wrapper">
                <div class="data-table-container">
                  <el-table
                      v-loading="loadingJudgmentItems"
                      :data="plaintiffJudgmentItems"
                      style="width: 100%"
                      empty-text="暂无判决结果"
                      class="custom-table responsive-table"
                      border
                      :table-layout="'fixed'"
                      @selection-change="handlePlaintiffJudgmentSelectionChange"
                  >
                    <el-table-column type="selection" width="55" align="center" header-align="center"/>
                    <el-table-column
                        prop="sequence"
                        label="序号"
                        width="80"
                        align="center"
                        header-align="center"
                    />
                    <el-table-column
                        prop="content"
                        label="判决结果"
                        min-width="300"
                        align="left"
                        header-align="center"
                        class-name="content-column"
                    >
                      <template #default="{ row }">
                        <div class="content-cell">
                          <el-popover placement="top" :width="400" trigger="hover"
                                      :disabled="!shouldShowHover(row.content, 'wide')">
                            <template #reference>
                              <el-input
                                  v-model="row.content"
                                  placeholder="请输入判决结果"
                                  size="default"
                                  @input="markPlaintiffJudgmentAsChanged(row)"
                              />
                            </template>
                            <div class="popover-content">
                              {{ row.content }}
                            </div>
                          </el-popover>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="result"
                        label="结果"
                        width="120"
                        align="center"
                        header-align="center"
                    >
                      <template #default="{ row }">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                          <el-switch
                              v-model="row.isSupported"
                              :active-value="true"
                              :inactive-value="false"
                              width="60"
                              inline-prompt
                              active-text="支持"
                              inactive-text="不支持"
                              active-color="#409eff"
                              inactive-color="#c0c4cc"
                              @change="() => handlePlaintiffJudgmentSupportChange(row)"
                          />
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        width="85"
                        align="center"
                        header-align="center"
                    >
                      <template #default="{ row, $index }">
                        <div class="operation-buttons">
                          <el-button
                              v-if="row.contentChanged"
                              type="success"
                              size="small"
                              circle
                              @click="savePlaintiffJudgmentItem(row, $index)"
                              title="保存"
                          >
                            <el-icon>
                              <Check/>
                            </el-icon>
                          </el-button>
                          <el-button
                              type="danger"
                              size="small"
                              circle
                              @click="deletePlaintiffJudgmentItem($index)"
                              title="删除"
                          >
                            <el-icon>
                              <Delete/>
                            </el-icon>
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <!-- 添加按钮 -->
                <div class="add-row-container">
                  <el-button
                      type="primary"
                      :icon="Plus"
                      circle
                      size="small"
                      @click="addNewPlaintiffJudgmentItem"
                  />
                </div>

              </div>
            </div>

            <!-- 本诉被告（反诉原告）部分 -->
            <div class="defendant">
              <div class="section-header">
                <h3>{{ defendantPartyInfo.ssdw }}：{{ defendantPartyInfo.dsrxm }}</h3>
              </div>
              <div class="judgment-toolbar">
                <div class="judgment-type-controls">
                  <el-button-group>
                    <el-button
                        :type="selectedDefendantJudgmentType === 'all_support' ? 'primary' : 'default'"
                        @click="setDefendantJudgmentType('all_support')"
                    >
                      全部支持
                    </el-button>
                    <el-button
                        :type="selectedDefendantJudgmentType === 'partial_support' ? 'primary' : 'default'"
                        @click="setDefendantJudgmentType('partial_support')"
                    >
                      部分支持
                    </el-button>
                    <el-button
                        :type="selectedDefendantJudgmentType === 'all_reject' ? 'primary' : 'default'"
                        @click="setDefendantJudgmentType('all_reject')"
                    >
                      全部驳回
                    </el-button>
                  </el-button-group>
                </div>
                <div class="action-buttons">
                  <el-button
                      type="danger"
                      size="small"
                      circle
                      @click="batchDeleteDefendantJudgmentItems"
                      :disabled="!hasSelectedDefendantJudgmentItems"
                      title="批量删除"
                  >
                    <el-icon>
                      <Delete/>
                    </el-icon>
                  </el-button>
                </div>
              </div>
              <div class="table-wrapper">

                <div class="data-table-container">
                  <el-table
                      v-loading="loadingJudgmentItems"
                      :data="defendantJudgmentItems"
                      style="width: 100%"
                      empty-text="暂无判决结果"
                      class="custom-table responsive-table"
                      border
                      :table-layout="'fixed'"
                      @selection-change="handleDefendantJudgmentSelectionChange"
                  >
                    <el-table-column type="selection" width="55" align="center" header-align="center"/>
                    <el-table-column
                        prop="sequence"
                        label="序号"
                        width="80"
                        align="center"
                        header-align="center"
                    />
                    <el-table-column
                        prop="content"
                        label="判决结果"
                        min-width="300"
                        align="left"
                        header-align="center"
                        class-name="content-column"
                    >
                      <template #default="{ row }">
                        <div class="content-cell">
                          <el-popover placement="top" :width="400" trigger="hover"
                                      :disabled="!shouldShowHover(row.content, 'medium')">
                            <template #reference>
                              <el-input
                                  v-model="row.content"
                                  placeholder="请输入判决结果"
                                  size="default"
                                  @input="markDefendantJudgmentAsChanged(row)"
                              />
                            </template>
                            <div class="popover-content">
                              {{ row.content }}
                            </div>
                          </el-popover>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="result"
                        label="结果"
                        width="120"
                        align="center"
                        header-align="center"
                    >
                      <template #default="{ row }">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                          <el-switch
                              v-model="row.isSupported"
                              :active-value="true"
                              :inactive-value="false"
                              width="60"
                              inline-prompt
                              active-text="支持"
                              inactive-text="不支持"
                              active-color="#409eff"
                              inactive-color="#c0c4cc"
                              @change="() => handleDefendantJudgmentSupportChange(row)"
                          />
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        width="85"
                        align="center"
                        header-align="center"
                    >
                      <template #default="{ row, $index }">
                        <div class="operation-buttons">
                          <el-button
                              v-if="row.contentChanged"
                              type="success"
                              size="small"
                              circle
                              @click="saveDefendantJudgmentItem(row, $index)"
                              title="保存"
                          >
                            <el-icon>
                              <Check/>
                            </el-icon>
                          </el-button>
                          <el-button
                              type="danger"
                              size="small"
                              circle
                              @click="deleteDefendantJudgmentItem($index)"
                              title="删除"
                          >
                            <el-icon>
                              <Delete/>
                            </el-icon>
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <!-- 添加按钮 -->
                <div class="add-row-container">
                  <el-button
                      type="primary"
                      :icon="Plus"
                      circle
                      size="small"
                      @click="addNewDefendantJudgmentItem"
                  />
                </div>

              </div>
            </div>
          </div>

        </div>

      </template>

      <!-- 民事二审判决书（驳回上诉，维持原判）内容 -->
      <template class="civil_judgement_2_reject" v-if="isCivilSecondInstanceReject">
        <!-- 判项情况 -->
        <div class="judgment-module">
          <div class="section-header">
            <h3>判项情况</h3>
          </div>
          <div class="judgment">
            <div class="data-table-container">
              <el-table
                  :data="[
                    { content: '二审判决结果' },
                    { content: '驳回上诉，维持原判。' }
                  ]"
                  style="width: 100%"
                  class="custom-table responsive-table"
                  border
                  :table-layout="'fixed'"
                  :show-header="false"
              >
                <el-table-column
                    prop="content"
                    align="center"
                    header-align="center"
                    class-name="content-column"
                >
                  <template #default="{ row, $index }">
                    <span :style="$index === 0 ? 'font-weight: 600; color: #303133;' : 'color: #303133;'">
                      {{ row.content }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>

      <!-- 民事二审判决书（二审改判）内容 -->
      <template class="civil_judgement_2_overrule" v-if="isCivilSecondInstanceOverrule">
        <!-- 判项情况 -->
        <div class="judgment-module">

          <div class="section-header">
            <h3>判项情况</h3>
          </div>

          <div class="table-wrapper">
            <div class="data-table-container" style="position: relative;">
              <!-- 任务生成状态 - 覆盖表格主体但保持表头可见 -->
              <TaskGeneratingStatus
                  v-if="isTaskRunning && currentTaskType"
                  ref="taskStatusRef3"
                  :case-import-id="caseImportId"
                  :task-type="currentTaskType"
                  :overlay="false"
                  :position="'absolute'"
                  :show-progress="false"
                  :show-estimated-time="true"
                  @task-completed="handleTaskCompleted"
                  @task-failed="handleTaskFailed"
                  style="position: absolute; top: 62px; left: 0; right: 0; bottom: 0; z-index: 10; background: rgba(255, 255, 255, 0.95);"
              />
              <el-table
                  :data="secondInstanceOverruleItems"
                  style="width: 100%"
                  empty-text="暂无判决结果"
                  class="custom-table responsive-table"
                  border
                  :table-layout="'fixed'"
              >
                <el-table-column type="selection" width="55" align="center" header-align="center"/>
                <el-table-column
                    prop="sequence"
                    label="序号"
                    width="55"
                    align="center"
                    header-align="center"
                />
                <el-table-column
                    prop="content"
                    label="二审判决结果"
                    min-width="400"
                    align="center"
                    header-align="center"
                    class-name="content-column"
                >
                  <template #default="{ row }">
                    <div class="content-cell">
                      <el-popover placement="top" :width="400" trigger="hover"
                                  :disabled="!shouldShowHover(row.content, 'extra-wide')">
                        <template #reference>
                          <el-input
                              v-model="row.content"
                              placeholder="请输入二审判决结果"
                              type="textarea"
                              size="default"
                              @input="markSecondInstanceOverruleAsChanged(row)"
                          />
                        </template>
                        <div class="popover-content">
                          {{ row.content }}
                        </div>
                      </el-popover>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="85"
                    align="center"
                    header-align="center"
                >
                  <template #default="{ row, $index }">
                    <div class="operation-buttons">
                      <el-button
                          v-if="row.contentChanged"
                          type="success"
                          size="small"
                          circle
                          @click="saveSecondInstanceOverruleItem(row, $index)"
                          title="保存"
                      >
                        <el-icon>
                          <Check/>
                        </el-icon>
                      </el-button>
                      <el-button
                          type="danger"
                          size="small"
                          circle
                          @click="deleteSecondInstanceOverruleItem($index)"
                          title="删除"
                      >
                        <el-icon>
                          <Delete/>
                        </el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 添加判决项按钮 -->
              <div class="add-row-container">
                <el-button
                    type="primary"
                    :icon="Plus"
                    circle
                    size="small"
                    @click="addNewSecondInstanceOverruleItem"
                />
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 民事二审判决书（部分改判）内容 -->
      <template class="civil_judgement_2_partial_overrule" v-if="isCivilSecondInstancePartialOverrule">
        <!-- 判项情况 -->
        <div class="judgment-module">
          <div class="section-header">
            <h3>判项情况</h3>
          </div>
          <div class="table-wrapper">
            <div class="data-table-container" style="position: relative;">
              <!-- 任务生成状态 - 覆盖表格主体但保持表头可见 -->
              <TaskGeneratingStatus
                  v-if="isTaskRunning && currentTaskType"
                  ref="taskStatusRef3"
                  :case-import-id="caseImportId"
                  :task-type="currentTaskType"
                  :overlay="false"
                  :position="'absolute'"
                  :show-progress="false"
                  :show-estimated-time="true"
                  @task-completed="handleTaskCompleted"
                  @task-failed="handleTaskFailed"
                  style="position: absolute; top: 62px; left: 0; right: 0; bottom: 0; z-index: 10; background: rgba(255, 255, 255, 0.95);"
              />
              <el-table
                  :data="partialOverruleItems"
                  style="width: 100%"
                  empty-text="暂无判决结果"
                  class="custom-table responsive-table partial-overrule-table"
                  border
                  :table-layout="'fixed'"
              >
                <el-table-column type="selection" width="55" align="center" header-align="center"/>
                <el-table-column
                    prop="sequence"
                    label="序号"
                    width="40"
                    align="center"
                    header-align="center"
                />
                <el-table-column
                    prop="firstInstanceResult"
                    label="一审判决结果"
                    min-width="280"
                    align="left"
                    header-align="center"
                    class-name="content-column"
                >
                  <template #default="{ row }">
                    <div class="content-cell">
                      <!-- 如果是新增数据（没有id）或者是可编辑状态，显示输入框 -->
                      <template v-if="!row.id || row.isEditable">
                        <el-popover placement="top" :width="500" trigger="hover"
                                    :disabled="!isTextOverflowByChars(row.firstInstanceResult, 10)">
                          <template #reference>
                            <el-input
                                v-model="row.firstInstanceResult"
                                placeholder="请输入一审判决结果"
                                size="default"
                                type="textarea"
                                @input="markPartialOverruleAsChanged(row)"
                            />
                          </template>
                          <div class="popover-content">
                            {{ row.firstInstanceResult }}
                          </div>
                        </el-popover>
                      </template>
                      <!-- 如果是从后端查询的数据（有id），显示只读文本 -->
                      <template v-else>
                        <el-popover placement="top" :width="500" trigger="hover"
                                    :disabled="!isTextOverflowByChars(row.firstInstanceResult, 10)">
                          <template #reference>
                            <span class="judgment-result-text">{{ row.firstInstanceResult }}</span>
                          </template>
                          <div class="popover-content">
                            {{ row.firstInstanceResult }}
                          </div>
                        </el-popover>
                      </template>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="二审判决结果"
                    min-width="280"
                    align="left"
                    header-align="center"
                    class-name="second-instance-column"
                >
                  <template #default="{ row }">
                    <div class="decision-options">
                      <div class="option-row">
                        <el-radio
                            v-if="row.dataType === 2"
                            v-model="row.secondInstanceDecision"
                            value="new"
                            size="small"
                            @change="markPartialOverruleAsChanged(row)"
                        >
                          新增
                        </el-radio>

                        <el-radio
                            v-model="row.secondInstanceDecision"
                            value="maintain"
                            size="small"
                            @change="markPartialOverruleAsChanged(row)"
                        >
                          维持
                        </el-radio>
                        <el-radio
                            v-model="row.secondInstanceDecision"
                            value="revoke"
                            size="small"
                            @change="markPartialOverruleAsChanged(row)"
                        >
                          撤销
                        </el-radio>
                      </div>

                      <div class="option-row">
                        <el-radio
                            v-model="row.secondInstanceDecision"
                            value="modify"
                            size="small"
                            @change="markPartialOverruleAsChanged(row)"
                        >
                          变更
                        </el-radio>
                        <!-- 变更内容输入框 -->
                        <div v-if="row.secondInstanceDecision === 'modify'" class="modify-content-input" style="width: 190px;">
                          <el-popover placement="top" :width="400" trigger="hover"
                                      :disabled="!isTextOverflowByChars(row.modifyContent, 15)">
                            <template #reference>
                              <el-input
                                  v-model="row.modifyContent"
                                  placeholder="请输入变更内容"
                                  size="small"
                                  type="textarea"
                                  @input="markPartialOverruleAsChanged(row)"
                              />
                            </template>
                            <div class="popover-content">
                              {{ row.modifyContent }}
                            </div>
                          </el-popover>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="80"
                    align="center"
                    header-align="center"
                >
                  <template #default="{ row, $index }">
                    <div class="operation-buttons">
                      <el-button
                          v-if="row.contentChanged"
                          type="success"
                          size="small"
                          circle
                          @click="savePartialOverruleItem(row, $index)"
                          title="保存"
                      >
                        <el-icon>
                          <Check/>
                        </el-icon>
                      </el-button>
                      <el-button
                          type="danger"
                          size="small"
                          circle
                          @click="deletePartialOverruleItem($index)"
                          title="删除"
                      >
                        <el-icon>
                          <Delete/>
                        </el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 添加判决项按钮 -->
              <div class="add-row-container">
                <el-button
                    type="primary"
                    :icon="Plus"
                    circle
                    size="small"
                    @click="addNewPartialOverruleItem"
                />
              </div>

            </div>
          </div>
        </div>
      </template>

      <!-- 诉讼费用 -->
      <div class="cost-module">
        <div class="cost">
          <div class="section-header">
            <h3>诉讼费用</h3>
            <div class="header-right">
              <div class="cost-actions">
                <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="batchDeleteCostItems"
                    :disabled="!hasSelectedCostItems"
                    title="批量删除"
                >
                  <el-icon>
                    <Delete/>
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <div class="cost-table">
            <el-table
                v-loading="loadingLegalFees"
                :data="litigationCostItems"
                style="width: 100%"
                empty-text="暂无诉讼费用"
                class="custom-table responsive-table judgment-table"
                border
                :table-layout="'fixed'"
                @selection-change="handleCostSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" header-align="center"/>
              <el-table-column
                  prop="sequence"
                  label="序号"
                  width="60"
                  align="center"
                  header-align="center"
              />
              <el-table-column
                  prop="location"
                  label="诉讼地位"
                  min-width="80"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row }">
                  <div class="content-cell">
                    <el-popover placement="top" :width="250" trigger="hover"
                                :disabled="!shouldShowHover(row.location, 'susongfeiyong')">
                      <template #reference>
                        <el-select
                            v-model="row.location"
                            placeholder="选择诉讼地位"
                            size="default"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                            @change="handleLocationChange(row)"
                        >
                          <el-option
                              v-for="(party, index) in getPartyOptions()"
                              :key="party.id"
                              :label="party.displayLabel"
                              :value="party.uniqueKey"
                              :disabled="isOptionDisabled(party, row)"
                          />
                        </el-select>
                      </template>
                      <div class="popover-content">
                        {{ row.location }}
                      </div>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                  prop="name"
                  label="姓名"
                  min-width="70"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row }">
                  <div class="content-cell readonly-name-cell">
                    <el-popover placement="top" :width="200" trigger="hover"
                                :disabled="!shouldShowHover(row.name, 'susongfeiyong')">
                      <template #reference>
                        <el-input
                            v-model="row.name"
                            size="default"
                            readonly
                            @input="markAsChanged(row)"
                        />
                      </template>
                      <div class="popover-content">
                        {{ row.name }}
                      </div>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                  prop="costType"
                  label="费用类型"
                  width="120"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row }">
                  <el-select
                      v-model="row.costType"
                      placeholder="选择费用类型"
                      size="default"
                      @change="markAsChanged(row)"
                  >
                    <el-option label="案件受理费" value="案件受理费"/>
                    <el-option label="保全费" value="保全费"/>
                    <el-option label="公告费" value="公告费"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                  prop="paidAmount"
                  label="已缴金额"
                  width="90"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row }">
                  <el-input
                      v-model="row.paidAmount"
                      type="number"
                      size="default"
                      :min="0"
                      @input="markAsChanged(row)"
                      @blur="validatePaidAmount(row)"
                  />
                </template>

              </el-table-column>
              <el-table-column
                  prop="totalAmount"
                  label="承担金额"
                  width="90"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row }">
                  <el-input
                      v-model="row.totalAmount"
                      type="number"
                      size="default"
                      :min="0.01"
                      :step="0.01"
                      placeholder="请输入大于0的金额"
                      @input="markAsChanged(row)"
                      @blur="validateTotalAmount(row)"
                  />
                </template>

              </el-table-column>
              <el-table-column
                  label="操作"
                  width="85"
                  align="center"
                  header-align="center"
              >
                <template #default="{ row, $index }">
                  <div class="operation-buttons">
                    <el-button
                        v-if="row.contentChanged"
                        type="success"
                        size="small"
                        circle
                        @click="saveCostItem(row, $index)"
                        title="保存"
                    >
                      <el-icon>
                        <Check/>
                      </el-icon>
                    </el-button>
                    <el-button
                        type="danger"
                        size="small"
                        circle
                        @click="deleteCostItem($index)"
                        title="删除"
                    >
                      <el-icon>
                        <Delete/>
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 添加费用项按钮 -->
            <div class="add-row-container">
              <el-button
                  type="primary"
                  :icon="Plus"
                  circle
                  size="small"
                  @click="addNewCostItem"
              />
            </div>
          </div>
        </div>
      </div>


    </div>


    <!-- 底部操作按钮 -->
    <div v-show="!showOnlyOffice" class="editor-footer">
      <el-button @click="goBack">返回</el-button>
      <el-button type="primary" @click="generateDocument" :loading="generating">
        {{ generating ? '生成中...' : '生成文书' }}
      </el-button>
    </div>


  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onUnmounted, watch, nextTick} from 'vue'
import {Delete, Plus, InfoFilled, Edit, Check, Loading, Menu, Document} from '@element-plus/icons-vue'
import {ElMessage, ElLoading, ElMessageBox} from 'element-plus'
import {http} from '@/utils/http'
import {documentGenerationApi, type DocumentGenerationInfo, type DocumentGenerationPreCheckVO} from '@/api/documentGeneration'
import {legalFeesApi, type LegalFeesWithParty} from '@/api/legalFees'
import {casePartyApi, type CasePartyTab} from '@/api/caseParty'
import {judgmentSituationApi, type JudgmentSituation} from '@/api/judgmentSituation'
import {caseApi, type CaseImportRecord} from '@/api/case'
import {trialOrganizationMembersApi, type TrialOrganizationMembers} from '@/api/trialOrganizationMembers'
import TaskGeneratingStatus from '@/components/task-generating/TaskGeneratingStatus.vue'

// 定义 props
interface Props {
  caseImportId: number
  caseType: string
  documentType: string
  hasCounterclaim?: boolean
  caseInfo?: any // 案件信息，包含案号等
}

const props = withDefaults(defineProps<Props>(), {
  hasCounterclaim: false
})

// 定义 emits
const emit = defineEmits<{
  close: []
}>()

// 从 props 获取数据
const caseImportId = computed(() => props.caseImportId)
const caseType = computed(() => props.caseType)
const documentType = computed(() => props.documentType)



// 页面数据

const generating = ref(false)

// 判决相关数据
const selectedJudgmentType = ref('all_support')
const selectedPlaintiffJudgmentType = ref('all_support')
const selectedDefendantJudgmentType = ref('all_support')
const hasCounterclaim = ref(props.hasCounterclaim)
const judgmentItems = ref<any[]>([])
const plaintiffJudgmentItems = ref<any[]>([])
const defendantJudgmentItems = ref<any[]>([])

// 原始值记录，用于比较内容是否真正改变
const originalValues = ref<{ [key: number]: string }>({})

// 任务状态相关
const currentTaskType = ref<string>('')
const isTaskRunning = ref<boolean>(false)
const taskStatusRef1 = ref()
const taskStatusRef2 = ref()

// OnlyOffice编辑器相关
const showOnlyOffice = ref<boolean>(false)
let currentDocumentId: number | null = null
let generatedContent = '' // 用于存储生成的内容，以防插入失败时手动复制

// 生成步骤状态
const isGenerating = ref(false)
const currentGenerationStatus = ref('')
const currentStepIndex = ref(0)
const hasRelease = ref(false)

// 任务队列系统
interface CharacterTask {
  id: string
  content: string
  needIndent: boolean
  isBookmark: boolean
  retryCount: number
  maxRetries: number
  successCallback?: () => void
}

const characterTaskQueue = ref<CharacterTask[]>([])
const isTaskExecuting = ref(false)
const currentExecutingTask = ref<CharacterTask | null>(null)
const messageCollector = ref('')
const messageCollectorNeedIndent = ref(false) // messageCollector中的内容是否需要缩进
const taskIdCounter = ref(0)
const TASK_TIMEOUT = 3000 // 3秒超时
const MAX_RETRIES = 3 // 最大重试次数
const totalSteps = ref(10)

// SSE连接管理
const activeSSEConnections = ref<EventSource[]>([])

// 轮询控制
const shouldStopPolling = ref(false)

// 书签相关状态
const bookmarkList = ref<Array<{name: string, displayName: string}>>([])
const showBookmarkNav = ref(false)

// 部分改判项原始值记录，用于比较一审判决结果和二审决定是否真正改变
const originalPartialOverruleValues = ref<{ [key: number]: { firstInstanceResult: string, secondInstanceDecision: string, modifyContent: string } }>({})

// 临时存储变更内容，用于在切换二审决定时保持数据
const tempModifyContent = ref<{ [key: number]: string }>({})

// 判项情况加载状态
const loadingJudgmentItems = ref(false)

// 选中的判项
const selectedJudgmentItems = ref<any[]>([])

// 选中的本诉原告判项
const selectedPlaintiffJudgmentItems = ref<any[]>([])

// 选中的本诉被告（反诉原告）判项
const selectedDefendantJudgmentItems = ref<any[]>([])

// 选中的诉讼费用项
const selectedCostItems = ref<any[]>([])

// 部分改判数据结构
const partialOverruleItems = ref<any[]>([])

// 二审改判专用数据结构
const secondInstanceOverruleItems = ref<any[]>([])

// 诉讼费用数据
const litigationCostItems = ref<any[]>([])
const loadingLegalFees = ref(false)

// 当事人信息
const caseParties = ref<CasePartyTab[]>([])
const judgmentData = ref<any[]>([]) // 存储判项数据，用于获取当事人信息

// 全文内容优化状态
const optimizeContentCompleted = ref(false)
const optimizeContentResult = ref<any>(null)


// 计算属性

const documentTypeText = computed(() => {
  const typeMap: Record<string, string> = {
    'civil_judgement_1': '民事判决书（一审普通程序用）',
    'civil_judgement_2_reject': '民事判决书(驳回上诉，维持原判)',
    'civil_judgement_2_overrule': '民事判决书(二审改判)',
    'civil_judgement_2_partial_overrule': '民事判决书(部分改判)'
  }
  return typeMap[documentType.value] || documentType.value
})

const documentTitle = computed(() => {
  return `${documentTypeText.value}生成`
})

// 方法

// 判断是否为民事一审案件
const isCivilFirstInstance = computed(() => {
  return caseType.value === 'civil1' && documentType.value === 'civil_judgement_1'
})

// 判断是否为民事二审驳回上诉案件
const isCivilSecondInstanceReject = computed(() => {
  return caseType.value === 'civil2' && documentType.value === 'civil_judgement_2_reject'
})

// 判断是否为民事二审改判案件
const isCivilSecondInstanceOverrule = computed(() => {
  return caseType.value === 'civil2' && documentType.value === 'civil_judgement_2_overrule'
})

// 判断是否为民事二审部分改判案件
const isCivilSecondInstancePartialOverrule = computed(() => {
  return caseType.value === 'civil2' && documentType.value === 'civil_judgement_2_partial_overrule'
})

// 是否有选中的判项
const hasSelectedJudgmentItems = computed(() => {
  return selectedJudgmentItems.value.length > 0
})

// 是否有选中的本诉原告判项
const hasSelectedPlaintiffJudgmentItems = computed(() => {
  return selectedPlaintiffJudgmentItems.value.length > 0
})

// 是否有选中的本诉被告（反诉原告）判项
const hasSelectedDefendantJudgmentItems = computed(() => {
  return selectedDefendantJudgmentItems.value.length > 0
})

// 是否有选中的诉讼费用项
const hasSelectedCostItems = computed(() => {
  return selectedCostItems.value.length > 0
})

// 本诉原告当事人信息
const plaintiffPartyInfo = computed(() => {
  const plaintiffData = judgmentData.value.find(item => item.ssdw === '本诉原告')
  return {
    ssdw: plaintiffData?.ssdw,
    dsrxm: plaintiffData?.dsrxm
  }
})

// 本诉被告（反诉原告）当事人信息
const defendantPartyInfo = computed(() => {
  const defendantData = judgmentData.value.find(item => item.ssdw === '本诉被告')
  return {
    ssdw: defendantData?.ssdw,
    dsrxm: defendantData?.dsrxm
  }
})
// ==================== 判决类型设置方法 ====================

/**
 * 设置主要判决项的判决类型
 * @param type 判决类型：'all_support'(全部支持)、'partial_support'(部分支持)、'all_reject'(全部驳回)
 */
const setJudgmentType = async (type: string) => {
  selectedJudgmentType.value = type

  // 根据判决类型自动设置所有判决项的结果
  const newSupportedValue = type === 'all_support' ? true : (type === 'all_reject' ? false : null)

  if (newSupportedValue !== null) {
    // 校验表格是否有数据
    if (judgmentItems.value.length === 0) {
      ElMessage.warning('请先添加判决项，再进行批量操作')
      return
    }

    // 校验是否有空的判决内容
    const emptyItems = judgmentItems.value.filter(item => !item.content || !item.content.trim())
    if (emptyItems.length > 0) {
      ElMessage.warning('请先填写所有判决项的内容，再进行批量操作')
      return
    }

    // 先更新本地状态
    judgmentItems.value.forEach(item => {
      item.isSupported = newSupportedValue
      item.contentChanged = false
    })

    try {
      // 构造批量保存或更新的数据
      const judgmentDataList: JudgmentSituation[] = judgmentItems.value.map(item => ({
         id: item.id,
         caseImportRecordId: caseImportId.value,
         dsrxm: item.dsrxm || '', // 当事人姓名
         ssdw: item.ssdw || '',   // 诉讼地位
         judgment: item.content,
         opinion: newSupportedValue ? 2 : 1
       }))

      // 使用批量保存或更新接口
      await judgmentSituationApi.saveOrUpdateBatch(judgmentDataList)
    } catch (error) {
      console.error('批量更新主要判决项失败:', error)
      // 恢复原状态
      judgmentItems.value.forEach(item => {
        item.isSupported = !newSupportedValue
        item.contentChanged = true
      })
      ElMessage.error('批量更新失败，请重试')
    }
  }
  // 'partial_support' 不自动设置，由用户手动选择
}

/**
 * 设置本诉原告判决项的判决类型
 * @param type 判决类型：'all_support'(全部支持)、'partial_support'(部分支持)、'all_reject'(全部驳回)
 */
const setPlaintiffJudgmentType = async (type: string) => {
  selectedPlaintiffJudgmentType.value = type

  // 根据判决类型自动设置所有判决项的结果
  const newSupportedValue = type === 'all_support' ? true : (type === 'all_reject' ? false : null)

  if (newSupportedValue !== null) {
    // 校验表格是否有数据
    if (plaintiffJudgmentItems.value.length === 0) {
      ElMessage.warning('请先添加本诉原告判决项，再进行批量操作')
      return
    }

    // 校验是否有空的判决内容
    const emptyItems = plaintiffJudgmentItems.value.filter(item => !item.content || !item.content.trim())
    if (emptyItems.length > 0) {
      ElMessage.warning('请先填写所有本诉原告判决项的内容，再进行批量操作')
      return
    }

    // 先更新本地状态
  plaintiffJudgmentItems.value.forEach(item => {
      item.isSupported = newSupportedValue
      item.contentChanged = false
    })

    try {
      // 构造批量保存或更新的数据
             const judgmentDataList: JudgmentSituation[] = plaintiffJudgmentItems.value.map(item => ({
         id: item.id,
         caseImportRecordId: caseImportId.value,
         dsrxm: item.dsrxm || '', // 当事人姓名
         ssdw: item.ssdw || '',   // 诉讼地位
         judgment: item.content,
         opinion: newSupportedValue ? 2 : 1
       }))

      // 使用批量保存或更新接口
      await judgmentSituationApi.saveOrUpdateBatch(judgmentDataList)
    } catch (error) {
      console.error('批量更新本诉原告判决项失败:', error)
      // 恢复原状态
      plaintiffJudgmentItems.value.forEach(item => {
        item.isSupported = !newSupportedValue
        item.contentChanged = true
      })
      ElMessage.error('批量更新失败，请重试')
    }
  }
  // 'partial_support' 不自动设置，由用户手动选择
}

/**
 * 设置 本诉被告（反诉原告）判决项的判决类型
 * @param type 判决类型：'all_support'(全部支持)、'partial_support'(部分支持)、'all_reject'(全部驳回)
 */
const setDefendantJudgmentType = async (type: string) => {
  selectedDefendantJudgmentType.value = type

  // 根据判决类型自动设置所有判决项的结果
  const newSupportedValue = type === 'all_support' ? true : (type === 'all_reject' ? false : null)

  if (newSupportedValue !== null) {
    // 校验表格是否有数据
    if (defendantJudgmentItems.value.length === 0) {
      ElMessage.warning('请先添加反诉原告判决项，再进行批量操作')
      return
    }

    // 校验是否有空的判决内容
    const emptyItems = defendantJudgmentItems.value.filter(item => !item.content || !item.content.trim())
    if (emptyItems.length > 0) {
      ElMessage.warning('请先填写所有反诉原告判决项的内容，再进行批量操作')
      return
    }

    // 先更新本地状态
  defendantJudgmentItems.value.forEach(item => {
      item.isSupported = newSupportedValue
      item.contentChanged = false
    })

    try {
      // 构造批量保存或更新的数据
      const judgmentDataList: JudgmentSituation[] = defendantJudgmentItems.value.map(item => ({
        id: item.id,
        caseImportRecordId: caseImportId.value,
        dsrxm: item.dsrxm || '', // 当事人姓名
        ssdw: item.ssdw || '',   // 诉讼地位
        judgment: item.content,
        opinion: newSupportedValue ? 2 : 1
      }))

      // 使用批量保存或更新接口
      await judgmentSituationApi.saveOrUpdateBatch(judgmentDataList)
    } catch (error) {
      console.error('批量更新反诉原告判决项失败:', error)
      // 恢复原状态
      defendantJudgmentItems.value.forEach(item => {
        item.isSupported = !newSupportedValue
        item.contentChanged = true
      })
      ElMessage.error('批量更新失败，请重试')
    }
  }
  // 'partial_support' 不自动设置，由用户手动选择
}

// ==================== 自动检测判决状态方法 ====================

/**
 * 自动检测主要判决项的状态并更新判决类型按钮选择
 * 根据支持/不支持的判决项数量自动判断应该选择哪种判决类型
 */
const autoDetectJudgmentType = () => {
  const supportedCount = judgmentItems.value.filter(item => item.isSupported).length
  const totalCount = judgmentItems.value.length

  if (supportedCount === 0) {
    selectedJudgmentType.value = 'all_reject'
  } else if (supportedCount === totalCount) {
    selectedJudgmentType.value = 'all_support'
  } else {
    selectedJudgmentType.value = 'partial_support'
  }
}

/**
 * 自动检测本诉原告判决项的状态并更新判决类型按钮选择
 * 根据支持/不支持的判决项数量自动判断应该选择哪种判决类型
 */
const autoDetectPlaintiffJudgmentType = () => {
  const supportedCount = plaintiffJudgmentItems.value.filter(item => item.isSupported).length
  const totalCount = plaintiffJudgmentItems.value.length

  if (supportedCount === 0) {
    selectedPlaintiffJudgmentType.value = 'all_reject'
  } else if (supportedCount === totalCount) {
    selectedPlaintiffJudgmentType.value = 'all_support'
  } else {
    selectedPlaintiffJudgmentType.value = 'partial_support'
  }
}

/**
 * 自动检测  本诉被告（反诉原告）判决项的状态并更新判决类型按钮选择
 * 根据支持/不支持的判决项数量自动判断应该选择哪种判决类型
 */
const autoDetectDefendantJudgmentType = () => {
  const supportedCount = defendantJudgmentItems.value.filter(item => item.isSupported).length
  const totalCount = defendantJudgmentItems.value.length

  if (supportedCount === 0) {
    selectedDefendantJudgmentType.value = 'all_reject'
  } else if (supportedCount === totalCount) {
    selectedDefendantJudgmentType.value = 'all_support'
  } else {
    selectedDefendantJudgmentType.value = 'partial_support'
  }
}

// ==================== 判决结果变化处理函数 ====================

/**
 * 主要判决项结果变化时的处理函数
 * 当用户手动切换判决项的支持/不支持状态时，自动更新判决类型
 */
const onJudgmentResultChange = () => {
  autoDetectJudgmentType()
}

/**
 * 本诉原告判决项结果变化时的处理函数
 * 当用户手动切换判决项的支持/不支持状态时，自动更新判决类型
 */
const onPlaintiffJudgmentResultChange = () => {
  autoDetectPlaintiffJudgmentType()
}

/**
 * 本诉被告（反诉原告）判决项结果变化时的处理函数
 * 当用户手动切换判决项的支持/不支持状态时，自动更新判决类型
 */
const onDefendantJudgmentResultChange = () => {
  autoDetectDefendantJudgmentType()
}


// ==================== 诉讼费用相关方法 ====================

/**
 * 加载当事人信息
 */
const loadCaseParties = async () => {
  if (!caseImportId.value) return
  try {
    const partiesData = await casePartyApi.listForLegalFees(caseImportId.value)
    caseParties.value = partiesData
  } catch (error) {
    console.error('加载当事人信息失败:', error)
    ElMessage.error('加载当事人信息失败')
  }
}

/**
 * 获取当事人选项，处理相同诉讼地位的情况
 */
const getPartyOptions = () => {
  const partyTypeCount: Record<string, number> = {}
  const partyTypeIndex: Record<string, number> = {}

  // 统计每种诉讼地位的数量
  caseParties.value.forEach(party => {
    partyTypeCount[party.partyType] = (partyTypeCount[party.partyType] || 0) + 1
  })

  // 生成选项
  const options = caseParties.value.map((party, index) => {
    const count = partyTypeCount[party.partyType]
    partyTypeIndex[party.partyType] = (partyTypeIndex[party.partyType] || 0) + 1

    let displayLabel = party.partyType
    let uniqueKey = party.partyType

    // 如果同一诉讼地位有多个当事人，添加当事人姓名而不是序号
    if (count > 1) {
      displayLabel = `${party.partyType}(${party.partyNameString})`
      uniqueKey = `${party.partyType}_${index + 1}`
    }
    return {
      id: index, // 使用索引作为ID
      partyType: party.partyType,
      partyName: party.partyNameString, // 使用 partyNameString
      partyNameList: party.partyNameList, // 新增字段
      displayLabel,
      uniqueKey,
      // 新增：用于反向查找的映射
      originalIndex: index
    }
  })
  return options
}

/**
 * 提取诉讼地位基础类型（去除当事人姓名，保留完整的诉讼地位类型）
 */
const getBasePartyType = (partyType: string): string => {
  if (!partyType) return ''
  // 处理"原告(张三)"、"原告(1)"等格式，提取基础类型"原告"
  const match = partyType.match(/^([^(（]+)/)
  if (match) {
    let baseType = match[1].trim()
    // 保留完整的诉讼地位类型，包括"-共同"等后缀
    // "原告"和"原告-共同"应该被视为不同类型
    return baseType
  }
  return partyType
}

/**
 * 判断选项是否应该被禁用
 */
const isOptionDisabled = (party: any, row: any): boolean => {
  // 如果当前行没有选择任何项，所有选项都可用
  if (!row.location || (Array.isArray(row.location) && row.location.length === 0)) {
    return false
  }
  
  // 获取当前已选择的诉讼地位基础类型
  const selectedLocations = Array.isArray(row.location) ? row.location : [row.location]
  if (selectedLocations.length === 0) {
    return false
  }
  
  // 获取所有选项以便查找
  const partyOptions = getPartyOptions()
  
  // 获取第一个选择项的基础类型
  const firstSelectedOption = partyOptions.find(opt => opt.uniqueKey === selectedLocations[0])
  const firstSelectedType = firstSelectedOption ? getBasePartyType(firstSelectedOption.partyType) : ''
  
  // 获取当前选项的基础类型
  const currentOptionType = getBasePartyType(party.partyType)
  
  // 如果基础类型不同，则禁用
  return firstSelectedType !== currentOptionType
}

/**
 * 加载诉讼费用数据
 */
const loadLegalFees = async () => {
  if (!caseImportId.value) {
    console.warn('案件ID为空，无法加载诉讼费用数据')
    return
  }

  loadingLegalFees.value = true
  try {
    console.log('开始加载诉讼费用数据，案件ID:', caseImportId.value)
    const legalFeesData = await legalFeesApi.basicList(caseImportId.value)
    console.log('获取到诉讼费用数据:', legalFeesData.length, '条')

    // 数据验证
    if (!Array.isArray(legalFeesData)) {
      console.error('诉讼费用数据格式错误，期望数组类型')
      ElMessage.error('诉讼费用数据格式错误')
      return
    }

    // 转换数据格式以适配前端表格
    litigationCostItems.value = legalFeesData.map((item, index) => {
      // 数据验证
      if (!item || typeof item !== 'object') {
        console.warn('发现无效的诉讼费用数据项:', item)
        return null
      }

      // 处理多选诉讼地位的回显
      let locationValue = item.ssdw || ''
      // 如果是逗号分隔的多选数据，转换为数组格式供el-select多选使用
      if (locationValue && locationValue.includes(',')) {
        const partyOptions = getPartyOptions()
        const locationArray: string[] = []
        
        locationValue.split(',').forEach((loc: string) => {
          const trimmedLoc = loc.trim()
          // 查找对应的uniqueKey
          const matchedOption = partyOptions.find(option => option.displayLabel === trimmedLoc)
          if (matchedOption) {
            locationArray.push(matchedOption.uniqueKey)
          }
        })
        
        locationValue = locationArray.length > 0 ? locationArray : locationValue
      } else if (locationValue) {
        // 单选情况，也需要转换为uniqueKey
        const partyOptions = getPartyOptions()
        const matchedOption = partyOptions.find(option => option.displayLabel === locationValue)
        if (matchedOption) {
          locationValue = [matchedOption.uniqueKey]
        }
      }

      // 处理多选当事人姓名的回显
      let nameValue = item.dsrxm || ''
      // 如果是逗号分隔的多选姓名，保持为字符串格式（因为name字段在表格中显示为文本）
      // 但需要确保与location的选择保持一致

      return {
        id: item.id || null,
        sequence: index + 1,
        location: locationValue,
        name: nameValue, // 使用处理后的姓名值
        costType: item.feeType || '案件受理费',
        paidAmount: Number(item.amountPaid) || 0,
        totalAmount: Number(item.amountToBear) || 0,
        contentChanged: false,
        casePartyId: item.casePartyId || null
      }
    }).filter(item => item !== null) // 过滤掉无效数据

    console.log('诉讼费用数据转换完成，有效数据:', litigationCostItems.value.length, '条')

  } catch (error) {
    console.error('加载诉讼费用数据失败:', error)
    const errorMessage = error?.response?.data?.message || error?.message || '加载诉讼费用数据失败'
    ElMessage.error(errorMessage)

    // 发生错误时清空数据，避免显示过期数据
    litigationCostItems.value = []
  } finally {
    loadingLegalFees.value = false
  }
}

/**
 * 确定诉讼地位显示值
 * @param item 诉讼费用数据项
 * @returns 诉讼地位显示值
 */
const determineLocationValue = (item: any): string => {
  // 直接使用保存的诉讼地位，现在它存储的就是用户看到的显示文本
  if (item.ssdw) {
    return item.ssdw
  }

  // 如果没有保存的诉讼地位，返回空字符串
  // 新的逻辑下，我们不再尝试从当事人数据中重新构建显示文本
  // 因为保存时已经存储了正确的显示文本
  return ''
}

/**
 * 文书生成前置检查
 */
const performPreCheck = async () => {
  if (!caseImportId.value) return

  try {
    console.log('执行文书生成前置检查，案件ID:', caseImportId.value, 'documentType:', documentType.value)

    const result: DocumentGenerationPreCheckVO = await documentGenerationApi.preCheck(caseImportId.value, documentType.value)

    console.log('前置检查结果:', result)

    if (result.loading && result.taskType) {
      // 设置任务状态
      currentTaskType.value = result.taskType
      isTaskRunning.value = true
      console.log('检测到任务正在运行，任务类型:', result.taskType)

      // 如果是新启动的任务，手动触发任务监控
      if (result.needsAsyncCall) {
        console.log('检测到新启动的异步任务，手动触发任务监控...')
        // 等待1秒让异步任务在后端启动，然后手动触发监控
        setTimeout(() => {
          console.log('开始手动触发TaskGeneratingStatus组件监控')
          // 手动触发两个TaskGeneratingStatus组件开始监控
          if (taskStatusRef1.value?.startTaskMonitoring) {
            taskStatusRef1.value.startTaskMonitoring()
          }
          if (taskStatusRef2.value?.startTaskMonitoring) {
            taskStatusRef2.value.startTaskMonitoring()
          }
        }, 1000)
      }
    } else {
      // 任务未运行或已完成
      currentTaskType.value = ''
      isTaskRunning.value = false
    }
  } catch (error) {
    console.error('文书生成前置检查失败:', error)
    currentTaskType.value = ''
    isTaskRunning.value = false
  }
}

/**
 * 处理任务完成事件
 */
const handleTaskCompleted = async (event?: any) => {
  console.log('任务完成事件:', event)

  // 重置任务状态
  currentTaskType.value = ''
  isTaskRunning.value = false

  // 重新加载判项情况数据
  await loadJudgmentItems()

  ElMessage.success('数据已更新')
}

/**
 * 处理任务失败事件
 */
const handleTaskFailed = (event?: any) => {
  console.error('任务失败事件:', event)

  // 重置任务状态
  currentTaskType.value = ''
  isTaskRunning.value = false

  ElMessage.error('任务执行失败，请重试')
}

/**
 * 加载判项情况数据---判项查询接口
 * 根据当前文书类型精确获取对应的判项数据并进行分类处理
 */
const loadJudgmentItems = async () => {
  if (!caseImportId.value) {
    console.warn('案件ID为空，无法加载判项数据')
    return
  }

  loadingJudgmentItems.value = true
  try {
    console.log('开始加载判项数据，案件ID:', caseImportId.value, '文书类型:', documentType.value)
    
    // 根据当前文书类型获取对应的判项数据
    const judgmentDataResult = await judgmentSituationApi.listByCaseImportIdAndDocumentType(caseImportId.value, documentType.value)
    
    console.log('获取到判项数据:', judgmentDataResult.length, '条')

    // 存储判项数据到全局变量
    judgmentData.value = judgmentDataResult

    // 当事人信息通过计算属性自动处理，无需单独调用

    // 清空现有数据
    judgmentItems.value = []
    plaintiffJudgmentItems.value = []
    defendantJudgmentItems.value = []
    secondInstanceOverruleItems.value = []
    partialOverruleItems.value = []

    // 处理判项数据分类
    judgmentDataResult.forEach((item) => {
      // 数据验证
      if (!item.documentCaseType) {
        console.warn('发现没有documentCaseType的判项数据，跳过:', item)
        return
      }

      // 记录原始值，用于后续比较
      if (item.id) {
        originalValues.value[item.id] = item.judgment || ''
      }
      
      // 根据文书类型进行分类处理
      switch (item.documentCaseType) {
        case 'civil_judgement_1':
          // 民事一审判决书：按当事人类型分类
          handleCivilFirstInstanceItem(item)
          break
        case 'civil_judgement_2_reject':
          // 民事二审驳回上诉案件：所有判项归为主判项
          handleCivilSecondInstanceRejectItem(item)
          break
        case 'civil_judgement_2_overrule':
          // 民事二审改判案件：所有判项归为二审改判项
          handleCivilSecondInstanceOverruleItem(item)
          break
        case 'civil_judgement_2_partial_overrule':
          // 民事二审部分改判案件：转换为部分改判格式
          handleCivilSecondInstancePartialOverruleItem(item)
          break
        default:
          console.warn('未知的文书类型:', item.documentCaseType, item)
      }
    })
    
    // 处理完数据后重新设置序号
    judgmentItems.value.forEach((item, index) => {
      item.sequence = index + 1
    })
    plaintiffJudgmentItems.value.forEach((item, index) => {
      item.sequence = index + 1
    })
    defendantJudgmentItems.value.forEach((item, index) => {
      item.sequence = index + 1
    })
    secondInstanceOverruleItems.value.forEach((item, index) => {
      item.sequence = index + 1
    })
    partialOverruleItems.value.forEach((item, index) => {
      item.sequence = index + 1
    })

    // 输出分类结果统计
    console.log('判项数据分类完成:', {
      documentType: documentType.value,
      totalItems: judgmentDataResult.length,
      mainItems: judgmentItems.value.length,
      plaintiffItems: plaintiffJudgmentItems.value.length,
      defendantItems: defendantJudgmentItems.value.length,
      secondInstanceItems: secondInstanceOverruleItems.value.length,
      partialOverruleItems: partialOverruleItems.value.length
    })
    
    // 自动检测判决类型
    autoDetectJudgmentType()
    autoDetectPlaintiffJudgmentType()
    autoDetectDefendantJudgmentType()
    
  } catch (error) {
    console.error('加载判项情况数据失败:', error)
    ElMessage.error('加载判项情况数据失败，请重试')
  } finally {
    loadingJudgmentItems.value = false
  }
}

/**
 * 处理民事一审判决书判项
 */
const handleCivilFirstInstanceItem = (item: any) => {
  const judgmentItem: any = {
    id: item.id,
    sequence: 0, // 序号稍后统一设置
    content: item.judgment || '',
    isSupported: item.opinion === 2,
    contentChanged: false,
    dsrxm: item.dsrxm || '', // 当事人姓名
    ssdw: item.ssdw || '',   // 诉讼地位
    documentCaseType: item.documentCaseType
  }

  // 根据诉讼地位进行分类
  if (!item.ssdw || item.ssdw === '') {
    // 没有诉讼地位，归为主判项
    judgmentItems.value.push(judgmentItem)
  } else if (item.ssdw === '本诉原告') {
    plaintiffJudgmentItems.value.push(judgmentItem)
  } else if (item.ssdw === '本诉被告') {
    defendantJudgmentItems.value.push(judgmentItem)
  } else {
    // 其他类型也归为主判项
    judgmentItems.value.push(judgmentItem)
  }
}

/**
 * 处理民事二审驳回上诉案件判项
 */
const handleCivilSecondInstanceRejectItem = (item: any) => {
  const judgmentItem: any = {
    id: item.id,
    sequence: 0, // 序号稍后统一设置
    content: item.judgment || '',
    isSupported: item.opinion === 2,
    contentChanged: false,
    dsrxm: item.dsrxm || '', // 当事人姓名
    ssdw: item.ssdw || '',   // 诉讼地位
    documentCaseType: item.documentCaseType
  }
  judgmentItems.value.push(judgmentItem)
}

/**
 * 处理民事二审改判案件判项
 */
const handleCivilSecondInstanceOverruleItem = (item: any) => {
  const judgmentItem: any = {
    id: item.id,
    sequence: 0, // 序号稍后统一设置
    content: item.judgment || '',
    isSupported: item.opinion === 2,
    contentChanged: false,
    dsrxm: item.dsrxm || '', // 当事人姓名
    ssdw: item.ssdw || '',   // 诉讼地位
    documentCaseType: item.documentCaseType
  }

  // 记录原始值，用于后续比较
  if (item.id) {
    originalValues.value[item.id] = item.judgment || ''
  }

  secondInstanceOverruleItems.value.push(judgmentItem)
}

/**
 * 处理民事二审部分改判案件判项
 */
const handleCivilSecondInstancePartialOverruleItem = (item: any) => {
  // 根据opinion判断二审决定
  let decision = 'maintain' // 默认维持
  if (item.opinion === 1) {
    decision = 'revoke' // 1表示撤销
  } else if (item.opinion === 2) {
    decision = 'maintain' // 2表示维持
  } else if (item.opinion === 3) {
    decision = 'modify' // 3表示变更
  } else if (item.opinion === 4) {
    decision = 'new' // 4表示新增
  }
  
  const partialOverruleItem = {
    id: item.id,
    sequence: 0, // 序号稍后统一设置
    firstInstanceResult: item.judgment || '',
    secondInstanceDecision: decision,
    modifyContent: item.newJudgment || '', // 从newJudgment字段加载变更内容
    dataType: item.dataType || 1, // 从后端获取dataType，默认为1(提取)
    contentChanged: false,
    dsrxm: item.dsrxm || '', // 当事人姓名
    ssdw: item.ssdw || '',   // 诉讼地位
    documentCaseType: item.documentCaseType
  }
  
  // 记录原始值，用于后续比较
  if (item.id) {
    originalValues.value[item.id] = item.judgment || ''
    // 记录部分改判项的完整原始值
    originalPartialOverruleValues.value[item.id] = {
      firstInstanceResult: item.judgment || '',
      secondInstanceDecision: decision,
      modifyContent: item.newJudgment || ''
    }
  }
  
  partialOverruleItems.value.push(partialOverruleItem)
}

/**
 * 在诉讼费用表格中添加新行
 * 创建一个新的费用项并添加到费用列表中
 */
const addNewCostItem = () => {
  // 检查是否有未保存的项
  const hasUnsavedItems = litigationCostItems.value.some(item => item.contentChanged)
  if (hasUnsavedItems) {
    ElMessage.warning('请先保存未保存的诉讼费用项，再添加新项')
    return
  }

  const newItem = {
    sequence: litigationCostItems.value.length + 1,
    location: '', // 用户选择诉讼地位
    name: '', // 自动填充姓名
    costType: '案件受理费',
    paidAmount: 0, // 用户输入已缴金额
    totalAmount: 0, // 用户输入承担金额
    contentChanged: true, // 新添加的行默认有变化
    casePartyId: null // 新增时没有关联的当事人ID
  }
  litigationCostItems.value.push(newItem)
}

/**
 * 保存费用项的修改
 * @param row 要保存的费用项数据
 * @param index 费用项在列表中的索引
 */
const saveCostItem = async (row: any, index: number) => {
  if (!row.location || !row.name || !row.costType) {
    ElMessage.warning('请填写费用项完整信息')
    return
  }


  if (Number(row.totalAmount) <= 0) {
    ElMessage.warning('承担金额必须大于0')
    return
  }

  try {
    // 处理多选数据：如果是数组则转换为逗号分隔的字符串
    const dsrxmValue = Array.isArray(row.name) ? row.name.join(',') : row.name
    const ssdwValue = Array.isArray(row.location) ? 
      row.location.map((loc: string) => {
        // 如果location是uniqueKey数组，需要转换为displayLabel
        const partyOptions = getPartyOptions()
        const option = partyOptions.find(opt => opt.uniqueKey === loc)
        return option ? option.displayLabel : loc
      }).join(',') : row.location

    const legalFeesData = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      casePartyId:  0, // 新增时可能为null，后端会处理
      feeType: row.costType, // 用户输入的费用类型
      amountPaid: Number(row.paidAmount) || 0, // 用户输入的已缴金额
      amountToBear: Number(row.totalAmount) || 0, // 用户输入的承担金额
      dsrxm: dsrxmValue, // 当事人姓名（字符串格式）
      ssdw: ssdwValue // 诉讼地位（字符串格式）
    }

    if (row.id) {
      // 更新现有记录
      await legalFeesApi.update(legalFeesData)
    } else {
      // 创建新记录 - 使用用户输入的数据
      await legalFeesApi.save(legalFeesData)
    }

    row.contentChanged = false
    // ElMessage.success('保存成功')

    // 重新加载数据以获取最新的ID等信息
    await loadLegalFees()
  } catch (error) {
    console.error('保存诉讼费用失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 标记费用项内容发生变化
 * @param row 发生变化的费用项数据
 */
const markAsChanged = (row: any) => {
  row.contentChanged = true
}

/**
 * 处理诉讼地位选择变化，自动填充对应的当事人姓名
 * @param row 费用项数据
 */
const handleLocationChange = (row: any) => {
  markAsChanged(row)

  // 处理多选诉讼地位
  if (row.location && Array.isArray(row.location) && row.location.length > 0) {
    const partyOptions = getPartyOptions()
    const selectedNames: string[] = []
    const selectedDisplayLabels: string[] = []
    const selectedCasePartyIds: string[] = []

    // 遍历所有选中的诉讼地位
    row.location.forEach((locationKey: string) => {
      const selectedOption = partyOptions.find(option => option.uniqueKey === locationKey)
      if (selectedOption) {
        const party = caseParties.value[selectedOption.originalIndex]
        if (party) {
          selectedNames.push(party.partyNameString)
          selectedDisplayLabels.push(selectedOption.displayLabel)
          selectedCasePartyIds.push(`${party.partyType}_${party.partyNameString}`)
        }
      }
    })

    // 保存多个值：name和casePartyId用逗号分隔，location保持数组格式供el-select使用
    row.name = selectedNames.join(',')
    // row.location保持数组格式，不要转换为字符串，这样el-select才能正确显示选中状态
    // row.location = selectedDisplayLabels.join(',') // 这行会导致显示问题
    row.casePartyId = selectedCasePartyIds.join(',')
  } else if (row.location && !Array.isArray(row.location)) {
    // 兼容单选情况（向后兼容）
    const partyOptions = getPartyOptions()
    const selectedOption = partyOptions.find(option => option.uniqueKey === row.location)

    if (selectedOption) {
      const party = caseParties.value[selectedOption.originalIndex]
      if (party) {
        row.name = party.partyNameString
        // 单选时也保持数组格式，确保el-select正确显示
        row.location = [selectedOption.uniqueKey]
        row.casePartyId = `${party.partyType}_${party.partyNameString}`
      }
    } else {
      const party = caseParties.value.find(p => p.partyType === row.location)
      if (party) {
        row.name = party.partyNameString
        // 查找对应的uniqueKey并转换为数组格式
        const partyOptions = getPartyOptions()
        const matchedOption = partyOptions.find(opt => opt.displayLabel === row.location || opt.originalIndex === caseParties.value.indexOf(party))
        row.location = matchedOption ? [matchedOption.uniqueKey] : [row.location]
        row.casePartyId = `${party.partyType}_${party.partyNameString}`
      } else {
        row.name = ''
        row.casePartyId = null
      }
    }
  } else {
    // 如果没有选择诉讼地位，清空相关字段
    row.name = ''
    row.casePartyId = null
  }
}

/**
 * 删除数字字符串的前导0
 * @param value 输入值
 * @returns 处理后的值
 */
const removeLeadingZeros = (value: string | number): string => {

  let str = String(value)
  // 删除前导0，但至少保留一个数字
  return str.replace(/^0+(?=\d)/, '') || '0'
}

/**
 * 验证已缴金额
 * @param row 费用项数据
 */
const validatePaidAmount = (row: any) => {
  // 先删除前导0
  row.paidAmount = removeLeadingZeros(row.paidAmount)
  const amount = Number(row.paidAmount)
  if (isNaN(amount) || amount < 0) {
    return;
  }
}

/**
 * 验证承担金额
 * @param row 费用项数据
 */
const validateTotalAmount = (row: any) => {
  // 先删除前导0
  row.totalAmount = removeLeadingZeros(row.totalAmount)
  const amount = Number(row.totalAmount)
  if (isNaN(amount) || amount <= 0) {
    // 重置为空值，让用户重新输入
    row.totalAmount = ''
    return;
  }
}

/**
 * 处理主要判决项支持状态变化
 * @param row 判决项数据
 */
const handleJudgmentSupportChange = async (row: any) => {
  try {
    // 构造判项情况数据
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1 // 2表示支持，1表示不支持
    }

    // 调用API更新
    await judgmentSituationApi.update(judgmentData)

    // 更新本地状态
    onJudgmentResultChange()
    // 不设置 contentChanged，因为只是修改了支持状态，不是内容变化

  } catch (error) {
    console.error('更新判决支持状态失败:', error)
    ElMessage.error('更新失败，请重试')
    // 恢复原状态
    row.isSupported = !row.isSupported
  }
}

/**
 * 处理本诉原告判决项支持状态变化
 * @param row 判决项数据
 */
const handlePlaintiffJudgmentSupportChange = async (row: any) => {
  try {
    // 构造判项情况数据
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1 // 2表示支持，1表示不支持
    }

    // 调用API更新
    await judgmentSituationApi.update(judgmentData)

    // 更新本地状态
    onPlaintiffJudgmentResultChange()
    // 不设置 contentChanged，因为只是修改了支持状态，不是内容变化

  } catch (error) {
    console.error('更新本诉原告判决支持状态失败:', error)
    ElMessage.error('更新失败，请重试')
    // 恢复原状态
    row.isSupported = !row.isSupported
  }
}

/**
 * 处理 本诉被告（反诉原告）判决项支持状态变化
 * @param row 判决项数据
 */
const handleDefendantJudgmentSupportChange = async (row: any) => {
  try {
    // 构造判项情况数据
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1 // 2表示支持，1表示不支持
    }

    // 调用API更新
    await judgmentSituationApi.update(judgmentData)

    // 更新本地状态
    onDefendantJudgmentResultChange()
    // 不设置 contentChanged，因为只是修改了支持状态，不是内容变化

  } catch (error) {
    console.error('更新本诉被告（反诉原告）判决支持状态失败:', error)
    ElMessage.error('更新失败，请重试')
    // 恢复原状态
    row.isSupported = !row.isSupported
  }
}

/**
 * 删除指定的费用项
 * @param index 要删除的费用项在列表中的索引
 */
const deleteCostItem = async (index: number) => {
  const item = litigationCostItems.value[index]

  if (item.id) {
    // 如果有ID，调用删除API
    try {
      await legalFeesApi.delete(item.id)
    } catch (error) {
      console.error('删除诉讼费用失败:', error)
      return
    }
  }

  // 从本地列表中移除
  litigationCostItems.value.splice(index, 1)
  // 重新计算序号
  litigationCostItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

// ==================== 主要判决项相关方法 ====================

/**
 * 标记主要判决项内容发生变化
 * @param row 发生变化的判决项数据
 */
const markJudgmentAsChanged = (row: any) => {
  // 比较当前内容与原始值
  const originalValue = originalValues.value[row.id] || ''
  const currentValue = row.content || ''
  
  // 只有当内容真正改变时才标记为变化
  if (originalValue !== currentValue) {
    row.contentChanged = true
  } else {
    row.contentChanged = false
  }
}

/**
 * 保存主要判决项的修改
 * @param row 要保存的判决项数据
 * @param index 判决项在列表中的索引
 */
const saveJudgmentItem = async (row: any, index: number) => {
  if (!row.content || !row.content.trim()) {
    ElMessage.warning('请填写判决结果')
    return
  }

  try {
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1,
      documentCaseType: documentType.value
    }

    if (row.id) {
      // 更新现有记录
      await judgmentSituationApi.update(judgmentData)
    } else {
      // 创建新记录
      await judgmentSituationApi.save(judgmentData)
    }

    row.contentChanged = false  // 保存后清除内容变化标记
    // 更新原始值
    if (row.id) {
      originalValues.value[row.id] = row.content || ''
    }
    // 重新加载数据以获取最新的ID等信息
    await loadJudgmentItems()

  } catch (error) {
    console.error('保存主要判决项失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 删除指定的主要判决项
 * @param index 要删除的判决项在列表中的索引
 */
const deleteJudgmentItem = async (index: number) => {
  const item = judgmentItems.value[index]

  if (item.id) {
    // 如果有ID，调用删除API
    try {
      await judgmentSituationApi.delete(item.id)
    } catch (error) {
      console.error('删除主要判决项失败:', error)
      ElMessage.error('删除失败，请重试')
      return
    }
  }

  // 从本地列表中移除
  judgmentItems.value.splice(index, 1)
  // 重新计算序号
  judgmentItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

/**
 * 添加新的主要判决项
 * 创建一个新的判决项并添加到判决项列表中
 */
const addNewJudgmentItem = () => {
  const newItem = {
    sequence: judgmentItems.value.length + 1,
    content: '',
    isSupported: false,
    contentChanged: true, // 新增时标记内容变化
    dsrxm: '', // 当事人姓名
    ssdw: ''   // 诉讼地位
  }
  judgmentItems.value.push(newItem)
}

/**
 * 处理判项选择变化
 * @param selection 选中的判项数组
 */
const handleJudgmentSelectionChange = (selection: any[]) => {
  selectedJudgmentItems.value = selection
}

/**
 * 处理本诉原告判项选择变化
 * @param selection 选中的本诉原告判项数组
 */
const handlePlaintiffJudgmentSelectionChange = (selection: any[]) => {
  selectedPlaintiffJudgmentItems.value = selection
}

/**
 * 处理本诉被告（反诉原告）判项选择变化
 * @param selection 选中的本诉被告（反诉原告）判项数组
 */
const handleDefendantJudgmentSelectionChange = (selection: any[]) => {
  selectedDefendantJudgmentItems.value = selection
}

/**
 * 处理诉讼费用选择变化
 * @param selection 选中的诉讼费用数组
 */
const handleCostSelectionChange = (selection: any[]) => {
  selectedCostItems.value = selection
}

/**
 * 批量删除选中的判项
 */
const batchDeleteJudgmentItems = async () => {
  if (selectedJudgmentItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的判项')
    return
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedJudgmentItems.value.length} 个判项吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const deletePromises = selectedJudgmentItems.value.map(item => {
      if (item.id) {
        return judgmentSituationApi.delete(item.id)
      }
      return Promise.resolve()
    })

    await Promise.all(deletePromises)

    // 从本地列表中移除
    selectedJudgmentItems.value.forEach(selectedItem => {
      const index = judgmentItems.value.findIndex(item => item.id === selectedItem.id)
      if (index > -1) {
        judgmentItems.value.splice(index, 1)
      }
    })

    // 重新计算序号
    judgmentItems.value.forEach((item, idx) => {
      item.sequence = idx + 1
    })

    // 清空选中项
    selectedJudgmentItems.value = []

    // ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除判项失败:', error)
      ElMessage.error('批量删除失败，请重试')
    }
  }
}

/**
 * 批量删除选中的本诉原告判项
 */
const batchDeletePlaintiffJudgmentItems = async () => {
  if (selectedPlaintiffJudgmentItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的本诉原告判项')
    return
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPlaintiffJudgmentItems.value.length} 个本诉原告判项吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const deletePromises = selectedPlaintiffJudgmentItems.value.map(item => {
      if (item.id) {
        return judgmentSituationApi.delete(item.id)
      }
      return Promise.resolve()
    })

    await Promise.all(deletePromises)

    // 从本地列表中移除
    selectedPlaintiffJudgmentItems.value.forEach(selectedItem => {
      const index = plaintiffJudgmentItems.value.findIndex(item => item.id === selectedItem.id)
      if (index > -1) {
        plaintiffJudgmentItems.value.splice(index, 1)
      }
    })

    // 重新计算序号
    plaintiffJudgmentItems.value.forEach((item, idx) => {
      item.sequence = idx + 1
    })

    // 清空选中项
    selectedPlaintiffJudgmentItems.value = []

    // ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除本诉原告判项失败:', error)
      ElMessage.error('批量删除失败，请重试')
    }
  }
}

/**
 * 批量删除选中的本诉被告（反诉原告）判项
 */
const batchDeleteDefendantJudgmentItems = async () => {
  if (selectedDefendantJudgmentItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的本诉被告判项')
    return
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDefendantJudgmentItems.value.length} 个本诉被告判项吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const deletePromises = selectedDefendantJudgmentItems.value.map(item => {
      if (item.id) {
        return judgmentSituationApi.delete(item.id)
      }
      return Promise.resolve()
    })

    await Promise.all(deletePromises)

    // 从本地列表中移除
    selectedDefendantJudgmentItems.value.forEach(selectedItem => {
      const index = defendantJudgmentItems.value.findIndex(item => item.id === selectedItem.id)
      if (index > -1) {
        defendantJudgmentItems.value.splice(index, 1)
      }
    })

    // 重新计算序号
    defendantJudgmentItems.value.forEach((item, idx) => {
      item.sequence = idx + 1
    })

    // 清空选中项
    selectedDefendantJudgmentItems.value = []

    // ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败，请重试')
    }
  }
}

/**
 * 批量删除选中的诉讼费用项
 */
const batchDeleteCostItems = async () => {
  if (selectedCostItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的诉讼费用项')
    return
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCostItems.value.length} 个诉讼费用项吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const deletePromises = selectedCostItems.value.map(item => {
      if (item.id) {
        return legalFeesApi.delete(item.id)
      }
      return Promise.resolve()
    })

    await Promise.all(deletePromises)

    // 从本地列表中移除
    selectedCostItems.value.forEach(selectedItem => {
      const index = litigationCostItems.value.findIndex(item => item.id === selectedItem.id)
      if (index > -1) {
        litigationCostItems.value.splice(index, 1)
      }
    })

    // 重新计算序号
    litigationCostItems.value.forEach((item, idx) => {
      item.sequence = idx + 1
    })

    // 清空选中项
    selectedCostItems.value = []

    // ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除诉讼费用项失败:', error)
      ElMessage.error('批量删除失败，请重试')
    }
  }
}

// ==================== 本诉原告判决项相关方法 ====================

/**
 * 标记本诉原告判决项内容发生变化
 * @param row 发生变化的判决项数据
 */
const markPlaintiffJudgmentAsChanged = (row: any) => {
  // 比较当前内容与原始值
  const originalValue = originalValues.value[row.id] || ''
  const currentValue = row.content || ''
  
  // 只有当内容真正改变时才标记为变化
  if (originalValue !== currentValue) {
    row.contentChanged = true
  } else {
    row.contentChanged = false
  }
}

/**
 * 保存本诉原告判决项的修改
 * @param row 要保存的判决项数据
 * @param index 判决项在列表中的索引
 */
const savePlaintiffJudgmentItem = async (row: any, index: number) => {
  if (!row.content || !row.content.trim()) {
    ElMessage.warning('请填写判决结果')
    return
  }

  try {
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1,
      documentCaseType: documentType.value
    }

    if (row.id) {
      // 更新现有记录
      await judgmentSituationApi.update(judgmentData)
    } else {
      // 创建新记录
      await judgmentSituationApi.save(judgmentData)
    }

    row.contentChanged = false  // 保存后清除内容变化标记
    // 更新原始值
    if (row.id) {
      originalValues.value[row.id] = row.content || ''
    }
    // 重新加载数据以获取最新的ID等信息
    await loadJudgmentItems()

  } catch (error) {
    console.error('保存本诉原告判决项失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 删除指定的本诉原告判决项
 * @param index 要删除的判决项在列表中的索引
 */
const deletePlaintiffJudgmentItem = async (index: number) => {
  const item = plaintiffJudgmentItems.value[index]

  if (item.id) {
    // 如果有ID，调用删除API
    try {
      await judgmentSituationApi.delete(item.id)
      // ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除本诉原告判决项失败:', error)
      ElMessage.error('删除失败，请重试')
      return
    }
  }

  // 从本地列表中移除
  plaintiffJudgmentItems.value.splice(index, 1)
  // 重新计算序号
  plaintiffJudgmentItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

/**
 * 添加新的本诉原告判决项
 * 创建一个新的判决项并添加到本诉原告判决项列表中
 */
const addNewPlaintiffJudgmentItem = () => {
  // 从判项数据中查找本诉原告当事人信息
  const plaintiffData = judgmentData.value.find(item => item.ssdw === '本诉原告')
  const newItem = {
    sequence: plaintiffJudgmentItems.value.length + 1,
    content: '',
    isSupported: false,
    contentChanged: true, // 新增时标记内容变化
    dsrxm: plaintiffData?.dsrxm || '', // 使用判项数据中的当事人姓名
    ssdw: '本诉原告' // 自动设置诉讼地位为本诉原告
  }
  plaintiffJudgmentItems.value.push(newItem)
}

// ==================== 反诉原告判决项相关方法 ====================

/**
 * 标记反诉原告判决项内容发生变化
 * @param row 发生变化的判决项数据
 */
const markDefendantJudgmentAsChanged = (row: any) => {
  // 比较当前内容与原始值
  const originalValue = originalValues.value[row.id] || ''
  const currentValue = row.content || ''
  
  // 只有当内容真正改变时才标记为变化
  if (originalValue !== currentValue) {
    row.contentChanged = true
  } else {
    row.contentChanged = false
  }
}

/**
 * 保存本诉被告（反诉原告）判决项的修改
 * @param row 要保存的判决项数据
 * @param index 判决项在列表中的索引
 */
const saveDefendantJudgmentItem = async (row: any, index: number) => {
  if (!row.content || !row.content.trim()) {
    ElMessage.warning('请填写判决结果')
    return
  }

  try {
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: row.isSupported ? 2 : 1,
      documentCaseType: documentType.value
    }

    if (row.id) {
      // 更新现有记录
      await judgmentSituationApi.update(judgmentData)
    } else {
      // 创建新记录
      await judgmentSituationApi.save(judgmentData)
    }

    row.contentChanged = false  // 保存后清除内容变化标记
    // 更新原始值
    if (row.id) {
      originalValues.value[row.id] = row.content || ''
    }
    // 重新加载数据以获取最新的ID等信息
    await loadJudgmentItems()

  } catch (error) {
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 删除指定的反诉原告判决项
 * @param index 要删除的判决项在列表中的索引
 */
const deleteDefendantJudgmentItem = async (index: number) => {
  const item = defendantJudgmentItems.value[index]

  if (item.id) {
    // 如果有ID，调用删除API
    try {
      await judgmentSituationApi.delete(item.id)
    } catch (error) {
      ElMessage.error('删除失败，请重试')
      return
    }
  }

  // 从本地列表中移除
  defendantJudgmentItems.value.splice(index, 1)
  // 重新计算序号
  defendantJudgmentItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

/**
 * 添加新的 本诉被告 判决项
 * 创建一个新的判决项并添加到 本诉被告 判决项列表中
 */
const addNewDefendantJudgmentItem = () => {
  // 从判项数据中查找本诉被告当事人信息
  const defendantData = judgmentData.value.find(item => item.ssdw === '本诉被告')
  const newItem = {
    sequence: defendantJudgmentItems.value.length + 1,
    content: '',
    isSupported: false,
    contentChanged: true, // 新增时标记内容变化
    dsrxm: defendantData?.dsrxm || '', // 使用判项数据中的当事人姓名
    ssdw: '本诉被告' // 自动设置诉讼地位为本诉被告
  }
  defendantJudgmentItems.value.push(newItem)
}

// ==================== 部分改判相关方法 ====================

/**
 * 标记部分改判项内容发生变化
 * @param row 发生变化的部分改判项数据
 */
const markPartialOverruleAsChanged = (row: any) => {
  // 获取原始值
  const originalData = originalPartialOverruleValues.value[row.id]
  
  if (originalData) {
    // 比较一审判决结果
    const firstInstanceChanged = originalData.firstInstanceResult !== (row.firstInstanceResult || '')
    // 比较二审决定
    const secondInstanceChanged = originalData.secondInstanceDecision !== row.secondInstanceDecision
    // 比较变更内容（只有当二审决定为变更时才比较）
    const modifyContentChanged = row.secondInstanceDecision === 'modify' && 
      originalData.modifyContent !== (row.modifyContent || '')
    
    // 只有当任一字段真正改变时才标记为变化
    row.contentChanged = firstInstanceChanged || secondInstanceChanged || modifyContentChanged
  } else {
    // 如果没有原始值记录，说明是新增项，标记为已改变
    row.contentChanged = true
  }
  
  // 处理二审决定变化时的变更内容临时存储
  const currentDecision = row.secondInstanceDecision
  const itemKey = row.id || `temp_${row.sequence}` // 使用ID或临时序列号作为key
  
  if (currentDecision === 'modify') {
    // 切换到变更时，恢复之前保存的变更内容
    if (tempModifyContent.value[itemKey]) {
      row.modifyContent = tempModifyContent.value[itemKey]
    }
  } else if (currentDecision === 'maintain' || currentDecision === 'revoke') {
    // 切换到维持或撤销时，保存当前的变更内容到临时存储
    if (row.modifyContent && row.modifyContent.trim()) {
      tempModifyContent.value[itemKey] = row.modifyContent
    }
    // 清空当前显示的变更内容
    row.modifyContent = ''
  }
}

/**
 * 保存 部分改判项 的修改
 * @param row 要保存的部分改判项数据
 * @param index 部分改判项在列表中的索引
 */
const savePartialOverruleItem = async (row: any, index: number) => {
  if (!row.firstInstanceResult || !row.secondInstanceDecision) {
    ElMessage.warning('请填写 部分改判-判项情况 完整信息!')
    return
  }
  
  // 如果选择变更，需要验证变更内容
  if (row.secondInstanceDecision === 'modify' && !row.modifyContent?.trim()) {
    ElMessage.warning('请填写变更内容')
    return
  }
  
  try {
    // 将部分改判格式转换为标准判项格式
    let opinion = 2 // 默认维持
    if (row.secondInstanceDecision === 'revoke') {
      opinion = 1 // 撤销
    } else if (row.secondInstanceDecision === 'maintain') {
      opinion = 2 // 维持
    } else if (row.secondInstanceDecision === 'modify') {
      opinion = 3 // 变更
    } else if (row.secondInstanceDecision === 'new') {
      opinion = 4 // 新增
    }
    
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.firstInstanceResult, // 一审判决结果作为主要内容
      opinion: opinion, // 根据二审决定设置opinion值
      dataType: row.dataType || 2, // 保存dataType字段，新增数据默认为2
      newJudgment: row.secondInstanceDecision === 'modify' ? row.modifyContent : '', // 只有变更时才存储新判决内容，维持和撤销时为空
      documentCaseType: documentType.value
    }
    
    if (row.id) {
      // 更新现有记录
      await judgmentSituationApi.update(judgmentData)
    } else {
      // 创建新记录
      await judgmentSituationApi.save(judgmentData)
    }
    
    row.contentChanged = false
    // 更新原始值
    if (row.id) {
      originalValues.value[row.id] = row.firstInstanceResult || ''
      // 更新部分改判项的完整原始值
      originalPartialOverruleValues.value[row.id] = {
        firstInstanceResult: row.firstInstanceResult || '',
        secondInstanceDecision: row.secondInstanceDecision,
        modifyContent: row.modifyContent || ''
      }
    }
    
    // 保存成功后，清空该项目的临时存储
    const itemKey = row.id || `temp_${row.sequence}`
    if (tempModifyContent.value[itemKey]) {
      delete tempModifyContent.value[itemKey]
    }
    
    // 重新加载数据以获取最新的ID等信息
    await loadJudgmentItems()
    
  } catch (error) {
    console.error('保存部分改判项失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 删除指定的部分改判项
 * @param index 要删除的部分改判项在列表中的索引
 */
const deletePartialOverruleItem = async (index: number) => {
  const item = partialOverruleItems.value[index]
  
  if ((item as any).id && (item as any).id !== null) {
    // 如果有ID，调用删除API
    try {
      await judgmentSituationApi.delete((item as any).id)
      // ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除部分改判项失败:', error)
      ElMessage.error('删除失败，请重试')
      return
    }
  }
  
  // 从本地列表中移除
  partialOverruleItems.value.splice(index, 1)
  // 重新计算序号
  partialOverruleItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

/**
 * 添加新的部分改判项
 * 创建一个新的部分改判项并添加到列表中
 */
const addNewPartialOverruleItem = () => {
  const newItem = {
    id: null, // 新增时没有ID
    sequence: partialOverruleItems.value.length + 1,
    firstInstanceResult: '',
    secondInstanceDecision: 'new',
    modifyContent: '', // 变更内容
    contentChanged: true,
    dsrxm: '', // 当事人姓名
    ssdw: '',   // 诉讼地位
    dataType: 2 // 新增项dataType为2
  }
  partialOverruleItems.value.push(newItem)
}

// ==================== 二审改判相关方法 ====================

/**
 * 标记二审改判项内容发生变化
 * @param row 发生变化的二审改判项数据
 */
const markSecondInstanceOverruleAsChanged = (row: any) => {
  // 比较当前内容与原始值
  const originalValue = originalValues.value[row.id] || ''
  const currentValue = row.content || ''
  
  // 只有当内容真正改变时才标记为变化
  if (originalValue !== currentValue) {
    row.contentChanged = true
  } else {
    row.contentChanged = false
  }
}

/**
 * 保存二审改判项的修改
 * @param row 要保存的二审改判项数据
 * @param index 二审改判项在列表中的索引
 */
const saveSecondInstanceOverruleItem = async (row: any, index: number) => {
  if (!row.content || !row.content.trim()) {
    ElMessage.warning('请填写二审判决结果')
    return
  }
  
  try {
    const judgmentData: JudgmentSituation = {
      id: row.id,
      caseImportRecordId: caseImportId.value,
      dsrxm: row.dsrxm || '', // 当事人姓名
      ssdw: row.ssdw || '',   // 诉讼地位
      judgment: row.content,
      opinion: 2, // 二审改判默认支持
      documentCaseType: documentType.value
    }
    
    if (row.id) {
      // 更新现有记录
      await judgmentSituationApi.update(judgmentData)
    } else {
      // 创建新记录
      await judgmentSituationApi.save(judgmentData)
    }
    
    row.contentChanged = false
    // 更新原始值
    if (row.id) {
      originalValues.value[row.id] = row.content || ''
    }
    // 重新加载数据以获取最新的ID等信息
    await loadJudgmentItems()
    
  } catch (error) {
    console.error('保存二审改判项失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

/**
 * 删除指定的二审改判项
 * @param index 要删除的二审改判项在列表中的索引
 */
const deleteSecondInstanceOverruleItem = async (index: number) => {
  const item = secondInstanceOverruleItems.value[index]
  
  if ((item as any).id && (item as any).id !== null) {
    // 如果有ID，调用删除API
    try {
      await judgmentSituationApi.delete((item as any).id)
      // ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除二审改判项失败:', error)
      ElMessage.error('删除失败，请重试')
      return
    }
  }
  
  // 从本地列表中移除
  secondInstanceOverruleItems.value.splice(index, 1)
  // 重新计算序号
  secondInstanceOverruleItems.value.forEach((item, idx) => {
    item.sequence = idx + 1
  })
}

/**
 * 添加新的二审改判项
 * 创建一个新的二审改判项并添加到列表中
 */
const addNewSecondInstanceOverruleItem = () => {
  const newItem = {
    id: null, // 新增时没有ID
    sequence: secondInstanceOverruleItems.value.length + 1,
    content: '',
    contentChanged: true,
    dsrxm: '', // 当事人姓名
    ssdw: ''   // 诉讼地位
  }
  secondInstanceOverruleItems.value.push(newItem)
}


// ==================== 监听器 ====================

/**
 * 监听 props.hasCounterclaim 变化，更新本地状态
 */
watch(
    () => props.hasCounterclaim,
    (newValue) => {
      hasCounterclaim.value = newValue
    }
)

/**
 * 监听主要判决项数据变化，自动更新判决类型
 * 当判决项的支持状态发生变化时，自动检测并更新对应的判决类型按钮状态
 */
watch(
    () => judgmentItems.value.map(item => item.isSupported),
    () => {
      autoDetectJudgmentType()
    },
    {deep: true}
)

/**
 * 监听本诉原告判决项数据变化，自动更新判决类型
 * 当本诉原告判决项的支持状态发生变化时，自动检测并更新对应的判决类型按钮状态
 */
watch(
    () => plaintiffJudgmentItems.value.map(item => item.isSupported),
    () => {
      autoDetectPlaintiffJudgmentType()
    },
    {deep: true}
)

/**
 * 监听反诉原告判决项数据变化，自动更新判决类型
 * 当反诉原告判决项的支持状态发生变化时，自动检测并更新对应的判决类型按钮状态
 */
watch(
    () => defendantJudgmentItems.value.map(item => item.isSupported),
    () => {
      autoDetectDefendantJudgmentType()
    },
    {deep: true}
)


// ==================== 文书生成相关方法 ====================

/**
 * 生成文书
 * 根据当前编辑的内容和选择的参数，调用API生成对应的法律文书
 */
const generateDocument = async () => {
  if (!caseImportId.value) {
    ElMessage.warning('案件ID不能为空')
    return
  }

  // 从案件记录中获取案件类型代码
  const caseInfo = await getCaseBasicInfo()
  const ajlxdm = caseInfo.ajlxdm

  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成文书...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  generating.value = true

  try {
    // 调用基于模板生成文书的接口
    const documentInfo = await documentGenerationApi.generateFromTemplate(caseImportId.value, ajlxdm, documentType.value)
    ElMessage.success('文书生成成功')

    // 调用initOnlyOfficeEditor方法显示编辑器
    if (documentInfo.documentUrl && documentInfo.id) {
      initOnlyOfficeEditor(documentInfo.documentUrl, documentInfo.id)
    }
  } catch (error) {
    const errorMsg = error?.response?.data?.message || error?.message || '文书生成失败，请稍后重试'
    ElMessage.error(errorMsg)
  } finally {
    generating.value = false
    loadingInstance.close()
  }
}

/**
 * 返回上级页面
 * 关闭当前编辑器，返回到文书生成列表页面
 */
const goBack = () => {
  emit('close')
}

/**
 * 格式化时间为无空格字符串
 */
function formatDateTime(date: string | number | Date): string {
  const d = new Date(date)
  const pad = (n: number) => n < 10 ? '0' + n : n
  return (
    d.getFullYear().toString() +
    pad(d.getMonth() + 1) +
    pad(d.getDate()) +
    pad(d.getHours()) +
    pad(d.getMinutes()) +
    pad(d.getSeconds())
  )
}

// 注意：currentDocumentId 和 generatedContent 变量已在上方声明

/**
 * 动态加载OnlyOffice API
 */
function loadOnlyOfficeAPI(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    // @ts-ignore
    if (window.DocsAPI) {
      console.log('OnlyOffice API已加载')
      resolve()
      return
    }

    // 获取OnlyOffice URL
    const scriptSrc = `${import.meta.env.VITE_ONLYOFFICE_URL}/web-apps/apps/api/documents/api.js`

    console.log('开始加载OnlyOffice API:', scriptSrc)

    // 创建script标签
    const script = document.createElement('script')
    script.src = scriptSrc
    script.async = true

    script.onload = () => {
      console.log('OnlyOffice API加载成功')
      resolve()
    }
    script.onerror = (error) => {
      console.error('OnlyOffice API加载失败:', error)
      reject(new Error('无法加载OnlyOffice API'))
    }

    // 添加到head
    document.head.appendChild(script)



  })
}

/**
 * 初始化OnlyOffice编辑器
 */
async function initOnlyOfficeEditor(fileUrl: string, documentId: number) {
  console.log('初始化OnlyOffice编辑器，文书ID:', documentId)

  try {
    // 先加载OnlyOffice API
    await loadOnlyOfficeAPI()

    // 保存文书ID
    currentDocumentId = documentId

    // 显示OnlyOffice编辑器
    showOnlyOffice.value = true

    // 添加消息监听器来接收插件发送的文档就绪消息
    const messageListener = (event: MessageEvent) => {
      if (event.data && event.data.type === 'onlyoffice-document-ready' && event.data.source === 'document-editor-plugin') {
        console.log('收到插件发送的文档就绪消息，调用handleDocumentReady');
        handleDocumentReady();
        // 移除监听器，避免重复调用
        window.removeEventListener('message', messageListener);
      }
    };
    window.addEventListener('message', messageListener);

    // 等待DOM更新后初始化编辑器
    nextTick(() => {
    // documentKey使用时间戳生成
    const docKey = String(caseImportId.value) + '_' + formatDateTime(Date.now())

    var onAppReady = function() {
      console.log('OnlyOffice应用已就绪');
    };

    var onError = function(event) {
      console.error('OnlyOffice编辑器错误:', event);
    };

  const config = {
    documentType: 'word',
    document: {
      fileType: 'docx',
      key: docKey,
      title: (props.caseInfo?.caseName) + '-' + documentTypeText.value,
      url: fileUrl,
      permissions: {
        edit: true,
        download: true,
        print: true,
        review: true,
        comment: true
      }
    },
    editorConfig: {
      mode: 'edit',
      lang: 'zh-CN',
      region: "zh-CN",
      callbackUrl: import.meta.env.VITE_API_BASE_URL + '/api/onlyoffice/callback?id=' + documentId,
      user: {
        id: 'user1',
        name: '用户'
      },
      coEditing: {
        mode: "strict",
        change: false,
      },
      customization: {
        autosave: true,
        // crtls +s 立即调用回调接口，否则关闭编辑器才会保存
        forcesave: "true",
        // 社区版支持的基本设置
        compactHeader: false,
        compactToolbar: false,
        zoom: 80,
        unit: 'cm', // 设置默认单位，但无法完全隐藏（需要商业版）
        // 尝试隐藏文件菜单中的高级设置（可能包含单位设置）
        layout: {
          toolbar: {
            file: {
              settings: false // 尝试隐藏高级设置选项
            }
          }
        },
        features: {
          spellcheck: {
            mode: false
          }
        }
        // 注意：logo.visible、customer、feedback等配置在社区版中无效
      },
      events: {
        onAppReady: onAppReady,
        onError: onError
      }
    },
    width: '100%',
    height: '100%'
  }

  // 销毁旧实例
  // @ts-ignore
  if (window.DocsAPI?.DocEditor?.destroyEditor) {
    // @ts-ignore
    window.DocsAPI.DocEditor.destroyEditor('onlyoffice-editor')
  }

  // 动态等待OnlyOffice脚本加载
  function tryInitEditor() {
    console.log('尝试初始化OnlyOffice编辑器...')
    // @ts-ignore
    if (window.DocsAPI?.DocEditor) {
      console.log('OnlyOffice API已加载，开始创建编辑器实例')
      try {
        // @ts-ignore
        window.docEditor = new window.DocsAPI.DocEditor('onlyoffice-editor', config)

      } catch (error) {
        console.error('创建OnlyOffice编辑器实例失败:', error)
      }
    } else {
      console.log('OnlyOffice API未加载，继续等待...')
      setTimeout(tryInitEditor, 200)
    }
  }

    tryInitEditor()
  })
  } catch (error) {
    console.error('初始化OnlyOffice编辑器失败:', error)
    ElMessage.error('OnlyOffice编辑器初始化失败: ' + (error as Error).message)
  }
}

/**
 * 关闭OnlyOffice编辑器
 */
async function closeOnlyOffice() {
  // 检查是否正在生成文书内容
  if (isGenerating.value && currentStepIndex.value < totalSteps.value) {
    try {
      await ElMessageBox.confirm(
        `文书内容正在生成中，返回编辑将中断生成过程。确定要返回编辑吗？`,
        '确认返回',
        {
          confirmButtonText: '确定返回',
          cancelButtonText: '继续生成',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )

      // 用户确认返回，停止生成过程
      isGenerating.value = false
      currentGenerationStatus.value = ''
      currentStepIndex.value = 0

    } catch (error) {
      // 用户取消，继续生成过程
      return
    }
  }

  // 关闭所有活跃的SSE连接
  closeAllActiveSSEConnections()

  showOnlyOffice.value = false

  // 销毁编辑器实例
  // @ts-ignore
  if (window.DocsAPI?.DocEditor?.destroyEditor) {
    // @ts-ignore
    window.DocsAPI.DocEditor.destroyEditor('onlyoffice-editor')
  }
}

/**
 * 关闭所有活跃的SSE连接
 * 提取的公共函数，在关闭编辑器和组件销毁时都会调用
 * @param isPageUnload 是否为页面卸载时调用
 */
function closeAllActiveSSEConnections() {
  console.log(`=== 开始关闭SSE连接 ===`)
  console.log(`当前活跃的SSE连接数: ${activeSSEConnections.value.length}`)

  // 停止轮询
  shouldStopPolling.value = true
  console.log('已设置停止轮询标志')

  if (activeSSEConnections.value.length === 0) {
    console.log(`没有活跃的SSE连接需要关闭`)
  } else {
    activeSSEConnections.value.forEach((eventSource, index) => {
      try {
        console.log(`检查SSE连接 ${index + 1}，状态: ${eventSource.readyState} (0=CONNECTING, 1=OPEN, 2=CLOSED)`)
        if (eventSource.readyState !== EventSource.CLOSED) {
          console.log(`正在关闭SSE连接 ${index + 1}`)
          eventSource.close()
          console.log(`SSE连接 ${index + 1} 已关闭`)
        } else {
          console.log(`SSE连接 ${index + 1} 已经是关闭状态`)
        }
      } catch (error) {
        console.warn(`关闭SSE连接 ${index + 1} 失败:`, error)
      }
    })
  }

  // 清空连接数组
  activeSSEConnections.value = []
  console.log(`SSE连接数组已清空，当前连接数: ${activeSSEConnections.value.length}`)

  if(!hasRelease.value && currentDocumentId){
    // 通知后端文书生成已完成，释放队列权限
    documentGenerationApi.completeDocumentGeneration(currentDocumentId)
    hasRelease.value = true
  }
  console.log(`=== SSE连接关闭完成 ===`)
}

/**
 * 获取案件基本信息
 */
/**
 * 从案件名称或案号中提取标准案号
 * 例如：测试包（2025）粤0605民初x9999号-test -> （2025）粤0605民初x9999号
 */
function extractCaseNumber(text: string): string {
  if (!text) return '案号'
  
  // 正则表达式匹配标准案号格式：（年份）地区代码+案件类型+编号
  const caseNumberRegex = /（\d{4}）[\u4e00-\u9fa5\d]+[民刑行执商知]+[初终再审]+[\dxX]+号/
  const match = text.match(caseNumberRegex)
  
  if (match) {
    return match[0]
  }
  
  // 如果没有匹配到标准格式，返回原文本
  return text
}

async function getCaseBasicInfo() {
  try {
    const caseInfo = await caseApi.getDetail(caseImportId.value)
    
    // 优先使用caseCode字段，如果没有则从caseName中提取
    let extractedCaseCode = '案号'
    if (caseInfo.caseCode) {
      extractedCaseCode = extractCaseNumber(caseInfo.caseCode)
    } else if (caseInfo.caseName) {
      extractedCaseCode = extractCaseNumber(caseInfo.caseName)
    }
    
    return {
      caseName: caseInfo.caseName || '案件名称',
      caseCode: extractedCaseCode,
      courtName: caseInfo.corpName, // 默认法院名称，可根据实际需求调整
      ajlxdm: caseInfo.ajlxdm || '0301', // 案件类型代码，默认一审
    }
  } catch (error) {
    console.warn('获取案件基本信息失败:', error)
    return {
      caseName: '案件名称',
      caseCode: '案号',
      courtName: '人民法院',
      ajlxdm: '0301', // 默认一审
    }
  }
}

/**
 * 插入文书头部信息
 */
async function insertDocumentHeader() {
  try {
    console.log('开始插入文书头部信息...')

    // 获取案件基本信息
    const caseInfo = await getCaseBasicInfo()

    // 确定文书名称
    let documentTitle = '民事判决书'
    if (documentType.value === 'civil_judgement_2_overrule') {
      documentTitle = '民事判决书'
    } else if (documentType.value === 'civil_judgement_2_partial_overrule') {
      documentTitle = '民事判决书'
    }
    await saveBookmarkToDatabase('标题')

    // 1. 先空两段
    await insertFormattedText('', 'spacing')
    await insertFormattedText('', 'spacing')

    // 2. 插入法院名称和文书名称（居中，宋体，二号，加粗）
    const courtName = caseInfo.courtName || '【法院名称】'
    await insertFormattedText(courtName , 'documentHeader',{},'[BOOKMARK:标题]')
    await insertFormattedText(documentTitle, 'documentHeader')

    // 3. 插入案号（三号仿宋，靠右，尾部缩进两格）
    await insertFormattedText(caseInfo.caseCode, 'caseNumber', { tailIndent: true })

    console.log('文书头部信息插入完成')
    // 重置停止轮询标志
    shouldStopPolling.value = false
    hasRelease.value = false
    // 4. 尝试获取队列权限
    await tryAcquireQueuePermission()
    
  } catch (error) {
    console.error('插入文书头部信息失败:', error)
    // 即使插入失败，也继续后续流程
  }
}

/**
 * 尝试获取队列权限
 */
async function tryAcquireQueuePermission() {
  try {
    // 1. 首次尝试获取权限
    const firstResult = await documentGenerationApi.acquire(currentDocumentId)
    
    console.log('首次队列权限获取结果:', firstResult)
    
    // 2. 如果可以直接执行，返回成功
    if (firstResult === true) {
      console.log('队列权限获取成功')
      return
    }
    
    // 3. 如果需要等待，开始轮询
    console.log('需要等待队列，开始轮询...')
    await pollQueuePermissionInternal()
    console.log('轮询完成，队列权限获取成功')
  } catch (error) {
    console.error('获取队列权限异常:', error)
    if (error.message === '轮询已被停止') {
      console.log('轮询已被主动停止')
      return
    }
    ElMessage.error('获取队列权限失败，请稍后重试')
    throw error
  }
}

/**
 * 内部轮询队列权限方法
 */
function pollQueuePermissionInternal(): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const pollInterval = 10000; // 轮询间隔10秒
    
    const poll = async () => {
      // 检查停止信号
      if (shouldStopPolling.value) {
        console.log('收到停止轮询信号，终止轮询');
        return;
      }
      
      try {
        const result = await documentGenerationApi.acquire(currentDocumentId)
        
        if (result === true) {
          resolve(true);
          return;
        }
        
        // 决策理由：移除最大时间限制，实现无限轮询直到获取权限
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error('轮询队列权限失败:', error);
        // 决策理由：即使出错也继续轮询，避免因网络波动中断
        setTimeout(poll, pollInterval);
      }
    };
    
    poll();
  });
}


/**
 * 插入文书结尾信息
 */
async function insertDocumentFooter() {
  try {
    console.log('等待任务队列执行完毕...')
    await waitForQueueCompletion()
    currentGenerationStatus.value = ''
    await documentGenerationApi.completeDocumentGeneration(currentDocumentId)
    hasRelease.value = true
    console.log('开始插入文书结尾信息前，先进行全文内容优化...')
    // 在插入文书结尾信息前，进行全文内容优化（包括外币检查、人民币字样处理等）
    await optimizeDocumentContent()
    
    console.log('开始插入文书结尾信息...')
    await saveBookmarkToDatabase('落款')
    // 从后端智能获取审判组织成员数据（优先从数据库获取，无数据时从庭审笔录提取）
    let hasCustomMembers = false
    try {
      const membersResponse = await trialOrganizationMembersApi.getOrExtract(caseImportId.value!)
      if (membersResponse && membersResponse.length > 0) {
        console.log('获取到审判组织成员数据:', membersResponse)
        await insertCustomTrialMembers(membersResponse)
        hasCustomMembers = true
      }
    } catch (error) {
      console.error('智能获取审判组织成员失败:', error)
    }

    // 如果没有自定义数据，使用默认数据
    if (!hasCustomMembers) {
      console.log('使用默认审判组织成员')
      await insertDefaultFooter()
    }

    // 插入完成后，检查并调整文档排版
    await checkAndAdjustDocumentLayout()

    console.log('文书结尾信息插入完成')
  } catch (error) {
    console.error('插入文书结尾信息失败:', error)
  }
}

/**
 * 将数字转换为中文数字
 */
function numberToChinese(num: number): string {
  const chineseNumbers = ['〇', '一', '二', '三', '四', '五', '六', '七', '八', '九']

  if (num === 0) return '〇'
  if (num < 10) return chineseNumbers[num]
  if (num === 10) return '十'
  if (num < 20) return '十' + chineseNumbers[num - 10]
  if (num < 100) {
    const tens = Math.floor(num / 10)
    const ones = num % 10
    return chineseNumbers[tens] + '十' + (ones === 0 ? '' : chineseNumbers[ones])
  }

  // 处理年份（如2025）
  return num.toString().split('').map(digit => chineseNumbers[parseInt(digit)]).join('')
}

/**
 * 格式化中文日期
 */
function formatChineseDate(date: Date): string {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  const chineseYear = numberToChinese(year)
  const chineseMonth = numberToChinese(month)
  const chineseDay = numberToChinese(day)

  return `${chineseYear}年${chineseMonth}月${chineseDay}日`
}

/**
 * 添加书签到前端书签列表
 */
function addBookmarkToList(bookmarkName: string) {
  let displayName = bookmarkName;
  if (!bookmarkList.value.find(b => b.name === bookmarkName)) {
    bookmarkList.value.push({ name: bookmarkName, displayName })
  }
}

/**
 * 保存书签到数据库（同步）
 */
async function saveBookmarkToDatabase(bookmarkName: string): Promise<void> {

  addBookmarkToList(bookmarkName)
  if (!currentDocumentId) {
    console.warn('无法保存书签：currentDocumentId为空')
    return
  }

  try {
    const bookmarkContent = `<bookmark>${bookmarkName}</bookmark>`
    await documentGenerationApi.saveBookmark(currentDocumentId, bookmarkContent)
    console.log(`${bookmarkName}书签保存到数据库成功`)
  } catch (error) {
    console.warn(`${bookmarkName}书签保存到数据库失败:`, error)
    throw error // 重新抛出错误，让调用者决定如何处理
  }
}

/**
 * 插入自定义审判组织成员信息
 */
async function insertCustomTrialMembers(members: TrialOrganizationMembers[]) {
  console.log('插入自定义审判组织成员:', members)


  // 分类成员
  const judgeMembers = members.filter(member =>
    ['审判长', '审判员', '人民陪审员'].includes(member.role)
  )

  const assistantMembers = members.filter(member =>
    ['书记员', '法官助理'].includes(member.role)
  )
  if(judgeMembers.length > 1){
    // 插入空行
    await insertCharacterToEditor('\n')
    await insertCharacterToEditor('\n')
    await insertCharacterToEditor('\n')
    await insertCharacterToEditor('\n')
  }else{
    await insertCharacterToEditor('\n')
    await insertCharacterToEditor('\n')
  }
  await new Promise(resolve => setTimeout(resolve, 100))

  // 计算所有成员中最长的角色名称长度和姓名长度
  const allRoles = members.map(member => member.role)
  const maxRoleLength = Math.max(...allRoles.map(role => role.length))
  const allNames = members.map(member => member.name || '')
  const maxNameLength = Math.max(...allNames.map(name => name.length), 2) // 至少为2，确保有基本的对齐空间

  // 插入审判人员
  for (let i = 0; i < judgeMembers.length; i++) {
    const member = judgeMembers[i]
    const formattedText = formatMemberWithAdvancedAlignment(member.role, member.name, maxRoleLength, maxNameLength)

    // 在第一个审判人员前插入书签
    const bookmark = i === 0 ? '[BOOKMARK:落款]' : undefined
    await insertFormattedText(formattedText, 'rightAlign', { tailIndent: true }, bookmark)

    // 每条记录之间等待0.2秒
    await new Promise(resolve => setTimeout(resolve, 300))
  }
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')

  // 插入判决日期
  const currentDate = new Date()
  await insertFormattedText(formatChineseDate(currentDate), 'rightAlign', { tailIndent: true })
  await new Promise(resolve => setTimeout(resolve, 300))
  await insertFormattedText("本件与原本核对无异", 'leftAlign')

  await new Promise(resolve => setTimeout(resolve, 300))

  await insertFormattedText('', 'rightAlign')

  // 插入审判辅助人员
  for (const member of assistantMembers) {
    const formattedText = formatMemberWithAdvancedAlignment(member.role, member.name, maxRoleLength, maxNameLength)
    await insertFormattedText(formattedText, 'rightAlign', { tailIndent: true })
    // 每条记录之间等待0.2秒
    await new Promise(resolve => setTimeout(resolve, 300))
  }
}

/**
 * 格式化单个审判组织成员（高级对齐，考虑姓名长度）
 */
function formatMemberWithAdvancedAlignment(role: string, name: string, maxRoleLength: number, maxNameLength: number): string {
  // 根据角色字符数量确定字符间隔
  let spacedRole = ''
  let roleSpacingWidth = 0 // 记录角色间隔占用的宽度

  if (role.length === 3) {
    // 3个字的角色：字符间用2个全角空格
    spacedRole = role.split('').join('　　')
    roleSpacingWidth = role.length * 3 - 2 // 每个字符+2个空格，最后一个字符不加
  } else if (role.length === 4) {
    // 4个字的角色：字符间用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1 // 每个字符+1个空格，最后一个字符不加
  } else if (role.length === 5) {
    // 5个字的角色：字符间用普通空格
    spacedRole = role.split('').join(' ')
    roleSpacingWidth = role.length + (role.length - 1) * 0.5 // 每个字符+0.5个空格宽度
  } else {
    // 其他长度：默认用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1
  }

  // 计算最长角色的宽度（假设最长角色是4个字符）
  const maxRoleSpacingWidth = maxRoleLength === 3 ? (maxRoleLength * 3 - 2) :
                             maxRoleLength === 4 ? (maxRoleLength * 2 - 1) :
                             maxRoleLength === 5 ? (maxRoleLength + (maxRoleLength - 1) * 0.5) :
                             (maxRoleLength * 2 - 1)

  // 计算需要在角色前面补齐的空格数量（右对齐）
  const paddingWidth = maxRoleSpacingWidth - roleSpacingWidth
  const leftPadding = '　'.repeat(Math.max(0, Math.round(paddingWidth)))

  // 角色与姓名间隔：3个全角空格
  const roleNameSeparator = '　　　'

  // 姓名处理：根据新的对齐规则
  let nameFormatted = ''
  if (name && name.trim()) {
    const nameChars = name.split('')
    const nameLength = nameChars.length

    // 1. 最长的姓名不要插入空格
    if (nameLength === maxNameLength) {
      nameFormatted = name
    } else {
      // 2. 其他姓名需要插入的空格为maxLength-当前姓名长度
      const needSpaces = maxNameLength - nameLength
      const gaps = nameLength - 1 // 姓名的间隙数（长度-1）
      
      if (gaps === 0) {
        // 单字姓名（实际不存在，但保险起见）
        nameFormatted = name
      } else if (gaps <= needSpaces) {
        // 3. 间隙数小于等于空格数，插入对应数量全角空格
        const spacesPerGap = Math.floor(needSpaces / gaps)
        const extraSpaces = needSpaces % gaps
        
        let result = nameChars[0]
        for (let i = 1; i < nameLength; i++) {
          const currentSpaces = spacesPerGap + (i <= extraSpaces ? 1 : 0)
          result += '　'.repeat(currentSpaces) + nameChars[i]
        }
        nameFormatted = result
      } else {
        // 4. 间隙数大于空格数，将全角空格转换成半角空格均匀插入
        const halfSpacesPerGap = Math.floor((needSpaces * 2) / gaps)
        const extraHalfSpaces = (needSpaces * 2) % gaps
        
        let result = nameChars[0]
        for (let i = 1; i < nameLength; i++) {
          const currentHalfSpaces = halfSpacesPerGap + (i <= extraHalfSpaces ? 1 : 0)
          result += ' '.repeat(currentHalfSpaces) + nameChars[i]
        }
        nameFormatted = result
      }
    }
  } else {
    // 没有姓名时，补齐到最大姓名长度
    nameFormatted = '　'.repeat(maxNameLength)
  }

  return leftPadding + spacedRole + roleNameSeparator + nameFormatted
}

// 按照spacing设置对齐，但是下载下来word打开没有对齐
function formatMemberWithAdvancedAlignmentSpacing(role: string, name: string, maxRoleLength: number, maxNameLength: number): { text: string, spacing: number } {
  // 根据角色字符数量确定字符间隔
  let spacedRole = ''
  let roleSpacingWidth = 0 // 记录角色间隔占用的宽度

  if (role.length === 3) {
    // 3个字的角色：字符间用2个全角空格
    spacedRole = role.split('').join('　　')
    roleSpacingWidth = role.length * 3 - 2 // 每个字符+2个空格，最后一个字符不加
  } else if (role.length === 4) {
    // 4个字的角色：字符间用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1 // 每个字符+1个空格，最后一个字符不加
  } else if (role.length === 5) {
    // 5个字的角色：字符间用普通空格
    spacedRole = role.split('').join(' ')
    roleSpacingWidth = role.length + (role.length - 1) * 0.5 // 每个字符+0.5个空格宽度
  } else {
    // 其他长度：默认用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1
  }

  // 计算最长角色的宽度（假设最长角色是4个字符）
  const maxRoleSpacingWidth = maxRoleLength === 3 ? (maxRoleLength * 3 - 2) :
      maxRoleLength === 4 ? (maxRoleLength * 2 - 1) :
          maxRoleLength === 5 ? (maxRoleLength + (maxRoleLength - 1) * 0.5) :
              (maxRoleLength * 2 - 1)

  // 计算需要在角色前面补齐的空格数量（右对齐）
  const paddingWidth = maxRoleSpacingWidth - roleSpacingWidth
  const leftPadding = '　'.repeat(Math.max(0, Math.round(paddingWidth)))

  // 角色与姓名间隔：3个全角空格
  const roleNameSeparator = '　　　'

  // 姓名处理：用<fw></fw>包裹除最后一个字外的所有字符（最长姓名不需要调整spacing）
  let nameFormatted = name
  let spacing = 0
  if (name && name.length > 1 && name.length < maxNameLength) {
    // 只有非最长姓名才需要用<fw></fw>包裹来调整spacing
    const namePrefix = name.slice(0, -1) // 除了最后一个字
    const nameLastChar = name.slice(-1) // 最后一个字
    nameFormatted = `<fw>${namePrefix}</fw>${nameLastChar}`
    spacing = (maxNameLength - name.length) * 160
  }
  return { text: leftPadding + spacedRole + roleNameSeparator + nameFormatted, spacing }
}

/**
 * 格式化单个角色（只显示角色，不显示姓名）
 */
function formatRoleOnlyWithAlignment(role: string, maxRoleLength: number): string {
  // 根据角色字符数量确定字符间隔
  let spacedRole = ''
  let roleSpacingWidth = 0 // 记录角色间隔占用的宽度

  if (role.length === 3) {
    // 3个字的角色：字符间用2个全角空格
    spacedRole = role.split('').join('　　')
    roleSpacingWidth = role.length * 3 - 2 // 每个字符+2个空格，最后一个字符不加
  } else if (role.length === 4) {
    // 4个字的角色：字符间用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1 // 每个字符+1个空格，最后一个字符不加
  } else if (role.length === 5) {
    // 5个字的角色：字符间用普通空格
    spacedRole = role.split('').join(' ')
    roleSpacingWidth = role.length + (role.length - 1) * 0.5 // 每个字符+0.5个空格宽度
  } else {
    // 其他长度：默认用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1
  }

  // 计算最长角色的宽度
  const maxRoleSpacingWidth = maxRoleLength === 3 ? (maxRoleLength * 3 - 2) :
                             maxRoleLength === 4 ? (maxRoleLength * 2 - 1) :
                             maxRoleLength === 5 ? (maxRoleLength + (maxRoleLength - 1) * 0.5) :
                             (maxRoleLength * 2 - 1)

  // 计算需要在角色前面补齐的空格数量（右对齐）
  const paddingWidth = maxRoleSpacingWidth - roleSpacingWidth
  const leftPadding = '　'.repeat(Math.max(0, Math.round(paddingWidth)))

  return leftPadding + spacedRole
}

/**
 * 格式化单个审判组织成员（带对齐）
 */
function formatMemberWithAlignment(role: string, name: string, maxRoleLength: number, maxNameLength: number = 4): string {
  // 根据角色字符数量确定字符间隔
  let spacedRole = ''
  let roleSpacingWidth = 0

  if (role.length === 3) {
    // 3个字的角色：字符间用2个全角空格
    spacedRole = role.split('').join('　　')
    roleSpacingWidth = role.length * 3 - 2
  } else if (role.length === 4) {
    // 4个字的角色：字符间用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1
  } else if (role.length === 5) {
    // 5个字的角色：字符间用普通空格
    spacedRole = role.split('').join(' ')
    roleSpacingWidth = role.length + (role.length - 1) * 0.5
  } else {
    // 其他长度：默认用1个全角空格
    spacedRole = role.split('').join('　')
    roleSpacingWidth = role.length * 2 - 1
  }

  // 计算最长角色的宽度
  const maxRoleSpacingWidth = maxRoleLength === 3 ? (maxRoleLength * 3 - 2) :
                             maxRoleLength === 4 ? (maxRoleLength * 2 - 1) :
                             maxRoleLength === 5 ? (maxRoleLength + (maxRoleLength - 1) * 0.5) :
                             (maxRoleLength * 2 - 1)

  // 计算需要在角色前面补齐的空格数量
  const paddingWidth = maxRoleSpacingWidth - roleSpacingWidth
  const leftPadding = '　'.repeat(Math.max(0, Math.round(paddingWidth)))

  // 角色与姓名间隔：3个全角空格
  const roleNameSeparator = '　　　'

  // 姓名处理：首尾对齐，在姓名中间均匀分布空格
  let nameFormatted = ''
  if (name && name.trim()) {
    const nameChars = name.split('')
    const nameLength = nameChars.length

    if (nameLength === 1) {
      // 单字姓名：前后各补一半空格
      const totalSpaces = maxNameLength + (maxNameLength - 1) - 1 // 总宽度减去1个字符
      const leftSpaces = Math.floor(totalSpaces / 2)
      const rightSpaces = totalSpaces - leftSpaces
      nameFormatted = '　'.repeat(leftSpaces) + nameChars[0] + '　'.repeat(rightSpaces)
    } else {
      // 多字姓名：在字符间均匀分布空格
      const totalWidth = maxNameLength + (maxNameLength - 1) // 最长姓名的总宽度
      const totalSpaces = totalWidth - nameLength // 需要分布的空格总数
      const gaps = nameLength - 1 // 字符间的间隙数量

      if (gaps > 0) {
        const spacesPerGap = Math.floor(totalSpaces / gaps) // 每个间隙的基础空格数
        const extraSpaces = totalSpaces % gaps // 多余的空格数

        let result = nameChars[0]
        for (let i = 1; i < nameLength; i++) {
          // 前面的间隙多分配一个空格
          const currentSpaces = spacesPerGap + (i <= extraSpaces ? 1 : 0)
          result += '　'.repeat(currentSpaces) + nameChars[i]
        }
        nameFormatted = result
      } else {
        nameFormatted = nameChars[0]
      }
    }

    // 现在使用尾部缩进，不需要在末尾添加空格
  } else {
    // 没有姓名时，补齐到最大姓名长度的宽度（兼容空姓名情况）
    const maxNameWidth = maxNameLength + (maxNameLength - 1)
    nameFormatted = '　'.repeat(maxNameWidth)
  }

  return leftPadding + spacedRole + roleNameSeparator + nameFormatted
}

/**
 * 格式化单个审判组织成员（旧版本，保留兼容性）
 */
function formatSingleMember(role: string, name: string, maxNameLength: number = 4): string {
  // 根据角色字符数量确定字符间隔
  let spacedRole = ''

  if (role.length === 3) {
    // 3个字的角色：字符间用2个全角空格
    spacedRole = role.split('').join('　　')
  } else if (role.length === 4) {
    // 4个字的角色：字符间用1个全角空格
    spacedRole = role.split('').join('　')
  } else if (role.length === 5) {
    // 5个字的角色：字符间用普通空格
    spacedRole = role.split('').join(' ')
  } else {
    // 其他长度：默认用1个全角空格
    spacedRole = role.split('').join('　')
  }

  // 角色与姓名间隔：3个全角空格
  const roleNameSeparator = '　　　'

  // 姓名处理：首尾对齐，在姓名中间均匀分布空格
  let nameFormatted = ''
  if (name && name.trim()) {
    const nameChars = name.split('')
    const nameLength = nameChars.length

    if (nameLength === 1) {
      // 单字姓名：前后各补一半空格
      const totalSpaces = maxNameLength + (maxNameLength - 1) - 1 // 总宽度减去1个字符
      const leftSpaces = Math.floor(totalSpaces / 2)
      const rightSpaces = totalSpaces - leftSpaces
      nameFormatted = '　'.repeat(leftSpaces) + nameChars[0] + '　'.repeat(rightSpaces)
    } else {
      // 多字姓名：在字符间均匀分布空格
      const totalWidth = maxNameLength + (maxNameLength - 1) // 最长姓名的总宽度
      const totalSpaces = totalWidth - nameLength // 需要分布的空格总数
      const gaps = nameLength - 1 // 字符间的间隙数量

      if (gaps > 0) {
        const spacesPerGap = Math.floor(totalSpaces / gaps) // 每个间隙的基础空格数
        const extraSpaces = totalSpaces % gaps // 多余的空格数

        let result = nameChars[0]
        for (let i = 1; i < nameLength; i++) {
          // 前面的间隙多分配一个空格
          const currentSpaces = spacesPerGap + (i <= extraSpaces ? 1 : 0)
          result += '　'.repeat(currentSpaces) + nameChars[i]
        }
        nameFormatted = result
      } else {
        nameFormatted = nameChars[0]
      }
    }

    nameFormatted += '　' // 最后加一个空格
  } else {
    // 没有姓名时，补齐到最大姓名长度的宽度
    const maxNameWidth = maxNameLength + (maxNameLength - 1)
    nameFormatted = '　'.repeat(maxNameWidth) + '　'
  }

  return spacedRole + roleNameSeparator + nameFormatted
}

/**
 * 插入默认的文书结尾信息
 */
async function insertDefaultFooter() {
  // 插入空行
  await insertCharacterToEditor('\n')
  await insertCharacterToEditor('\n')
  await insertCharacterToEditor('\n')
  await insertCharacterToEditor('\n')
  await new Promise(resolve => setTimeout(resolve, 100))
  // 默认审判组织成员数据（只包含角色）
  const defaultRoles = [
    '审判长',
    '审判员',
    '审判员',
    '法官助理',
    '书记员'
  ]

  // 计算最长角色名称长度
  const maxRoleLength = Math.max(...defaultRoles.map(role => role.length))

  // 分类成员
  const judgeRoles = defaultRoles.filter(role =>
    ['审判长', '审判员', '人民陪审员'].includes(role)
  )

  const assistantRoles = defaultRoles.filter(role =>
    ['书记员', '法官助理'].includes(role)
  )

  // 插入审判人员
  for (let i = 0; i < judgeRoles.length; i++) {
    const role = judgeRoles[i]
    const formattedText = formatRoleOnlyWithAlignment(role, maxRoleLength)

    // 在第一个审判人员前插入书签
    const bookmark = i === 0 ? '[BOOKMARK:落款]' : undefined
    await insertFormattedText(formattedText, 'rightAlign', { tailIndent: true }, bookmark)
  }
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')
  await insertFormattedText('', 'rightAlign')

  // 判决日期（右对齐）
  const currentDate = new Date()
  await insertFormattedText(formatChineseDate(currentDate), 'rightAlign', { tailIndent: true })
  await new Promise(resolve => setTimeout(resolve, 300))
  await insertFormattedText("本件与原本核对无异", 'leftAlign')
  await new Promise(resolve => setTimeout(resolve, 300))
  await insertFormattedText('', 'rightAlign')


  // 插入审判辅助人员
  for (const role of assistantRoles) {
    const formattedText = formatRoleOnlyWithAlignment(role, maxRoleLength)
    await insertFormattedText(formattedText, 'rightAlign', { tailIndent: true })
  }
}

/**
 * 文档就绪后的处理函数
 */
async function handleDocumentReady() {
  try {
    console.log('开始处理文档内容...')

    // 等待编辑器完全初始化
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 开始流式生成内容（包含头部、内容和结尾）
    await startStreamGeneration()
  } catch (error) {
    console.error('处理文档内容失败:', error)
    ElMessage.error('文书内容生成失败: ' + (error as Error).message)
  }
}

/**
 * 文书部分格式配置
 */
const documentFormatConfig = {
  title: { font: '宋体', fontSize: '三号', position: '居中', bold: true },
  subtitle: { font: '宋体', fontSize: '四号', position: '左对齐', bold: true },
  content: { font: '宋体', fontSize: '四号', position: '左对齐' },
  legal: { font: '楷体', fontSize: '四号', position: '左对齐' },
  // 新增格式配置
  documentHeader: { font: '宋体', fontSize: '二号', position: '居中', bold: true },
  caseNumber: { font: '仿宋', fontSize: '三号', position: '右对齐' },
  mainContent: { font: '仿宋', fontSize: '三号', position: '左对齐', indentFirstLine: true },
  leftAlign: { font: '仿宋', fontSize: '三号', position: '左对齐' },
  rightAlign: { font: '仿宋', fontSize: '三号', position: '右对齐' },
  spacing: { font: '仿宋', fontSize: '三号', position: '两端对齐' }
}

/**
 * 插入格式化文本到OnlyOffice编辑器
 * @param text 要插入的文本
 * @param formatType 格式类型
 * @param extraOptions 额外选项
 * @param bookmark 书签名称，如果有值则在插入文本时同时加入书签
 */
function insertFormattedText(text: string, formatType: string = 'content', extraOptions: any = {}, bookmark?: string): Promise<boolean> {
  return new Promise(async (resolve, reject) => {
    console.log(`尝试插入格式化文本 [${formatType}]:`, text.substring(0, 50) + (text.length > 50 ? '...' : ''))
    // @ts-ignore
    if (window.docEditor?.serviceCommand) {
      try {
        const format = documentFormatConfig[formatType] || documentFormatConfig.content

        // 使用原来的serviceCommand方式
        // @ts-ignore
        window.docEditor.serviceCommand('PasteText', {
          text: text,
          font: format.font,
          fontSize: format.fontSize,
          position: format.position,
          bold: format.bold || false,
          indentFirstLine: format.indentFirstLine || false,
          bookmark: bookmark, // 传递书签参数给插件
          ...extraOptions
        })

        console.log(`格式化文本插入成功 [${formatType}]`)
        resolve(true)
        return
      } catch (error) {
        console.warn('格式化文本插入失败:', error)
      }
    }
  })
}


/**
 * 开始流式生成内容 - 按顺序调用9个流式接口
 */
async function startStreamGeneration() {
  try {
    if (!currentDocumentId) {
      throw new Error('文书ID不存在')
    }

    console.log('开始按顺序流式生成文书内容，文书ID:', currentDocumentId)
    ElMessage.info('开始生成文书内容，请稍候...')

    // 初始化生成状态
    isGenerating.value = true
    currentStepIndex.value = 0
    currentGenerationStatus.value = '正在生成中'

    generatedContent = '' // 重置生成内容
    bookmarkList.value = [] // 重置书签列表

    // 从案件记录中获取案件类型代码
    const caseInfo = await getCaseBasicInfo()
    const ajlxdm = caseInfo.ajlxdm

    // 根据案件类型动态构建生成步骤
    const generationSteps = []

    // 1. 文书头部信息（所有案件都有）
    generationSteps.push({ name: '文书头部信息', handler: insertDocumentHeader, formatType: 'header' })

    // 测试步骤：循环插入大量数据测试连接稳定性
    // 123123.push({ name: '测试数据插入', handler: insertTestData, formatType: 'mainContent' })

    // 2. 当事人基本情况（所有案件都有）
    generationSteps.push({ name: '当事人基本情况', sseApi: documentGenerationApi.streamPartyBasicInfoSSE, formatType: 'mainContent' })

    // 3. 审理经过（所有案件都有）
    generationSteps.push({ name: '审理经过', sseApi: documentGenerationApi.streamTrialProcessSSE, formatType: 'mainContent' })

    // 4. 诉辩信息（所有案件都有）
    generationSteps.push({ name: '诉辩信息', sseApi: documentGenerationApi.streamLitigationDefenseInfoSSE, formatType: 'mainContent' })

    // 5-7. 原审相关步骤（仅二审案件有）
    if (ajlxdm === '0302') {
      generationSteps.push({ name: '原审诉讼请求', sseApi: documentGenerationApi.streamOriginalLitigationRequestSSE, formatType: 'mainContent' })
      generationSteps.push({ name: '原审认定事实', sseApi: documentGenerationApi.streamOriginalRecognizedFactsSSE, formatType: 'mainContent' })
      generationSteps.push({ name: '原审本院认为、原审裁判结果', sseApi: documentGenerationApi.streamOriginalCourtOpinionAndJudgmentSSE, formatType: 'mainContent' })
    }

    // 8. 认定事实（所有案件都有）
    generationSteps.push({ name: '认定事实', sseApi: documentGenerationApi.streamRecognizedFactsSSE, formatType: 'mainContent' })

    // 9. 裁判理由、综上所述、裁判结果、诉讼费（所有案件都有）
    generationSteps.push({ name: '裁判理由、综上所述、裁判结果、诉讼费', sseApi: documentGenerationApi.streamSummarySSE, formatType: 'mainContent' })

    // 10. 文书结尾信息（所有案件都有）
    generationSteps.push({ name: '文书结尾信息', handler: insertDocumentFooter, formatType: 'footer' })

    console.log(`案件类型: ${ajlxdm === '0301' ? '一审' : '二审'} (${ajlxdm})，生成步骤数: ${generationSteps.length}`)

    // 更新总步骤数
    totalSteps.value = generationSteps.length

    // 按顺序执行每个步骤
    for (let i = 0; i < generationSteps.length; i++) {
      const step = generationSteps[i]


      console.log(`开始生成第${i + 1}步: ${step.name}`)

      try {
        // 根据步骤类型选择处理方式
        if (step.handler) {
          // 头部和结尾步骤：直接调用处理函数


          await step.handler()
          console.log(`第${i + 1}步完成: ${step.name}，插入头部/结尾信息`)
        } else if (step.sseApi) {
          // 内容步骤：使用 SSE 处理
          await processSSEStreamStep(step.name, step.formatType, step.sseApi)
          console.log(`第${i + 1}步完成: ${step.name}`)
        }

      } catch (stepError) {
        console.error(`第${i + 1}步失败: ${step.name}`, stepError)
        // ElMessage.warning(`${step.name} 生成失败，继续下一步`)
        // 继续执行下一步，不中断整个流程
      }

      // 无论成功还是失败，都添加模型间分隔换行（除了头部和结尾步骤）
      if (step.formatType === 'mainContent') {
        await addModelSeparator(i, generationSteps.length, step.name)
      }

      // 步骤间添加小延迟
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    console.log('所有步骤完成')

    // 生成完成，重置状态
    isGenerating.value = false
    currentStepIndex.value = 0

    ElMessage.success('文书内容生成完成！')

  } catch (error) {
    console.error('流式生成失败:', error)
    ElMessage.error('文书内容生成失败: ' + (error as Error).message)

    // 发生错误时重置状态
    isGenerating.value = false
    currentGenerationStatus.value = ''
    currentStepIndex.value = 0
  }
}


/**
 * 检查并调整文档排版，确保审判组织成员等内容不跨页
 */
async function checkAndAdjustDocumentLayout(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      console.log('开始检查并调整文档排版...')

      // 设置排版状态
      currentGenerationStatus.value = '排版中'

      // @ts-ignore
      if (window.docEditor?.serviceCommand) {
        // @ts-ignore
        window.docEditor.serviceCommand('CheckAndAdjustLayout', {})

        console.log('文档排版检查和调整命令已发送')
        resolve(true)
      } else {
        console.warn('OnlyOffice编辑器API不可用，跳过排版调整')
        // 清除排版状态
        currentGenerationStatus.value = ''
        resolve(false)
      }
    } catch (error) {
      console.error('检查并调整文档排版失败:', error)
      // 清除排版状态
      currentGenerationStatus.value = ''
      resolve(false)
    }
  })
}

/**
 * 添加模型间分隔换行
 */
async function addModelSeparator(stepIndex: number, totalSteps: number, stepName: string) {
  // 除了最后一个步骤，都添加分隔换行
  // 由于onComplete已经处理了模型结尾的换行符，这里不需要再添加额外的换行符
  // 只在必要时添加段落间分隔，避免多余的空行
  if (stepIndex < totalSteps - 1) {
    console.log(`第${stepIndex + 1}步(${stepName})完成，由onComplete已处理换行符`)
  }
}

/**
 * 插入书签到OnlyOffice文档
 */
async function insertBookmark(bookmarkName: string) {
  try {
    // @ts-ignore
    if (window.docEditor?.serviceCommand) {
      // 使用特殊的不可见标记作为书签锚点
      const bookmarkMarker = `[BOOKMARK:${bookmarkName}]`

      // @ts-ignore
      window.docEditor.serviceCommand('InsertBookmark', {
        name: bookmarkMarker
      })

      console.log(`书签标记插入成功: ${bookmarkName}`)
    }
  } catch (error) {
    console.error(`书签插入失败: ${bookmarkName}`, error)
  }
}

/**
 * 跳转到指定书签
 */
async function gotoBookmark(bookmarkName: string) {
  try {
    // @ts-ignore
    if (window.docEditor?.serviceCommand) {
      // 搜索书签标记
      const bookmarkMarker = `[BOOKMARK:${bookmarkName}]`

      // @ts-ignore
      window.docEditor.serviceCommand('GotoBookmark', {
        name: bookmarkMarker
      })

      console.log(`跳转到书签: ${bookmarkName}`)
    }
  } catch (error) {
    console.error(`跳转书签失败: ${bookmarkName}`, error)
  }
}

/**
 * 获取书签显示名称
 */
function getBookmarkDisplayName(bookmarkName: string): string {
  const nameMap: Record<string, string> = {
    '标题': '标题',
    '诉讼参与人基本情况': '诉讼参与人基本情况',
    '案件由来和审理经过': '案件由来和审理经过',
    '当事人诉辩意见': '当事人诉辩意见',
    '一审诉讼请求': '一审诉讼请求',
    '一审法院查明': '一审法院查明',
    '一审法院认为与裁判': '一审法院认为与裁判',
    '证据和事实认定': '证据和事实认定',
    '裁判理由': '裁判理由',
    '裁判依据': '裁判依据',
    '裁判结果': '裁判结果',
    '落款': '落款'
  }
  return nameMap[bookmarkName] || bookmarkName
}

/**
 * 处理 SSE 流式生成步骤
 */
async function processSSEStreamStep(stepName: string, formatType: string = 'content', sseApi?: Function): Promise<void> {
  return new Promise((resolve, reject) => {
    // 换行处理状态
    let pendingNewlines = '' // 收集的连续换行符
    let hasReceivedText = false // 是否接收到文字的标志位
    let nextCharNeedIndent = false // 下一个字符是否需要缩进

    // 如果没有提供sseApi，默认使用总结的SSE API
    const apiToUse = sseApi

    const eventSource = apiToUse(currentDocumentId, {
      onMessage: async (content: string) => {
        try {
          // 检测书签标记
          const bookmarkMatch = content.match(/<bookmark>([^<]+)<\/bookmark>/)
          if (bookmarkMatch) {
            const bookmarkName = bookmarkMatch[1]
            console.log(`检测到书签标记: ${bookmarkName}`)
            currentGenerationStatus.value = `正在生成${bookmarkName}`
            // 如果有积累的换行符，先插入换行符
            if (pendingNewlines && hasReceivedText) {
              // 文字尾的换行，只保留一个换行
              processStreamMessage('\n')
              pendingNewlines = '' // 清空收集的换行符
            }

            // 通过processStreamMessage处理书签插入
            const bookmarkMarker = `[BOOKMARK:${bookmarkName}]`
            processStreamMessage(bookmarkMarker, false, true) // 第三个参数表示这是书签

            // 添加到书签列表
            const displayName = getBookmarkDisplayName(bookmarkName)
            if (!bookmarkList.value.find(b => b.name === bookmarkName)) {
              bookmarkList.value.push({ name: bookmarkName, displayName })
            }

            // 过滤掉书签标记，不插入到文档中
            return
          }

          // 检测缩进标记
          if (content === '<indent>') {
            nextCharNeedIndent = true
            return // 不插入缩进标记到文档中
          }

          // 如果是换行符，先收集
          if (content === '\n' || content === '\r' || content === '\r\n' || (pendingNewlines && content === '')) {
            pendingNewlines += content
            return
          }

          // 如果是其他字符，处理之前收集的换行符
          if (pendingNewlines) {
            if (hasReceivedText) {
              // 文字尾的换行，只保留一个换行
              processStreamMessage('\n')
            }
            // 段落开始前的换行直接丢弃（不插入）
            pendingNewlines = '' // 清空收集的换行符
          }

          // 插入当前字符
          if (content.trim()) { // 非空白字符
            hasReceivedText = true
          }
          // 插入字符，如果需要缩进则传递缩进参数
          processStreamMessage(content, nextCharNeedIndent)

          // 重置缩进标记
          if (nextCharNeedIndent) {
            nextCharNeedIndent = false
          }
        } catch (insertError) {
          console.warn(`${stepName} - 插入字符失败:`, insertError)
        }
      },
      onError: (error: Error) => {
        console.error(`${stepName} SSE 错误:`, error)
        // 从活跃连接数组中移除
        activeSSEConnections.value = activeSSEConnections.value.filter(conn => conn !== eventSource)
        eventSource.close()
        reject(error)
      },
      onComplete: async () => {
        // 处理剩余的换行符：如果模型输出以换行符结尾且之前有文字，则插入一个换行符
        if (pendingNewlines && hasReceivedText) {
          try {
            processStreamMessage('\n')
          } catch (error) {
            console.warn(`${stepName} - 处理结尾换行符失败:`, error)
          }
        }

        // 处理messageCollector中剩余的内容
        if (messageCollector.value.trim()) {
          // SSE连接结束，处理messageCollector剩余内容
          try {
            // 创建任务处理剩余内容，使用正确的缩进信息
            const task = createCharacterTask(messageCollector.value, messageCollectorNeedIndent.value)
            addTaskToQueue(task)
            // 清空messageCollector和缩进标记
            messageCollector.value = ''
            messageCollectorNeedIndent.value = false
            // 如果当前没有任务在执行，立即执行
            if (!isTaskExecuting.value) {
              executeNextTask()
            }
          } catch (error) {
            console.warn(`${stepName} - 处理messageCollector剩余内容失败:`, error)
          }
        }

        // 从活跃连接数组中移除
        activeSSEConnections.value = activeSSEConnections.value.filter(conn => conn !== eventSource)

        // 重置状态，为下一个步骤准备
        hasReceivedText = false
        console.log(`${stepName} - 流式生成完成`)
        resolve()
      }
    })

    // 将新创建的SSE连接添加到跟踪数组
    activeSSEConnections.value.push(eventSource)
  })
}

/**
 * 逐字符插入到编辑器
 */
/**
 * 全文内容优化处理（包括外币检查、人民币字样处理等）
 */
async function optimizeDocumentContent(): Promise<boolean> {
  try {
    // @ts-ignore
    if (window.docEditor && window.docEditor.serviceCommand) {
      console.log('开始调用OnlyOffice插件进行全文内容优化...')
      
      // 重置状态
      optimizeContentCompleted.value = false
      optimizeContentResult.value = null
      
      // 调用OnlyOffice插件命令
      // @ts-ignore
      window.docEditor.serviceCommand('optimizeDocumentContent', {})
      
      // 循环检查是否完成，最多等待10秒
      const maxWaitTime = 10000 // 10秒
      const checkInterval = 100 // 100毫秒检查一次
      let waitedTime = 0
      
      while (!optimizeContentCompleted.value && waitedTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, checkInterval))
        waitedTime += checkInterval
      }
      
      if (optimizeContentCompleted.value) {
        return optimizeContentResult.value?.status === 'completed'
      } else {
        console.warn('全文内容优化超时，继续执行后续流程')
        return false
      }
      
    } else {
      console.warn('OnlyOffice编辑器API不可用，跳过全文内容优化')
      return false
    }
  } catch (error) {
    console.error('全文内容优化失败:', error)
    return false
  }
}

async function insertCharacterToEditor(character: string, needIndent: boolean = false, isBookmark: boolean = false): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // @ts-ignore
      if (window.docEditor && window.docEditor.serviceCommand) {
        if (isBookmark) {
          // 如果是书签，使用 InsertBookmark 命令
          let bookmarkName = character
          // @ts-ignore
          window.docEditor.serviceCommand('InsertBookmark', {
            name: bookmarkName
          })
          console.log(`书签插入成功: ${bookmarkName}`)
        } else {
          // 普通字符插入
          // @ts-ignore
          window.docEditor.serviceCommand('InsertCharacter', {
            character: character,
            needIndent: needIndent,
            fontFamily: '仿宋'
          })
        }

        resolve(true)
      } else {
        console.warn('OnlyOffice编辑器API不可用')
        resolve(false)
      }
    } catch (error) {
      console.error('插入字符到编辑器失败:', error)
      resolve(false)
    }
  })
}

// ==================== 工具函数 ====================

/**
 * 根据内容长度和列宽判断是否需要展示 hover 提示
 * 优化性能，避免不必要的 popover 渲染
 * @param text 要检查的文本内容
 * @param columnWidth 列宽类型或最大字符数
 * @returns 是否需要展示 hover
 */
const shouldShowHover = (text: string | undefined | null, columnWidth: number | string = 'default'): boolean => {
  // 快速检查：空值直接返回 false
  if (!text || typeof text !== 'string' || text.trim().length === 0) {
    return false
  }

  // 根据列宽类型设置不同的字符数阈值
  let maxChars: number

  if (typeof columnWidth === 'number') {
    maxChars = columnWidth
  } else {
    // 根据不同的列宽预设不同的字符数阈值
    switch (columnWidth) {
      case 'susongfeiyong':     // 姓名
        maxChars = 3
        break
      case 'narrow':     // 窄列（如序号、操作列）
        maxChars = 10
        break
      case 'medium':     // 中等列（如结果列）
        maxChars = 20
        break
      case 'wide':       // 宽列（如判决结果列）
        maxChars = 20
        break
      case 'extra-wide': // 超宽列（如二审判决结果列）
        maxChars = 25
        break
      default:           // 默认
        maxChars = 30
    }
  }

  // 性能优化：先检查长度，避免复杂计算
  const textLength = text.length
  if (textLength <= maxChars) {
    return false
  }

  // 进一步检查：如果文本包含换行符或特殊字符，降低阈值
  if (text.includes('\n') || text.includes('\r') || text.includes('\t')) {
    return textLength > Math.floor(maxChars * 0.7)
  }

  // 检查中文字符比例（中文字符占用更多显示空间）
  const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length
  const chineseRatio = chineseCharCount / textLength

  // 如果中文字符比例高，降低阈值
  if (chineseRatio > 0.5) {
    return textLength > Math.floor(maxChars * 0.8)
  }

  return textLength > maxChars
}

/**
 * 兼容旧方法：根据字符数判断文本是否超出指定长度
 * @param text 要检查的文本
 * @param maxChars 最大字符数
 * @returns 是否超出
 * @deprecated 建议使用 shouldShowHover 方法
 */
const isTextOverflowByChars = (text: string, maxChars: number): boolean => {
  return shouldShowHover(text, maxChars)
}

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成后的初始化逻辑
 * 输出组件加载信息，用于调试和日志记录
 */
onMounted(async () => {
  // 页面加载时的初始化逻辑
  console.log('文书生成编辑器加载完成', {
    caseImportId: caseImportId.value,
    caseType: caseType.value,
    documentType: documentType.value
  })

  // 添加页面关闭事件监听器
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 添加消息监听器，监听来自OnlyOffice插件的消息
  window.addEventListener('message', handlePluginMessage)

  // 执行前置检查
  await performPreCheck()

  // 先加载当事人信息，再加载其他数据
  await loadCaseParties()
  await loadJudgmentItems()
  await loadLegalFees()
})

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  console.log('DocumentGenerationEditor 组件正在卸载，开始清理资源...')

  // 移除beforeunload事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 移除消息监听器
  window.removeEventListener('message', handlePluginMessage)

  // 关闭所有活跃的SSE连接
  closeAllActiveSSEConnections()

  // 清理任务队列
  characterTaskQueue.value = []
  isTaskExecuting.value = false
  currentExecutingTask.value = null
  messageCollector.value = ''
  console.log('任务队列已清理')

  // 销毁OnlyOffice编辑器实例
  try {
    // @ts-ignore
    if (window.DocsAPI?.DocEditor?.destroyEditor) {
      // @ts-ignore
      window.DocsAPI.DocEditor.destroyEditor('onlyoffice-editor')
      console.log('OnlyOffice编辑器实例已销毁')
    }
  } catch (error) {
    console.warn('销毁OnlyOffice编辑器实例失败:', error)
  }

  console.log('DocumentGenerationEditor 组件资源清理完成')
})

/**
 * 处理页面关闭前的清理工作
 */
function handleBeforeUnload(event: BeforeUnloadEvent) {
  console.log('页面即将关闭，执行清理工作...')

  if(!hasRelease.value && currentDocumentId){
    // 通知后端文书生成已完成，释放队列权限
    documentGenerationApi.completeDocumentGeneration(currentDocumentId)
    hasRelease.value = true
  }
}

/**
 * 处理来自OnlyOffice插件的消息
 */
function handlePluginMessage(event: MessageEvent) {
  try {
    // 检查消息来源（可以根据需要添加更严格的来源验证）
    if (event.data && typeof event.data === 'object') {
      const { type, status } = event.data

      // console.log('收到插件消息:', event.data)

      // 处理排版完成消息
      if (type === 'layoutAdjustment' && status === 'completed') {
        console.log('排版调整完成，清除排版状态')
        currentGenerationStatus.value = ''
        ElMessage.success('文档排版调整完成')
      }

      // 处理排版失败消息
      if (type === 'layoutAdjustment' && status === 'failed') {
        console.log('排版调整失败，清除排版状态')
        currentGenerationStatus.value = ''
        ElMessage.warning('文档排版调整失败')
      }

      // 处理字符插入成功消息
      if (type === 'characterInserted' && status === 'success') {
        handleCharacterInsertSuccess(event.data)
      }

      // 处理全文内容优化完成消息
      if (type === 'optimizeContentCompleted') {
        console.log('收到全文内容优化完成消息:', event.data)
        optimizeContentCompleted.value = true
        optimizeContentResult.value = event.data
      }
    }
  } catch (error) {
    console.error('处理插件消息失败:', error)
  }
}

// ==================== 任务队列系统 ====================

/**
 * 等待任务队列执行完毕
 */
async function waitForQueueCompletion(): Promise<void> {
  return new Promise((resolve, reject) => {
    const checkInterval = 100 // 检查间隔100ms
    const maxWaitTime = 30000 // 最大等待30秒
    let waitedTime = 0
    
    const check = () => {
      // 检查队列是否为空、没有任务在执行、messageCollector也为空
      const queueEmpty = characterTaskQueue.value.length === 0
      const notExecuting = !isTaskExecuting.value
      const collectorEmpty = !messageCollector.value.trim()
      
      if (queueEmpty && notExecuting && collectorEmpty) {
        console.log('[TaskQueue] 队列执行完毕，可以继续执行文书结尾信息')
        resolve()
      } else if (waitedTime >= maxWaitTime) {
        console.warn('[TaskQueue] 等待队列完成超时，继续执行文书结尾信息')
        resolve() // 即使超时也继续执行，不阻塞流程
      } else {
        waitedTime += checkInterval
        // 等待队列完成中...
        setTimeout(check, checkInterval)
      }
    }
    
    check()
  })
}

/**
 * 创建字符插入任务
 */
function createCharacterTask(content: string, needIndent: boolean = false, isBookmark: boolean = false): CharacterTask {
  const task = {
    id: `task_${++taskIdCounter.value}`,
    content,
    needIndent,
    isBookmark,
    retryCount: 0,
    maxRetries: MAX_RETRIES
  }
  // 创建任务
  return task
}

/**
 * 添加任务到队列
 */
function addTaskToQueue(task: CharacterTask) {
  characterTaskQueue.value.push(task)
  // 任务已添加到队列
  
  // 如果当前没有任务在执行，立即执行
  if (!isTaskExecuting.value) {
    executeNextTask()
  }
}

/**
 * 执行下一个任务
 */
async function executeNextTask() {
  // executeNextTask 被调用
  
  if (characterTaskQueue.value.length === 0 || isTaskExecuting.value) {
    // 跳过执行 - 队列为空或正在执行
    return
  }

  const task = characterTaskQueue.value.shift()
  if (!task) {
    console.error('[TaskQueue] 从队列取出的任务为空')
    return
  }

  isTaskExecuting.value = true
  currentExecutingTask.value = task

  // 开始执行任务

  try {
    await executeTaskWithRetry(task)
  } catch (error) {
    console.error(`任务执行失败: ${task.id}`, error)
    // 任务失败，继续执行下一个任务
    isTaskExecuting.value = false
    currentExecutingTask.value = null
    executeNextTask()
  }
}

/**
 * 执行任务并处理重试逻辑
 */
async function executeTaskWithRetry(task: CharacterTask): Promise<void> {
  return new Promise((resolve, reject) => {
    let timeoutId: NodeJS.Timeout

    // 设置超时处理
    const setupTimeout = () => {
      // 清理之前的超时定时器
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        console.warn(`任务超时: ${task.id}, 重试次数: ${task.retryCount}`)
        
        if (task.retryCount < task.maxRetries) {
          task.retryCount++
          console.log(`重试任务: ${task.id}, 第${task.retryCount}次重试`)
          // 重新执行任务
          executeTask(task)
          setupTimeout() // 重新设置超时
        } else {
          console.error(`任务最终失败: ${task.id}, 已达到最大重试次数`)
          cleanup()
          reject(new Error(`任务执行失败: ${task.id}`))
        }
      }, TASK_TIMEOUT)
    }

    // 清理函数
    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      isTaskExecuting.value = false
      currentExecutingTask.value = null
    }

    // 成功回调
    const onSuccess = () => {
      // 任务执行成功
      cleanup()
      resolve()
      // 执行下一个任务
      // 准备执行下一个任务
      executeNextTask()
    }

    // 设置成功回调
    task.successCallback = onSuccess

    // 执行任务
    executeTask(task)
    setupTimeout()
  })
}

/**
 * 实际执行字符插入任务
 */
async function executeTask(task: CharacterTask) {
  try {
    await insertCharacterToEditor(task.content, task.needIndent, task.isBookmark)
  } catch (error) {
    console.error(`执行任务失败: ${task.id}`, error)
    throw error
  }
}

/**
 * 处理字符插入成功消息
 */
function handleCharacterInsertSuccess(data: any) {
  // 收到字符插入成功消息
  const currentTask = currentExecutingTask.value
  if (currentTask && currentTask.successCallback) {
    // 任务成功确认
    currentTask.successCallback()
  } else {
    console.warn(`[TaskQueue] 收到成功消息但没有对应的执行任务或回调函数`, { currentTask, hasCallback: currentTask?.successCallback })
  }
}

/**
  * 处理流式消息，集成任务队列机制
  */
 function processStreamMessage(content: string, needIndent: boolean = false, isBookmark: boolean = false) {
   // processStreamMessage 收到内容
   
   // 检测分割点：书签、换行或缩进标记
   const isBreakPoint = isBookmark || 
                       content.includes('<bookmark>') || 
                       content === '\n' || 
                       content === '\r' || 
                       content === '\r\n' || 
                       content === '<indent>'
   
   // 检测分割点   

   if (isTaskExecuting.value) {
     // 有任务正在执行，进入收集模式
     // 如果有任务在执行，收集消息
     if (!isBreakPoint) {
       // 非分割点内容，直接收集
       if (messageCollector.value === '' && needIndent) {
         // 如果是messageCollector的第一个内容且需要缩进，记录缩进信息
         messageCollectorNeedIndent.value = true
       }
       messageCollector.value += content
       // 收集非分割点内容
     } else {
       // 遇到分割点，先处理收集的内容
       if (messageCollector.value.length > 0) {
         // 处理收集的内容
         const task = createCharacterTask(messageCollector.value, messageCollectorNeedIndent.value, false)
         addTaskToQueue(task)
         messageCollector.value = ''
         messageCollectorNeedIndent.value = false
       }
       
       // 如果分割点本身有内容（如换行符），也作为任务
       if (content.length > 0) {
         // 处理分割点内容
         const task = createCharacterTask(content, needIndent, isBookmark)
         addTaskToQueue(task)
       }
     }
   } else {
     // 没有任务在执行，直接创建任务
     // 没有任务在执行，先处理messageCollector中的内容（如果有的话）
     if (messageCollector.value.length > 0) {
       // 先处理messageCollector中的内容
       const collectedTask = createCharacterTask(messageCollector.value, messageCollectorNeedIndent.value, false)
       addTaskToQueue(collectedTask)
       messageCollector.value = ''
       messageCollectorNeedIndent.value = false
     }
     
     // 然后处理当前内容
     const task = createCharacterTask(content, needIndent, isBookmark)
     addTaskToQueue(task)
     executeNextTask()
   }
 }
</script>

<style scoped>
.document-generation-editor {
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 120px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

/* ==================== 重构后的简洁样式系统 ==================== */

/* 1. 工作区布局 */
.document-editor-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.workspace-header {
  position: relative;
}

.workspace-header h4 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  padding-left: 10px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workspace-header h4::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 18px;
  background: linear-gradient(to bottom, #67c23a, #e6a23c);
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 2. 模块样式 */
.judgment-module,
.cost-module {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  position: relative;
  min-height: 200px;
  overflow: visible;
}



/* 3. 工具栏样式 */
.judgment-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-shrink: 0;
  background: #fff;
  z-index: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  min-width: 25px;
  height: 25px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* 4. 表格容器样式 */
.table-wrapper {
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.data-table-container {
  min-height: 280px;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
}

.add-row-container {
  display: flex;
  justify-content: center;
  margin-top: 8px;
  padding: 1px;
  background-color: transparent;
}

/* 5. 表格样式 */
.custom-table {
  border: 1px solid #5a87ed !important;
}

/* 统一所有表格边框颜色 */
:deep(.custom-table .el-table) {
  border: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__header-wrapper) {
  background-color: rgba(90, 135, 237, 0.1);
  border-bottom: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__header th) {
  background-color: rgba(90, 135, 237, 0.1) !important;
  border-color: #5a87ed !important;
  border-right: 1px solid #5a87ed !important;
  border-bottom: 1px solid #5a87ed !important;
  color: #303133;
  font-weight: 600;
  text-align: center !important;
  vertical-align: middle !important;
}

:deep(.custom-table .el-table__header .el-table__cell) {
  text-align: center !important;
  vertical-align: middle !important;
  border-right: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__header .el-table__cell .cell) {
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  font-weight: 600 !important;
}

:deep(.custom-table .el-table__border-bottom-patch) {
  border-color: #5a87ed !important;
}

:deep(.custom-table .el-table__border-right-patch) {
  border-color: #5a87ed !important;
}

:deep(.custom-table .el-table__body td) {
  text-align: center !important;
  vertical-align: middle !important;
  border-color: #5a87ed !important;
  border-right: 1px solid #5a87ed !important;
  border-bottom: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__body .el-table__cell) {
  text-align: center !important;
  vertical-align: middle !important;
  border-right: 1px solid #5a87ed !important;
}

:deep(.custom-table .el-table__body .el-table__cell .cell) {
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}

/* 移除最后一列的右边框 */
:deep(.custom-table .el-table__header th:last-child),
:deep(.custom-table .el-table__body td:last-child) {
  border-right: none !important;
}

/* 移除最后一行的底边框 */
:deep(.custom-table .el-table__body tr:last-child td) {
  border-bottom: none !important;
}

/* 6. 内容单元格样式 */
:deep(.content-cell) {
  width: 100% !important;
  min-width: 0 !important;
  overflow: visible !important;
}

:deep(.content-cell .el-input),
:deep(.content-cell .el-textarea) {
  width: 100% !important;
}

:deep(.custom-table .content-column .cell) {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  line-height: 1.5 !important;
  padding: 12px 8px !important;
  overflow: visible !important;
}

/* 7. 特殊模板样式 */
/* 民事一审判决结果列左对齐 */
.civil_judgement_1 .custom-table .content-column {
  text-align: left !important;
}

.civil_judgement_1 .custom-table .content-column .cell {
  text-align: left !important;
  justify-content: flex-start !important;
  padding-left: 12px !important;
}

.civil_judgement_1 .custom-table .content-column .content-cell {
  text-align: left !important;
}

.civil_judgement_1 .custom-table .content-column .content-cell .el-input__inner {
  text-align: left !important;
}

/* 民事一审判决书表格边框颜色统一 */
.civil_judgement_1 .custom-table {
  border: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table {
  border: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table__header th {
  border-color: #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table__body td {
  border-color: #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table__border-bottom-patch {
  border-color: #fff !important;
}

.civil_judgement_1 .custom-table .el-table__border-right-patch {
  border-color: #fff !important;
}

/* 确保表格内部所有边框都使用统一颜色 */
.civil_judgement_1 .custom-table .el-table--border {
  border: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table--border .el-table__cell {
  border-right: 1px solid #fff !important;
}

.civil_judgement_1 .custom-table .el-table--border .el-table__header th:last-child,
.civil_judgement_1 .custom-table .el-table--border .el-table__body td:last-child {
  border-right: none !important;
}

/* 表格行边框 */
.civil_judgement_1 .custom-table .el-table__row {
  border-bottom: 1px solid #5a87ed !important;
}

.civil_judgement_1 .custom-table .el-table__row:last-child {
  border-bottom: none !important;
}

/* 其他模板表格边框颜色统一 */
.civil_judgement_2_reject .custom-table,
.civil_judgement_2_overrule .custom-table,
.civil_judgement_2_partial_overrule .custom-table {
  border: 1px solid #5a87ed !important;
}

.civil_judgement_2_reject .custom-table .el-table,
.civil_judgement_2_overrule .custom-table .el-table,
.civil_judgement_2_partial_overrule .custom-table .el-table {
  border: 1px solid #5a87ed !important;
}

.civil_judgement_2_reject .custom-table .el-table__header th,
.civil_judgement_2_overrule .custom-table .el-table__header th,
.civil_judgement_2_partial_overrule .custom-table .el-table__header th {
  border-color: #5a87ed !important;
}

.civil_judgement_2_reject .custom-table .el-table__body td,
.civil_judgement_2_overrule .custom-table .el-table__body td,
.civil_judgement_2_partial_overrule .custom-table .el-table__body td {
  border-color: #5a87ed !important;
}

.civil_judgement_2_reject .custom-table .el-table__border-bottom-patch,
.civil_judgement_2_overrule .custom-table .el-table__border-bottom-patch,
.civil_judgement_2_partial_overrule .custom-table .el-table__border-bottom-patch {
  border-color: #5a87ed !important;
}

/* 8. 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.operation-buttons .el-button {
  min-width: 25px;
  height: 25px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* 9. 响应式和滚动条样式 */
:deep(.data-table-container)::-webkit-scrollbar {
  width: 6px;
}

:deep(.data-table-container)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.data-table-container)::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 10. 部分改判表格特殊样式 */
.partial-overrule-table {
  table-layout: fixed !important;
}

:deep(.partial-overrule-table .el-table__row) {
  height: 120px !important;
  min-height: 120px !important;
  max-height: 120px !important;
  overflow: hidden !important;
}

:deep(.partial-overrule-table .el-table__row td) {
  height: 120px !important;
  min-height: 120px !important;
  max-height: 120px !important;
  vertical-align: top !important;
  padding: 5px 4px !important;
  box-sizing: border-box !important;
}

/* 11. 响应式设计 */
@media (max-width: 900px) {
  .data-table-container {
    height: 300px;
  }
}

@media (max-width: 700px) {
  .data-table-container {
    height: 250px;
  }
}

/* 12. 必要的补充样式 */
/* popover 样式 */
.popover-content {
  color: #303133;
  line-height: 1.6;
  word-break: break-all;
  font-size: 16px;
  max-height: 300px;
  overflow-y: auto;
  word-wrap: break-word;
  max-width: 100%;
  padding: 8px 0;
}

/* ==================== 结束重构样式系统 ==================== */

/* 部分改判决策选项样式 */
.decision-options {
  padding: 4px 4px;
  text-align: left;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为靠上对齐 */
  align-items: flex-start; /* 内容靠上对齐 */
}

.decision-options .option-row {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start; /* 改为靠上对齐 */
  margin-bottom: 8px; /* 增加底部间距 */
  padding-bottom: 4px; /* 增加内部底部间距 */
  height: auto; /* 高度自适应 */
  min-height: 24px; /* 增加最小高度从24px到32px */
  line-height: 1.5; /* 调整行高为相对值，提供更多垂直空间 */
}

.decision-options .option-row:last-child {
  margin-bottom: 0;
}

.decision-options .el-radio {
  margin-right: 0;
  height: 20px;
  line-height: 20px;
}

/* 确保radio按钮圆圈完整显示 */
:deep(.decision-options .el-radio__input) {
  margin-right: 6px;
  line-height: 20px;
  overflow: visible !important;
}

:deep(.decision-options .el-radio__inner) {
  width: 14px !important;
  height: 14px !important;
  border: 1px solid #dcdfe6 !important;
  box-sizing: border-box !important;
  display: inline-block !important;
  position: relative !important;
  vertical-align: middle !important;
}

:deep(.decision-options .el-radio__input.is-checked .el-radio__inner) {
  border-color: #409eff !important;
  background: #409eff !important;
}

:deep(.decision-options .el-radio__input.is-checked .el-radio__inner::after) {
  width: 4px !important;
  height: 4px !important;
  border-radius: 100% !important;
  background-color: #fff !important;
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) scale(1) !important;
  transition: transform 0.15s ease-in !important;
}

:deep(.decision-options .el-radio__label) {
  padding-left: 6px;
  font-size: 14px;
  line-height: 20px;
  color: #606266;
}

/* 强制radio按钮整体可见性 */
:deep(.decision-options .el-radio) {
  overflow: visible !important;
  display: inline-flex !important;
  align-items: center !important;
  position: relative !important;
  margin-right: 0 !important;
  white-space: nowrap !important;
}

/* 第一个option-row中的radio按钮间距 */
:deep(.decision-options .option-row:first-child .el-radio) {
  margin-right: 15px !important;
}

:deep(.decision-options .option-row:first-child .el-radio:last-child) {
  margin-right: 0 !important;
}

/* 第二个option-row（包含变更按钮和输入框）的布局 */
.decision-options .option-row:nth-child(2) {
  align-items: center;
  gap: 5px;
  min-height: 24px !important;
  overflow: hidden !important;
}

/* OnlyOffice编辑器样式 */
.onlyoffice-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.onlyoffice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.onlyoffice-header h4 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.generation-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
}

.status-icon {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.status-text {
  color: #1e40af;
  font-weight: 500;
}

.status-progress {
  color: #6b7280;
  font-size: 12px;
  margin-left: 4px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.onlyoffice-actions {
  display: flex;
  gap: 12px;
}

.onlyoffice-editor-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
}

.onlyoffice-editor {
  flex: 1;
  background: #fff;
}

/* 书签导航样式 */
.bookmark-navigation {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 0 8px 8px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  width: 50px;
  overflow: hidden;
}

.bookmark-navigation.expanded {
  width: 280px;
}

.bookmark-toggle {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s;
}


.bookmark-toggle .el-icon {
  font-size: 18px;
  margin-right: 8px;
}

.bookmark-toggle span {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
}

.bookmark-list {
  max-height: 400px;
  overflow-y: auto;
}

.bookmark-header {
  padding: 12px 16px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #909399;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bookmark-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}



.bookmark-icon {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
  flex-shrink: 0;
}

.bookmark-text {
  font-size: 13px;
  color: #303133;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



/* 标题前竖条样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom:6px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  padding-left: 10px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
}

.section-header h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background: linear-gradient(to bottom, #67c23a, #e6a23c);
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header .header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .cost-table,
  .judgment-table {
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }

  :deep(.responsive-table .el-table__cell) {
    padding: 8px 4px !important;
  }

  /* 在小屏幕下进一步减少列的最小宽度 */
  :deep(.judgment-table .el-table__body .content-column) {
    min-width: 200px !important;
  }
}

/* 响应式高度调整 */
@media (max-height: 900px) {
  .judgment-table {
    height: 300px;
  }

  .judgment-section .judgment-table {
    height: 280px;
  }
}

@media (max-height: 700px) {
  .judgment-table {
    height: 250px;
  }

  .judgment-section .judgment-table {
    height: 230px;
  }
}

@media (max-height: 600px) {
  .judgment-table {
    height: 200px;
  }

  .judgment-section .judgment-table {
    height: 180px;
  }
}

/* ==================== 统一组件hover样式系统 ==================== */

/* 定义hover样式变量 */
:root {


  --success-hover-bg: #85ce61;
  --danger-hover-bg: #f78989;

  --success-shadow: 0 2px 8px rgba(103, 194, 58, 0.15);
  --danger-shadow: 0 2px 8px rgba(245, 108, 108, 0.15);
}

/* 统一去掉输入框包装器的默认padding */
:deep(.el-input__wrapper) {
  padding: 0 !important;
}

/* 统一输入框和文本框hover效果 */
:deep(.el-input__wrapper:hover),
:deep(.el-input__inner:hover),
:deep(.el-textarea__inner:hover),
:deep(.el-select .el-input__wrapper:hover),
:deep(.el-select .el-input__inner:hover),
:deep(.el-date-editor .el-input__wrapper:hover),
:deep(.el-date-editor .el-input__inner:hover) {
  border-color: var(--hover-border-color) !important;
  box-shadow: var(--hover-shadow) !important;
  transition: var(--hover-transition) !important;
}

/* 展示模式文本hover效果 */
.judgment-result-text:hover {
  border-color: var(--hover-border-color) !important;
  box-shadow: var(--hover-shadow) !important;
}

/* 统一按钮hover效果 */
:deep(.el-button:hover) {
  box-shadow: var(--hover-shadow) !important;
  transition: var(--hover-transition) !important;
}

:deep(.el-button--primary:hover) {
  border-color: var(--primary-hover-bg) !important;
  box-shadow: var(--hover-shadow) !important;
}

:deep(.el-button--success:hover) {
  border-color: var(--success-hover-bg) !important;
  box-shadow: var(--success-shadow) !important;
}

:deep(.el-button--danger:hover) {
  border-color: var(--danger-hover-bg) !important;
  box-shadow: var(--danger-shadow) !important;
}

/* ==================== 结束统一样式系统 ==================== */

/* 删除统一的31px高度限制 */

/* 部分改判判项区块外层加滚动容器 */
/*.judgment-section {
  max-height: 800px;
  overflow-y: auto;
}*/

/* ==================== 部分改判表格输入框优化样式 ==================== */
/* 优化部分改判表格输入框，使其完美适应120px表格行高度，支持滚动 */

/* 输入框包装器 */
:deep(.partial-overrule-table .el-input__wrapper) {
  height: 100px !important;
  width: 100% !important;
  padding: 0 !important;
}

/* 文本域内部输入框 - 编辑模式 */
:deep(.partial-overrule-table .el-textarea__inner) {

  width: 100% !important;
  padding: 8px 12px !important;
  box-sizing: border-box !important;
  resize: both !important; /* 允许水平+垂直拖拽 */
  line-height: 1.4 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  font-size: 14px !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
  border: 1px solid #dcdfe6 !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
  color: #606266 !important;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
}

/* 输入框焦点样式 */
:deep(.partial-overrule-table .el-textarea__inner:focus) {
  outline: none !important;
}

/* 一审判决结果列（content-column）textarea 最大高度限制为单元格可用高度 */
:deep(.partial-overrule-table .content-column .el-textarea__inner) {
  max-height: 120px !important; /* 行高120px - 上下各10px内边距 = 100px */
}

/* ==================== 部分改判表格行高度控制 ==================== */
/* 表格行和单元格统一高度设置 */
:deep(.partial-overrule-table .el-table__row) {
  height: 120px !important;
}

:deep(.partial-overrule-table .el-table__row td) {
  height: 120px !important;
  vertical-align: top !important;
  padding: 5px 4px !important;
}

:deep(.partial-overrule-table .el-table__cell) {
  height: 120px !important;
  padding: 0 !important;
}

:deep(.partial-overrule-table .el-table__cell .cell) {
  height: 120px !important;
  display: flex !important;
  align-items: flex-start !important;
  padding: 5px 4px !important;
  overflow: visible !important;
}

/* ==================== 判决内容单元格专用样式 ==================== */
:deep(.partial-overrule-table .judgment-content-cell) {
  height: 100px !important;
  width: 100% !important;
  display: flex !important;
  align-items: stretch !important;
}

:deep(.partial-overrule-table .judgment-content-cell .el-popover__reference) {
  height: 100px !important;
  width: 100% !important;
  display: flex !important;
  align-items: stretch !important;
}

/* ==================== 表格内容显示优化 ==================== */
/* 判决结果文本显示样式优化 - 查看模式下的文本显示 */
:deep(.partial-overrule-table .judgment-result-text) {
  display: block !important; /* 块级元素，支持高度设置 */
  height: 100px !important; /* 与输入框高度一致 */
  min-height: 100px !important;
  max-height: 100px !important;
  width: 100% !important;
  padding: 8px 12px !important; /* 与输入框内边距一致 */
  box-sizing: border-box !important;
  line-height: 1.4 !important; /* 行高1.4倍，提高可读性 */
  word-wrap: break-word !important; /* 长单词自动换行 */
  white-space: pre-wrap !important; /* 保留换行符和空格，自动换行 */
  overflow-y: auto !important; /* 垂直滚动条，内容超出时显示 */
  overflow-x: hidden !important; /* 隐藏水平滚动条 */
  border: 1px solid #e4e7ed !important; /* 边框样式，与输入框类似但颜色稍浅 */
  border-radius: 4px !important; /* 圆角 */
  background-color: #f5f7fa !important; /* 浅灰背景，表示只读状态 */
  color: #606266 !important; /* 文字颜色 */
  font-size: 14px !important; /* 字体大小与输入框一致 */
  text-align: left !important;
}

/* 决策选项区域样式 - 允许内容自适应高度，靠上对齐 */
.civil_judgement_2_partial_overrule :deep(.partial-overrule-table .decision-options) {
  height: auto !important; /* 高度自适应内容 */
  min-height: 52px !important; /* 最小高度52px */
  overflow: visible !important; /* 允许内容可见，不裁剪 */
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important; /* 靠上对齐 */
  align-items: flex-start !important; /* 内容靠上对齐 */
}

/* option-row 靠上对齐 - 增加高度 */
.civil_judgement_2_partial_overrule :deep(.partial-overrule-table .decision-options .option-row) {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: flex-start !important; /* 选项行内容靠上对齐 */
  margin-bottom: 8px !important; /* 增加底部间距 */
  padding-bottom: 4px !important; /* 增加内部底部间距 */
  height: auto !important; /* 高度自适应 */
  min-height: 24px !important; /* 增加最小高度到32px */
  line-height: 1.5 !important; /* 调整行高 */
}

/* 修改内容输入框样式 - 增加输入框高度 */
:deep(.partial-overrule-table .modify-content-input .el-textarea__inner) {
  min-height: 60px !important;
  max-height: 80px !important; /* 最大高度80px，防止超出表格底部 */
  resize: vertical !important; /* 允许垂直调整大小 */
  overflow-y: auto !important; /* 超出时显示滚动条，不撑破单元格 */
}

/* 修改内容输入框包装器样式 */
:deep(.partial-overrule-table .modify-content-input .el-input__wrapper) {
  height: auto !important;
  min-height: 60px !important;
}

/* 民事二审改判表格表头样式 */
:deep(.judgment-section .judgment-table .custom-table:not(.partial-overrule-table) .el-table__header th) {
  height: 61px !important;
  padding: 8px !important;
  vertical-align: middle !important;
}

:deep(.judgment-section .judgment-table .custom-table:not(.partial-overrule-table) .el-table__header .el-table__cell) {
  height: 61px !important;
  padding: 0 !important;
}

:deep(.judgment-section .judgment-table .custom-table:not(.partial-overrule-table) .el-table__header .el-table__cell .cell) {
  height: 45px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px !important;
}

/* ==================== 部分改判表格表头样式 ==================== */
/* 通用部分改判表格表头样式 */
:deep(.partial-overrule-table .el-table__header th) {
  height: 60px !important;
  padding: 6px !important;
  vertical-align: middle !important;
}

:deep(.partial-overrule-table .el-table__header .el-table__cell) {
  height: 60px !important;
  padding: 0 !important;
}

:deep(.partial-overrule-table .el-table__header .el-table__cell .cell) {
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 6px !important;
  font-weight: 600 !important;
}


.civil_judgement_2_partial_overrule :deep(.partial-overrule-table .el-table__header .el-table__cell .cell) {
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 6px !important;
  font-weight: 600 !important;
}
/* 民事二审判决书（驳回上诉，维持原判）表格居中样式 */
.civil_judgement_2_reject :deep(.el-table),
.civil_judgement_2_reject :deep(.el-table__header th),
.civil_judgement_2_reject :deep(.el-table__body td),
.civil_judgement_2_reject :deep(.el-table__cell) {
  text-align: center !important;
  vertical-align: middle !important;
}

.civil_judgement_2_reject :deep(.el-table__cell .cell) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.civil_judgement_2_reject :deep(.el-table__cell .cell span) {
  width: 100% !important;
  display: block !important;
}

.editor-footer {
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
  bottom: -25px;
  z-index: 100;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  position: sticky;
}


/* 诉讼费用姓名列置灰样式=======开始 */
.readonly-name-cell {
  background-color: #f5f7fa !important;
  border-radius: 4px;
  opacity: 0.8;
}

.readonly-name-cell :deep(.el-input__wrapper) {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  cursor: not-allowed !important;
}

.readonly-name-cell :deep(.el-input__inner) {
  background-color: #f5f7fa !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}
/* 诉讼费用姓名列置灰样式=======结束*/

</style>