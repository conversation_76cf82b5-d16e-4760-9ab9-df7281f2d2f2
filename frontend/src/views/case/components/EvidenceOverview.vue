<template>
    <div class="evidence-overview-container">
        <!-- 证据情况内容 -->
        <div v-if="caseParties.length > 0">
            <!-- 当事人TAB页 -->
            <el-tabs v-model="activePartyTab" type="card" class="party-tabs" @tab-change="handleTabChange">
                <el-tab-pane v-for="party in partyList" :key="party.key" :label="party.label" :name="party.key">
                    <!-- 证据表格 -->
                    <div class="card-container">
                        <!-- 表格工具栏 -->
                        <div class="table-toolbar">
                            <div class="toolbar-actions">
                                <el-button type="primary" text @click="handleAddEvidence(activePartyTab)">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                    新增
                                </el-button>
                                <el-button type="danger" text @click="handleBatchDelete(party.key)"
                                    :disabled="getCurrentPartyData(party.key).length === 0">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                    批量删除
                                </el-button>


                            </div>
                        </div>

                        <!-- 表格滚动容器 -->
                        <div class="table-scroll-container">
                            <el-table :data="getCurrentPartyData(party.key)" stripe border :max-height="600"
                                size="small" style="width: 100%"
                                :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', fontSize: '13px' }"
                                :row-class-name="getRowClassName"
                                @selection-change="handleSelectionChange"
                                @row-click="handleRowClick">
                                <!-- 选择列 -->
                                <el-table-column type="selection" max-width="40" align="center" />

                                <!-- 序号列 -->
                                <el-table-column label="序号" type="index" max-width="60" align="center"
                                    :index="getRowIndex" />

                                <!-- 证据名称列 -->
                                <el-table-column label="证据名称" prop="evidenceName" max-width="140">
                                    <template #default="{ row }">
                                        <div class="evidence-name-cell">
                                            <!-- 编辑模式 -->
                                            <div v-if="editingRows[row.id!]" class="edit-content">
                                                <el-input :model-value="getEditingValue(row, 'evidenceName')"
                                                    @input="updateEditingContent(row, 'evidenceName', $event)"
                                                    placeholder="请输入证据名称" size="small" />
                                            </div>
                                            <!-- 显示模式 -->
                                            <div v-else class="display-content">
                                                <el-popover placement="top" :width="160" trigger="hover" :disabled="!row.evidenceName">
                                                    <template #reference>
                                                        <span class="content-text">
                                                            {{ row.evidenceName }}
                                                        </span>
                                                    </template>
                                                    <div class="popover-content text-center">
                                                        {{ row.evidenceName }}
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 证明目的列 -->
                                <el-table-column label="证明目的" prop="evidencePurpose" max-width="200">
                                    <template #default="{ row }">
                                        <div class="evidence-purpose-cell">
                                            <!-- 编辑模式 -->
                                            <div v-if="editingRows[row.id!]" class="edit-content">
                                                <el-input :model-value="getEditingValue(row, 'evidencePurpose')"
                                                    @input="updateEditingContent(row, 'evidencePurpose', $event)"
                                                    type="textarea" :rows="1" :autosize="{ minRows: 1, maxRows: 3 }"
                                                    placeholder="请输入证明目的" size="small" />
                                            </div>
                                            <!-- 显示模式 -->
                                            <div v-else class="display-content">
                                                <el-popover placement="top" :width="300" trigger="hover" :disabled="!row.evidencePurpose">
                                                    <template #reference>
                                                        <span class="content-text">
                                                            {{ row.evidencePurpose }}
                                                        </span>
                                                    </template>
                                                    <div class="popover-content text-center">
                                                        {{ row.evidencePurpose }}
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 证据综述列 -->
                                <el-table-column label="内容摘要" prop="evidenceSummary" min-width="250">
                                    <template #default="{ row }">
                                        <div class="evidence-summary-cell">
                                            <!-- 编辑模式 -->
                                            <div v-if="editingRows[row.id!]" class="edit-content">
                                                <el-input :model-value="getEditingValue(row, 'evidenceSummary')"
                                                    @input="updateEditingContent(row, 'evidenceSummary', $event)"
                                                    type="textarea" :rows="1" :autosize="{ minRows: 1, maxRows: 4 }"
                                                    placeholder="请输入证据综述" size="small" />
                                            </div>
                                            <!-- 显示模式 -->
                                            <div v-else class="display-content">
                                                <el-popover placement="top" :width="400" trigger="hover" :disabled="!row.evidenceSummary">
                                                    <template #reference>
                                                        <span class="content-text">
                                                            {{ row.evidenceSummary }}
                                                        </span>
                                                    </template>
                                                    <div class="popover-content">
                                                        {{ row.evidenceSummary }}
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 是否采纳列 -->
                                <el-table-column label="采纳" prop="adopted" width="100" align="center">
                                    <template #default="{ row }">
                                        <el-switch v-model="row.adopted" width="60"
                                            inline-prompt active-text="采纳" inactive-text="不采纳" 
                                            active-color="#67c23a" inactive-color="#f56c6c"
                                            @change="row.id! > 0 ? handleAdoptStatusChange(row) : null"
                                            :disabled="row.id! < 0" />
                                    </template>
                                </el-table-column>

                                <!-- 操作列 -->
                                <el-table-column label="操作" width="80" align="center" fixed="right">
                                    <template #default="{ row }">
                                        <!-- 新增行的操作 -->
                                        <div v-if="row.id! < 0" class="row-actions">
                                            <el-button type="primary" size="small" @click.stop="handleSaveNewEvidence(row)"
                                                :disabled="!isValidNewRow(row)">
                                                保存
                                            </el-button>
                                            <el-button size="small" @click.stop="handleCancelAdd(row)">
                                                取消
                                            </el-button>
                                        </div>
                                        <!-- 普通行的操作 -->
                                        <div v-else class="row-actions">
                                            <!-- 编辑模式 -->
                                            <template v-if="editingRows[row.id!]">
                                                <el-button type="success" size="small" @click.stop="handleSaveEdit(row)">
                                                    保存
                                                </el-button>
                                                <el-button size="small" @click.stop="handleCancelEdit(row)">
                                                    取消
                                                </el-button>
                                            </template>
                                            <!-- 显示模式 -->
                                            <template v-else>
                                                <el-button type="primary" size="small" text
                                                    @click.stop="handleStartEdit(row)">
                                                    <el-icon>
                                                        <Edit />
                                                    </el-icon>
                                                </el-button>
                                                <el-button type="danger" size="small" text
                                                    @click.stop="handleDeleteRow(row)">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                </el-button>
                                            </template>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
            <el-empty description="暂无当事人信息" :image-size="120" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { evidenceOverviewApi, type EvidenceOverviewInfo, type EvidenceOverviewRequest, type EvidenceOverviewResponse } from '@/api/evidenceOverview'
import { casePartyApi, type CasePartyTab } from '@/api/caseParty'
import {
  evidenceOverviewEvents
} from '@/utils/eventBus'
// Props
interface Props {
    caseImportId: number
}

const props = withDefaults(defineProps<Props>(), {})

// 定义事件
const emit = defineEmits<{
    detailFileJump: [evidence: Array<{
        fileId: number
        fileName: string
        pageNumber?: number
    }>]
}>()

// 响应式数据
const evidenceData = ref<{ [partyKey: string]: EvidenceOverviewInfo[] }>({}) // 按当事人分组存储证据数据
const activePartyTab = ref('')
const caseParties = ref<CasePartyTab[]>([])
const partyNameListMap = ref<{ [partyKey: string]: string[] }>({}) // 存储每个当事人的partyNameList

const selectedRows = ref<EvidenceOverviewInfo[]>([])
const editingRows = ref<{ [key: number]: boolean }>({})
const tempRowId = ref(0)
const editingContent = ref<{ [key: number]: { [field: string]: string } }>({})
const newRowData = ref<{ [key: string]: EvidenceOverviewInfo | null }>({})

// 获取编辑状态下的值
const getEditingValue = (row: EvidenceOverviewInfo, field: string): string => {
    if (editingRows.value[row.id!] && editingContent.value[row.id!]?.[field] !== undefined) {
        return editingContent.value[row.id!][field]
    }
    return row[field as keyof EvidenceOverviewInfo] as string || ''
}

// 更新编辑中的内容
const updateEditingContent = (row: EvidenceOverviewInfo, field: string, newContent: string) => {
    if (!editingContent.value[row.id!]) {
        editingContent.value[row.id!] = {}
    }
    editingContent.value[row.id!][field] = newContent
}

// 计算属性 - 当事人列表
const partyList = computed(() => {
    if (caseParties.value.length === 0) {
        return []
    }

    const parties: Array<{ key: string; label: string; sortOrder: number }> = []

    // 定义当事人类型优先级（现在直接使用中文）
    const partyTypeOrder: { [key: string]: number } = {
        '原告': 1,
        '被告': 2,
        '第三方证人': 3,
        '第三人': 4,
        '代理人': 5
    }

    // 根据当事人接口返回的数据生成tab列表
    caseParties.value.forEach((party, index) => {
        const sortOrder = (partyTypeOrder[party.partyType] || 999) * 1000 + index + 1
        const partyKey = `${party.partyType}-${party.partyNameString}`
        
        // 存储partyNameList数据
        partyNameListMap.value[partyKey] = party.partyNameList || []
        
        parties.push({
            key: partyKey,
            label: `${party.partyType}：${party.partyNameString}`,
            sortOrder
        })
    })

    // 按优先级排序
    return parties.sort((a, b) => a.sortOrder - b.sortOrder)
})

// 获取当前当事人的证据数据
const getCurrentPartyData = (partyKey: string) => {
    // 从按当事人分组的数据中获取
    const existingData = evidenceData.value[partyKey] || []

    // 如果有新增行数据，添加到列表末尾
    const newRow = newRowData.value[partyKey]
    if (newRow) {
        return [...existingData, newRow]
    }

    return existingData
}

// 获取行索引（从1开始）
const getRowIndex = (index: number) => {
    return index + 1
}

// 转换后端响应数据为前端数据格式
const transformResponseToFrontend = (responses: EvidenceOverviewResponse[]): EvidenceOverviewInfo[] => {
    return responses.map(response => ({
        id: response.id,
        caseImportId: response.caseImportId,
        partyType: response.partyType || '未知', // 后端现在直接返回中文，如果没有则默认为原告
        partyName: response.partyName || '未知', // 使用后端返回的值，如果没有则默认为未知
        evidenceName: response.evidenceName,
        evidencePurpose: response.evidencePurpose || '',
        evidenceSummary: response.evidenceSummary || '',
        originalFileId: response.originalFileId,
        originalFileName: response.originalFileName,
        originalFileStartPage: response.originalFileStartPage,
        adopted: response.adopted,
        isAdopt: response.adopted ? 1 : 0, // 兼容性转换
        createTime: response.createTime,
        updateTime: response.updateTime
    }))
}

// 转换前端数据为后端请求格式
const transformFrontendToRequest = (info: EvidenceOverviewInfo): EvidenceOverviewRequest => {
    return {
        id: info.id,
        caseImportId: info.caseImportId,
        partyType: info.partyType,
        partyName: info.partyName,
        evidenceName: info.evidenceName,
        evidencePurpose: info.evidencePurpose,
        evidenceSummary: info.evidenceSummary,
        adopted: info.adopted !== undefined ? info.adopted : (info.isAdopt === 1)
    }
}

// 加载当事人数据
const loadCaseParties = async () => {
    try {
        const parties = await casePartyApi.listTab(props.caseImportId)
        console.log('当事人数据:', parties)
        caseParties.value = parties || []
    } catch (error) {
        console.error('加载当事人数据失败:', error)
        ElMessage.error('加载当事人数据失败')
        caseParties.value = []
    }
}

// 加载指定当事人的证据数据
const loadEvidenceDataForParty = async (partyKey: string) => {
    try {
        // 获取该当事人的partyNameList
        const partyNameList = partyNameListMap.value[partyKey] || []
        
        if (partyNameList.length === 0) {
            console.warn(`当事人 ${partyKey} 的partyNameList为空`)
            evidenceData.value[partyKey] = []
            return
        }
        
        // 使用partyNameList调用API获取证据数据
        const responses = await evidenceOverviewApi.listByCaseImportId(props.caseImportId, partyNameList)
        if (responses && responses.length > 0) {
            evidenceData.value[partyKey] = transformResponseToFrontend(responses)
        } else {
            evidenceData.value[partyKey] = []
        }
    } catch (error) {
        console.error(`加载当事人 ${partyKey} 的证据数据失败:`, error)
        evidenceData.value[partyKey] = []
    }
}

// 加载证据情况数据（保持向后兼容）
const loadEvidenceData = async () => {
    // 只加载当前激活的当事人数据
    if (activePartyTab.value) {
        await loadEvidenceDataForParty(activePartyTab.value)
    }
}

// 首次进入时加载当事人和证据情况
const initialLoad = async () => {
    await loadCaseParties()
    initActiveTab()
    // 加载第一个当事人的证据数据
    if (activePartyTab.value) {
        await loadEvidenceDataForParty(activePartyTab.value)
    }
}

// 初始化激活的tab
const initActiveTab = () => {
    if (partyList.value.length > 0 && !activePartyTab.value) {
        activePartyTab.value = partyList.value[0].key
    }
}

// 处理tab切换
const handleTabChange = async (partyKey: string) => {
    // 每次切换tab时都刷新该当事人的证据数据
    await loadEvidenceDataForParty(partyKey)
}



// 清理所有临时数据
const clearAllTempData = () => {
    editingRows.value = {}
    editingContent.value = {}
    selectedRows.value = []
    newRowData.value = {}
}

// 刷新数据只刷新当前激活的当事人证据情况
const refreshData = async () => {
    if (activePartyTab.value) {
        await loadEvidenceDataForParty(activePartyTab.value)
    }
    clearAllTempData()
}

// 处理采纳状态变更
const handleAdoptStatusChange = async (row: EvidenceOverviewInfo) => {
    try {
        const request = transformFrontendToRequest(row)
        await evidenceOverviewApi.update(request)
        evidenceOverviewEvents.updated(Number(props.caseImportId), row)
        ElMessage.success('采纳状态更新成功')
    } catch (error) {
        console.error('更新采纳状态失败:', error)
        ElMessage.error('更新采纳状态失败')
        // 恢复原状态
        row.adopted = !row.adopted
        row.isAdopt = row.adopted ? 1 : 0
    }
}



// 处理批量删除
const handleBatchDelete = async (partyKey: string) => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请选择要删除的记录')
        return
    }

    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedRows.value.length} 条证据情况信息吗？`,
            '批量删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        // 调用后端接口批量删除
        const selectedIds = selectedRows.value.map(row => row.id!).filter(id => id > 0)
        const deleteCount = selectedRows.value.length
        
        if (selectedIds.length > 0) {
            await evidenceOverviewApi.batchDelete(selectedIds)
        }

        // 从本地数据中移除
        const allSelectedIds = selectedRows.value.map(row => row.id)
        // 从当前激活的当事人数据中移除
        if (activePartyTab.value && evidenceData.value[activePartyTab.value]) {
            evidenceData.value[activePartyTab.value] = evidenceData.value[activePartyTab.value].filter(
                item => !allSelectedIds.includes(item.id)
            )
        }
        
        selectedRows.value = []
        evidenceOverviewEvents.updated(Number(props.caseImportId), { deletedCount: deleteCount })
        ElMessage.success(`成功删除 ${deleteCount} 条证据情况信息`)
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error)
            ElMessage.error('批量删除失败')
        }
    }
}

// 处理选择变化
const handleSelectionChange = (selection: EvidenceOverviewInfo[]) => {
    selectedRows.value = selection
}

// 开始编辑行
const handleStartEdit = (row: EvidenceOverviewInfo) => {
    if (!row.id) return

    editingRows.value[row.id] = true
    // 初始化编辑内容
    if (!editingContent.value[row.id]) {
        editingContent.value[row.id] = {}
    }
    editingContent.value[row.id]['evidenceName'] = row.evidenceName
    editingContent.value[row.id]['evidencePurpose'] = row.evidencePurpose
    editingContent.value[row.id]['evidenceSummary'] = row.evidenceSummary
}

// 保存编辑
const handleSaveEdit = async (row: EvidenceOverviewInfo) => {
    if (!row.id) return

    const updatedRow = {
        ...row,
        evidenceName: getEditingValue(row, 'evidenceName').trim(),
        evidencePurpose: getEditingValue(row, 'evidencePurpose').trim(),
        evidenceSummary: getEditingValue(row, 'evidenceSummary').trim()
    }

    // 验证必填字段
    if (!updatedRow.evidenceName || !updatedRow.evidencePurpose || !updatedRow.evidenceSummary) {
        ElMessage.warning('请填写完整的证据情况信息')
        return
    }

    try {
        // 调用后端接口更新
        const request = transformFrontendToRequest(updatedRow)
        const response = await evidenceOverviewApi.update(request)
        
        // 更新本地数据
        if (activePartyTab.value && evidenceData.value[activePartyTab.value]) {
            const index = evidenceData.value[activePartyTab.value].findIndex(item => item.id === row.id)
            if (index > -1) {
                evidenceData.value[activePartyTab.value][index] = {
                    ...updatedRow,
                    ...transformResponseToFrontend([response])[0]
                }
            }
        }
        evidenceOverviewEvents.updated(Number(props.caseImportId), updatedRow)

        // 清理编辑状态
        delete editingRows.value[row.id]
        delete editingContent.value[row.id]
        ElMessage.success('证据情况更新成功')
    } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error('更新失败')
    }
}

// 取消编辑
const handleCancelEdit = (row: EvidenceOverviewInfo) => {
    if (!row.id) return

    // 清理编辑状态
    delete editingRows.value[row.id]
    delete editingContent.value[row.id]
}

// 删除单行
const handleDeleteRow = async (row: EvidenceOverviewInfo) => {
    if (!row.id) return

    try {
        await ElMessageBox.confirm(
            '确定要删除这条证据情况信息吗？',
            '删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        // 调用后端接口删除
        await evidenceOverviewApi.delete(row.id)

        // 从本地数据中移除
        if (activePartyTab.value && evidenceData.value[activePartyTab.value]) {
            const index = evidenceData.value[activePartyTab.value].findIndex(item => item.id === row.id)
            if (index > -1) {
                evidenceData.value[activePartyTab.value].splice(index, 1)
            }
        }

        ElMessage.success('删除成功')
        evidenceOverviewEvents.updated(Number(props.caseImportId), row)
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
        }
    }
}

// 处理新增证据情况
const handleAddEvidence = (partyKey: string) => {
    const [partyType, partyNameString] = partyKey.split('-')
    
    // 获取该当事人的partyNameList中的第一个名称作为默认值
    const partyNameList = partyNameListMap.value[partyKey] || []
    const partyName = partyNameList.length > 0 ? partyNameList[0] : partyNameString

    // 创建临时新行数据
    const newTempId = --tempRowId.value // 使用负数作为临时ID
    const newRow: EvidenceOverviewInfo = {
        id: newTempId,
        caseImportId: Number(props.caseImportId),
        partyType,
        partyName,
        evidenceName: '',
        evidencePurpose: '',
        evidenceSummary: '',
        adopted: true, // 默认采纳
        isAdopt: 1
    }

    // 设置新行数据和编辑状态
    newRowData.value[partyKey] = newRow
    editingRows.value[newTempId] = true
    editingContent.value[newTempId] = {
        evidenceName: '',
        evidencePurpose: '',
        evidenceSummary: ''
    }
}

// 验证新行数据
const isValidNewRow = (row: EvidenceOverviewInfo): boolean => {
    const evidenceName = getEditingValue(row, 'evidenceName').trim()
    const evidencePurpose = getEditingValue(row, 'evidencePurpose').trim()
    const evidenceSummary = getEditingValue(row, 'evidenceSummary').trim()

    return !!(evidenceName && evidencePurpose && evidenceSummary)
}

// 保存新增的证据情况
const handleSaveNewEvidence = async (row: EvidenceOverviewInfo) => {
    if (!isValidNewRow(row)) {
        ElMessage.warning('请填写完整的证据情况信息')
        return
    }

    try {
        const newEvidence: EvidenceOverviewInfo = {
            caseImportId: Number(props.caseImportId),
            partyType: row.partyType,
            partyName: row.partyName,
            evidenceName: getEditingValue(row, 'evidenceName').trim(),
            evidencePurpose: getEditingValue(row, 'evidencePurpose').trim(),
            evidenceSummary: getEditingValue(row, 'evidenceSummary').trim(),
            adopted: row.adopted,
            isAdopt: row.isAdopt
        }

        // 调用后端接口创建
        const request = transformFrontendToRequest(newEvidence)
        const response = await evidenceOverviewApi.create(request)
        
        // 添加到本地数据
        const createdEvidence = {
            ...newEvidence,
            ...transformResponseToFrontend([response])[0]
        }
        
        // 找到正确的partyKey（基于当前激活的tab）
        const currentPartyKey = activePartyTab.value
        if (!evidenceData.value[currentPartyKey]) {
            evidenceData.value[currentPartyKey] = []
        }
        evidenceData.value[currentPartyKey].push(createdEvidence)

        // 清除新行数据和编辑状态
        delete newRowData.value[currentPartyKey]
        delete editingRows.value[row.id!]
        delete editingContent.value[row.id!]

        ElMessage.success('新增证据情况成功')
        evidenceOverviewEvents.updated(Number(props.caseImportId), createdEvidence)
    } catch (error) {
        console.error('新增证据情况失败:', error)
        ElMessage.error('新增证据情况失败')
    }
}

// 取消新增
const handleCancelAdd = (row: EvidenceOverviewInfo) => {
    // 清除新行数据（基于当前激活的tab）
    const currentPartyKey = activePartyTab.value
    delete newRowData.value[currentPartyKey]
    delete editingRows.value[row.id!]
    delete editingContent.value[row.id!]
}

// 处理行点击事件
const handleRowClick = (row: EvidenceOverviewInfo) => {
    // 如果该行正在编辑状态，或者是新增行，则不处理点击事件
    if (editingRows.value[row.id!] || row.id! < 0) {
        return
    }
    
    // 如果有originalFileId，则触发文件高亮
    if (row.originalFileId) {
        console.log('EvidenceOverview: 点击证据行，尝试跳转到文件:', row.originalFileName)
        
        // 发送高亮事件，父组件会切换到对应文件并高亮
        emit('detailFileJump', [{
            fileId: row.originalFileId,
            fileName: row.originalFileName,
            pageNumber: row.originalFileStartPage
        }])
    }
}

// 获取表格行的CSS类名
const getRowClassName = ({ row }: { row: EvidenceOverviewInfo }) => {
    // 如果该行正在编辑状态，或者是新增行，则不添加可点击样式
    if (editingRows.value[row.id!] || row.id! < 0) {
        return ''
    }
    
    // 如果有originalFileId，则添加可点击样式
    if (row.originalFileId) {
        return 'clickable-row'
    }
    
    return ''
}

// 组件挂载时的初始化
onMounted(() => {
    initialLoad()
})

// 暴露方法供父组件调用
defineExpose({
    refreshData: refreshData,
})
</script>

<style scoped>
.evidence-overview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.loading-container {
    padding: 20px;
}

.party-tabs {
    min-height: 300px;
}

.card-container {
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.card-container:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-container .table-toolbar {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toolbar-actions {
    display: flex;
    gap: 8px;
}

.table-scroll-container {
    flex: 0 0 auto;
    overflow: visible;
}

.table-scroll-container :deep(.el-table) {
    font-size: 13px;
    border: none;
}

.table-scroll-container :deep(.el-table .cell) {
    padding: 6px 8px;
    line-height: 1.4;
    overflow: hidden;
    position: relative;
}

/* 简化表格行样式 */
.table-scroll-container :deep(.el-table .el-table__row) {
    overflow: hidden;
    transition: height 0.2s ease;
}

.table-scroll-container :deep(.el-table th .cell) {
    font-weight: 600;
    padding: 8px 8px;
}

/* 合并表格边框样式 */
.table-scroll-container :deep(.el-table__header),
.table-scroll-container :deep(.el-table__body) {
    border-radius: 0;
}

.table-scroll-container :deep(.el-table::before) {
    display: none;
}

/* 单元格样式优化 */
.table-scroll-container :deep(.el-table__row .el-table__cell) {
    overflow: hidden;
    vertical-align: middle;
}

.table-scroll-container :deep(.el-table__row .cell) {
    overflow: hidden;
    box-sizing: border-box;
}

/* 序号列垂直居中 */
.table-scroll-container :deep(.el-table__column--selection .cell),
.table-scroll-container :deep(.el-table .el-table__column--index .cell) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 8px;
}

/* 阻止选择列点击事件冒泡 */
.table-scroll-container :deep(.el-table__column--selection .cell) {
    pointer-events: auto;
}

.table-scroll-container :deep(.el-table__column--selection .el-checkbox) {
    pointer-events: auto;
}

.edit-content {
    width: 100%;
    position: relative;
    overflow: hidden;
}

/* 优化编辑组件样式 */
.edit-content .el-input,
.edit-content .el-textarea {
    margin: 0;
}

.edit-content .el-input__inner,
.edit-content .el-textarea__inner {
    padding: 4px 8px;
    line-height: 1.4;
    border-radius: 4px;
    box-sizing: border-box;
}

.edit-content .el-textarea__inner {
    resize: none;
    overflow: hidden;
}

.display-content {
    width: 100%;
    padding: 4px;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.content-text {
    display: block;
    padding: 4px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.row-actions {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 简化按钮间距 */
.row-actions .el-button {
    margin: 0;
}

.row-actions .el-button + .el-button {
    margin-left: 4px;
}

.empty-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-toolbar {
        padding: 8px 12px;
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .toolbar-actions {
        justify-content: center;
    }
}

/* 证据内容 popover 样式优化 */
.popover-content {
    color: #303133;
    line-height: 1.6;
    white-space: normal;
    word-break: break-all;
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
    word-wrap: break-word;
    max-width: 100%;
    padding: 8px 0;
}

.text-center {
    text-align: center;
}


/* 可点击行的样式 */
:deep(.clickable-row) {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

:deep(.clickable-row:hover) {
    background-color: #f0f9ff !important;
}

:deep(.clickable-row td) {
    position: relative;
}




</style>