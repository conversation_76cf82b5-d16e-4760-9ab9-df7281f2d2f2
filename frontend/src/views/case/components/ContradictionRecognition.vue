<template>
  <div class="contradiction-recognition">
    <!-- 头部工具栏 -->
    <div class="header-toolbar">
      <div class="title-section">
        <h3 class="section-title">矛盾识别分析</h3>
        <p class="section-description">智能识别当事人之间的矛盾焦点</p>
      </div>
      
      <div class="action-buttons">
        <el-button
          type="primary"
          :loading="regenerating"
          @click="handleRegenerate"
          class="regenerate-btn"
        >
          <el-icon><Refresh /></el-icon>
          重新分析
        </el-button>
      </div>
    </div>

    <!-- 任务状态显示 -->
    <task-generating-status
      v-if="taskStatusVisible"
      :task-type="'CONTRADICTION_RECOGNITION'"
      :case-import-id="Number(caseImportId)"
      @task-completed="handleTaskCompleted"
      @task-failed="handleTaskFailed"
    />

    <!-- 加载状态 -->
    <div v-if="loading && !taskStatusVisible" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && contradictionList.length === 0 && !taskStatusVisible" class="empty-state">
      <el-empty
        description="暂无矛盾识别数据"
        :image-size="120"
      >
        <el-button type="primary" @click="handleRegenerate">开始分析</el-button>
      </el-empty>
    </div>

    <!-- 矛盾识别内容 -->
    <div v-else-if="!loading && contradictionList.length > 0" class="contradiction-content">
      <!-- 按当事人分组展示 -->
      <div v-for="party in partyGroups" :key="party.key" class="party-section">
        <div class="party-header">
          <div class="party-info">
            <el-tag
              :type="party.type === '原告' ? 'success' : party.type === '被告' ? 'warning' : 'info'"
              class="party-type-tag"
            >
              {{ party.type }}
            </el-tag>
            <span class="party-name">{{ party.name }}</span>
          </div>
          <span class="contradiction-count">{{ party.items.length }} 个矛盾点</span>
        </div>

        <!-- 矛盾列表 -->
        <div class="contradiction-list">
          <div
            v-for="(item, index) in party.items"
            :key="item.id"
            class="contradiction-item"
          >
            <div class="contradiction-header">
              <div class="contradiction-title">
                <span class="contradiction-index">{{ index + 1 }}</span>
                <h4 class="contradiction-point">{{ item?.contradictionPoint || '未知矛盾点' }}</h4>
              </div>
              <div class="contradiction-actions">
                <el-button
                  type="text"
                  @click="handleEdit(item)"
                  class="edit-btn"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  type="text"
                  class="delete-btn"
                  @click="handleDelete(item)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>

            <div class="contradiction-body">
              <!-- 内容描述 -->
              <div v-if="item?.content" class="contradiction-description">
                {{ item.content }}
              </div>
              <div v-else class="contradiction-description empty">
                暂无详细描述
              </div>
              
              <!-- 证据信息 -->
              <div v-if="item?.evidence && item.evidence.length > 0" class="evidence-section">
                <div class="evidence-header">
                  <el-tag size="small" type="info" class="evidence-tag">
                    <el-icon><Document /></el-icon>
                    相关证据 ({{ item.evidence.length }})
                  </el-tag>
                </div>
                
                <div class="evidence-list">
                  <div
                    v-for="(evidence, evidenceIndex) in item.evidence"
                    :key="evidenceIndex"
                    class="evidence-item"
                    @click="handleEvidenceClick(evidence)"
                  >
                    <div v-if="evidence" class="evidence-info">
                      <div class="evidence-file">
                        <el-icon><Document /></el-icon>
                        <span class="file-name">{{ evidence?.fileName || '未知文件' }}</span>
                        <span v-if="evidence?.pageNumber" class="page-info">第{{ evidence.pageNumber }}页</span>
                      </div>
                      
                      <div v-if="evidence?.highlight" class="evidence-highlight">
                        <div class="highlight-label">高亮内容：</div>
                        <div class="highlight-content" :title="evidence.highlight">
                          {{ evidence.highlight }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑矛盾信息"
      width="600px"
      class="edit-dialog"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="120px"
      >
        <el-form-item label="矛盾点" prop="contradictionPoint">
          <el-input
            v-model="editForm.contradictionPoint"
            placeholder="请输入矛盾点描述"
          />
        </el-form-item>
        <el-form-item label="详细描述" prop="content">
          <el-input
            v-model="editForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入详细的矛盾内容描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Edit, Delete, Document } from '@element-plus/icons-vue'
import { contradictionApi, type ContradictionRecognition } from '@/api/contradiction'
import TaskGeneratingStatus from '@/components/task-generating/TaskGeneratingStatus.vue'

// Props
interface Props {
  caseImportId: string
  fileList: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  detailHighlight: [data: any]
}>()

// 响应式数据
const loading = ref(false)
const regenerating = ref(false)
const taskStatusVisible = ref(false)
const contradictionList = ref<ContradictionRecognition[]>([])
const editDialogVisible = ref(false)
const saving = ref(false)

// 编辑表单
const editForm = reactive({
  id: null as number | null,
  contradictionPoint: '',
  content: ''
})

const editFormRef = ref()

const editRules = {
  contradictionPoint: [
    { required: true, message: '请输入矛盾点描述', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入详细描述', trigger: 'blur' }
  ]
}

// 计算属性 - 按当事人分组
const partyGroups = computed(() => {
  const groupMap = new Map<string, ContradictionRecognition[]>()
  
  // 按 type + name 分组
  contradictionList.value.forEach(item => {
    const key = `${item.type || '未知'}-${item.name || '未知当事人'}`
    if (!groupMap.has(key)) {
      groupMap.set(key, [])
    }
    groupMap.get(key)!.push(item)
  })
  
  // 转换为展示格式
  return Array.from(groupMap.entries()).map(([key, items]) => {
    const firstItem = items[0]
    return {
      key,
      type: firstItem.type || '未知',
      name: firstItem.name || '未知当事人',
      items: items.sort((a, b) => a.id - b.id) // 按ID排序
    }
  }).sort((a, b) => {
    // 原告优先，然后被告，最后其他
    if (a.type === '原告' && b.type !== '原告') return -1
    if (a.type !== '原告' && b.type === '原告') return 1
    if (a.type === '被告' && b.type !== '被告' && b.type !== '原告') return -1
    if (a.type !== '被告' && b.type === '被告' && a.type !== '原告') return 1
    return a.name.localeCompare(b.name)
  })
})

// 获取矛盾识别列表
const fetchContradictionList = async () => {
  loading.value = true
  try {
    console.log('开始获取矛盾识别数据, caseImportId:', props.caseImportId)
    
    const response = await contradictionApi.getContradictionList(props.caseImportId)
    console.log('接口响应:', response)
    
    const { data } = response
    
    if (Array.isArray(data)) {
      console.log('原始数据:', data)
      
      // 数据清理和验证 - 更强的容错能力
      contradictionList.value = data
        .filter(item => {
          const isValid = item && typeof item.id !== 'undefined'
          if (!isValid) {
            console.warn('过滤无效数据项:', item)
          }
          return isValid
        })
        .map(item => {
          // 处理可能被截断或格式错误的数据
          const processedItem = {
            id: item.id,
            caseImportId: item.caseImportId || 0,
            type: item.type || '未知',
            name: item.name || '未知当事人',
            contradictionPoint: item.contradictionPoint || item.t || '未知矛盾点', // 兼容截断的字段
            content: item.content || '暂无详细描述',
            evidence: Array.isArray(item.evidence) ? 
              item.evidence.filter(e => e && (e.fileId || e.fileName)) : [], // 放宽证据过滤条件
            createTime: item.createTime || item.ateTime || new Date().toISOString(), // 兼容截断的时间字段
            updateTime: item.updateTime || item.createTime || item.ateTime || new Date().toISOString()
          }
          
          // 清理证据数据
          processedItem.evidence = processedItem.evidence.map(e => ({
            fileId: e.fileId || 0,
            fileName: e.fileName || '未知文件',
            pageNumber: e.pageNumber,
            highlight: e.highlight || ''
          }))
          
          console.log('处理后的数据项:', processedItem)
          return processedItem
        })
      
      console.log('最终矛盾识别数据:', contradictionList.value)
      console.log('分组结果:', partyGroups.value)
    } else {
      console.warn('接口返回数据格式不正确，期望数组，实际收到:', typeof data, data)
      ElMessage.warning('接口返回数据格式不正确')
      contradictionList.value = []
    }
  } catch (error) {
    console.error('获取矛盾识别列表失败，详细错误:', error)
    
    // 更详细的错误处理
    let errorMessage = '获取矛盾识别列表失败'
    if (error && typeof error === 'object') {
      if ('message' in error) {
        errorMessage += ': ' + error.message
      }
      if ('response' in error && error.response) {
        console.error('HTTP响应错误:', error.response)
      }
    }
    
    ElMessage.error(errorMessage)
    contradictionList.value = []
  } finally {
    loading.value = false
  }
}

// 重新生成分析
const handleRegenerate = async () => {
  regenerating.value = true
  taskStatusVisible.value = true
  try {
    await contradictionApi.reanalyze(props.caseImportId)
  } catch (error) {
    console.error('重新分析失败:', error)
    ElMessage.error('重新分析失败')
    taskStatusVisible.value = false
  } finally {
    regenerating.value = false
  }
}

// 任务完成
const handleTaskCompleted = async () => {
  taskStatusVisible.value = false
  await fetchContradictionList()
  ElMessage.success('矛盾识别分析完成')
}

// 任务失败
const handleTaskFailed = (error: any) => {
  taskStatusVisible.value = false
  ElMessage.error(`矛盾识别分析失败: ${error.message || '未知错误'}`)
}

// 编辑矛盾信息
const handleEdit = (item: ContradictionRecognition) => {
  editForm.id = item.id
  editForm.contradictionPoint = item.contradictionPoint
  editForm.content = item.content
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    await editFormRef.value?.validate()
    
    if (!editForm.id) return
    
    saving.value = true
    await contradictionApi.updateContradiction(editForm.id, {
      contradictionPoint: editForm.contradictionPoint,
      content: editForm.content
    })
    
    ElMessage.success('保存成功')
    editDialogVisible.value = false
    await fetchContradictionList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 删除矛盾信息
const handleDelete = async (item: ContradictionRecognition) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除矛盾点"${item.contradictionPoint}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await contradictionApi.deleteContradiction(item.id)
    ElMessage.success('删除成功')
    await fetchContradictionList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 证据点击
const handleEvidenceClick = (evidence: any) => {
  if (evidence.fileId) {
    emit('detailHighlight', {
      fileId: evidence.fileId,
      pageNumber: evidence.pageNumber,
      highlight: evidence.highlight,
      fileName: evidence.fileName
    })
  }
}

// 对外暴露的方法
const regenerateFromParent = async () => {
  await handleRegenerate()
}

const refreshData = async () => {
  await fetchContradictionList()
}

const clearData = () => {
  contradictionList.value = []
}

defineExpose({
  regenerateFromParent,
  refreshData,
  clearData
})

// 组件挂载
onMounted(() => {
  fetchContradictionList()
})
</script>

<style scoped lang="scss">
.contradiction-recognition {
  padding: 0;
  
  .header-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(229, 229, 234, 0.3);
    
    .title-section {
      .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2328;
        margin: 0 0 4px 0;
      }
      
      .section-description {
        font-size: 14px;
        color: #656d76;
        margin: 0;
      }
    }
    
    .action-buttons {
      .regenerate-btn {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
        border: none;
        border-radius: 8px;
        
        &:hover {
          background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
        }
      }
    }
  }
  
  .loading-container {
    padding: 40px 0;
  }
  
  .empty-state {
    padding: 60px 0;
    text-align: center;
  }
  
  .contradiction-content {
    .party-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .party-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 16px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        border: 1px solid rgba(229, 229, 234, 0.3);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        
        .party-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .party-type-tag {
            font-weight: 500;
            font-size: 13px;
          }
          
          .party-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2328;
          }
        }
        
        .contradiction-count {
          font-size: 14px;
          color: #656d76;
          background: #f0f0f0;
          padding: 4px 8px;
          border-radius: 12px;
        }
      }
      
      .contradiction-list {
        .contradiction-item {
          margin-bottom: 16px;
          background: #ffffff;
          border-radius: 12px;
          border: 1px solid rgba(229, 229, 234, 0.3);
          transition: all 0.2s ease;
          overflow: hidden;
          
          &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border-color: rgba(64, 158, 255, 0.3);
          }
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .contradiction-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 20px 20px 12px 20px;
            border-bottom: 1px solid #f5f5f5;
            
            .contradiction-title {
              display: flex;
              align-items: flex-start;
              gap: 12px;
              flex: 1;
              
              .contradiction-index {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
                color: white;
                border-radius: 50%;
                font-size: 14px;
                font-weight: 600;
                flex-shrink: 0;
                margin-top: 2px;
              }
              
              .contradiction-point {
                font-size: 16px;
                font-weight: 600;
                color: #1f2328;
                margin: 0;
                line-height: 1.4;
              }
            }
            
            .contradiction-actions {
              display: flex;
              gap: 8px;
              
              .edit-btn {
                color: #409eff;
                &:hover {
                  color: #66b3ff;
                }
              }
              
              .delete-btn {
                color: #f56565;
                &:hover {
                  color: #fc8181;
                }
              }
            }
          }
          
          .contradiction-body {
            padding: 20px;
            
            .contradiction-description {
              font-size: 14px;
              color: #4a5568;
              line-height: 1.6;
              margin-bottom: 16px;
              
              &.empty {
                color: #909399;
                font-style: italic;
              }
            }
            
            .evidence-section {
              .evidence-header {
                margin-bottom: 12px;
                
                .evidence-tag {
                  background: #f0f9ff;
                  color: #0369a1;
                  border: 1px solid #bae6fd;
                }
              }
              
              .evidence-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
              }
              
              .evidence-item {
                cursor: pointer;
                padding: 12px;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
                background: #fafbfc;
                transition: all 0.2s ease;
                
                &:hover {
                  background: #f0f9ff;
                  border-color: #409eff;
                  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
                }
                
                .evidence-info {
                  .evidence-file {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;
                    
                    .el-icon {
                      color: #409eff;
                    }
                    
                    .file-name {
                      font-size: 14px;
                      color: #409eff;
                      font-weight: 500;
                      flex: 1;
                    }
                    
                    .page-info {
                      font-size: 12px;
                      color: #909399;
                      background: #f0f0f0;
                      padding: 2px 6px;
                      border-radius: 4px;
                    }
                  }
                  
                  .evidence-highlight {
                    .highlight-label {
                      font-size: 12px;
                      color: #909399;
                      margin-bottom: 4px;
                    }
                    
                    .highlight-content {
                      font-size: 13px;
                      color: #4a5568;
                      line-height: 1.5;
                      background: #ffffff;
                      padding: 8px 12px;
                      border-radius: 6px;
                      border-left: 3px solid #409eff;
                      display: -webkit-box;
                      -webkit-line-clamp: 4;
                      -webkit-box-orient: vertical;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.edit-dialog {
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>