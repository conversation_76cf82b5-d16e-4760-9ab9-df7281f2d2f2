<template>
  <div class="case-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="search-header">
        <h2 class="search-title">案件管理</h2>
        <p class="search-subtitle">查询和管理案件文件</p>
      </div>

      <div class="search-content">
        <el-form :model="searchForm" class="search-form">
          <div class="search-row">
            <div class="search-field">
              <label class="field-label">文件名</label>
              <el-input
                v-model="searchForm.fileName"
                placeholder="请输入文件名"
                clearable
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon class="input-icon"><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <div class="search-field">
              <label class="field-label">操作人</label>
              <el-input
                v-model="searchForm.operator"
                placeholder="请输入操作人"
                clearable
                class="search-input"
                @keyup.enter="handleSearch"
              />
            </div>
          </div>

          <div class="search-row">
            <div class="search-field date-field">
              <label class="field-label">操作时间</label>
              <el-date-picker
                v-model="searchForm.operateTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="date-picker"
                :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 1, 1, 23, 59, 59)
                ]"
              />
            </div>
          </div>

          <div class="search-actions">
            <el-button type="primary" :loading="loading" @click="handleSearch" class="action-btn primary-btn">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="showImportDialog" class="action-btn secondary-btn">
              <el-icon><Upload /></el-icon>
              导入文件
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 结果展示区域 -->
    <div class="result-container">
      <!-- 模型选择和批量操作区域 -->
      <div class="batch-actions">
        <el-select v-model="selectedModel" placeholder="请选择模型" style="width: 200px">
          <el-option
            v-for="item in modelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button 
          type="primary" 
          :disabled="!selectedModel || selectedRows.length === 0"
          @click="handleBatchRun"
        >
          <el-icon><VideoPlay /></el-icon>
          批量运行
        </el-button>
        <el-button type="info" @click="showPromptConfig">
          <el-icon><Setting /></el-icon>
          提示词配置
        </el-button>
        <el-button type="warning" @click="showTaskInfo">
          <el-icon><InfoFilled /></el-icon>
          任务信息
        </el-button>
      </div>

      <el-table
        :data="caseList"
        style="width: 100%"
        v-loading="loading"
        border
        stripe
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column label="序号" width="60" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="caseName" label="案号" min-width="200" show-overflow-tooltip />
        <el-table-column prop="importTime" label="导入时间" width="160" align="center" />
        <el-table-column prop="totalCount" label="文件数" width="80" align="center" />
        <el-table-column prop="importerName" label="操作人" width="100" align="center" />
        <el-table-column prop="statusText" label="状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag :type="row.importStatus === 1 ? 'success' : row.importStatus === 2 ? 'warning' : 'danger'">
              {{ row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>

    <!-- 导入模态框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入文件"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-upload
        class="upload-area"
        drag
        multiple
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        :show-file-list="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持任意格式文件上传
          </div>
        </template>
      </el-upload>

      <!-- 上传进度列表 -->
      <div class="upload-progress-list" v-if="fileList.length > 0">
        <div v-for="file in fileList" :key="file.uid" class="upload-progress-item">
          <div class="file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSizeLocal(file.size) }}</span>
          </div>
          <el-progress 
            :percentage="file.percentage || 0"
            :status="file.status === 'success' ? 'success' : file.status === 'fail' ? 'exception' : ''"
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">
            开始上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 提示词配置对话框 -->
    <el-dialog
      v-model="promptConfigVisible"
      title="提示词配置"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="prompt-config-container">
        <div class="prompt-config-main">
          <el-form :model="promptConfig" label-width="120px">
            <el-form-item label="提示词类型">
              <el-select v-model="promptConfig.type" placeholder="请选择提示词类型" @change="handlePromptTypeChange">
                <el-option label="关系图谱" value="RelationGraphHandler" />
                <el-option label="时序链" value="CaseTimelineHandler" />
                <el-option label="争议焦点" value="DisputePointsHandler" />
              </el-select>
            </el-form-item>
            <el-form-item label="提示词内容" class="prompt-content-item">
              <el-input
                v-model="promptConfig.content"
                type="textarea"
                :rows="25"
                placeholder="请输入提示词内容"
                resize="none"
                class="prompt-content-input"
              />
            </el-form-item>
          </el-form>
        </div>
        <PromptHistoryPanel
          ref="historyPanelRef"
          :prompt-key="promptConfig.type"
          :current-content="promptConfig.content"
          @view-version="handleViewVersion"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="promptConfigVisible = false">取消</el-button>
          <el-button type="primary" @click="savePromptConfig" :loading="savingPrompt">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务信息对话框 -->
    <el-dialog
      v-model="taskInfoVisible"
      title="任务信息"
      width="1000px"
    >
      <el-table
        :data="taskList"
        style="width: 100%"
        v-loading="loadingTaskInfo"
        border
        stripe
      >
        <el-table-column prop="id" label="ID" width="60" show-overflow-tooltip />
        <el-table-column prop="taskType" label="类型" width="100" />
        <el-table-column prop="modelName" label="模型" width="120" />
        <el-table-column prop="taskStatus" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="160">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="updateTime" label="更新时间" width="160" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              link
              :disabled="row.status !== '已完成'"
              @click="handleDownload(row)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { 
  Search, 
  Upload, 
  View, 
  UploadFilled, 
  VideoPlay,
  Setting,
  InfoFilled,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadFiles, UploadStatus } from 'element-plus'
import { useRouter } from 'vue-router'
import PromptHistoryPanel from '@/components/PromptHistoryPanel.vue'
import { caseApi, promptApi, type CaseImportRecord, type PageResult } from '@/api'
import { formatFileSize } from '@/utils/api-helper'

// 使用统一的类型定义
type CaseItem = CaseImportRecord

// 加载状态
const loading = ref(false)

// 搜索表单数据
const searchForm = reactive({
  fileName: '',
  operator: '',
  operateTime: null
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 案件列表数据
const caseList = ref<CaseItem[]>([])

// 导入相关
const importDialogVisible = ref(false)
const fileList = ref<UploadFiles>([])
const uploading = ref(false)

// 模型选择相关
const selectedModel = ref('')
const modelOptions = ref<{ label: string; value: string }[]>([])

// 加载模型列表
const loadModelList = async () => {
  try {
    // TODO: 需要创建对应的API
    // const result = await batchAnalysisApi.getModelList()
    // modelOptions.value = result.map((model: any) => ({
    //   label: model.modelName,
    //   value: model.modelName
    // }))
    console.log('模型列表加载功能待实现')
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取模型列表失败:', error)
  }
}

// 选中的行
const selectedRows = ref<CaseItem[]>([])

const router = useRouter()

// 提示词配置相关
const historyPanelRef = ref<InstanceType<typeof PromptHistoryPanel> | null>(null)
const promptConfigVisible = ref(false)
const savingPrompt = ref(false)
const promptConfig = reactive({
  type: 'RelationGraphHandler',
  content: ''
})

// Add debounce utility
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function (...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 处理暂存提示词
const handleStaging = debounce(async (content: string) => {
  try {
    await promptApi.staging({
      key: promptConfig.type,
      prompt: content
    })
    // 刷新历史版本列表
    historyPanelRef.value?.loadHistory()
  } catch (error) {
    console.error('暂存提示词失败:', error)
  }
}, 1000)

// 监听提示词变化
watch(() => promptConfig.content, (newVal) => {
  if (newVal) {
    handleStaging(newVal)
  }
})

// 提示词历史版本相关
interface PromptHistoryItem {
  id: number
  promptKey: string
  promptMessage: string
  promptVersion: number
  createTime: string
}

// 查看版本内容
const handleViewVersion = (version: PromptHistoryItem) => {
  historyPanelRef.value?.loadHistory()
  promptConfig.content = version.promptMessage
}

// 保存提示词配置
const savePromptConfig = async () => {
  if (!promptConfig.type || !promptConfig.content) {
    // 保留表单验证警告提示
    ElMessage.warning('请填写完整的提示词配置')
    return
  }

  savingPrompt.value = true
  try {
    // 使用全局成功提示
    await promptApi.save({
      key: promptConfig.type,
      prompt: promptConfig.content
    }, {
      showSuccess: true,
      successMessage: '保存成功'
    })
    historyPanelRef.value?.loadHistory()
  } catch (error) {
    // 全局已处理错误提示
    console.error('保存提示词失败:', error)
  } finally {
    savingPrompt.value = false
  }
}

// 任务信息相关
interface BatchAnalysisTask {
    id: Number
    taskType: string
    modelName: string
    taskStatus: string
    createTime: string
    updateTime: string
}

const taskInfoVisible = ref(false)
const loadingTaskInfo = ref(false)
const taskList = ref<BatchAnalysisTask[]>([])

// 处理搜索
const handleSearch = async () => {
  try {
    // 使用全局loading和成功提示
    const result = await caseApi.getFileList({
      page: currentPage.value,
      pageSize: pageSize.value,
      // fileName: searchForm.fileName, // 根据后端API调整字段名
      // operator: searchForm.operator,
      startTime: searchForm.operateTime?.[0],
      endTime: searchForm.operateTime?.[1]
    }, {
      loading: true,
      showSuccess: true,
      successMessage: '查询成功'
    })

    caseList.value = result.records
    total.value = result.total
  } catch (error) {
    // 全局已处理错误提示
    console.error('查询失败:', error)
  }
}

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 处理文件变化
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  file.percentage = 0
  file.status = 'ready' as UploadStatus
  fileList.value = files
}

// 处理文件上传
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    // 保留表单验证警告提示
    ElMessage.warning('请选择要上传的文件')
    return
  }

  uploading.value = true

  try {
    const files = fileList.value.map(file => file.raw as File)
    await caseApi.uploadBatch(files)

    importDialogVisible.value = false
    handleSearch() // 刷新列表
  } catch (error) {
    // 全局已处理错误提示
    console.error('上传失败:', error)
    fileList.value.forEach(file => {
      file.status = 'fail'
    })
  } finally {
    uploading.value = false
  }
}

// 处理查看
const handleView = async (row: CaseItem) => {
  try {
    // 先查询最新的案件状态
    const statusResult = await caseApi.getCaseStatus(row.id)

    // 检查文件状态
    if (statusResult.fileStatus === 0) {
      ElMessage.warning('案件文件正在后台处理中，请稍后再试')
      return
    }

    // 状态正常，跳转到详情页
    router.push(`/case/detail/${row.id}`)
  } catch (error) {
    // 如果查询状态失败，使用本地状态判断
    console.warn('查询案件状态失败，使用本地状态:', error)
    if (row.fileStatus === 0) {
      ElMessage.warning('案件文件正在后台处理中，请稍后再试')
      return
    }

    router.push(`/case/detail/${row.id}`)
  }
}

// // 处理编辑
// const handleEdit = (row: CaseItem) => {
//   // TODO: 实现编辑功能
//   ElMessage.info('编辑功能开发中')
// }

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

// 使用统一的文件大小格式化函数
const formatFileSizeLocal = (size: number | undefined) => {
  return formatFileSize(size || 0)
}

// 处理表格多选
const handleSelectionChange = (selection: CaseItem[]) => {
  selectedRows.value = selection
}

// 处理批量运行
const handleBatchRun = async () => {
  if (!selectedModel.value) {
    // 保留表单验证警告提示
    ElMessage.warning('请选择模型')
    return
  }
  if (selectedRows.value.length === 0) {
    // 保留表单验证警告提示
    ElMessage.warning('请选择要处理的文件')
    return
  }

  try {
    // TODO: 需要创建批量分析API
    // const caseImportIds = selectedRows.value.map(row => row.id)
    // await batchAnalysisApi.start({
    //   modelName: selectedModel.value,
    //   caseImportIds: caseImportIds
    // }, {
    //   loading: true,
    //   showSuccess: true,
    //   successMessage: '批量处理任务已启动'
    // })

    // 临时成功提示，等API实现后移除
    ElMessage.success('批量处理任务已启动')
    // 刷新列表
    handleSearch()
    // 刷新任务信息
    showTaskInfo()
  } catch (error) {
    // 全局已处理错误提示
    console.error('批量处理失败:', error)
  }
}

// 显示提示词配置对话框
const showPromptConfig = async () => {
  promptConfigVisible.value = true
  await handlePromptTypeChange()
}

// 处理提示词类型变更
const handlePromptTypeChange = async () => {
  promptConfig.content = ''
  try {
    const result = await promptApi.getByKey(promptConfig.type)
    if (result) {
      promptConfig.content = result
    }
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取提示词失败:', error)
  }
}

// 显示任务信息对话框
const showTaskInfo = async () => {
  taskInfoVisible.value = true
  loadingTaskInfo.value = true
  try {
    // TODO: 需要创建批量分析任务API
    // const result = await batchAnalysisApi.getTaskList()
    // taskList.value = result.map((task: any) => ({
    //   ...task,
    //   status: getTaskStatusText(task.taskStatus),
    //   progress: task.progress || 0
    // }))
    taskList.value = []
  } catch (error) {
    // 全局已处理错误提示
    console.error('获取任务信息失败:', error)
  } finally {
    loadingTaskInfo.value = false
  }
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'running':
      return '运行中'
    case 'finished':
      return '已完成'
    case 'error':
      return '失败'
    default:
      return '未知'
  }
}

// 获取任务状态对应的标签类型
const getTaskStatusType = (status: string) => {
  switch (status) {
    case '运行中':
      return 'primary'
    case '已完成':
      return 'success'
    case '失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 处理下载
const handleDownload = (row: BatchAnalysisTask) => {
  // TODO: 需要创建下载API
  // const downloadUrl = batchAnalysisApi.getDownloadUrl(row.id)
  // window.open(downloadUrl, '_blank')
  ElMessage.info('下载功能待实现')
}

// 初始化
onMounted(() => {
  handleSearch()
  loadModelList()
})
</script>

<style scoped lang="scss">
.case-container {
  padding: 32px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;

  .search-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    }

    .search-header {
      margin-bottom: 32px;
      text-align: center;

      .search-title {
        font-size: 28px;
        font-weight: 700;
        color: #1d1d1f;
        margin: 0 0 8px 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, #1d1d1f 0%, #409EFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .search-subtitle {
        font-size: 16px;
        color: #86868b;
        margin: 0;
        font-weight: 400;
        letter-spacing: -0.01em;
      }
    }

    .search-content {
      .search-form {
        .search-row {
          display: flex;
          gap: 24px;
          margin-bottom: 24px;

          @media (max-width: 768px) {
            flex-direction: column;
            gap: 16px;
          }
        }

        .search-field {
          flex: 1;

          &.date-field {
            flex: 2;
          }

          .field-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            letter-spacing: -0.01em;
          }
        }

        .search-actions {
          display: flex;
          justify-content: center;
          gap: 16px;
          margin-top: 32px;

          @media (max-width: 768px) {
            flex-direction: column;
            align-items: center;
          }
        }
      }
    }
  }

  // 苹果风格输入框样式
  :deep(.search-input) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.8);
      border: 1.5px solid rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      padding: 12px 16px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      &:hover {
        border-color: rgba(64, 158, 255, 0.3);
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }

      &.is-focus {
        border-color: #409EFF;
        background: rgba(255, 255, 255, 0.95);
        box-shadow:
          0 0 0 3px rgba(64, 158, 255, 0.1),
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }
    }

    .el-input__inner {
      color: #1d1d1f;
      font-size: 16px;
      font-weight: 400;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      letter-spacing: -0.01em;

      &::placeholder {
        color: #86868b;
        font-weight: 400;
      }
    }

    .input-icon {
      color: #86868b;
      font-size: 16px;
    }
  }

  // 苹果风格日期选择器样式
  :deep(.date-picker) {
    width: 100%;

    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.8);
      border: 1.5px solid rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      padding: 12px 16px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      &:hover {
        border-color: rgba(64, 158, 255, 0.3);
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }

      &.is-focus {
        border-color: #409EFF;
        background: rgba(255, 255, 255, 0.95);
        box-shadow:
          0 0 0 3px rgba(64, 158, 255, 0.1),
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }
    }

    .el-input__inner {
      color: #1d1d1f;
      font-size: 16px;
      font-weight: 400;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      letter-spacing: -0.01em;
    }
  }

  // 苹果风格按钮样式
  .action-btn {
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    letter-spacing: -0.01em;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &.primary-btn {
      background: linear-gradient(135deg, #409EFF 0%, #337ecc 100%);
      color: white;
      box-shadow:
        0 4px 12px rgba(64, 158, 255, 0.3),
        0 2px 4px rgba(64, 158, 255, 0.2);

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          0 8px 20px rgba(64, 158, 255, 0.4),
          0 4px 8px rgba(64, 158, 255, 0.3);
        background: linear-gradient(135deg, #337ecc 0%, #2c6bb3 100%);
      }

      &:active {
        transform: translateY(0);
        box-shadow:
          0 2px 8px rgba(64, 158, 255, 0.3),
          0 1px 4px rgba(64, 158, 255, 0.2);
      }
    }

    &.secondary-btn {
      background: rgba(255, 255, 255, 0.9);
      color: #409EFF;
      border: 1.5px solid rgba(64, 158, 255, 0.2);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);

      &:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(64, 158, 255, 0.4);
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }

      &:active {
        transform: translateY(0);
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.04),
          inset 0 1px 0 rgba(255, 255, 255, 0.6);
      }
    }
  }

  .result-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 32px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    
    .batch-actions {
      margin-bottom: 32px;
      display: flex;
      gap: 16px;
      align-items: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }

      :deep(.el-select) {
        .el-input__wrapper {
          background: rgba(255, 255, 255, 0.8);
          border: 1.5px solid rgba(0, 0, 0, 0.1);
          border-radius: 10px;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            border-color: rgba(64, 158, 255, 0.3);
            background: rgba(255, 255, 255, 0.9);
          }

          &.is-focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
          }
        }
      }

      .el-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;

        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF 0%, #337ecc 100%);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }

          &:disabled {
            background: #c0c4cc;
            transform: none;
            box-shadow: none;
          }
        }

        &.el-button--info {
          background: rgba(144, 147, 153, 0.1);
          color: #909399;
          border: 1px solid rgba(144, 147, 153, 0.2);

          &:hover {
            background: rgba(144, 147, 153, 0.2);
            transform: translateY(-1px);
          }
        }

        &.el-button--warning {
          background: rgba(230, 162, 60, 0.1);
          color: #e6a23c;
          border: 1px solid rgba(230, 162, 60, 0.2);

          &:hover {
            background: rgba(230, 162, 60, 0.2);
            transform: translateY(-1px);
          }
        }
      }
    }
    
    :deep(.el-table) {
      width: 100% !important;
      
      .el-table__body-wrapper {
        overflow-x: hidden;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .upload-area {
    width: 100%;
    
    :deep(.el-upload-dragger) {
      width: 100%;
    }
  }
  
  .upload-progress-list {
    margin-top: 20px;
    
    .upload-progress-item {
      margin-bottom: 10px;
      
      .file-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        
        .file-name {
          color: #606266;
        }
        
        .file-size {
          color: #909399;
        }
      }
    }
  }
  
  .prompt-config-container {
    display: flex;
    gap: 20px;
    
    .prompt-config-main {
      flex: 1;
      min-width: 0; // 防止flex子项溢出

      .prompt-content-item {
        margin-bottom: 0;
      }

      .prompt-content-input {
        :deep(.el-textarea__inner) {
          height: 500px !important;
        }
      }
    }
    
    .prompt-config-history {
      width: 400px;
      border-left: 1px solid #dcdfe6;
      padding-left: 20px;
      
      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #ebeef5;
        
        h4 {
          margin: 0;
          font-size: 16px;
          color: #303133;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #409eff;
            border-radius: 2px;
          }
        }

        .history-actions {
          display: flex;
          gap: 8px;
        }
      }

      :deep(.el-table) {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        
        .el-table__header {
          th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
            height: 44px;
          }
        }

        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }

        .el-button {
          padding: 4px 8px;
          font-size: 13px;
        }

        .el-table__cell {
          padding: 8px 0;
        }

        .el-table__body-wrapper {
          overflow-x: hidden;
        }
      }
    }
  }

  :deep(.el-dialog) {
    .el-table {
      width: 100% !important;
      
      .el-table__body-wrapper,
      .el-table__header-wrapper {
        overflow-x: hidden;
      }

      .el-table__header,
      .el-table__body {
        width: 100% !important;
      }

      .el-table__header th {
        background-color: #f5f7fa;
      }
    }
  }
}
</style> 