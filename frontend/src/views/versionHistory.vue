<template>
  <div class="version-history-page">
    <div class="content-layout">
      <!-- 左侧：变更记录 -->
      <el-card class="change-log-card">
        <template #header>
          <div class="card-header">
            <span>变更记录</span>
          </div>
        </template>
        
        <div class="change-log-content">
          <el-empty description="暂无变更记录" />
        </div>
      </el-card>
      
      <!-- 右侧：构建信息 -->
      <div class="right-column">
        <!-- 后台构建信息 -->
        <el-card class="build-info-card backend-build">
          <template #header>
            <div class="card-header">
              <span>后台构建信息</span>
            </div>
          </template>
          
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="8" animated />
          </div>
          
          <div v-else-if="versionInfo" class="build-info-content">
            <pre class="build-text">{{ versionInfo }}</pre>
          </div>
          
          <div v-else class="empty-state">
            <el-empty description="暂无构建信息" />
          </div>
        </el-card>
        
        <!-- 前端构建信息 -->
        <el-card class="build-info-card frontend-build">
          <template #header>
            <div class="card-header">
              <span>前端构建信息</span>
            </div>
          </template>
          
          <div class="build-info-content">
            <pre class="build-text">{{ frontendBuildInfo }}</pre>
          </div>
        </el-card>
        
        <!-- 系统与浏览器信息 -->
        <el-card class="build-info-card system-info">
          <template #header>
            <div class="card-header">
              <span>系统与浏览器信息</span>
            </div>
          </template>
          
          <div class="build-info-content">
            <div class="info-item">
              <span class="info-label">操作系统:</span>
              <span class="info-value">{{ systemInfo.os }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">浏览器:</span>
              <span class="info-value">{{ systemInfo.browserName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Chrome 内核完整版本:</span>
              <span class="info-value">{{ systemInfo.chromeVersion || '非 Chrome 浏览器' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Chrome 内核版本是否低于 {{ systemInfo.recommendedVersion }}:</span>
              <span class="info-value">{{ systemInfo.isChromeBelowRecommended === null ? '非 Chrome 浏览器' : (systemInfo.isChromeBelowRecommended ? '是' : '否') }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { versionApi } from '@/api/version';
import {
  getOS,
  getBrowserName,
  getChromeVersion,
  isChromeVersionBelow
} from '@/utils/systemDetector';

// 定义响应式数据
const versionInfo = ref<string>('');
const loading = ref<boolean>(false);
const frontendBuildInfo = ref<string>('');

// 系统信息
const systemInfo = reactive({
  os: '检测中...',
  browserName: '检测中...',
  chromeVersion: null as string | null,
  isChromeBelowRecommended: null as boolean | null,
  recommendedVersion: '获取中...' as string
});

// 获取前端构建信息
const fetchFrontendBuildInfo = async () => {
  try {
    const response = await fetch('/log.txt');
    if (response.ok) {
      frontendBuildInfo.value = await response.text();
    } else {
      frontendBuildInfo.value = '无法获取构建信息';
    }
  } catch (error) {
    console.error('获取前端构建信息失败:', error);
    frontendBuildInfo.value = '获取构建信息时发生错误';
  }
};

// 获取版本信息
const fetchVersionInfo = async () => {
  try {
    loading.value = true;
    versionInfo.value = await versionApi.getVersionInfo();
  } catch (error) {
    console.error('获取版本信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取系统信息
const fetchSystemInfo = async () => {
  systemInfo.os = getOS();
  systemInfo.browserName = getBrowserName();
  systemInfo.chromeVersion = getChromeVersion();
  
  // 从后端获取推荐版本号
  try {
    const os = getOS();
    const chromeInfo = await versionApi.getChromeRecommendation(os);
    systemInfo.recommendedVersion = chromeInfo.version;
    
    // 比较当前版本是否低于推荐版本
    systemInfo.isChromeBelowRecommended = isChromeVersionBelow(chromeInfo.version);
  } catch (error) {
    console.error('获取Chrome推荐版本失败:', error);
    systemInfo.recommendedVersion = '86.0.4240.198'; // 默认版本号
    systemInfo.isChromeBelowRecommended = isChromeVersionBelow("86.0.4240.198");
  }
};

// 刷新所有信息
const refreshAll = async () => {
  fetchVersionInfo();
  fetchFrontendBuildInfo();
  await fetchSystemInfo();
};

// 组件挂载时获取版本信息
onMounted(async () => {
  fetchVersionInfo();
  fetchFrontendBuildInfo();
  await fetchSystemInfo();
});
</script>

<style scoped>
.version-history-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-layout {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0; /* 允许子元素收缩 */
}

.change-log-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  height: 100%;
}

.right-column {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.build-info-card, .system-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  height: 33.33%; /* 每个卡片占据三分之一高度 */
}

.backend-build {
  flex: 1;
}

.frontend-build {
  flex: 1;
}

:deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  height: 100%;
}

.change-log-content,
.build-info-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  height: 100%;
}

.info-item {
  margin-bottom: 15px;
  line-height: 1.5;
}

.info-label {
  font-weight: bold;
  display: inline-block;
  width: 200px;
  vertical-align: top;
}

.info-value {
  display: inline-block;
  width: calc(100% - 210px);
  word-break: break-all;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.loading-container {
  flex: 1;
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.build-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  color: #333;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
  }
  
  .change-log-card {
    flex: none;
    height: auto;
  }
  
  .right-column {
    width: 100%;
    height: auto;
  }
  
  .build-info-card, .system-info {
    height: auto;
  }
  
  .info-label {
    width: 100%;
    display: block;
    margin-bottom: 5px;
  }
  
  .info-value {
    width: 100%;
    display: block;
  }
}
</style>