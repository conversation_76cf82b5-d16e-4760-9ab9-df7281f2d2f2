import CryptoJS from 'crypto-js';

// 加密密钥，建议使用环境变量存储
const SECRET_KEY = 'smxz-fayuan';

// 性能日志开关
const ENABLE_PERFORMANCE_LOG = true;

/**
 * 将字符串转换为 URL 安全的格式
 * @param str 要转换的字符串
 * @returns URL 安全的字符串
 */
function toUrlSafe(str: string): string {
  return str.replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * 将 URL 安全的字符串转换回原始格式
 * @param str URL 安全的字符串
 * @returns 原始字符串
 */
function fromUrlSafe(str: string): string {
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  while (str.length % 4) str += '=';
  return str;
}

/**
 * 加密数据
 * @param data 要加密的数据
 * @returns 加密后的 URL 安全字符串
 */
export function encrypt(data: string): string {
  const startTime = performance.now();
  
  const encrypted = CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
  const result = toUrlSafe(encrypted);
  
  if (ENABLE_PERFORMANCE_LOG) {
    const endTime = performance.now();
    const duration = (endTime - startTime).toFixed(2);
    console.log(`[Crypto] encrypt() - 数据长度: ${data.length} 字符, 耗时: ${duration}ms`);
  }
  
  return result;
}

/**
 * 解密数据
 * @param encryptedData 加密后的 URL 安全字符串
 * @returns 解密后的原始数据
 */
export function decrypt(encryptedData: string): string {
  const startTime = performance.now();
  
  const originalEncrypted = fromUrlSafe(encryptedData);
  const bytes = CryptoJS.AES.decrypt(originalEncrypted, SECRET_KEY);
  const result = bytes.toString(CryptoJS.enc.Utf8);
  
  if (ENABLE_PERFORMANCE_LOG) {
    const endTime = performance.now();
    const duration = (endTime - startTime).toFixed(2);
    console.log(`[Crypto] decrypt() - 加密数据长度: ${encryptedData.length} 字符, 解密后长度: ${result.length} 字符, 耗时: ${duration}ms`);
  }
  
  return result;
} 