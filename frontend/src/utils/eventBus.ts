import mitt from 'mitt'

/**
 * 模块更新事件类型
 */
export interface ModuleUpdateEvents {
  // 证据情况模块更新事件
  'evidence-overview:created': { caseImportId: number; data: any }
  'evidence-overview:updated': { caseImportId: number; data: any }
  'evidence-overview:deleted': { caseImportId: number; id: number }
  
  // 质证情况模块更新事件
  'verification-evidence:created': { caseImportId: number; data: any }
  'verification-evidence:updated': { caseImportId: number; data: any }
  'verification-evidence:deleted': { caseImportId: number; id: number }
  
  // 认定事实模块更新事件
  'determine-facts:generated': { caseImportId: number }
  'determine-facts:updated': { caseImportId: number }
  
  // 无争议事实模块更新事件
  'undisputed-facts:generated': { caseImportId: number }
  'undisputed-facts:updated': { caseImportId: number }
  
  // 争议焦点模块更新事件
  'dispute-focus:generated': { caseImportId: number }
  'dispute-focus:updated': { caseImportId: number }
  
  // 当事人分析模块更新事件
  'case-party:analyzed': { caseImportId: number }
  'case-party:updated': { caseImportId: number }
  
  // 通用模块更新事件
  'module:updated': { caseImportId: number; moduleCode: string; action: string }
  
  // 红点重置事件
  'red-dot:reset': { caseImportId: number; sourceModule: string; operatedModule: string }
  
  // 添加索引签名以满足Record<EventType, unknown>约束
  [key: string | symbol]: unknown
}

/**
 * 全局事件总线
 */
export const eventBus = mitt<ModuleUpdateEvents>()

/**
 * 发布证据情况相关事件的工具函数
 */
export const evidenceOverviewEvents = {
  created: (caseImportId: number, data: any) => {
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'EVIDENCE_OVERVIEW', action: 'created' })
  },
  
  updated: (caseImportId: number, data: any) => {
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'EVIDENCE_OVERVIEW', action: 'updated' })
  },
  
  deleted: (caseImportId: number, id: number) => {
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'EVIDENCE_OVERVIEW', action: 'deleted' })
  }
}

/**
 * 发布质证情况相关事件的工具函数
 */
export const verificationEvidenceEvents = {
  created: (caseImportId: number, data: any) => {
    eventBus.emit('verification-evidence:created', { caseImportId, data })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'VERIFICATION_EVIDENCE', action: 'created' })
  },
  
  updated: (caseImportId: number, data: any) => {
    eventBus.emit('verification-evidence:updated', { caseImportId, data })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'VERIFICATION_EVIDENCE', action: 'updated' })
  },
  
  deleted: (caseImportId: number, id: number) => {
    eventBus.emit('verification-evidence:deleted', { caseImportId, id })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'VERIFICATION_EVIDENCE', action: 'deleted' })
  }
}

/**
 * 发布认定事实相关事件的工具函数
 */
export const determineFactsEvents = {
  generated: (caseImportId: number) => {
    eventBus.emit('determine-facts:generated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'DETERMINE_FACTS', action: 'generated' })
  },
  
  updated: (caseImportId: number) => {
    eventBus.emit('determine-facts:updated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'DETERMINE_FACTS', action: 'updated' })
  }
}

/**
 * 发布无争议事实相关事件的工具函数
 */
export const undisputedFactsEvents = {
  generated: (caseImportId: number) => {
    eventBus.emit('undisputed-facts:generated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'UNDISPUTED_FACTS', action: 'generated' })
  },
  
  updated: (caseImportId: number) => {
    eventBus.emit('undisputed-facts:updated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'UNDISPUTED_FACTS', action: 'updated' })
  }
}

/**
 * 发布争议焦点相关事件的工具函数
 */
export const disputeFocusEvents = {
  generated: (caseImportId: number) => {
    eventBus.emit('dispute-focus:generated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'DISPUTE_FOCUS', action: 'generated' })
  },
  
  updated: (caseImportId: number) => {
    eventBus.emit('dispute-focus:updated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'DISPUTE_FOCUS', action: 'updated' })
  }
}

/**
 * 发布当事人分析相关事件的工具函数
 */
export const casePartyEvents = {
  analyzed: (caseImportId: number) => {
    eventBus.emit('case-party:analyzed', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'CASE_PARTY', action: 'analyzed' })
  },
  
  updated: (caseImportId: number) => {
    eventBus.emit('case-party:updated', { caseImportId })
    eventBus.emit('module:updated', { caseImportId, moduleCode: 'CASE_PARTY', action: 'updated' })
  }
}
