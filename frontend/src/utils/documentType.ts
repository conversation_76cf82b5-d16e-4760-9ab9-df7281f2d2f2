/**
 * 文档类型相关工具函数
 */

/**
 * 判断是否应该使用PDF预览器
 * 统一使用图片预览，不再根据文档类型判断
 * @param documentType 文档类型
 * @returns 是否使用PDF预览器
 */
export function shouldUsePdfViewer(documentType: string | null | undefined): boolean {
  return false // 统一使用图片预览
}

/**
 * 判断是否应该使用图片预览器（DocumentViewer）
 * 统一使用图片预览器
 * @param documentType 文档类型
 * @returns 是否使用图片预览器
 */
export function shouldUseImageViewer(documentType: string | null | undefined): boolean {
  return true // 统一使用图片预览
}

/**
 * 获取预览器类型
 * @param documentType 文档类型
 * @returns 预览器类型：'pdf' | 'image'
 */
export function getViewerType(documentType: string | null | undefined): 'pdf' | 'image' {
  const shouldUsePdf = shouldUsePdfViewer(documentType)
  return shouldUsePdf ? 'pdf' : 'image'
}

/**
 * 文档类型显示名称映射
 */
export const DOCUMENT_TYPE_NAMES: Record<string, string> = {
  QSZ: '起诉状',
  DBZ: '答辩状',
  TSBL: '庭审笔录',
  SSZ: '上诉状',
  ZJML: '证据目录',
  ZJ: '证据',
  FSZ: '反诉状',
  BCQSYJ: '补充起诉意见',
  BCDBYJ: '补充答辩意见',
  PJS: '判决书',
  YSPJS: '一审判决书',
  ESPJS: '二审判决书',
  QT: '其他类型'
}

/**
 * 获取文档类型显示名称
 * @param documentType 文档类型
 * @returns 显示名称
 */
export function getDocumentTypeName(documentType: string | null | undefined): string {
  if (!documentType) return '未知类型'
  return DOCUMENT_TYPE_NAMES[documentType] || documentType
}
