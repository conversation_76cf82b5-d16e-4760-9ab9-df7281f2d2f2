import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
import type { ApiResult, RequestConfig, ErrorInfo, ResultCode } from '@/types/api';
import { useUserStore } from '@/stores/userStore';
import router from '@/router';

/**
 * HTTP客户端类
 */
class HttpClient {
  private instance: AxiosInstance;
  private loadingInstance: any = null;
  private loadingCount = 0;

  constructor() {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
      timeout: 300000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();
    
    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor() {
    this.instance.interceptors.request.use(
      (config) => {
        // 添加JWT token到请求头
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.satoken = `${token}`;
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor() {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResult>) => {
        return response;
      },
      (error) => {
        // 处理网络错误
        if (error.response) {
          // 服务器返回了错误状态码
          const { status, data } = error.response;
          
          // 处理401未授权错误
          if (status === 401) {
            // 如果不在登录页面，说明是token过期，需要清除状态并跳转
            if (window.location.pathname !== '/login') {
              const userStore = useUserStore();
              userStore.logout();
              router.push('/login');
            }
            
            // 无论在哪个页面，都使用服务器返回的原始错误消息
            const errorInfo: ErrorInfo = {
              code: status,
              message: data?.message || this.getErrorMessage(status),
              details: data,
            };
            return Promise.reject(errorInfo);
          }
          
          const errorInfo: ErrorInfo = {
            code: status,
            message: data?.message || this.getErrorMessage(status),
            details: data,
          };
          return Promise.reject(errorInfo);
        } else if (error.request) {
          // 请求已发出但没有收到响应
          const errorInfo: ErrorInfo = {
            code: 0,
            message: '网络连接失败，请检查网络设置',
          };
          return Promise.reject(errorInfo);
        } else {
          // 其他错误
          const errorInfo: ErrorInfo = {
            code: -1,
            message: error.message || '请求失败',
          };
          return Promise.reject(errorInfo);
        }
      }
    );
  }

  /**
   * 根据状态码获取错误消息
   */
  private getErrorMessage(status: number): string {
    const errorMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问，请重新登录',
      403: '禁止访问',
      404: '请求的资源不存在',
      405: '请求方法不允许',
      408: '请求超时',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    };
    return errorMessages[status] || `请求失败 (${status})`;
  }

  /**
   * 显示加载提示
   */
  private showLoading() {
    this.loadingCount++;
    if (this.loadingCount === 1) {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...'
      });
    }
  }

  /**
   * 隐藏加载提示
   */
  private hideLoading() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      if (this.loadingInstance) {
        this.loadingInstance.close();
        this.loadingInstance = null;
      }
    }
  }

  /**
   * 统一请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    const {
      url,
      method = 'GET',
      params,
      data,
      headers,
      timeout,
      loading = false,
      showError = true,
      showSuccess = false,
      successMessage,
      responseType,
    } = config;

    // 构建axios配置
    const axiosConfig: AxiosRequestConfig = {
      url,
      method,
      params,
      data,
      headers,
      timeout,
      responseType,
    };

    try {
      // 显示加载提示
      if (loading) {
        this.showLoading();
      }

      // 发送请求
      const response = await this.instance.request(axiosConfig);

      // 如果是blob类型，直接返回
      if (responseType === 'blob') {
        return response.data as T;
      }

      const result = response.data as ApiResult<T>;

      // 检查业务状态码
      if (result.code === 200) {
        // 显示成功提示
        if (showSuccess && (successMessage || result.message)) {
          ElMessage.success(successMessage || result.message);
        }
        return result.data;
      } else {
        // 业务错误
        const errorInfo: ErrorInfo = {
          code: result.code,
          message: result.message,
          details: result,
        };
        throw errorInfo;
      }
    } catch (error) {
      // 显示错误提示
      if (showError && error instanceof Object && 'message' in error) {
        ElMessage.error((error as ErrorInfo).message);
      }
      throw error;
    } finally {
      // 隐藏加载提示
      if (loading) {
        this.hideLoading();
      }
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'GET',
      params,
      ...config,
    });
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config,
    });
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config,
    });
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'DELETE',
      params,
      ...config,
    });
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      ...config,
    });
  }
}

// 创建并导出HTTP客户端实例
export const http = new HttpClient();
export default http;
