/**
 * 系统和浏览器检测工具
 */

/**
 * 检测当前操作系统是否为 macOS
 * @returns boolean
 */
export function isMacOS(): boolean {
  return navigator.userAgent.includes('Mac OS');
}

/**
 * 获取完整的 Chrome 内核版本信息
 * @returns string | null (如果非 Chrome 浏览器则返回 null)
 */
export function getChromeVersion(): string | null {
  const userAgent = navigator.userAgent;
  // 匹配 Chrome/Chromium 版本号 (可能包含多个部分，如 91.0.4472.124)
  console.log(userAgent);
  const chromeMatch = userAgent.match(/(?:Chrome|Chromium)\/(\d+\.\d+\.\d+(?:\.\d+)?)/);
  
  if (chromeMatch && chromeMatch[1]) {
    return chromeMatch[1];
  }
  
  return null;
}

/**
 * 获取 Chrome 内核主版本号
 * @returns number | null (如果非 Chrome 浏览器则返回 null)
 */
export function getChromeMajorVersion(): number | null {
  const fullVersion = getChromeVersion();
  
  if (fullVersion) {
    const majorVersion = fullVersion.split('.')[0];
    return parseInt(majorVersion, 10);
  }
  
  return null;
}

/**
 * 判断 Chrome 内核版本是否低于指定版本
 * @param version 要比较的版本号（可以是完整版本号字符串或主版本号数字）
 * @returns boolean | null (如果非 Chrome 浏览器则返回 null)
 */
export function isChromeVersionBelow(version: string | number): boolean | null {
  const currentVersion = getChromeVersion();
  
  if (currentVersion === null) {
    return null; // 非 Chrome 浏览器
  }
  
  // 如果传入的是数字，只比较主版本号
  if (typeof version === 'number') {
    const currentMajorVersion = getChromeMajorVersion();
    return currentMajorVersion !== null && currentMajorVersion < version;
  }
  
  // 如果传入的是字符串，比较完整版本号
  return compareVersions(currentVersion, version) < 0;
}

/**
 * 比较两个版本号
 * @param v1 版本号1
 * @param v2 版本号2
 * @returns -1: v1 < v2, 0: v1 = v2, 1: v1 > v2
 */
function compareVersions(v1: string, v2: string): number {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);
  const maxLength = Math.max(parts1.length, parts2.length);
  
  for (let i = 0; i < maxLength; i++) {
    const part1 = i < parts1.length ? parts1[i] : 0;
    const part2 = i < parts2.length ? parts2[i] : 0;
    
    if (part1 < part2) return -1;
    if (part1 > part2) return 1;
  }
  
  return 0;
}

/**
 * 获取操作系统信息
 * @returns 'Windows' | 'Linux' | 'MacOS' | 'Unknown'
 */
export function getOS(): 'Windows' | 'Linux' | 'MacOS' | 'Unknown' {
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Linux') && !userAgent.includes('Android')) return 'Linux';
  if (userAgent.includes('Mac OS')) return 'MacOS';
  return 'Unknown';
}

/**
 * 获取浏览器名称
 * @returns string
 */
export function getBrowserName(): string {
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    return 'Chrome';
  }
  
  if (userAgent.includes('Firefox')) {
    return 'Firefox';
  }
  
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    return 'Safari';
  }
  
  if (userAgent.includes('Edg')) {
    return 'Edge';
  }
  
  if (userAgent.includes('Opera') || userAgent.includes('OPR/')) {
    return 'Opera';
  }
  
  return 'Unknown';
}

export default {
  isMacOS,
  getChromeVersion,
  getChromeMajorVersion,
  isChromeVersionBelow,
  getOS,
  getBrowserName
};