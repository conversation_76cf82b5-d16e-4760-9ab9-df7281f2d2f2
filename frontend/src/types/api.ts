/**
 * 统一API响应格式
 */
export interface ApiResult<T = any> {
  /** 响应状态码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 分页查询参数
 */
export interface PageParams {
  /** 当前页码 */
  page?: number;
  /** 每页大小 */
  pageSize?: number;
}

/**
 * 分页响应数据
 */
export interface PageResult<T = any> {
  /** 当前页码 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 当前页数据 */
  records: T[];
  /** 是否有上一页 */
  hasPrevious: boolean;
  /** 是否有下一页 */
  hasNext: boolean;
}

/**
 * HTTP请求方法
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * 请求配置
 */
export interface RequestConfig {
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method?: HttpMethod;
  /** 请求参数 */
  params?: Record<string, any>;
  /** 请求体数据 */
  data?: any;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 超时时间 */
  timeout?: number;
  /** 响应类型 */
  responseType?: 'json' | 'blob' | 'text' | 'arraybuffer';
  /** 是否显示加载提示 */
  loading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
  /** 是否显示成功提示 */
  showSuccess?: boolean;
  /** 成功提示消息 */
  successMessage?: string;
}

/**
 * 响应状态码枚举
 */
export enum ResultCode {
  /** 成功 */
  SUCCESS = 200,
  /** 请求参数错误 */
  BAD_REQUEST = 400,
  /** 未授权访问 */
  UNAUTHORIZED = 401,
  /** 禁止访问 */
  FORBIDDEN = 403,
  /** 资源不存在 */
  NOT_FOUND = 404,
  /** 请求方法不允许 */
  METHOD_NOT_ALLOWED = 405,
  /** 参数校验失败 */
  VALIDATION_ERROR = 422,
  /** 服务器内部错误 */
  INTERNAL_SERVER_ERROR = 500,
  /** 服务不可用 */
  SERVICE_UNAVAILABLE = 503,
  /** 业务处理失败 */
  BUSINESS_ERROR = 1000,
  /** 文件上传失败 */
  FILE_UPLOAD_ERROR = 1001,
  /** 文件处理失败 */
  FILE_PROCESS_ERROR = 1002,
  /** AI模型调用失败 */
  AI_MODEL_ERROR = 1003,
  /** 数据不存在 */
  DATA_NOT_FOUND = 1004,
  /** 数据已存在 */
  DATA_ALREADY_EXISTS = 1005,
  /** 操作不被允许 */
  OPERATION_NOT_ALLOWED = 1006,
}

/**
 * 错误信息
 */
export interface ErrorInfo {
  /** 错误码 */
  code: number;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: any;
}
