/**
 * OCR相关类型定义
 */

/**
 * OCR坐标点
 */
export interface OcrPosition {
  x: number
  y: number
}

/**
 * OCR文字块
 */
export interface OcrTextBlock {
  text: string
  x: number
  y: number
  width: number
  height: number
  pos: OcrPosition[]
}

/**
 * OCR坐标信息
 */
export interface OcrCoordinate {
  index: number
  type: string
  text: string
  blocks: OcrTextBlock[]
}

/**
 * OCR识别结果（用于API响应）
 */
export interface OcrApiResult {
  logId: string
  errorCode: number
  errorMsg?: string
  result: OcrCoordinate[]
}

/**
 * 新版本文字块（后端返回的新结构）
 */
export interface NewTextBlock {
  /** 块标签（如：paragraph_title, text 等） */
  blockLabel: string
  /** 块内容/文本内容 */
  blockContent: string
  /** 块边界框 [x1, y1, x2, y2] */
  blockBbox: number[]
}

/**
 * 新版本OCR页面结果
 */
export interface OcrPageResult {
  /** 页码 */
  pageNumber: number
  /** 文本块列表 */
  blocks: NewTextBlock[]
}

/**
 * OCR识别结果（数据库存储的单个结果）
 */
export interface OcrResult {
  // 根据实际的StructuredOcrResponse.OcrResult结构定义
  // 这里需要根据后端的具体结构来定义
  [key: string]: any
}

/**
 * OCR图片预览（新版本结构）
 */
export interface ImagePreview {
  // 图片URL
  ocrImage?: string
  docPreprocessingImage?: string
  inputImage?: string

  // 图片尺寸信息（根据实际接口返回的字段）
  inputImageWidth?: number
  inputImageHeight?: number
  ocrImageWidth?: number
  ocrImageHeight?: number
  docImageWidth?: number
  docImageHeight?: number

  // 页面信息
  totalPages?: number
  currentPage?: number
  imageType?: string

  // 兼容性字段
  width?: number
  height?: number
  imageWidth?: number
  imageHeight?: number

  // 其他可能的字段
  [key: string]: any
}

/**
 * 统一的文字块接口（用于组件内部）
 */
export interface UnifiedTextBlock {
  /** 文本内容 */
  text: string
  /** X坐标 */
  x: number
  /** Y坐标 */
  y: number
  /** 宽度 */
  width: number
  /** 高度 */
  height: number
  /** 块标签 */
  blockLabel?: string
}

/**
 * 文字块点击事件
 */
export interface TextBlockClickEvent {
  block: OcrTextBlock
  coordinate: OcrCoordinate
  originalEvent: MouseEvent
}

/**
 * 文字块悬停事件
 */
export interface TextBlockHoverEvent {
  block: OcrTextBlock
  coordinate: OcrCoordinate
  isHover: boolean
}
