import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/userStore'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/case'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue'),
      meta: {
        title: '登录'
      }
    },
    {
      path: '/case',
      name: 'Case',
      component: () => import('../views/case/index.vue'),
      meta: {
        requiresAuth: true,
        title: '案件管理'
      }
    },
    {
      path: '/case/detail/:id',
      name: 'CaseDetail',
      component: () => import('../views/case/detail.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        title: '案件详情'
      }
    },
    {
      path: '/case/document-generation/:id',
      name: 'DocumentGenerationEditor',
      component: () => import('../views/case/components/DocumentGenerationEditor.vue'),
      meta: {
        requiresAuth: true,
        title: '文书生成'
      }
    },
    {
      path: '/debug',
      name: 'CaseList',
      component: () => import('../views/case/list.vue'),
      meta: {
        requiresAuth: true,
        title: '案件列表'
      }
    },
    {
      path: '/task',
      name: 'TaskList',
      component: () => import('../views/task/index.vue'),
      meta: {
        requiresAuth: true,
        requiredPermission: 'smxz.rmfywsscfzgj.task',
        title: '任务管理'
      }
    },
    {
      path: '/task/:id',
      name: 'TaskDetail',
      component: () => import('../views/task/detail.vue'),
      props: true,
      meta: {
        requiresAuth: true,
        requiredPermission: 'smxz.rmfywsscfzgj.task',
        title: '任务详情'
      }
    },
    {
      path: '/config',
      name: 'KnowledgeBaseConfig',
      component: () => import('../views/config/knowledgeBase.vue'),
      meta: {
        requiresAuth: true,
        title: '知识库配置'
      }
    },
    {
      path: '/version-history',
      name: 'VersionHistory',
      component: () => import('../views/versionHistory.vue'),
      meta: {
        requiresAuth: true,
        title: '版本历史'
      }
    }
  ]
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 如果访问根路径且已登录，重定向到案件页面
  if (to.path === '/' && userStore.isAuthenticated) {
    next('/case')
    return
  }

  // 如果已登录用户访问登录页面，重定向到案件页面
  if (to.path === '/login' && userStore.isAuthenticated) {
    next('/case')
    return
  }

  // 检查该路由是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查是否已登录
    if (!userStore.isAuthenticated) {
      // 未登录，跳转到登录页面，并保存当前路径用于登录后重定向
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 已登录，验证token有效性
    const isValid = await userStore.checkTokenValidity()
    if (!isValid) {
      // Token无效，跳转到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查权限
    const requiredPermission = to.meta.requiredPermission as string
    if (requiredPermission && !userStore.hasRight(requiredPermission)) {
      // 无权限访问，显示错误信息并跳转到案件页面
      ElMessage.error('您没有访问该页面的权限')
      next('/case')
      return
    }
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - ${import.meta.env.VITE_SYSTEM_TITLE}`
  }

  next()
})

export default router