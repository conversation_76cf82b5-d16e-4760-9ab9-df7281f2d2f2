import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { validateToken, type UserInfo, type UserRight } from '../api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const userRights = ref<UserRight[]>([])
  const token = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!token.value && !!userInfo.value
  })

  const rightKeys = computed(() => {
    return userRights.value.map(right => right.rightkey)
  })

  // 检查用户是否有指定权限
  const hasRight = (rightKey: string) => {
    return rightKeys.value.includes(rightKey)
  }

  // 设置用户信息
  const setUserInfo = async (info: UserInfo, permissions?: string[]) => {
    userInfo.value = info
    token.value = info.token
    
    // 保存到localStorage
    localStorage.setItem('token', info.token)
    localStorage.setItem('userInfo', JSON.stringify(info))
    
    // 同时保存到cookie，供smxz-console使用
    document.cookie = `satoken=${info.token}; path=/; max-age=86400; SameSite=Lax`
    
    // 如果提供了权限数据，直接使用；否则调用API获取
    if (permissions && permissions.length > 0) {
      // 将权限字符串数组转换为UserRight格式
      userRights.value = permissions.map((permission, index) => ({
        rightkey: permission,
        name: permission,
        order: index,
        description: permission
      }))
      
      // 保存权限到localStorage
      localStorage.setItem('userRights', JSON.stringify(userRights.value))
    }
  }



  // 从localStorage恢复用户状态
  const restoreUserState = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')
    const savedUserRights = localStorage.getItem('userRights')
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = JSON.parse(savedUserInfo)
      
      if (savedUserRights) {
        userRights.value = JSON.parse(savedUserRights)
      }
    }
  }

  // 验证token有效性
  const checkTokenValidity = async () => {
    if (!token.value) {
      return false
    }
    
    try {
      await validateToken()
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      // Token无效，清除用户状态
      logout()
      return false
    }
  }

  // 登出
  const logout = () => {
    userInfo.value = null
    userRights.value = []
    token.value = null
    
    // 清除localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('userRights')
    
    // 清除cookie
    document.cookie = 'satoken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

  }

  // 初始化store
  const init = async () => {
    restoreUserState()
    
    // 如果有token，验证其有效性
    if (token.value) {
      const isValid = await checkTokenValidity()
      if (!isValid) {
      }
    }
  }

  return {
    // 状态
    userInfo,
    userRights,
    token,
    
    // 计算属性
    isAuthenticated,
    rightKeys,
    
    // 方法
    hasRight,
    setUserInfo,

    restoreUserState,
    checkTokenValidity,
    logout,
    init
  }
})