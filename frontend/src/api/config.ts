/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 案件相关接口
  case: {
    upload: '/api/case/upload',
    uploadBatch: '/api/case/upload/batch',
    list: '/api/case/import/list',
    fileList: '/api/case/file/upload/list',
    detail: (id: string | number) => `/api/case/detail/${id}`,
    status: (id: string | number) => `/api/case/status/${id}`,
    type: (id: string | number) => `/api/case/type/${id}`,
    delete: (id: string | number) => `/api/case/${id}`,
    preprocess: (id: string | number) => `/api/case/preprocess/${id}`,
    reprocess: (id: string | number) => `/api/case/reprocess/${id}`,
    regenerateAll: (id: string | number) => `/api/case/regenerate-all/${id}`,
    chat: '/api/case/chat',
  },

  // 文件相关接口
  file: {
    uploadList: '/api/file/upload/list',
    uploadSingle: '/api/file/upload/single',
    downloadStream: '/api/file/download/stream',
    pdfContent: (id: string | number) => `/api/file/pdf/${id}`,
    delete: (id: string | number) => `/api/file/delete/${id}`,
    recycleBin: (caseImportId: string | number) => `/api/file/recycle-bin/${caseImportId}`,
    restore: (id: string | number) => `/api/file/restore/${id}`,
    permanentDelete: (id: string | number) => `/api/file/permanent/${id}`,
    clearRecycleBin: (caseImportId: string | number) => `/api/file/recycle-bin/clear/${caseImportId}`,
  },

  // 关系图谱相关接口
  relationGraph: {
    get: (id: string | number) => `/api/relationGraph/${id}/getRelationGraph`,
    regenerate: (id: string | number) => `/api/relationGraph/${id}/regenerate`,
  },

  // 案件时序链相关接口
  caseTimeline: {
    list: (caseImportId: string | number) => `/api/case/${caseImportId}/timeline`,
    delete: (caseImportId: string | number, timelineId: string | number) => `/api/case/${caseImportId}/timeline/${timelineId}`,
    update: (caseImportId: string | number, timelineId: string | number) => `/api/case/${caseImportId}/timeline/${timelineId}`,
    regenerate: (id: string | number) => `/api/case/${id}/regenerate`,
  },

  // 争议焦点相关接口
  disputeFocus: {
    get: (id: string | number) => `/api/disputeFocus/${id}/getDisputeFocus`,
    save: '/api/disputeFocus/save',
    delete: (focusId: string | number) => `/api/disputeFocus/delete/${focusId}`,
    regenerate: (id: string | number) => `/api/disputeFocus/${id}/regenerate`,
  },

  // 诉辩观点相关接口
  litigationPoints: {
    get: (id: string | number) => `/api/litigationPoints/${id}/getLitigationPoints`,
    save: '/api/litigationPoints/save',
    delete: (pointId: string | number) => `/api/litigationPoints/delete/${pointId}`,
    update: (pointId: string | number) => `/api/litigationPoints/update/${pointId}`,
    updatePoint: (pointId: string | number) => `/api/litigationPoints/updatePoint/${pointId}`,
    updatePointPair: '/api/litigationPoints/updatePointPair',
    addRelation: '/api/litigationPoints/relation/add',
    updateRelation: (relationId: string | number) => `/api/litigationPoints/relation/update/${relationId}`,
    deleteRelation: (relationId: string | number) => `/api/litigationPoints/relation/delete/${relationId}`,
    regenerate: (id: string | number) => `/api/litigationPoints/regenerate/${id}`,
  },

  // 矛盾识别相关接口
  contradiction: {
    list: '/api/contradiction/list',
    save: '/api/contradiction',
    update: '/api/contradiction',
    delete: (id: string | number) => `/api/contradiction/${id}`,
    regenerate: (id: string | number) => `/api/contradiction/${id}/regenerate`,
  },

  // 任务状态相关接口
  taskStatus: {
    getAll: (caseId: string | number) => `/api/task-status/${caseId}`,
    getByType: (caseId: string | number, taskType: string) => `/api/task-status/${caseId}/${taskType}`,
  },

  // 系统提示词相关接口
  prompt: {
    getByKey: '/api/prompt/getPromptByKey',
    save: '/api/prompt/savePrompt',
    staging: '/api/prompt/staging',
    history: '/api/prompt/history',
  },

  // 当事人相关接口
  caseParty: {
    list: '/api/caseParty/list', 
    listTab: '/api/caseParty/listTab',
    listForLegalFees: '/api/caseParty/listForLegalFees',
    regenerate: (caseImportId: string | number) => `/api/caseParty/regenerate-case-party2/${caseImportId}`,
  },

  // 文书生成相关接口
  documentGeneration: {
    list: (caseImportId: string | number) => `/api/document-generation/list/${caseImportId}`,
    create: '/api/document-generation/create',
    update: '/api/document-generation/update',
    delete: (id: string | number) => `/api/document-generation/delete/${id}`,
    generate: (caseImportId: string | number) => `/api/document-generation/generate/${caseImportId}`,
  },

  // 诉讼费相关接口
  legalFees: {
    list: '/api/legalFees/list',
    basicList: '/api/legalFees/basic/list',
    save: '/api/legalFees/save',
    saveBatch: '/api/legalFees/saveBatch',
    update: '/api/legalFees/update',
    delete: (id: string | number) => `/api/legalFees/delete/${id}`,
    deleteByCaseId: (caseImportRecordId: string | number) => `/api/legalFees/deleteByCaseId/${caseImportRecordId}`,
  },

  // 判项情况相关接口
  judgmentSituation: {
    list: '/api/judgmentSituation/list',
    listByCaseImportIdAndDocumentType: '/api/judgmentSituation/listByCaseImportIdAndDocumentCaseType',
    save: '/api/judgmentSituation/save',
    saveOrUpdateBatch: '/api/judgmentSituation/saveOrUpdateBatch',
    update: '/api/judgmentSituation/update',
    delete: (id: string | number) => `/api/judgmentSituation/delete/${id}`,
    deleteByCaseId: (caseImportRecordId: string | number) => `/api/judgmentSituation/deleteByCaseId/${caseImportRecordId}`,
  },

  // 审判组织成员相关接口
  trialOrganizationMembers: {
    list: '/api/trial-organization-members/list',
    saveBatch: '/api/trial-organization-members/saveBatch',
    getOrExtract: '/api/trial-organization-members/getOrExtract',
  },

  // 知识库配置相关接口
  knowledgeBaseConfig: {
    list: '/api/knowledge-base-config/list',
    byKey: (configKey: string) => `/api/knowledge-base-config/by-key/${configKey}`,
    detail: (id: string | number) => `/api/knowledge-base-config/${id}`,
    create: '/api/knowledge-base-config',
    update: (id: string | number) => `/api/knowledge-base-config/${id}`,
    delete: (id: string | number) => `/api/knowledge-base-config/${id}`,
    toggle: (id: string | number) => `/api/knowledge-base-config/${id}/toggle`,
    initDefault: '/api/knowledge-base-config/init-default',
  },
} as const;