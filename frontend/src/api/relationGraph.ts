import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 关系角色
 */
export interface RelationRole {
  id: string;
  name: string;
  category: string;
  description?: string;
  caseImportId: number;
}

/**
 * 关系链接
 */
export interface RelationLink {
  id: string;
  source: string;
  target: string;
  relation: string;
  description?: string;
  caseImportId: number;
}

/**
 * 关系图谱
 */
export interface RelationGraph {
  nodes: RelationRole[];
  links: RelationLink[];
}

/**
 * 关系图谱相关API
 */
export const relationGraphApi = {
  /**
   * 获取关系图谱数据
   */
  get(caseImportId: string | number): Promise<RelationGraph> {
    return http.get(API_ENDPOINTS.relationGraph.get(caseImportId), undefined, {
      loading: true,
    });
  },

  /**
   * 重新生成关系图谱
   */
  regenerate(caseImportId: string | number, prompt: string): Promise<string> {
    return http.post(API_ENDPOINTS.relationGraph.regenerate(caseImportId), undefined, {
      params: { prompt },
      loading: true,
      showSuccess: true,
      successMessage: '重新生成任务已启动',
    });
  },

  /**
   * 重新生成关系图谱分析（别名方法）
   */
  regenerateAnalysis(caseImportId: string | number): Promise<string> {
    return this.regenerate(caseImportId, '');
  },
};
