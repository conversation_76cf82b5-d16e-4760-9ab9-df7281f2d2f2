import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 时序链事件
 */
export interface TimelineEvent {
  id: number;
  eventResume: string;
  eventDescription: string;
  eventTime: string;
  evidence: Evidence[];
  caseImportId: number;
  createTime: string;
  updateTime: string;
}

export interface Evidence {
  fileId: number;
  fileName: string;
  pageNumber: number;
  highlight: string;
}

/**
 * 时序链相关API
 */
export const timelineApi = {
  /**
   * 获取时序链列表
   */
  getList(caseImportId: string | number): Promise<TimelineEvent[]> {
    return http.get(API_ENDPOINTS.caseTimeline.list(caseImportId), undefined, {
      loading: true,
    });
  },

  /**
   * 更新时序链（重新生成）
   */
  regenerate(caseImportId: string | number, prompt: string): Promise<string> {
    return http.post(API_ENDPOINTS.caseTimeline.update(caseImportId, ''), { prompt }, {
      loading: true,
      showSuccess: true,
      successMessage: '重新生成任务已启动',
    });
  },

  /**
   * 删除时序链事件
   */
  delete(caseImportId: string | number, timelineId: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.caseTimeline.delete(caseImportId, timelineId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '删除成功',
    });
  },

  /**
   * 更新时序链事件
   */
  update(caseImportId: string | number, timelineId: string | number, data: Partial<TimelineEvent>): Promise<boolean> {
    return http.put(API_ENDPOINTS.caseTimeline.update(caseImportId, timelineId), data, {
      loading: true,
      showSuccess: true,
      successMessage: '更新成功',
    });
  },

  /**
   * 创建时序链事件
   */
  create(caseImportId: string | number, data: Omit<TimelineEvent, 'id' | 'createTime' | 'updateTime'>): Promise<TimelineEvent> {
    return http.post(API_ENDPOINTS.caseTimeline.list(caseImportId), data, {
      loading: true,
      showSuccess: true,
      successMessage: '添加成功',
    });
  },

  /**
   * 重新生成时序链分析（别名方法）
   */
  regenerateAnalysis(caseImportId: string | number): Promise<string> {
    return http.post(API_ENDPOINTS.caseTimeline.regenerate(caseImportId), {
      prompt: '' // 传递空的 prompt，使用默认重新生成逻辑
    }, {
      loading: true,
      showSuccess: true,
      successMessage: '时序链重新生成任务已启动',
    });
  },
};
