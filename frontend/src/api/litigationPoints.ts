import { http } from '@/utils/http'
import { API_ENDPOINTS } from './config'

/**
 * 诉辩观点相关接口
 */

export const litigationPointsApi = {
  /**
   * 获取诉辩观点数据
   */
  get(caseImportId: string | number): Promise<LitigationPointsResponse> {
    return http.get(API_ENDPOINTS.litigationPoints.get(caseImportId), undefined, {
      loading: true,
    });
  },

  /**
   * 保存诉辩观点
   */
  save(data: SaveLitigationPointsRequest): Promise<{ success: boolean; message: string }> {
    return http.post(API_ENDPOINTS.litigationPoints.save, data, {
      loading: true,
      showSuccess: true,
      successMessage: '诉辩观点保存成功',
    });
  },

  /**
   * 删除诉辩观点
   */
  delete(pointId: string | number): Promise<{ success: boolean; message: string }> {
    return http.delete(API_ENDPOINTS.litigationPoints.delete(pointId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '观点删除成功',
    });
  },

  /**
   * 更新诉辩观点
   */
  update(pointId: string | number, data: Partial<DisputePoint>): Promise<{ success: boolean; message: string }> {
    return http.put(API_ENDPOINTS.litigationPoints.update(pointId), data, {
      loading: true,
      showSuccess: true,
      successMessage: '观点更新成功',
    });
  },

  /**
   * 更新诉辩观点（使用结构化数据）
   */
  updatePoint(pointId: string | number, data: {
    content?: string;
    evidence?: Evidence[];
    pointDate?: string;
    sortOrder?: number;
  }): Promise<{ success: boolean; message: string; data: any }> {
    return http.put(API_ENDPOINTS.litigationPoints.updatePoint(pointId), data, {
      loading: true,
      showSuccess: true,
      successMessage: '观点更新成功',
    });
  },

  /**
   * 批量更新诉辩观点对和关系
   */
  updatePointPair(data: {
    caseImportId: number;
    plaintiffPoint?: {
      id?: number;
      partyName?: string;
      content?: string;
      evidence?: Evidence[];
      pointDate?: string;
      sortOrder?: number;
      needUpdate?: boolean;
    };
    defendantPoint?: {
      id?: number;
      partyName?: string;
      content?: string;
      evidence?: Evidence[];
      pointDate?: string;
      sortOrder?: number;
      needUpdate?: boolean;
    };
    relation?: {
      id?: number;
      relationType?: string;
      plaintiffPointId?: string;
      defendantPointId?: string;
      needUpdate?: boolean;
    };
  }): Promise<{ success: boolean; message: string; data: any }> {
    return http.put(API_ENDPOINTS.litigationPoints.updatePointPair, data, {
      loading: true,
      showSuccess: true,
      successMessage: '观点更新成功',
    });
  },

  /**
   * 添加诉辩关系
   */
  addRelation(data: any): Promise<{ success: boolean }> {
    return http.post(API_ENDPOINTS.litigationPoints.addRelation, data, {
      loading: true,
    });
  },

  /**
   * 更新诉辩关系
   */
  updateRelation(relationId: string | number, data: any): Promise<{ success: boolean }> {
    return http.put(API_ENDPOINTS.litigationPoints.updateRelation(relationId), data, {
      loading: true,
    });
  },

  /**
   * 删除诉辩关系
   */
  deleteRelation(relationId: string | number): Promise<{ success: boolean }> {
    return http.delete(API_ENDPOINTS.litigationPoints.deleteRelation(relationId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '诉辩关系删除成功',
    });
  },

  /**
   * 重新生成诉辩关系
   */
  regenerate(caseImportId: string | number): Promise<string> {
    return http.post(API_ENDPOINTS.litigationPoints.regenerate(caseImportId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '诉辩关系重新生成任务已启动',
    });
  },
};

/**
 * 诉辩观点数据接口
 */
export interface DisputePoint {
  id?: number;
  side: 'plaintiff' | 'defendant';
  partyName: string;
  content: string;
  evidence?: Evidence[];
  pointDate?: string;
  sortOrder?: number;
  caseImportId: number;
  sourceId?: number;
  pointId?: string;
}

/**
 * 证据数据接口
 */
export interface Evidence {
  fileId: number;
  fileName: string;
  pageNumber?: number; // 页码变为可选，支持全文搜索
  highlight: string;
}

/**
 * 保存诉辩观点请求接口
 */
export interface SaveLitigationPointsRequest {
  caseImportId: number;
  points: {
    side: 'plaintiff' | 'defendant';
    partyName: string;
    content: string;
    evidence?: Evidence[];
    pointDate?: string;
    sortOrder?: number;
  }[];
  relation?: {
    relationType: string;
    description?: string;
    plaintiffPointId?: string;
    defendantPointId?: string;
  };
}

/**
 * 诉辩观点响应数据接口
 */
export interface LitigationPointsResponse {
  plaintiffs: PartyPointsData[];
  defendants: PartyPointsData[];
  relations: any[];
}

/**
 * 当事人观点数据接口
 */
export interface PartyPointsData {
  name: string;
  pointsByDate: {
    [date: string]: DisputePoint[];
  };
}

export default litigationPointsApi;
