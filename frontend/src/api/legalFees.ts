import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 诉讼费信息
 */
export interface LegalFees {
  id?: number;
  caseImportRecordId: number;
  casePartyId: number;
  feeType: string;
  amountPaid: number;
  amountToBear: number;
  ssdw: string;
  dsrxm: string;
}

/**
 * 包含当事人信息的诉讼费信息
 */
export interface LegalFeesWithParty {
  id?: number;
  caseImportRecordId: number;
  casePartyId: number;
  feeType: string;
  amountPaid: number;
  amountToBear: number;
  partyName: string;
  partyType: string;
}

/**
 * 诉讼费相关API
 */
export const legalFeesApi = {
  /**
   * 获取案件诉讼费列表（包含当事人信息）
   */
  list(caseImportRecordId: string | number): Promise<LegalFeesWithParty[]> {
    return http.get(API_ENDPOINTS.legalFees.list, undefined, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
    });
  },

  /**
   * 获取案件诉讼费基础列表
   */
  basicList(caseImportRecordId: string | number): Promise<LegalFees[]> {
    return http.get(API_ENDPOINTS.legalFees.basicList, undefined, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
    });
  },

  /**
   * 保存诉讼费信息
   */
  save(legalFees: LegalFees): Promise<boolean> {
    return http.post(API_ENDPOINTS.legalFees.save, legalFees, {
      loading: true,
      showSuccess: true,
      successMessage: '诉讼费信息保存成功',
    });
  },

  /**
   * 批量保存诉讼费信息
   */
  saveBatch(legalFeesList: LegalFees[]): Promise<boolean> {
    return http.post(API_ENDPOINTS.legalFees.saveBatch, legalFeesList, {
      loading: true,
      showSuccess: true,
      successMessage: '诉讼费信息批量保存成功',
    });
  },

  /**
   * 更新诉讼费信息
   */
  update(legalFees: LegalFees): Promise<boolean> {
    return http.put(API_ENDPOINTS.legalFees.update, legalFees, {
      loading: true,
      showSuccess: true,
      successMessage: '诉讼费信息更新成功',
    });
  },

  /**
   * 删除诉讼费信息
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.legalFees.delete(id), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '诉讼费信息删除成功',
    });
  },

  /**
   * 根据案件ID删除诉讼费信息
   */
  deleteByCaseId(caseImportRecordId: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.legalFees.deleteByCaseId(caseImportRecordId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '案件诉讼费信息删除成功',
    });
  },
};
