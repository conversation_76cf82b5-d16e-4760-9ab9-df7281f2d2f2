import { http } from './index'

/**
 * 分析任务记录接口
 */

/**
 * 分析任务记录类型
 */
export interface AnalysisTaskRecord {
  id: number
  caseImportId: number
  taskType: string
  taskName: string
  status: number
  createTime: string
  startTime?: string
  endTime?: string
  durationMs?: number
  errorMessage?: string
  agentTaskInfo?: AgentTaskInfo[]
}

/**
 * Agent任务信息类型
 */
export interface AgentTaskInfo {
  name: string
  state: string
  reason?: string
  result: string
  tokens?: TokenInfo
  duration?: number
  startTime?: number
  partResult?: string
  curTaskPath?: string
  subTaskResults?: AgentSubTaskResult[]
}

/**
 * Token信息类型
 */
export interface TokenInfo {
  upTokens: number
  downTokens: number
  totalTokens: number
}

/**
 * Agent子任务结果类型
 */
export interface AgentSubTaskResult {
  path: string
  name: string
  prompt: string
  result: any
  reason?: string
  tokens?: TokenInfo
  duration?: number
  startTime?: number
}

/**
 * 任务列表查询参数
 */
export interface TaskListParams {
  current?: number
  size?: number
  caseImportId?: number
  taskType?: string
  status?: number
}

/**
 * 分页结果类型 (MyBatis-Plus IPage 结构)
 */
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
  // MyBatis-Plus IPage 可能包含的其他字段
  searchCount?: boolean
  optimizeCountSql?: boolean
  isSearchCount?: boolean
  hitCount?: boolean
  countId?: string
  maxLimit?: number
}

/**
 * 获取任务详情
 */
export const getTaskDetail = (id: number) => {
  return http.get<AnalysisTaskRecord>(`/api/analysis-task/${id}`)
}

/**
 * 获取任务列表（分页）
 */
export const getTaskList = (params: TaskListParams) => {
  // 过滤掉空值参数
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined && value !== '' && value !== null)
  )
  return http.get<PageResult<AnalysisTaskRecord>>('/api/analysis-task/list', filteredParams)
}

/**
 * 删除任务记录
 */
export const deleteTask = (id: number) => {
  return http.delete(`/api/analysis-task/${id}`)
}

/**
 * 任务状态枚举
 */
export const TASK_STATUS = {
  PENDING: 0,    // 待执行
  RUNNING: 1,    // 执行中
  SUCCESS: 2,    // 执行成功
  FAILED: 3      // 执行失败
} as const

/**
 * 任务状态文本映射
 */
export const TASK_STATUS_TEXT = {
  [TASK_STATUS.PENDING]: '待执行',
  [TASK_STATUS.RUNNING]: '执行中',
  [TASK_STATUS.SUCCESS]: '执行完成',
  [TASK_STATUS.FAILED]: '执行失败'
} as const

/**
 * 任务状态标签类型映射
 */
export const TASK_STATUS_TAG_TYPE = {
  [TASK_STATUS.PENDING]: 'info',
  [TASK_STATUS.RUNNING]: 'primary',
  [TASK_STATUS.SUCCESS]: 'success',
  [TASK_STATUS.FAILED]: 'danger'
} as const

/**
 * 任务类型DTO
 */
export interface TaskTypeDTO {
  code: string
  name: string
}

/**
 * 任务类型名称映射（缓存）
 */
let taskTypeNamesCache: Record<string, string> = {}

/**
 * 获取任务类型列表
 */
export const getTaskTypes = () => {
  console.log('=== API: 调用 getTaskTypes ===')
  console.log('请求 URL: /api/analysis-task/task-types')

  const promise = http.get<TaskTypeDTO[]>('/api/analysis-task/task-types')

  promise.then(data => {
    console.log('=== API: getTaskTypes 响应成功 ===')
    console.log('返回的数据:', data)
    console.log('数据类型:', typeof data)
    console.log('是否为数组:', Array.isArray(data))
    if (Array.isArray(data)) {
      console.log('数组长度:', data.length)
      console.log('第一个元素:', data[0])
    }
  }).catch(error => {
    console.error('=== API: getTaskTypes 响应失败 ===')
    console.error('错误:', error)
  })

  return promise
}

/**
 * 初始化任务类型名称映射
 */
export const initTaskTypeNames = async (): Promise<void> => {
  try {
    const data = await getTaskTypes()
    if (data && Array.isArray(data)) {
      taskTypeNamesCache = data.reduce((acc, taskType) => {
        acc[taskType.code] = taskType.name
        return acc
      }, {} as Record<string, string>)
    }
  } catch (error) {
    console.error('初始化任务类型名称失败:', error)
  }
}

/**
 * 获取任务类型名称
 */
export const getTaskTypeName = (taskType: string): string => {
  return taskTypeNamesCache[taskType] || taskType
}

/**
 * 获取任务状态文本
 */
export const getTaskStatusText = (status: number): string => {
  return TASK_STATUS_TEXT[status as keyof typeof TASK_STATUS_TEXT] || '未知状态'
}

/**
 * 获取任务状态标签类型
 */
export const getTaskStatusTagType = (status: number): string => {
  return TASK_STATUS_TAG_TYPE[status as keyof typeof TASK_STATUS_TAG_TYPE] || 'info'
}

/**
 * 格式化执行时长
 */
export const formatDuration = (durationMs?: number): string => {
  if (!durationMs) return '-'
  
  const seconds = Math.floor(durationMs / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分${seconds % 60}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 判断是否为JSON格式
 */
export const isJsonString = (str: any): boolean => {
  if (typeof str !== 'string') return false
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

/**
 * 分析任务API
 */
export const analysisTaskApi = {
  /**
   * 获取案件所有任务状态
   */
  getCaseTaskStatus(caseImportId: string | number): Promise<AnalysisTaskRecord[]> {
    return http.get(`/api/analysis-task/case/${caseImportId}`)
  },

  /**
   * 获取特定任务状态
   */
  getTaskStatus(caseImportId: string | number, taskType: string): Promise<AnalysisTaskRecord> {
    return http.get(`/api/analysis-task/case/${caseImportId}/task/${taskType}`)
  },

  /**
   * 获取特定任务的完整记录（包含agentTaskInfo）
   */
  getFullTaskRecord(caseImportId: string | number, taskType: string): Promise<AnalysisTaskRecord> {
    return http.get(`/api/analysis-task/case/${caseImportId}/task/${taskType}/full`)
  },

  /**
   * 检查任务是否正在执行
   */
  isTaskRunning(caseImportId: string | number, taskType: string): Promise<boolean> {
    return http.get(`/api/analysis-task/case/${caseImportId}/task/${taskType}/running`)
  }
}
