import { http } from '@/utils/http'

/**
 * 模块更新记录接口
 */
export interface ModuleUpdateRecord {
  id: number
  caseImportId: number
  moduleCode: string
  hasUpdate: number
  operateModule: string | null
  createTime: string
  updateTime: string
}

/**
 * 模块状态映射接口
 */
export interface ModuleStatusMap {
  [moduleCode: string]: boolean
}

/**
 * 模块类型枚举
 */
export const ModuleType = {
  DISPUTE_FOCUS: 'DISPUTE_FOCUS',                      // 争议焦点
  DISPUTE_FOCUS_REASONING: 'DISPUTE_FOCUS_REASONING',  // 争议焦点说理
  DETERMINE_FACTS: 'DETERMINE_FACTS',                  // 认定事实
  UNDISPUTED_FACTS: 'UNDISPUTED_FACTS',               // 无争议事实
  CASE_PARTY: 'CASE_PARTY',                           // 当事人分析
  LITIGATION_RELATION: 'LITIGATION_RELATION',         // 诉辩关系
  EVIDENCE_OVERVIEW: 'EVIDENCE_OVERVIEW',             // 证据情况
  VERIFICATION_EVIDENCE: 'VERIFICATION_EVIDENCE',     // 质证情况
  CASE_CAUSE_EXTRACTION: 'CASE_CAUSE_EXTRACTION',     // 案由提取
  DOCUMENT_GENERATION: 'DOCUMENT_GENERATION'          // 文书生成
} as const

/**
 * 模块类型值类型
 */
export type ModuleTypeValue = typeof ModuleType[keyof typeof ModuleType]

/**
 * 模块名称映射
 */
export const ModuleNames: Record<ModuleTypeValue, string> = {
  [ModuleType.DISPUTE_FOCUS]: '争议焦点',
  [ModuleType.DISPUTE_FOCUS_REASONING]: '争议焦点说理',
  [ModuleType.DETERMINE_FACTS]: '认定事实',
  [ModuleType.UNDISPUTED_FACTS]: '无争议事实',
  [ModuleType.CASE_PARTY]: '当事人分析',
  [ModuleType.LITIGATION_RELATION]: '诉辩关系',
  [ModuleType.EVIDENCE_OVERVIEW]: '证据情况',
  [ModuleType.VERIFICATION_EVIDENCE]: '质证情况',
  [ModuleType.CASE_CAUSE_EXTRACTION]: '案由提取',
  [ModuleType.DOCUMENT_GENERATION]: '文书生成'
}

/**
 * 模块更新API
 */
export const moduleUpdateApi = {
  /**
   * 获取案件所有模块更新状态
   * @param caseImportId 案件导入ID
   * @returns 模块状态映射
   */
  getAllModuleStatus(caseImportId: number): Promise<ModuleStatusMap> {
    return http.get(`/api/module-update/status/${caseImportId}`)
  },

  /**
   * 获取案件特定模块更新状态（新逻辑）
   * @param caseImportId 案件导入ID
   * @param currentModule 当前模块代码
   * @returns 模块状态映射
   */
  getAllModuleStatusNew(caseImportId: number, currentModule: string): Promise<ModuleStatusMap> {
    return http.get(`/api/module-update/status/${caseImportId}/${currentModule}`)
  },

  /**
   * 检查特定模块是否需要更新
   * @param caseImportId 案件导入ID
   * @param moduleCode 模块代码
   * @returns 是否需要更新
   */
  checkModuleNeedUpdate(caseImportId: number, moduleCode: string): Promise<boolean> {
    return http.get(`/api/module-update/check/${caseImportId}/${moduleCode}`)
  },

  /**
   * 检查特定模块是否需要更新（新逻辑）
   * @param caseImportId 案件导入ID
   * @param sourceModule 源模块代码
   * @param operatedModule 目标操作模块代码
   * @returns 是否需要更新
   */
  checkModuleNeedUpdateNew(caseImportId: number, sourceModule: string, operatedModule: string): Promise<boolean> {
    return http.get(`/api/module-update/check/${caseImportId}/${sourceModule}/${operatedModule}`)
  },

  /**
   * 标记模块已操作
   * @param caseImportId 案件导入ID
   * @param sourceModule 源模块代码
   * @param operatedModule 已操作模块代码
   * @returns 操作是否成功
   */
  markModuleAsOperated(caseImportId: number, sourceModule: string, operatedModule: string): Promise<boolean> {
    return http.post(`/api/module-update/mark-operated/${caseImportId}`, null, {
      params: { sourceModule, operatedModule }
    })
  },

  /**
   * 获取需要更新的模块列表
   * @param caseImportId 案件导入ID
   * @returns 需要更新的模块代码列表
   */
  getNeedUpdateModules(caseImportId: number): Promise<string[]> {
    return http.get(`/api/module-update/need-update/${caseImportId}`)
  },

  /**
   * 重置特定模块更新状态
   * @param caseImportId 案件导入ID
   * @param moduleCode 模块代码
   * @returns 操作结果
   */
  resetModuleStatus(caseImportId: number, moduleCode: string): Promise<boolean> {
    return http.post(`/api/module-update/reset/${caseImportId}/${moduleCode}`)
  },

  /**
   * 批量重置模块更新状态
   * @param caseImportId 案件导入ID
   * @param moduleCodes 模块代码列表，为空则重置所有模块
   * @returns 操作结果
   */
  batchResetModuleStatus(caseImportId: number, moduleCodes?: string[]): Promise<boolean> {
    return http.post(`/api/module-update/reset-batch/${caseImportId}`, moduleCodes)
  },

  /**
   * 重置案件所有模块状态
   * @param caseImportId 案件导入ID
   * @returns 操作结果
   */
  resetAllModuleStatus(caseImportId: number): Promise<boolean> {
    return http.post(`/api/module-update/reset-all/${caseImportId}`)
  },

  /**
   * 获取案件模块更新记录详情
   * @param caseImportId 案件导入ID
   * @returns 模块更新记录列表
   */
  getModuleUpdateRecords(caseImportId: number): Promise<ModuleUpdateRecord[]> {
    return http.get(`/api/module-update/records/${caseImportId}`)
  },

  /**
   * 手动标记模块需要更新
   * @param caseImportId 案件导入ID
   * @param moduleCode 模块代码
   * @returns 操作结果
   */
  markModuleNeedUpdate(caseImportId: number, moduleCode: string): Promise<boolean> {
    return http.post(`/api/module-update/mark/${caseImportId}/${moduleCode}`)
  }
}

/**
 * 获取模块显示名称
 * @param moduleCode 模块代码
 * @returns 模块显示名称
 */
export function getModuleName(moduleCode: string): string {
  return ModuleNames[moduleCode as ModuleTypeValue] || moduleCode
}

/**
 * 检查模块代码是否有效
 * @param moduleCode 模块代码
 * @returns 是否有效
 */
export function isValidModuleCode(moduleCode: string): boolean {
  return Object.values(ModuleType).includes(moduleCode as ModuleTypeValue)
}
