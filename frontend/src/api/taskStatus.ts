import { http } from '@/utils/http'
import { getTaskTypeName as getTaskTypeNameFromAnalysisTask } from './analysisTask'

/**
 * 任务状态DTO接口
 */
export interface TaskStatusDTO {
  taskType: string
  taskName: string
  status: number
  statusText: string
  startTime?: string
  endTime?: string
  durationMs?: number
  errorMessage?: string
  createTime?: string
}

/**
 * 任务状态API
 */
export const taskStatusApi = {
  /**
   * 获取案件所有任务状态
   */
  getCaseTaskStatus(caseImportId: string | number): Promise<TaskStatusDTO[]> {
    return http.get(`/api/analysis-task/case/${caseImportId}`)
  },

  /**
   * 获取特定任务状态
   */
  getTaskStatus(caseImportId: string | number, taskType: string): Promise<TaskStatusDTO> {
    return http.get(`/api/analysis-task/case/${caseImportId}/task/${taskType}`)
  },

  /**
   * 检查任务是否正在执行
   */
  isTaskRunning(caseImportId: string | number, taskType: string): Promise<boolean> {
    return http.get(`/api/analysis-task/case/${caseImportId}/task/${taskType}/running`)
  },

  /**
   * 获取任务预估执行时间（分钟）
   */
  getEstimatedDuration(taskType: string): Promise<number> {
    return http.get(`/api/analysis-task/estimated-duration/${taskType}`)
  }
}

/**
 * 任务类型枚举
 */
export const TaskType = {
  LITIGATION_RELATION: 'LITIGATION_RELATION',      // 诉辩关系分析
  DISPUTE_FOCUS: 'DISPUTE_FOCUS',                  // 争议焦点分析
  CASE_FACT: 'CASE_FACT',                          // 案件事实认定提取
  CASE_PARTY: 'CASE_PARTY',                        // 当事人分析
  CASE_CAUSE_EXTRACTION: 'CASE_CAUSE_EXTRACTION', // 案由提取
  EVIDENCE_OVERVIEW: 'EVIDENCE_OVERVIEW',          // 证据情况分析
  VERIFICATION_EVIDENCE: 'VERIFICATION_EVIDENCE',  // 质证情况分析
  UNDISPUTED_FACTS: 'UNDISPUTED_FACTS',           // 无争议事实生成
  DISPUTE_FOCUS_REASONING: 'DISPUTE_FOCUS_REASONING', // 争议焦点说理分析
  DETERMINE_FACTS: 'DETERMINE_FACTS',             // 认定事实生成
  TQ_YSXX_FROM_YSPJS: 'TQ_YSXX_FROM_YSPJS',       // 提取原审信息
  TQ_YSCPJG_FROM_YSPJS: 'TQ_YSCPJG_FROM_YSPJS'   // 提取原审判决结果信息
} as const

/**
 * 任务状态枚举
 */
export const TaskStatus = {
  PENDING: 0,    // 待执行
  RUNNING: 1,    // 执行中
  SUCCESS: 2,    // 执行成功
  FAILED: 3      // 执行失败
} as const

/**
 * 任务类型值类型
 */
export type TaskTypeValue = typeof TaskType[keyof typeof TaskType]

/**
 * 任务状态值类型
 */
export type TaskStatusValue = typeof TaskStatus[keyof typeof TaskStatus]

/**
 * 获取任务类型名称
 * 注意：这个函数已废弃，请使用 analysisTask.ts 中的 getTaskTypeName
 */
export const getTaskTypeName = (taskType: string): string => {
  return getTaskTypeNameFromAnalysisTask(taskType)
}

/**
 * 获取任务状态名称
 */
export const getTaskStatusName = (status: number): string => {
  const statusNames: Record<number, string> = {
    [TaskStatus.PENDING]: '待执行',
    [TaskStatus.RUNNING]: '执行中',
    [TaskStatus.SUCCESS]: '执行成功',
    [TaskStatus.FAILED]: '执行失败'
  }
  return statusNames[status] || '未知状态'
}

/**
 * 获取任务预估执行时间（分钟）
 */
export const getEstimatedDuration = (taskType: string): number => {
  const estimatedDurations: Record<string, number> = {
    [TaskType.LITIGATION_RELATION]: 2,      // 诉辩关系分析：2分钟
    [TaskType.DISPUTE_FOCUS]: 5,            // 争议焦点分析：5分钟
    [TaskType.CASE_FACT]: 4,                // 案件事实认定提取：4分钟
    [TaskType.CASE_PARTY]: 1,               // 当事人分析：1分钟
    [TaskType.CASE_CAUSE_EXTRACTION]: 1,    // 案由提取：1分钟
    [TaskType.EVIDENCE_OVERVIEW]: 3,        // 证据情况分析：3分钟
    [TaskType.VERIFICATION_EVIDENCE]: 2,    // 质证情况分析：2分钟
    [TaskType.UNDISPUTED_FACTS]: 2,         // 无争议事实生成：2分钟
    [TaskType.DISPUTE_FOCUS_REASONING]: 5,  // 争议焦点说理分析：5分钟
    [TaskType.TQ_YSXX_FROM_YSPJS]: 1,       // 提取原审信息：1分钟
    [TaskType.TQ_YSCPJG_FROM_YSPJS]: 2      // 提取原审判决结果信息：2分钟
  }
  return estimatedDurations[taskType] || 3 // 默认3分钟
}
