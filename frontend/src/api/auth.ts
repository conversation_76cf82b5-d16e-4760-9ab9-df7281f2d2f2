import { http } from '../utils/http'

// 权限相关接口类型定义
export interface UserRight {
  rightkey: string
  name: string
  order: number
  description: string
}

export interface LoginResponse {
  token: string
  name: string
  loginId: string
  userId: number
}

export interface UserInfo {
  token: string
  name: string
  loginId: string
  userId: number
}

// 登录接口 - 调用smxz-console的JWT认证接口
export const login = (username: string, password: string) => {
  // 登录请求使用较短的超时时间（5秒），提升用户体验
  return http.post<LoginResponse>('/api/auth/login', { username, password }, { timeout: 5000 })
}

// 获取用户权限接口
export const getUserRights = (userId: number) => {
  return http.get<UserRight[]>(`/api/auth/user-rights/${userId}`)
}

// 验证token接口
export const validateToken = () => {
  return http.post('/api/auth/validate')
}

// 获取当前用户信息接口
export const getCurrentUser = () => {
  return http.get<UserInfo>('/api/auth/userinfo')
}

export default http