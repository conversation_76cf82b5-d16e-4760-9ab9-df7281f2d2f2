import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';
import type { OcrResult, ImagePreview } from '@/types/ocr';
import type { PageParams, PageResult } from '@/types/api';

/**
 * 案件导入记录
 */
export interface CaseImportRecord {
  id: number;
  caseName: string;
  caseCode?: string;
  ajlxdm?: string;
  caseCause?: string;
  importFilePath?: string;
  importStatus: number;
  fileStatus?: number; // 文件处理状态：0-处理中，1-已完成
  importTime: string;
  updateTime: string;
  importerId?: number;
  importerName?: string;
  errorMessage?: string;
  totalCount?: number;
  successCount?: number;
  failCount?: number;
  knowledgeSessionId?: string;
  relationAnalysisStatus?: number;
  relationAnalysisTime?: string;
  timelineAnalysisStatus?: number;
  timelineAnalysisTime?: string;
  disputeAnalysisStatus?: number;
  disputeAnalysisTime?: string;
  contradictionAnalysisStatus?: number;
  contradictionAnalysisTime?: string;
  factExtractStatus?: number;
  factExtractTime?: string;
  disputeFocuseReasoningAnalysisStatus?: number;
  disputeFocuseReasoningAnalysisTime?: string;
  determineFactAnalysisStatus?: number;
  determineFactAnalysisTime?: string;
  deleted?: number;
  caseType?: string | number;

  // 兼容性字段（用于前端显示）
  caseDescription?: string;
  status?: string;
  fileCount?: number;
  analysisStatus?: string;
  createTime?: string;
  progress?: number;
  statusText?: string;
  corpName?: string;
}

/**
 * 文件上传记录（不包含OCR字段）
 */
export interface FileUploadRecord {
  id: number;
  caseImportId: number;
  fileName: string;
  filePath: string;
  pdfPath?: string;
  fileSize: number;
  fileType: string;
  uploaderId?: number;
  uploaderName?: string;
  uploadTime: string;
  status: number;
  md5Hash?: string;
  description?: string;
  downloadCount?: number;
  extractedText?: string;
  deleted?: number;
  deletedTime?: string;
  deletedBy?: string;
  documentType?: string;
}

/**
 * 案件搜索参数
 */
export interface CaseImportSearchDTO {
  caseName?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
  operator?: string;
}

/**
 * 文件预处理结果
 */
export interface PreprocessResult {
  success: boolean;
  message: string;
  processedFileCount?: number;
  failedFileCount?: number;
  exception?: any;
}

/**
 * 案件相关API
 */
export const caseApi = {
  /**
   * 批量上传文件
   * 简单案件类型/普通案件类型
   *
   */
  uploadBatch(files: File[], caseType?: string): Promise<string> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    if (caseType) {
      formData.append('caseType', caseType);
    }
    return http.post(API_ENDPOINTS.case.uploadBatch, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      loading: true,
      showSuccess: true,
      successMessage: '文件上传成功',
    });
  },

  /**
   * 获取案件导入记录列表
   */
  getImportList(params: PageParams & CaseImportSearchDTO): Promise<PageResult<CaseImportRecord>> {
    return http.post(API_ENDPOINTS.case.list, params, {
      loading: true,
    });
  },

  /**
   * 获取文件上传记录列表
   */
  getFileList(params: PageParams & CaseImportSearchDTO): Promise<PageResult<CaseImportRecord>> {
    return http.post(API_ENDPOINTS.case.fileList, params, {
      loading: true,
    });
  },

  /**
   * 获取案件详情
   */
  getDetail(id: string | number): Promise<CaseImportRecord> {
    return http.get(API_ENDPOINTS.case.detail(id), undefined, {
      loading: true,
    });
  },

  /**
   * 获取案件状态（轻量级查询，只返回状态信息）
   */
  getCaseStatus(id: string | number): Promise<{ fileStatus: number; importStatus: number }> {
    return http.get(API_ENDPOINTS.case.status(id), undefined, {
      loading: false, // 不显示loading，快速查询
    });
  },

  /**
   * 获取案件类型
   */
  getCaseType(id: string | number): Promise<CaseTypeInfo> {
    return http.get(API_ENDPOINTS.case.type(id), undefined, {
      loading: false,
    });
  },

  /**
   * 删除案件记录
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.case.delete(id), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '删除成功',
    });
  },

  /**
   * 预处理案件文件
   */
  preprocess(id: string | number): Promise<PreprocessResult> {
    return http.post(API_ENDPOINTS.case.preprocess(id), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '预处理任务已启动',
    });
  },

  /**
   * 重新处理案件文件
   */
  reprocess(id: string | number): Promise<PreprocessResult> {
    return http.post(API_ENDPOINTS.case.reprocess(id), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '重新处理任务已启动',
    });
  },

  /**
   * AI聊天测试
   */
  chat(): Promise<string> {
    return http.post(API_ENDPOINTS.case.chat, undefined, {
      loading: true,
    });
  },

  /**
   * 获取案件的文件列表
   */
  getCaseFileList(caseImportId: string | number): Promise<FileUploadRecord[]> {
    return http.post(API_ENDPOINTS.file.uploadList, undefined, {
      params: { caseImportId: Number(caseImportId) },
      loading: true,
    });
  },

  /**
   * 下载文件流
   */
  downloadFileStream(fileId: string | number): Promise<Blob> {
    return http.request({
      url: API_ENDPOINTS.file.downloadStream,
      method: 'GET',
      params: { id: fileId },
      responseType: 'blob',
      headers: {
        'Accept': 'application/octet-stream',
      },
    });
  },

  /**
   * 删除文件（移入回收站）
   */
  deleteFile(fileId: string | number, deletedBy?: string): Promise<boolean> {
    return http.post(API_ENDPOINTS.file.delete(fileId), undefined, {
      params: { deletedBy },
      loading: true,
      showSuccess: true,
      successMessage: '文件已移入回收站',
    });
  },

  /**
   * 获取回收站文件列表
   */
  getRecycleBinFiles(caseImportId: string | number): Promise<FileUploadRecord[]> {
    return http.get(API_ENDPOINTS.file.recycleBin(caseImportId), undefined, {
      loading: true,
    });
  },

  /**
   * 从回收站恢复文件
   */
  restoreFile(fileId: string | number): Promise<boolean> {
    return http.post(API_ENDPOINTS.file.restore(fileId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '文件恢复成功',
    });
  },

  /**
   * 永久删除文件
   */
  permanentDeleteFile(fileId: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.file.permanentDelete(fileId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '文件已永久删除',
    });
  },

  /**
   * 单文件上传
   */
  uploadSingleFile(caseImportId: string | number, file: File): Promise<boolean> {
    const formData = new FormData();
    formData.append('caseImportId', String(caseImportId));
    formData.append('file', file);

    return http.post(API_ENDPOINTS.file.uploadSingle, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      loading: false, // 不显示全局loading，使用进度条代替
      showSuccess: false, // 不显示成功提示，由组件统一处理
    });
  },

  /**
   * 清空回收站（批量永久删除）
   */
  clearRecycleBin(caseImportId: string | number): Promise<number> {
    return http.delete(API_ENDPOINTS.file.clearRecycleBin(caseImportId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '回收站已清空',
    });
  },

  /**
   * 重新生成全部分析任务
   */
  regenerateAllAnalysis(caseImportId: string | number): Promise<string> {
    return http.post(API_ENDPOINTS.case.regenerateAll(caseImportId), undefined, {
      loading: true,
      showSuccess: false, // 后端已经有提示信息，前端不再重复显示
    });
  },

  /**
   * 获取文件OCR结果
   */
  getFileOcrResult(fileId: string | number): Promise<OcrResult[]> {
    return http.get(`/api/file/ocr/${fileId}`, undefined, {
      loading: false,
    });
  },

  /**
   * 获取文件OCR预览
   */
  getFileOcrPreviews(fileId: string | number): Promise<ImagePreview[]> {
    return http.get(`/api/file/ocr/previews/${fileId}`, undefined, {
      loading: false,
    });
  },

  /**
   * 获取文件PDF内容（base64格式）
   */
  getFilePdfContent(fileId: string | number): Promise<string> {
    return http.get(API_ENDPOINTS.file.pdfContent(fileId), undefined, {
      loading: false,
    });
  },

  /**
   * 重新OCR识别文件
   */
  retryFileOcr(fileId: string | number): Promise<string> {
    return http.post(`/api/file/ocr/retry/${fileId}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: 'OCR重新识别任务已启动',
    });
  },



};

/**
 * 案件类型信息接口
 */
export interface CaseTypeInfo {
  ajlxdm: string;
  caseType: string;
  caseTypeCode: string;
  isSecondInstance: boolean;
}
