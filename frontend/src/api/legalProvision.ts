import { http } from '@/utils/http'

// 法律名称响应类型
export interface LawNameResponse {
  lawId: number
  lawName: string
  displayName: string
}

// 法条树节点类型
export interface LegalProvisionTreeNode {
  id: number
  title: string
  content: string
  level: number
  articleNo?: number
  paragraphNo?: number
  subparagraphNo?: number
  children: LegalProvisionTreeNode[]
}

// 法条记录类型
export interface LegalProvisionRecord {
  id: number
  provisionNumber: string // 法条编号，如"第二十七条第二款第一项"
  content: string
}

// 获取所有法律名称
export const getLawNames = (): Promise<LawNameResponse[]> => {
  return http.get('/api/legal-provision/law-names')
}

// 根据法律ID获取法条树形结构
export const getProvisionTree = (lawId: number): Promise<LegalProvisionTreeNode[]> => {
  return http.get(`/api/legal-provision/tree/${lawId}`)
}

// 刷新缓存
export const refreshCache = (): Promise<string> => {
  return http.post('/api/legal-provision/refresh-cache')
}
