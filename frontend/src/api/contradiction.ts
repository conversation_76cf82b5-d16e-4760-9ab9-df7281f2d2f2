import { http } from '@/utils/http';

/**
 * 证据信息
 */
export interface Evidence {
  fileId: number;
  fileName: string;
  pageNumber?: number | null;
  highlight?: string;
}

/**
 * 矛盾识别
 */
export interface ContradictionRecognition {
  id: number;
  caseImportId: number;
  type: string; // 原告/被告/第三人等
  name: string; // 当事人姓名
  contradictionPoint: string; // 矛盾点描述
  content: string; // 详细的矛盾内容描述
  evidence?: Evidence[]; // 证据列表
  createTime: string;
  updateTime: string;
}

/**
 * 矛盾识别DTO
 */
export interface ContradictionUpdateDTO {
  contradictionPoint: string;
  content: string;
}

/**
 * 矛盾识别相关API
 */
export const contradictionApi = {
  /**
   * 根据案件导入ID获取矛盾识别列表
   */
  getContradictionList(caseImportId: string | number) {
    return http.get(`/api/contradiction/${caseImportId}`);
  },

  /**
   * 根据案件导入ID和当事人类型获取矛盾识别列表
   */
  getContradictionListByType(caseImportId: string | number, type: string) {
    return http.get(`/api/contradiction/${caseImportId}/type/${type}`);
  },

  /**
   * 编辑矛盾识别记录
   */
  updateContradiction(id: number, data: ContradictionUpdateDTO) {
    return http.put(`/api/contradiction/${id}`, data);
  },

  /**
   * 删除矛盾识别记录
   */
  deleteContradiction(id: number) {
    return http.delete(`/api/contradiction/${id}`);
  },

  /**
   * 重新分析矛盾识别
   */
  reanalyze(caseImportId: string | number) {
    return http.post(`/api/contradiction/${caseImportId}/reanalyze`);
  }
};
