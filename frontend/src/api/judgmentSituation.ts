import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 判项情况信息
 */
export interface JudgmentSituation {
  id?: number;
  caseImportRecordId: number;
  dsrxm: string; // 当事人姓名
  ssdw: string;  // 诉讼地位
  judgment: string;
  opinion: number;
  newJudgment?: string;
  documentCaseType?: string;
  dataType?: string;
}

/**
 * 判项情况相关API
 */
export const judgmentSituationApi = {
  /**
   * 获取案件判项情况列表
   */
  list(caseImportRecordId: string | number): Promise<JudgmentSituation[]> {
    return http.get(API_ENDPOINTS.judgmentSituation.list, undefined, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
    });
  },

  /**
   * 根据案件ID和文书类型获取判项情况列表
   */
  listByCaseImportIdAndDocumentType(caseImportRecordId: string | number, documentCaseType: string): Promise<JudgmentSituation[]> {
    return http.get(API_ENDPOINTS.judgmentSituation.listByCaseImportIdAndDocumentType, undefined, {
      params: { 
        caseImportRecordId: Number(caseImportRecordId),
        documentCaseType: documentCaseType
      },
      loading: true,
    });
  },

  /**
   * 保存判项情况信息
   */
  save(judgmentSituation: JudgmentSituation): Promise<boolean> {
    return http.post(API_ENDPOINTS.judgmentSituation.save, judgmentSituation, {
      loading: true,
      showSuccess: true,
      successMessage: '判项情况信息保存成功',
    });
  },

  /**
   * 批量保存或更新判项情况信息
   */
  saveOrUpdateBatch(judgmentSituationList: JudgmentSituation[]): Promise<boolean> {
    return http.post(API_ENDPOINTS.judgmentSituation.saveOrUpdateBatch, judgmentSituationList, {
      loading: true,
      showSuccess: true,
      successMessage: '判项情况信息批量保存成功',
    });
  },

  /**
   * 更新判项情况信息
   */
  update(judgmentSituation: JudgmentSituation): Promise<boolean> {
    return http.put(API_ENDPOINTS.judgmentSituation.update, judgmentSituation, {
      loading: true,
      showSuccess: true,
      successMessage: '判项情况信息更新成功',
    });
  },

  /**
   * 删除判项情况信息
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.judgmentSituation.delete(id), undefined, {
      loading: true,
      showSuccess: true,
    });
  },

  /**
   * 根据案件ID删除判项情况信息
   */
  deleteByCaseId(caseImportRecordId: string | number): Promise<boolean> {
    return http.delete(API_ENDPOINTS.judgmentSituation.deleteByCaseId(caseImportRecordId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '案件判项情况信息删除成功',
    });
  },
};
