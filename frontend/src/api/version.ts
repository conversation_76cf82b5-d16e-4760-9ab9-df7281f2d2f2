import { http } from '@/utils/http';
import type { ApiResult } from '@/types/api';

/**
 * 版本信息API
 */

// 定义版本信息类型
export interface VersionInfo {
  buildTime: string;
  branch: string;
  commitId: string;
  commitTime: string;
  commitMessageFull: string;
  commitUserName: string;
  buildUserName: string;
  totalCommitCount: string;
}

// 定义Chrome版本信息类型
export interface ChromeVersionInfo {
  version: string;
  downloadUrl: string;
  os: string;
}

/**
 * 获取Git版本信息
 */
export async function getVersionInfo(): Promise<string> {
  const response = await http.get<string>('/api/version/getBuildInfo');
  return response;
}

/**
 * 获取Chrome推荐版本信息
 * @param os 操作系统 (Windows, Linux, MacOS)
 */
export async function getChromeRecommendation(os?: string): Promise<ChromeVersionInfo> {
  const response = await http.get<ChromeVersionInfo>('/api/version/getChromeRecommendation', { os: os });
  return response;
}

// 导出API对象
export const versionApi = {
  getVersionInfo,
  getChromeRecommendation
};