import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 系统提示词配置
 */
export interface SystemPromptConfig {
  id: number;
  promptKey: string;
  promptMessage: string;
  promptVersion: number;
  createTime: string;
  updateTime: string;
}

/**
 * 保存提示词请求
 */
export interface SavePromptRequest {
  key: string;
  prompt: string;
}

/**
 * 系统提示词相关API
 */
export const promptApi = {
  /**
   * 通过key获取提示词
   */
  getByKey(key: string): Promise<string> {
    return http.get(API_ENDPOINTS.prompt.getByKey, { key });
  },

  /**
   * 保存提示词
   */
  save(data: SavePromptRequest): Promise<boolean> {
    return http.post(API_ENDPOINTS.prompt.save, data, {
      loading: true,
      showSuccess: true,
      successMessage: '保存成功',
    });
  },

  /**
   * 暂存提示词
   */
  staging(data: SavePromptRequest): Promise<boolean> {
    return http.post(API_ENDPOINTS.prompt.staging, data, {
      loading: true,
      showSuccess: true,
      successMessage: '暂存成功',
    });
  },

  /**
   * 获取历史提示词列表
   */
  getHistory(key: string): Promise<SystemPromptConfig[]> {
    return http.get(API_ENDPOINTS.prompt.history, { key });
  },
};
