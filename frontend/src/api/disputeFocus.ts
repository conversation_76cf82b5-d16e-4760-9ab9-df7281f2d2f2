import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 争议焦点
 */
export interface DisputeFocus {
  id: number;
  title: string;
  description: string;
  category: string;
  caseImportId: number;
  plaintiffClaim?: string;
  defendantClaim?: string;
  conclusion?: string; // 争议焦点结论（"是"/"否"/具体内容）
  createTime: string;
  updateTime: string;
}

/**
 * 争议焦点列表响应数据
 */
export interface DisputeFocusListResponse {
  data: DisputeFocus[];
}

/**
 * 新增争议焦点请求数据
 */
export interface CreateDisputeFocusRequest {
  description: string;
  conclusion?: string;
  caseImportId: number;
  laws?: string[];
  lawSummaries?: string[];
}

/**
 * 更新争议焦点请求数据
 */
export interface UpdateDisputeFocusRequest {
  description?: string;
  conclusion?: string;
  laws?: string[];
  lawSummaries?: string[];
}

/**
 * 法条进度响应数据
 */
export interface LawProgressResponse {
  total: number;
  completed: number;
}

/**
 * 争议焦点相关API
 */
export const disputeFocusApi = {
  /**
   * 获取争议焦点数据
   */
  get(caseImportId: string | number): Promise<DisputeFocus[]> {
    return http.get(API_ENDPOINTS.disputeFocus.get(caseImportId), undefined, {
      loading: true,
    });
  },

  /**
   * 获取争议焦点法条进度
   */
  getLawProgress(caseImportId: string | number): Promise<LawProgressResponse> {
    return http.get(`/api/disputeFocus/${caseImportId}/lawProgress`, undefined, {
      loading: false, // 不显示loading，因为这是轮询调用
    });
  },

  /**
   * 新增争议焦点
   */
  create(data: CreateDisputeFocusRequest): Promise<DisputeFocus> {
    return http.post(API_ENDPOINTS.disputeFocus.save, data, {
      loading: true
    });
  },

  /**
   * 更新争议焦点
   */
  update(id: number, data: UpdateDisputeFocusRequest): Promise<DisputeFocus> {
    return http.put(`${API_ENDPOINTS.disputeFocus.save}/${id}`, data, {
      loading: true
    });
  },

  /**
   * 保存或更新争议焦点（保持向后兼容）
   */
  save(data: DisputeFocus): Promise<DisputeFocus> {
    return http.post(API_ENDPOINTS.disputeFocus.save, data, {
      loading: true
    });
  },

  /**
   * 删除争议焦点
   */
  delete(focusId: string | number): Promise<string> {
    return http.delete(API_ENDPOINTS.disputeFocus.delete(focusId), undefined, {
      loading: true
    });
  },

  /**
   * 删除案件下的所有争议焦点
   */
  deleteAll(caseImportId: string | number): Promise<string> {
    return http.delete(`/api/disputeFocus/deleteAll/${caseImportId}`, undefined, {
      loading: true
    });
  },

  /**
   * 重新生成争议焦点
   */
  regenerate(caseImportId: string | number): Promise<string> {
    return http.post(`/api/disputeFocus/${caseImportId}/regenerate`, undefined, {
      loading: true
    });
  },

  /**
   * 重新生成争议焦点分析（别名方法）
   */
  regenerateAnalysis(caseImportId: string | number): Promise<string> {
    return this.regenerate(caseImportId);
  },

  /**
   * 重新生成争议焦点标签的所有模型任务
   */
  regenerateAll(caseImportId: string | number): Promise<string> {
    return http.post(`/api/disputeFocus/${caseImportId}/regenerate-all`, undefined, {
      loading: true
    });
  },
};
