import { http } from '@/utils/http'

// 知识库配置相关接口
export interface LlmConfig {
  modelName?: string
  temperature?: number
  topP?: number
  presencePenalty?: number
  frequencyPenalty?: number
}

export interface PromptConfig {
  similarityThreshold?: number
  keywordsSimilarityWeight?: number
  topN?: number
  rerankModel?: string
  topK?: number
  emptyResponse?: string
  opener?: string
  showQuote?: boolean
  prompt?: string
  variables?: any[]
}

export interface KnowledgeBaseConfig {
  id?: number
  configKey: string
  configName: string
  datasetIds: string
  llmConfig?: LlmConfig
  promptConfig?: PromptConfig
  isEnabled?: boolean
  createTime?: string
  updateTime?: string
}

// 获取知识库配置列表
export const getKnowledgeBaseConfigList = (params?: any) => {
  return http.get('/api/knowledge-base-config/list', { params })
}

// 根据配置键获取启用的配置
export const getKnowledgeBaseConfigByKey = (configKey: string) => {
  return http.get(`/api/knowledge-base-config/by-key/${configKey}`)
}

// 根据ID获取知识库配置
export const getKnowledgeBaseConfigById = (id: number) => {
  return http.get(`/api/knowledge-base-config/${id}`)
}

// 创建知识库配置
export const createKnowledgeBaseConfig = (data: KnowledgeBaseConfig) => {
  return http.post('/api/knowledge-base-config', data)
}

// 更新知识库配置
export const updateKnowledgeBaseConfig = (id: number, data: KnowledgeBaseConfig) => {
  return http.put(`/api/knowledge-base-config/${id}`, data)
}

// 删除知识库配置
export const deleteKnowledgeBaseConfig = (id: number) => {
  return http.delete(`/api/knowledge-base-config/${id}`)
}

// 启用/禁用知识库配置
export const toggleKnowledgeBaseConfig = (id: number) => {
  return http.put(`/api/knowledge-base-config/${id}/toggle`)
}

// 重新初始化默认配置
export const initDefaultKnowledgeBaseConfig = () => {
  return http.post('/api/knowledge-base-config/init-default')
}
