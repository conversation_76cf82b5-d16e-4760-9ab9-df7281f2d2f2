import { http } from '@/utils/http'

/**
 * 获取争议焦点说理
 * @param {number} caseImportId 案件导入ID
 * @returns {Promise} API响应
 */
export function getDisputeReasoning(caseImportId) {
  return http.get(`/api/dispute-focuse-detail/${caseImportId}`)
}

/**
 * 生成争议焦点说理
 * @param {number} caseImportId 案件导入ID
 * @returns {Promise} API响应
 */
export function generateDisputeReasoning(caseImportId) {
  return http.post(`/api/dispute-focuse-detail/generate/${caseImportId}`)
}

/**
 * 重新生成争议焦点说理
 * @param {number} caseImportId 案件导入ID
 * @returns {Promise} API响应
 */
export function regenerateDisputeReasoning(caseImportId) {
  return http.post(`/api/dispute-focuse-detail/regenerate/${caseImportId}`)
}

/**
 * 更新争议焦点说理
 * @param {number} caseImportId 案件导入ID
 * @param {string} reasoning 争议焦点说理内容
 * @returns {Promise} API响应
 */
export function updateDisputeReasoning(caseImportId, reasoning) {
  return http.put(`/api/dispute-focuse-detail/${caseImportId}`, reasoning, {
    headers: {
      'Content-Type': 'text/plain'
    },
    showSuccess: true,
    successMessage: '保存成功'
  })
}

/**
 * 删除争议焦点说理
 * @param {number} caseImportId 案件导入ID
 * @returns {Promise} API响应
 */
export function deleteDisputeReasoning(caseImportId) {
  return http.delete(`/api/dispute-focuse-detail/${caseImportId}`)
}

/**
 * 获取案件状态信息
 * @param {number} caseImportId 案件导入ID
 * @returns {Promise} API响应
 */
export function getCaseStatus(caseImportId) {
  return http.get(`/api/case/detail/${caseImportId}`)
}
