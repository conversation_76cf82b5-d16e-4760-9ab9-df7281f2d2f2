import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';
import { EventSourcePolyfill } from 'event-source-polyfill';

/**
 * 文书生成信息
 */
export interface DocumentGenerationInfo {
  id?: number;
  caseImportId: number;
  caseTrialType?: string;
  documentType: string;
  complexity?: string;
  judgeOpinion?: string;
  documentContent?: string;
  documentUrl?: string;
  documentKey?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  durationMs?: number;
  deleted?: number;
  bookmark?: string;
}



/**
 * 更新文书内容请求参数
 */
export interface UpdateDocumentContentRequest {
  id: number;
  documentContent?: string;
  documentUrl?: string;
}

/**
 * 批量删除文书请求参数
 */
export interface BatchDeleteDocumentRequest {
  documentIds: number[];
}

/**
 * 文书生成前置检查响应
 */
export interface DocumentGenerationPreCheckVO {
  loading: boolean;
  taskType: string;
  needsAsyncCall?: string;
}

/**
 * 文书生成相关API
 */
export const documentGenerationApi = {
  /**
   * 根据案件ID查询文书生成信息列表（兼容旧接口）
   */
  listByCaseImportId(caseImportId: string | number): Promise<DocumentGenerationInfo[]> {
    return http.get(`/api/document-generation/list/${caseImportId}`, undefined, {
      loading: true,
    });
  },

  /**
   * 创建文书生成信息
   */
  create(documentInfo: DocumentGenerationInfo): Promise<string> {
    return http.post('/api/document-generation/create', documentInfo, {
      loading: true,
      showSuccess: true,
      successMessage: '文书生成任务已启动',
    });
  },

  /**
   * 删除文书生成信息
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(`/api/document-generation/delete/${id}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '删除成功',
    });
  },


  /**
   * 根据ID获取文书生成信息
   */
  getById(id: string | number): Promise<DocumentGenerationInfo> {
    return http.get(`/api/document-generation/${id}`, undefined, {
      loading: true,
    });
  },

  /**
   * 根据案件ID获取文书生成信息列表（标准接口）
   */
  listByCaseId(caseImportId: string | number): Promise<DocumentGenerationInfo[]> {
    return http.get(`/api/document-generation/case/${caseImportId}`, undefined, {
      loading: true,
    });
  },

  /**
   * 根据案件ID和文书类型获取文书生成信息
   */
  listByCaseIdAndType(caseImportId: string | number, documentType: string): Promise<DocumentGenerationInfo[]> {
    return http.get(`/api/document-generation/case/${caseImportId}/type/${documentType}`, undefined, {
      loading: true,
    });
  },

  /**
   * 分页查询文书生成信息
   */
  getPage(params: {
    current?: number;
    size?: number;
    caseImportId?: number;
    documentType?: string;
  }): Promise<{
    records: DocumentGenerationInfo[];
    total: number;
    current: number;
    size: number;
  }> {
    return http.get('/api/document-generation/page', undefined, {
      params,
      loading: true,
    });
  },

  /**
   * 更新文书生成信息
   */
  update(documentInfo: DocumentGenerationInfo): Promise<boolean> {
    return http.put('/api/document-generation/update', documentInfo, {
      loading: true,
      showSuccess: true,
      successMessage: '更新成功',
    });
  },

  /**
   * 保存书签信息
   */
  saveBookmark(documentId: number, bookmarkContent: string): Promise<string> {
    return http.post(`/api/document-generation/save-bookmark/${documentId}`, bookmarkContent, {
      loading: false, // 不显示加载状态，避免影响用户体验
      showSuccess: false, // 不显示成功提示，避免干扰
    });
  },

  /**
   * 根据案件ID删除所有相关文书生成信息
   */
  deleteByCaseId(caseImportId: string | number): Promise<number> {
    return http.delete(`/api/document-generation/case/${caseImportId}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '批量删除成功',
    });
  },

  /**
   * 批量删除文书生成信息
   */
  batchDelete(documentIds: number[]): Promise<number> {
    return http.request({
      url: '/api/document-generation/batch-delete',
      method: 'DELETE',
      data: { documentIds },
      loading: true,
      showSuccess: true,
    });
  },

  /**
   * 检查案件是否已有指定类型的文书
   */
  exists(caseImportId: string | number, documentType: string): Promise<boolean> {
    return http.get(`/api/document-generation/exists/case/${caseImportId}/type/${documentType}`, undefined, {
      loading: true,
    });
  },

  /**
   * 文书生成前置检查
   */
  preCheck(caseImportId: string | number, documentType: string): Promise<DocumentGenerationPreCheckVO> {
    return http.get(`/api/document-generation/pre-check`, {
      caseImportId,
      documentType
    }, {
      loading: true,
    });
  },

  /**
   * 标记文书生成完成
   */
  completeDocumentGeneration(documentId: string | number): Promise<boolean> {
    return http.post(`/api/document-generation-queue/complete/${documentId}`, {}, {
      loading: false,
    });
  },

  acquire(documentId: string | number): Promise<boolean> {
    return http.post(`/api/document-generation-queue/acquire/${documentId}`, {}, {
      loading: false,
    });
  },

  /**
   * 基于模板生成文书
   */
  generateFromTemplate(caseImportId: string | number, ajlxdm: string, documentType: string): Promise<DocumentGenerationInfo> {
    return http.post('/api/document-generation/generateFromTemplate', undefined, {
      params: {
        caseImportId,
        ajlxdm,
        documentType
      },
      loading: true,
    });
  },

  /**
   * 1. 流式生成当事人基本情况
   */
  streamPartyBasicInfo(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-party-basic/${documentId}`);
  },

  /**
   * 2. 流式生成审理经过
   */
  streamTrialProcess(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-trial-process/${documentId}`);
  },

  /**
   * 3. 流式生成诉辩信息
   */
  streamLitigationDefenseInfo(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-litigation-defense/${documentId}`);
  },

  /**
   * 4. 流式生成原审诉讼请求
   */
  streamOriginalLitigationRequest(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-original-litigation/${documentId}`);
  },



  /**
   * 6. 流式生成原审裁判结果
   */
  streamOriginalJudgmentResult(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-original-judgment/${documentId}`);
  },

  /**
   * 7. 流式生成认定事实
   */
  streamRecognizedFacts(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-recognized-facts/${documentId}`);
  },

  /**
   * 8. 流式生成裁判理由
   */
  streamJudgmentReason(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-judgment-reason/${documentId}`);
  },

  /**
   * 9. 流式生成总结
   */
  streamSummary(documentId: string | number): Promise<ReadableStream<Uint8Array>> {
    return createStreamRequest(`/api/document-generation/stream-summary/${documentId}`);
  },

  /**
   * 1. 流式生成当事人基本情况 (SSE版本)
   */
  streamPartyBasicInfoSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-party-basic/${documentId}`, callbacks);
  },

  /**
   * 2. 流式生成审理经过 (SSE版本)
   */
  streamTrialProcessSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-trial-process/${documentId}`, callbacks);
  },

  /**
   * 3. 流式生成诉辩信息 (SSE版本)
   */
  streamLitigationDefenseInfoSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-litigation-defense/${documentId}`, callbacks);
  },

  /**
   * 4. 流式生成原审诉讼请求 (SSE版本)
   */
  streamOriginalLitigationRequestSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-original-litigation/${documentId}`, callbacks);
  },

  /**
   * 5. 流式生成原审认定事实 (SSE版本)
   */
  streamOriginalRecognizedFactsSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-original-recognized-facts/${documentId}`, callbacks);
  },

  /**
   * 6. 流式生成原审本院认为+原审裁判结果 (SSE版本)
   */
  streamOriginalCourtOpinionAndJudgmentSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-original-court-opinion-judgment/${documentId}`, callbacks);
  },

  /**
   * 6. 流式生成原审裁判结果 (SSE版本)
   */
  streamOriginalJudgmentResultSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-original-judgment/${documentId}`, callbacks);
  },

  /**
   * 7. 流式生成认定事实 (SSE版本)
   */
  streamRecognizedFactsSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-recognized-facts/${documentId}`, callbacks);
  },

  /**
   * 8. 流式生成裁判理由 (SSE版本)
   */
  streamJudgmentReasonSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-judgment-reason/${documentId}`, callbacks);
  },

  /**
   * 9. 流式生成总结 (SSE版本)
   */
  streamSummarySSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection(`/api/document-generation/stream-summary/${documentId}`, callbacks);
  },

  /**
   * 统一流式生成接口 - 并发执行多个生成任务，按顺序返回结果
   */
  streamUnifiedGenerationSSE(documentId: string | number, callbacks: {
    onMessage?: (content: string) => void;
    onStepStart?: (step: string, description: string) => void;
    onStepComplete?: (step: string, description: string) => void;
    onStepError?: (step: string, error: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createUnifiedSSEConnection(`/api/document-generation/stream-unified-generation/${documentId}`, callbacks);
  },

  /**
   * 测试SSE流式输出 - 一个字一个字返回
   */
  testStreamCharByChar(callbacks: {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): EventSourcePolyfill {
    return createSSEConnection('/api/document-generation/test-stream-char-by-char', callbacks);
  },
};

/**
 * 创建统一流式生成SSE连接的方法（支持satoken认证）
 */
function createUnifiedSSEConnection(url: string, callbacks: {
  onMessage?: (content: string) => void;
  onStepStart?: (step: string, description: string) => void;
  onStepComplete?: (step: string, description: string) => void;
  onStepError?: (step: string, error: string) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}): EventSourcePolyfill {
  // 获取token并设置请求头
  const token = localStorage.getItem('token');
  const headers: Record<string, string> = {};
  
  if (token) {
    headers.satoken = token;
  }

  // 使用EventSourcePolyfill支持自定义header
  const eventSource = new EventSourcePolyfill(`${import.meta.env.VITE_API_BASE_URL}${url}`, {
    headers
  });
  
  let isCompleted = false;
  let hasReceivedData = false;

  eventSource.addEventListener('message', (event: any) => {
    hasReceivedData = true;
    if (callbacks.onMessage) {
      callbacks.onMessage(event.data);
    }
  });

  eventSource.addEventListener('stepStart', (event: any) => {
    hasReceivedData = true;
    if (callbacks.onStepStart) {
      try {
        const data = JSON.parse(event.data);
        callbacks.onStepStart(data.step, data.description);
      } catch (e) {
        console.error('解析stepStart事件数据失败:', e);
      }
    }
  });

  eventSource.addEventListener('stepComplete', (event: any) => {
    hasReceivedData = true;
    if (callbacks.onStepComplete) {
      try {
        const data = JSON.parse(event.data);
        callbacks.onStepComplete(data.step, data.description);
      } catch (e) {
        console.error('解析stepComplete事件数据失败:', e);
      }
    }
  });

  eventSource.addEventListener('stepError', (event: any) => {
    hasReceivedData = true;
    if (callbacks.onStepError) {
      try {
        const data = JSON.parse(event.data);
        callbacks.onStepError(data.step, data.error);
      } catch (e) {
        console.error('解析stepError事件数据失败:', e);
      }
    }
  });

  eventSource.addEventListener('complete', () => {
    isCompleted = true;
    eventSource.close();
    if (callbacks.onComplete) {
      callbacks.onComplete();
    }
  });

  eventSource.addEventListener('close', () => {
    isCompleted = true;
    eventSource.close();
    if (callbacks.onComplete) {
      callbacks.onComplete();
    }
  });

  eventSource.addEventListener('error', (error: any) => {
    if (!isCompleted) {
      console.error('统一SSE连接错误:', error);
      eventSource.close();

      if (hasReceivedData) {
        console.log('统一SSE连接在接收数据后结束，视为正常完成');
        isCompleted = true;
        if (callbacks.onComplete) {
          callbacks.onComplete();
        }
      } else {
        if (callbacks.onError) {
          callbacks.onError(new Error('连接失败，请检查网络或稍后重试'));
        }
      }
    }
  });

  return eventSource;
}

/**
 * 创建SSE连接的通用方法（支持satoken认证）
 */
function createSSEConnection(url: string, callbacks: {
  onMessage?: (content: string) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}): EventSourcePolyfill {
  // 获取token并设置请求头
  const token = localStorage.getItem('token');
  const headers: Record<string, string> = {};
  
  if (token) {
    headers.satoken = token;
  }

  // 使用EventSourcePolyfill支持自定义header
  const eventSource = new EventSourcePolyfill(`${import.meta.env.VITE_API_BASE_URL}${url}`, {
    headers
  });
  
  let isCompleted = false;
  let hasReceivedData = false;

  eventSource.addEventListener('message', (event: any) => {
    hasReceivedData = true;
    if (callbacks.onMessage) {
      callbacks.onMessage(event.data);
    }
  });

  eventSource.addEventListener('close', () => {
    isCompleted = true;
    eventSource.close();
    if (callbacks.onComplete) {
      callbacks.onComplete();
    }
  });

  eventSource.addEventListener('error', (error: any) => {
    if (!isCompleted) {
      console.error('SSE连接错误:', error);
      eventSource.close();

      // 如果已经接收到数据，说明连接是正常的，可能是正常结束时的error事件
      if (hasReceivedData) {
        console.log('SSE连接在接收数据后结束，视为正常完成');
        isCompleted = true;
        if (callbacks.onComplete) {
          callbacks.onComplete();
        }
      } else {
        // 如果没有接收到数据就出错，才是真正的连接错误
        if (callbacks.onError) {
          callbacks.onError(new Error('连接失败，请检查网络或稍后重试'));
        }
      }
    }
  });

  return eventSource;
}

/**
 * 创建流式请求的通用方法
 */
function createStreamRequest(url: string): Promise<ReadableStream<Uint8Array>> {
  return fetch(`${import.meta.env.VITE_API_BASE_URL}${url}`, {
    method: 'GET',
    headers: {
      'Accept': 'text/plain',
      'Content-Type': 'text/plain; charset=utf-8',
    },
  }).then(response => {
    if (!response.ok) {
      throw new Error(`流式生成失败: ${response.status} ${response.statusText}`);
    }
    if (!response.body) {
      throw new Error('响应体为空');
    }
    return response.body;
  });
}