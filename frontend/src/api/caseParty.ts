import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';


/**
 * 当事人信息
 */
export interface CaseParty {
  id: number;
  caseImportId: number;
  partyName: string;
  partyType: string;
  partyLabel: string;
}

/**
 * 当事人信息
 */
export interface CasePartyTab {
  partyNameString: string;
  partyType: string;
  partyNameList: string[];
}

/**
 * 当事人相关API
 */
export const casePartyApi = {
  /**
   * 获取案件当事人列表
   */
  list(caseImportId: number): Promise<CaseParty[]> {
    return http.get(API_ENDPOINTS.caseParty.list, undefined, {
      params: { caseImportId: caseImportId },
      loading: true,
    });
  },

  listTab(caseImportId: number): Promise<CasePartyTab[]> {
    return http.get(API_ENDPOINTS.caseParty.listTab, undefined, {
      params: { caseImportId: caseImportId },
      loading: true,
    });
  },

  listForLegalFees(caseImportId: number): Promise<CasePartyTab[]> {
    return http.get(API_ENDPOINTS.caseParty.listForLegalFees, undefined, {
      params: { caseImportId: caseImportId },
      loading: true,
    });
  },

  /**
   * 重新分析当事人
   */
  regenerate(caseImportId: string | number): Promise<boolean> {
    return http.post(API_ENDPOINTS.caseParty.regenerate(caseImportId), undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '重新分析当事人任务已启动',
    });
  },
}; 