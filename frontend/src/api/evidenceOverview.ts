import { http } from '@/utils/http';

/**
 * 证据情况信息
 */
export interface EvidenceOverviewInfo {
  id?: number;
  caseImportId: number;
  partyType?: string;
  partyName?: string;
  evidenceName: string;
  evidencePurpose: string;
  evidenceSummary: string;
  originalFileId?: number;
  originalFileName?: string;
  originalFileStartPage?: number;
  adopted?: boolean; // 后端使用boolean
  isAdopt?: number; // 前端兼容性，0: 不采纳, 1: 采纳
  createTime?: string;
  updateTime?: string;
}

/**
 * 证据情况请求参数
 */
export interface EvidenceOverviewRequest {
  id?: number;
  caseImportId: number;
  partyType?: string;
  partyName?: string;
  evidenceName: string;
  evidencePurpose?: string;
  evidenceSummary?: string;
  adopted?: boolean;
}

/**
 * 证据情况响应数据
 */
export interface EvidenceOverviewResponse {
  id: number;
  caseImportId: number;
  partyType?: string;
  partyName?: string;
  evidenceName: string;
  evidencePurpose?: string;
  evidenceSummary?: string;
  originalFileId?: number;
  originalFileName?: string;
  originalFileStartPage?: number;
  adopted?: boolean;
  createTime?: string;
  updateTime?: string;
}

/**
 * 证据情况相关API
 */
export const evidenceOverviewApi = {
  /**
   * 根据案件ID查询证据情况信息列表
   */
  listByCaseImportId(caseImportId: string | number, partyNames: string[]): Promise<EvidenceOverviewResponse[]> {
    return http.post('/api/evidenceOverview/list', {
      caseImportId: caseImportId,
      partyNames: partyNames
    }, {
      loading: true,
    });
  },

  /**
   * 根据案件ID和当事人名称查询证据情况信息列表
   */
  listByCaseImportIdAndPartyName(caseImportId: string | number, partyName: string): Promise<EvidenceOverviewResponse[]> {
    return http.post('/api/evidenceOverview/list', {
      caseImportId: caseImportId,
      partyNames: [partyName]
    }, {
      loading: true,
    });
  },

  /**
   * 根据ID查询证据情况详情
   */
  getById(id: string | number): Promise<EvidenceOverviewResponse> {
    return http.get(`/api/evidenceOverview/${id}`, undefined, {
      loading: true,
    });
  },

  /**
   * 创建证据情况信息
   */
  create(evidenceInfo: EvidenceOverviewRequest): Promise<EvidenceOverviewResponse> {
    return http.post('/api/evidenceOverview/create', evidenceInfo, {
      loading: true,
    });
  },

  /**
   * 更新证据情况信息
   */
  update(evidenceInfo: EvidenceOverviewRequest): Promise<EvidenceOverviewResponse> {
    return http.put('/api/evidenceOverview/update', evidenceInfo, {
      loading: true,
    });
  },

  /**
   * 保存证据情况信息（根据ID自动判断新增或更新）
   */
  save(evidenceInfo: EvidenceOverviewRequest): Promise<EvidenceOverviewResponse> {
    return http.post('/api/evidenceOverview/save', evidenceInfo, {
      loading: true,
    });
  },

  /**
   * 删除证据情况信息
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(`/api/evidenceOverview/delete/${id}`, undefined, {
      loading: true,
    });
  },

  /**
   * 批量删除证据情况信息
   */
  batchDelete(ids: (string | number)[]): Promise<boolean> {
    // 使用不显示成功提示的删除方法
    const deletePromises = ids.map(id => 
      http.delete(`/api/evidenceOverview/delete/${id}`, undefined, {
        loading: false, // 避免多个loading重叠
      })
    );
    return Promise.all(deletePromises).then(() => true);
  },

  /**
   * 重新生成证据情况
   */
  reanalyze(caseImportId: string | number): Promise<void> {
    return http.post(`/api/evidenceOverview/reanalyze/${caseImportId}`, undefined, {
      loading: true,
    });
  },

}; 