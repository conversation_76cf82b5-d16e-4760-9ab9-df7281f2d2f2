import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 证据信息
 */
export interface Evidence {
  fileId: number;
  fileName: string;
  pageNumber?: number; // 页码变为可选，支持全文搜索
  highlight: string;
}

/**
 * 质证情况信息
 */
export interface VerificationEvidenceInfo {
  id?: number;
  caseImportId: number;
  partyType: string;
  partyName: string;
  verificationContent: string;
  isAdopt: number; // 0: 不采纳, 1: 采纳
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  durationMs?: number;
  evidence?: Evidence[];
  deleted?: number;
}

/**
 * 质证情况相关API
 */
export const verificationEvidenceApi = {
  /**
   * 根据案件ID查询质证情况信息列表
   */
  listByCaseImportId(caseImportId: string | number, options?: { loading?: boolean }): Promise<VerificationEvidenceInfo[]> {
    return http.get(`/api/verification-evidence/list/${caseImportId}`, undefined, {
      loading: options?.loading ?? true,
    });
  },

  /**
   * 批量删除质证情况信息（前端并发删除）
   * 使用 Promise.all 并发调用单条删除接口，减少总耗时
   */
  batchDelete(ids: Array<string | number>): Promise<boolean> {
    if (!Array.isArray(ids) || ids.length === 0) {
      return Promise.resolve(true)
    }
    const requests = ids.map((id) =>
      // 统一关闭单条成功提示，避免多次弹窗；加载动画也关闭，由调用方控制
      http.delete(`/api/verification-evidence/delete/${id}`, undefined, {
        loading: false,
        showSuccess: false,
      })
    )
    return Promise.all(requests).then(() => true)
  },

  /**
   * 根据当事人类型查询质证情况信息列表
   */
  listByPartyType(caseImportId: string | number, partyType: string): Promise<VerificationEvidenceInfo[]> {
    return http.get(`/api/verification-evidence/list/${caseImportId}/party-type/${partyType}`, undefined, {
      loading: true,
    });
  },

  /**
   * 根据案件ID和当事人姓名列表查询质证情况信息列表
   */
  listByCaseImportIdAndPartyNames(caseImportId: string | number, partyNames: string[]): Promise<VerificationEvidenceInfo[]> {
    return http.post('/api/verification-evidence/listByCaseImportIdAndPartyNames', {
      caseImportId: caseImportId,
      partyNames: partyNames
    }, {
      loading: true,
    });
  },

  /**
   * 根据采纳状态查询质证情况信息列表
   */
  listByAdoptStatus(caseImportId: string | number, isAdopt: number): Promise<VerificationEvidenceInfo[]> {
    return http.get(`/api/verification-evidence/list/${caseImportId}/adopt-status/${isAdopt}`, undefined, {
      loading: true,
    });
  },

  /**
   * 创建质证情况信息
   */
  create(verificationInfo: VerificationEvidenceInfo): Promise<boolean> {
    return http.post('/api/verification-evidence/create', verificationInfo, {
      loading: true,
      showSuccess: true,
      successMessage: '创建成功',
    });
  },

  /**
   * 更新质证情况信息
   */
  update(verificationInfo: VerificationEvidenceInfo, options?: { loading?: boolean; showSuccess?: boolean; successMessage?: string }): Promise<boolean> {
    return http.put('/api/verification-evidence/update', verificationInfo, {
      loading: options?.loading ?? true,
      showSuccess: options?.showSuccess ?? true,
      successMessage: options?.successMessage ?? '更新成功',
    });
  },

  /**
   * 删除质证情况信息
   */
  delete(id: string | number): Promise<boolean> {
    return http.delete(`/api/verification-evidence/delete/${id}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '删除成功',
    });
  },

  /**
   * 批量删除案件相关的质证情况信息
   */
  deleteByCaseImportId(caseImportId: string | number): Promise<boolean> {
    return http.delete(`/api/verification-evidence/delete/case/${caseImportId}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '批量删除成功',
    });
  },

  /**
   * 更新质证情况的采纳状态
   */
  updateAdoptStatus(id: string | number, isAdopt: number): Promise<boolean> {
    return http.put(`/api/verification-evidence/update-adopt-status/${id}/${isAdopt}`, undefined, {
      loading: true,
      showSuccess: true,
      successMessage: '更新成功',
    });
  },

  /**
   * 生成质证情况信息
   */
  generate(caseImportId: string | number, customPrompt?: string): Promise<string> {
    const params = customPrompt ? { customPrompt } : undefined;
    return http.post(`/api/verification-evidence/generate/${caseImportId}`, undefined, {
      params,
      loading: true,
    });
  },
}; 