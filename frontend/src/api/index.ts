/**
 * API统一导出文件
 */

// 导出HTTP客户端
export { http } from '@/utils/http';

// 导出API配置
export { API_ENDPOINTS } from './config';

// 导出各模块API
export { caseApi } from './case';
export { casePartyApi } from './caseParty';
export { relationGraphApi } from './relationGraph';
export { contradictionApi } from './contradiction';
export { disputeFocusApi } from './disputeFocus';
export { promptApi } from './prompt';
export { timelineApi } from './timeline';
export { verificationEvidenceApi } from './verificationEvidence';
export { evidenceFactsApi } from './evidenceFacts';
export { evidenceOverviewApi } from './evidenceOverview';
export { documentGenerationApi } from './documentGeneration';
export { moduleUpdateApi } from './moduleUpdate';

// 导出知识库配置API
export * from './knowledgeBase';

// 导出类型定义

// 案件相关类型
export type {
  CaseImportRecord,
  CaseImportSearchDTO,
  PreprocessResult,
  FileUploadRecord,
} from './case';

// 关系图谱相关类型
export type {
  RelationRole,
  RelationLink,
  RelationGraph,
} from './relationGraph';

// 矛盾识别相关类型
export type {
  Contradiction,
  ContradictionDTO,
} from './contradiction';

// 争议焦点相关类型
export type {
  DisputeFocus
} from './disputeFocus';

// 系统提示词相关类型
export type {
  SystemPromptConfig,
  SavePromptRequest,
} from './prompt';

// 时序链相关类型
export type {
  TimelineEvent,
} from './timeline';

// 质证情况相关类型
export type {
  VerificationEvidenceInfo,
  Evidence,
} from './verificationEvidence';

// 证据情况相关类型
export type {
  EvidenceOverviewInfo,
} from './evidenceOverview';

// 通用API类型
export type {
  ApiResult,
  PageParams,
  PageResult,
  RequestConfig,
  ErrorInfo,
  ResultCode,
} from '@/types/api';

// 证据事实相关类型
export type { EvidenceFactsDetails } from './evidenceFacts';

// 当事人相关类型
export type { CaseParty } from './caseParty';

export type { CasePartyTab } from './caseParty';

// 诉讼费相关类型
export type { LegalFees, LegalFeesWithParty } from './legalFees';

// 判项情况相关类型
export type { JudgmentSituation } from './judgmentSituation';

// 文书生成相关类型
export type {
  DocumentGenerationInfo,
  UpdateDocumentContentRequest,
} from './documentGeneration';

// 模块更新相关类型
export type {
  ModuleUpdateRecord,
  ModuleStatusMap,
  ModuleTypeValue,
} from './moduleUpdate';

// 知识库配置相关类型
export type {
  KnowledgeBaseConfig,
  LlmConfig,
  PromptConfig,
} from './knowledgeBase';
