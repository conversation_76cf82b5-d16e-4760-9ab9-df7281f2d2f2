import { http } from '@/utils/http';
import { API_ENDPOINTS } from './config';

/**
 * 审判组织成员信息
 */
export interface TrialOrganizationMembers {
  id?: number;
  caseImportRecordId: number;
  role: string;
  name: string;
  xh?: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
}

/**
 * 审判组织成员相关API
 */
export const trialOrganizationMembersApi = {
  /**
   * 获取案件审判组织成员列表
   */
  list(caseImportRecordId: string | number): Promise<TrialOrganizationMembers[]> {
    return http.get(API_ENDPOINTS.trialOrganizationMembers.list, undefined, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
    });
  },

  /**
   * 批量保存审判组织成员
   */
  saveBatch(caseImportRecordId: string | number, trialOrganizationMembersList: TrialOrganizationMembers[]): Promise<boolean> {
    return http.post(API_ENDPOINTS.trialOrganizationMembers.saveBatch, trialOrganizationMembersList, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
      showSuccess: true,
      successMessage: '审判组织成员保存成功',
    });
  },

  /**
   * 智能获取审判组织成员（优先从数据库获取，无数据时从庭审笔录提取）
   */
  getOrExtract(caseImportRecordId: string | number): Promise<TrialOrganizationMembers[]> {
    return http.get(API_ENDPOINTS.trialOrganizationMembers.getOrExtract, undefined, {
      params: { caseImportRecordId: Number(caseImportRecordId) },
      loading: true,
    });
  },
};