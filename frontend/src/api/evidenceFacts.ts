import { http } from '@/utils/http';

export interface EvidenceFactsDetails {
  id?: number;
  caseImportId: number | string;
  undisputedFacts: string;
  controversialFacts: string;
  dictum: string;
  determineFacts: string;
  determineFactsExtract: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  deleted?: number;
}

export const evidenceFactsApi = {
  // 获取证据事实
  get(caseImportId: string | number) {
    return http.get<EvidenceFactsDetails>(`/api/evidence-facts-details/queryByCase/${caseImportId}`);
  },
  // 新增或更新证据事实（后端自动判断）
  saveOrUpdate(data: EvidenceFactsDetails) {
    return http.post<boolean>('/api/evidence-facts-details/save', data);
  },
  // 生成无争议事实
  generateUndisputedFacts(caseImportId: string | number) {
    return http.post<string>(`/api/evidence-facts-details/generate-undisputed-facts/${caseImportId}`);
  },
  // 生成认定事实
  generateDetermineFacts(caseImportId: string | number) {
    return http.post<string>(`/api/evidence-facts-details/generate-determine-facts/${caseImportId}`);
  }
};