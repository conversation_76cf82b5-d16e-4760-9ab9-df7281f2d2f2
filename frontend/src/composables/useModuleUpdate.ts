import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  moduleUpdateApi, 
  type ModuleStatusMap, 
  type ModuleUpdateRecord,
  type ModuleTypeValue,
  getModuleName
} from '@/api/moduleUpdate'

/**
 * 模块更新状态管理Hook
 * @param caseImportId 案件导入ID
 * @param options 配置选项
 */
export function useModuleUpdate(
  caseImportId: number | null,
  options: {
    /** 是否在组件挂载时立即加载 */
    immediate?: boolean
  } = {}
) {
  const {
    immediate = true
  } = options

  // 响应式状态
  const loading = ref(false)
  const moduleStatus = ref<ModuleStatusMap>({})
  const needUpdateModules = ref<string[]>([])
  const updateRecords = ref<ModuleUpdateRecord[]>([])
  const error = ref<string | null>(null)

  /**
   * 计算属性：是否有模块需要更新
   */
  const hasUpdates = computed(() => {
    return Object.values(moduleStatus.value).some(status => status === true)
  })

  /**
   * 计算属性：需要更新的模块数量
   */
  const updateCount = computed(() => {
    return Object.values(moduleStatus.value).filter(status => status === true).length
  })

  /**
   * 计算属性：需要更新的模块名称列表
   */
  const updateModuleNames = computed(() => {
    return needUpdateModules.value.map(code => getModuleName(code))
  })

  /**
   * 加载所有模块状态
   */
  const loadModuleStatus = async () => {
    if (!caseImportId) {
      return
    }

    try {
      loading.value = true
      error.value = null

      const [statusMap, needUpdateList] = await Promise.all([
        moduleUpdateApi.getAllModuleStatus(caseImportId),
        moduleUpdateApi.getNeedUpdateModules(caseImportId)
      ])

      moduleStatus.value = statusMap
      needUpdateModules.value = needUpdateList

    } catch (err: any) {
      error.value = err.message || '加载模块状态失败'
      console.error('加载模块状态失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查特定模块是否需要更新
   */
  const checkModuleUpdate = async (moduleCode: string): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      const needUpdate = await moduleUpdateApi.checkModuleNeedUpdate(caseImportId, moduleCode)
      
      // 更新本地状态
      moduleStatus.value = {
        ...moduleStatus.value,
        [moduleCode]: needUpdate
      }

      return needUpdate
    } catch (err: any) {
      console.error(`检查模块 ${moduleCode} 更新状态失败:`, err)
      return false
    }
  }

  /**
   * 检查模块是否需要更新（新逻辑）
   */
  const checkModuleUpdateNew = async (sourceModule: string, operatedModule: string): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      return await moduleUpdateApi.checkModuleNeedUpdateNew(caseImportId, sourceModule, operatedModule)
    } catch (err: any) {
      console.error(`检查模块更新状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 标记模块为已操作（新逻辑）
   */
  const markModuleAsOperated = async (sourceModule: string, operatedModule: string): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      await moduleUpdateApi.markModuleAsOperated(caseImportId, sourceModule, operatedModule)
      
      // 更新本地状态
      moduleStatus.value = {
        ...moduleStatus.value,
        [sourceModule]: false
      }

      // 从需要更新列表中移除
      needUpdateModules.value = needUpdateModules.value.filter(code => code !== sourceModule)

      return true
    } catch (err: any) {
      console.error(`标记模块操作失败: ${err.message}`)
      return false
    }
  }

  /**
   * 重置特定模块状态
   */
  const resetModuleStatus = async (moduleCode: string): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      await moduleUpdateApi.resetModuleStatus(caseImportId, moduleCode)
      
      // 更新本地状态
      moduleStatus.value = {
        ...moduleStatus.value,
        [moduleCode]: false
      }

      // 从需要更新列表中移除
      needUpdateModules.value = needUpdateModules.value.filter(code => code !== moduleCode)

      ElMessage.success(`${getModuleName(moduleCode)}状态已重置`)
      return true
    } catch (err: any) {
      ElMessage.error(`重置${getModuleName(moduleCode)}状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 批量重置模块状态
   */
  const batchResetModuleStatus = async (moduleCodes?: string[]): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      await moduleUpdateApi.batchResetModuleStatus(caseImportId, moduleCodes)
      
      if (moduleCodes && moduleCodes.length > 0) {
        // 重置指定模块
        moduleCodes.forEach(code => {
          moduleStatus.value[code] = false
        })
        needUpdateModules.value = needUpdateModules.value.filter(
          code => !moduleCodes.includes(code)
        )
      } else {
        // 重置所有模块
        Object.keys(moduleStatus.value).forEach(code => {
          moduleStatus.value[code] = false
        })
        needUpdateModules.value = []
      }

      ElMessage.success('模块状态批量重置成功')
      return true
    } catch (err: any) {
      ElMessage.error(`批量重置模块状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 重置所有模块状态
   */
  const resetAllModuleStatus = async (): Promise<boolean> => {
    return batchResetModuleStatus()
  }

  /**
   * 加载模块更新记录详情
   */
  const loadUpdateRecords = async () => {
    if (!caseImportId) {
      return
    }

    try {
      updateRecords.value = await moduleUpdateApi.getModuleUpdateRecords(caseImportId)
    } catch (err: any) {
      console.error('加载模块更新记录失败:', err)
    }
  }

  /**
   * 手动标记模块需要更新
   */
  const markModuleNeedUpdate = async (moduleCode: string): Promise<boolean> => {
    if (!caseImportId) {
      return false
    }

    try {
      await moduleUpdateApi.markModuleNeedUpdate(caseImportId, moduleCode)
      
      // 更新本地状态
      moduleStatus.value = {
        ...moduleStatus.value,
        [moduleCode]: true
      }

      if (!needUpdateModules.value.includes(moduleCode)) {
        needUpdateModules.value.push(moduleCode)
      }

      ElMessage.success(`${getModuleName(moduleCode)}已标记为需要更新`)
      return true
    } catch (err: any) {
      ElMessage.error(`标记${getModuleName(moduleCode)}失败: ${err.message}`)
      return false
    }
  }



  /**
   * 刷新数据
   */
  const refresh = () => {
    loadModuleStatus()
  }

  // 监听案件ID变化
  watch(() => caseImportId, (newCaseId) => {
    if (newCaseId) {
      loadModuleStatus()
    } else {
      // 清空状态
      moduleStatus.value = {}
      needUpdateModules.value = []
      updateRecords.value = []
    }
  })

  // 组件挂载时的处理
  onMounted(() => {
    if (immediate && caseImportId) {
      loadModuleStatus()
    }
  })

  return {
    // 状态
    loading,
    moduleStatus,
    needUpdateModules,
    updateRecords,
    error,

    // 计算属性
    hasUpdates,
    updateCount,
    updateModuleNames,

    // 方法
    loadModuleStatus,
    checkModuleUpdate,
    checkModuleUpdateNew,
    markModuleAsOperated,
    resetModuleStatus,
    batchResetModuleStatus,
    resetAllModuleStatus,
    loadUpdateRecords,
    markModuleNeedUpdate,
    refresh
  }
}
