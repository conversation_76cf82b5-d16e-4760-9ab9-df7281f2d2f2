import { createApp } from 'vue'
import './style.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import './styles/index.scss'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { createPinia } from 'pinia'
import { useUserStore } from './stores/userStore'
import { initTaskTypeNames } from '@/api/analysisTask'

// 初始化PDF.js Worker
import { GlobalWorkerOptions } from 'pdfjs-dist/legacy/build/pdf.mjs'
import PdfWorker from 'pdfjs-dist/legacy/build/pdf.worker.min.mjs?url'

if (!GlobalWorkerOptions || !GlobalWorkerOptions.workerSrc) {
  GlobalWorkerOptions.workerSrc = PdfWorker
}

// 设置页面标题
document.title = import.meta.env.VITE_SYSTEM_TITLE || '文书生成辅助工具'

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})


// 初始化用户store
const userStore = useUserStore()
userStore.init()

// 初始化任务类型名称映射
initTaskTypeNames().catch(console.error)

app.mount('#app')
