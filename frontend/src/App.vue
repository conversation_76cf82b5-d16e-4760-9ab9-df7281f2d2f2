<script setup lang="ts">
import AppHeader from '@/components/AppHeader.vue'
import { useRoute } from 'vue-router'
import { computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/userStore'

const route = useRoute()
const userStore = useUserStore()

// 定义需要隐藏头部的路由
const hideHeaderRoutes = ['/version-history', '/login']

// 计算是否应该隐藏头部
const shouldHideHeader = computed(() => {
  return hideHeaderRoutes.includes(route.path)
})

// 应用启动时初始化用户状态
onMounted(async () => {
  await userStore.init()
})
</script>

<template>
  <el-container class="layout-container">
    <el-header v-if="!shouldHideHeader" height="80px">
      <AppHeader />
    </el-header>
    
    <el-container class="main-container" :class="{ 'no-header': shouldHideHeader }">
      <el-main>
        <router-view v-slot="{ Component, route }">
          <keep-alive :include="['TaskList']">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<style scoped lang="scss">
.layout-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;

  .el-header {
    padding: 0;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .main-container {
    height: calc(100vh - 80px);
    overflow: hidden;
  }
  
  .main-container.no-header {
    height: 100vh;
  }
  
  .el-aside {
    background-color: #fff;
    border-right: 1px solid #dcdfe6;
    height: 100%;
    overflow-y: auto;
    
    .el-menu {
      border-right: none;
    }
  }
  
  .el-main {
    background-color: #f5f7fa;
    padding: 0;
    height: 100%;
    overflow: hidden;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }
}
</style>
