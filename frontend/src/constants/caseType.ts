/**
 * 案件类型相关常量
 */

/**
 * 案件类型代码
 */
export const CASE_TYPE_CODE = {
  /** 民事一审案件 */
  CIVIL_FIRST_INSTANCE: '0301',
  
  /** 民事二审案件 */
  CIVIL_SECOND_INSTANCE: '0302',
} as const

/**
 * 案件名称关键词
 */
export const CASE_NAME_KEYWORDS = {
  /** 民事一审关键词 */
  CIVIL_FIRST_INSTANCE_KEYWORD: '民初',
  
  /** 民事二审关键词 */
  CIVIL_SECOND_INSTANCE_KEYWORD: '民终',

} as const

/**
 * 案件类型显示文本映射
 */
export const CASE_TYPE_DISPLAY = {
  [CASE_TYPE_CODE.CIVIL_FIRST_INSTANCE]: '民事一审',
  [CASE_TYPE_CODE.CIVIL_SECOND_INSTANCE]: '民事二审',
} as const

/**
 * 工具函数：判断是否为民事二审案件
 * @param ajlxdm 案件类型代码
 * @returns 是否为民事二审案件
 */
export const isCivilSecondInstance = (ajlxdm?: string): boolean => {
  return ajlxdm === CASE_TYPE_CODE.CIVIL_SECOND_INSTANCE
}

/**
 * 工具函数：判断是否为民事一审案件
 * @param ajlxdm 案件类型代码
 * @returns 是否为民事一审案件
 */
export const isCivilFirstInstance = (ajlxdm?: string): boolean => {
  return ajlxdm === CASE_TYPE_CODE.CIVIL_FIRST_INSTANCE
}

/**
 * 工具函数：获取案件类型显示文本
 * @param ajlxdm 案件类型代码
 * @returns 案件类型显示文本
 */
export const getCaseTypeDisplay = (ajlxdm?: string): string => {
  if (!ajlxdm) return '未知类型'
  return CASE_TYPE_DISPLAY[ajlxdm as keyof typeof CASE_TYPE_DISPLAY] || '未知类型'
}

/**
 * 工具函数：根据案件名称判断案件类型代码
 * @param caseName 案件名称
 * @returns 案件类型代码
 */
export const determineAjlxdmByCaseName = (caseName?: string): string => {
  if (!caseName || caseName.trim() === '') {
    return CASE_TYPE_CODE.CIVIL_FIRST_INSTANCE // 默认为民事一审
  }
  
  const caseNameTrimmed = caseName.trim()
  
  // 按优先级判断
  if (caseNameTrimmed.includes(CASE_NAME_KEYWORDS.CIVIL_SECOND_INSTANCE_KEYWORD)) {
    return CASE_TYPE_CODE.CIVIL_SECOND_INSTANCE
  } else if (caseNameTrimmed.includes(CASE_NAME_KEYWORDS.CIVIL_FIRST_INSTANCE_KEYWORD)) {
    return CASE_TYPE_CODE.CIVIL_FIRST_INSTANCE
  } else {
    // 默认为民事一审
    return CASE_TYPE_CODE.CIVIL_FIRST_INSTANCE
  }
}

/**
 * 类型定义
 */
export type CaseTypeCode = typeof CASE_TYPE_CODE[keyof typeof CASE_TYPE_CODE]
