/**
 * 文件夹配置常量
 */

/**
 * 一审案件文件夹配置
 */
export const FIRST_INSTANCE_FOLDERS = {
  '起诉状及相关材料': ['QSZ'], // 起诉状、变更诉讼请求申请书
  '反诉状及相关材料': ['FSZ'], // 反诉状
  '答辩状及相关材料': ['DBZ', 'BCDBYJ'], // 答辩状
  '诉讼参与人主体资格材料': ['SSCYRZTZGCL', 'SFZ', 'LSZ', 'HKB', 'HJXX', 'CZKRKXX', 'LDRKXX', 'HJRKXX', 'FDDRBSFZMGS', 'YYZZ', 'TYSHXYDMZS', 'GJQYXYXXGSXT', 'GH_LSSWS', 'SQWTS', 'ZYZ', 'YGSFZM', 'YGLDHT'], // 身份证、户口本、户籍信息、常住人口信息、流动人口信息、户籍人口信息、法定代表人身份证明书、营业执照、统一社会信用代码证书、国家企业信用信息公示系统、公函（律师事务所函/法律服务所函）、授权委托书、执业证、员工身份证明、员工劳动合同
  '诉讼参与人举证材料': ['ZJML', 'ZJ', 'JTQT', 'GZS', 'SDHZ', 'SFYJJDS'], // 证据清单/目录、借条/欠条、法律文书、公证书、送达回证（仲裁）、司法鉴定意见书
  '庭前会议笔录、法庭笔录及相关材料': ['TSBL'], // 法庭笔录、询问笔录
  "本院法律文书正本":["YSPJS", 'PJS'], //一审判决书
  "上诉案件相关材料":["SSZ"],
  "案件审判流程管理信息表、案件登记表":["LADJB","JAXXB","SPLCGLXXB"],
  "受理案件通知书、应诉通知书及相关材料": ["AJPDTZS","HYTZCRYTZS"],
  '其他材料': ['QT'] // 其他类型
} as const

/**
 * 二审案件文件夹配置
 */
export const SECOND_INSTANCE_FOLDERS = {
  '原审法院法律文书': ['YSPJS', 'PJS'], // 原审法院判决书、原审法院裁定书、原审法院调解书、原审法院决定书
  '上诉状及相关材料': ['SSZ'], // 上诉状
  '答辩状及相关材料': ['DBZ', 'BCDBYJ'], // 答辩状
  '诉讼参与人主体资格材料': ['SSCYRZTZGCL', 'SFZ', 'LSZ', 'HKB', 'HJXX', 'CZKRKXX', 'LDRKXX', 'HJRKXX', 'FDDRBSFZMGS', 'YYZZ', 'TYSHXYDMZS', 'GJQYXYXXGSXT', 'GH_LSSWS', 'SQWTS', 'ZYZ', 'YGSFZM', 'YGLDHT'], // 身份证、户口本、户籍信息、常住人口信息、流动人口信息、户籍人口信息、法定代表人身份证明书、营业执照、统一社会信用代码证书、国家企业信用信息公示系统、公函（律师事务所函/法律服务所函）、授权委托书、执业证、员工身份证明、员工劳动合同
  '诉讼参与人举证材料': ['ZJML', 'ZJ', 'JTQT', 'GZS', 'SDHZ', 'SFYJJDS'], // 证据清单/目录、借条/欠条、法律文书、公证书、送达回证（仲裁）、司法鉴定意见书
  '庭前会议笔录、法庭笔录及相关材料': ['TSBL'], // 法庭笔录、询问笔录
  "案件审判流程管理信息表、案件登记表":["LADJB","JAXXB","SPLCGLXXB"],
  "受理案件通知书、应诉通知书及相关材料": ["AJPDTZS","HYTZCRYTZS"],
  '其他材料': ['QT'] // 其他类型
} as const

/**
 * 文件夹配置类型
 */
export type FolderConfig = typeof FIRST_INSTANCE_FOLDERS | typeof SECOND_INSTANCE_FOLDERS

/**
 * 根据案件类型获取文件夹配置
 * @param ajlxdm 案件类型代码
 * @returns 文件夹配置
 */
export const getFolderConfig = (ajlxdm?: string): FolderConfig => {
  // 判断是否为二审案件
  if (ajlxdm === '0302' || ajlxdm === 'MSES') {
    return SECOND_INSTANCE_FOLDERS
  }
  // 默认返回一审配置
  return FIRST_INSTANCE_FOLDERS
}

/**
 * 根据案件类型获取文件夹名称列表
 * @param ajlxdm 案件类型代码
 * @returns 文件夹名称列表
 */
export const getFolderNames = (ajlxdm?: string): string[] => {
  const config = getFolderConfig(ajlxdm)
  return Object.keys(config)
}

/**
 * 根据案件类型和文档类型获取所属文件夹名称
 * @param ajlxdm 案件类型代码
 * @param documentType 文档类型
 * @returns 文件夹名称，如果未找到则返回"其他材料"
 */
export const getFolderNameByDocumentType = (ajlxdm?: string, documentType?: string): string => {
  if (!documentType) return '其他材料'

  const config = getFolderConfig(ajlxdm)

  for (const [folderName, documentTypes] of Object.entries(config)) {
    if ((documentTypes as readonly string[]).includes(documentType as string)) {
      return folderName
    }
  }

  return '其他材料'
}
