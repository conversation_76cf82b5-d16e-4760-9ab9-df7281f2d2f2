/**
 * 争议焦点相关常量
 */

/**
 * 诉讼当事人类型
 */
export const PARTY_TYPE = {
  /** 诉方/原告 */
  PLAINTIFF: 'plaintiff',
  
  /** 辩方/被告 */
  DEFENDANT: 'defendant'
} as const

/**
 * 争议焦点结论类型
 */
export const FOCUS_CONCLUSION = {
  /** 是 */
  YES: 'yes',

  /** 否 */
  NO: 'no',

  /** 其他 */
  OTHER: 'other'
} as const

/**
 * 诉辩关系类型
 */
export const RELATION_TYPE = {
  /** 对立关系 */
  OPPOSITION: 'opposition',

  /** 支持关系 */
  SUPPORT: 'support',

  /** 补充关系 */
  SUPPLEMENT: 'supplement',

  /** 其他关系 */
  OTHER: 'other'
} as const

/**
 * 当事人类型的显示文本映射
 */
export const PARTY_TYPE_DISPLAY = {
  [PARTY_TYPE.PLAINTIFF]: '诉方',
  [PARTY_TYPE.DEFENDANT]: '辩方'
} as const

/**
 * 工具函数：获取当事人类型的显示文本
 */
export const getPartyTypeDisplay = (partyType: string): string => {
  return PARTY_TYPE_DISPLAY[partyType as keyof typeof PARTY_TYPE_DISPLAY] || partyType
}

/**
 * 工具函数：根据显示文本获取当事人类型值
 */
export const getPartyTypeValue = (displayText: string): string => {
  const entry = Object.entries(PARTY_TYPE_DISPLAY).find(([_, display]) => display === displayText)
  return entry ? entry[0] : displayText
}

/**
 * 类型定义
 */
export type PartyType = typeof PARTY_TYPE[keyof typeof PARTY_TYPE]
export type FocusConclusion = typeof FOCUS_CONCLUSION[keyof typeof FOCUS_CONCLUSION]
export type RelationType = typeof RELATION_TYPE[keyof typeof RELATION_TYPE]
