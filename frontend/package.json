{"name": "yuejuanzhushou", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node buildWithGitLog.js", "vite-build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@handsontable/vue3": "^15.3.0", "@types/crypto-js": "^4.2.2", "@types/d3": "^7.4.3", "@types/echarts": "^4.9.22", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "axios": "^1.9.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.9", "event-source-polyfill": "^1.0.31", "github-markdown-css": "^5.8.1", "handsontable": "^15.3.0", "highlight.js": "^11.11.1", "jsplumb": "^2.15.6", "mammoth": "^1.9.0", "marked": "^15.0.11", "mitt": "^3.0.1", "pdfjs-dist": "^4.10.38", "pinia": "^3.0.3", "qs": "^6.14.0", "sass": "^1.87.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^22.15.12", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}