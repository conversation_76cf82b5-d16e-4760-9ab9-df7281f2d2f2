<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/src/assets/court-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>%VITE_SYSTEM_TITLE%</title>
    <script>
      // 从环境变量注入配置
      window.ENV = {
        VITE_API_BASE_URL: '%VITE_API_BASE_URL%'
      }
    </script>
      <script>
          // 兼容低版本浏览器的工具函数
          function getChromeVersion(){
              var userAgent = navigator.userAgent;
              console.log(userAgent);
              var chromeMatch = userAgent.match(/(?:Chrome|Chromium)\/(\d+\.\d+\.\d+(?:\.\d+)?)/);

              if (chromeMatch && chromeMatch[1]) {
                  return chromeMatch[1];
              }

              return null;
          }
          
          function getOS(){
              var userAgent = navigator.userAgent;

              if (userAgent.indexOf('Windows') !== -1) return 'Windows';
              if (userAgent.indexOf('Linux') !== -1 && userAgent.indexOf('Android') === -1) return 'Linux';
              if (userAgent.indexOf('Mac OS') !== -1) return 'MacOS';
              return 'Unknown';
          }
          
          function compareVersions(v1, v2) {
              var parts1 = v1.split('.').map(function(n) { return parseInt(n, 10); });
              var parts2 = v2.split('.').map(function(n) { return parseInt(n, 10); });
              var maxLength = Math.max(parts1.length, parts2.length);
              
              for (var i = 0; i < maxLength; i++) {
                  var part1 = i < parts1.length ? parts1[i] : 0;
                  var part2 = i < parts2.length ? parts2[i] : 0;
                  
                  if (part1 < part2) return -1;
                  if (part1 > part2) return 1;
              }
              
              return 0;
          }
          
          function isChromeVersionBelow(version) {
              var currentVersion = getChromeVersion();
              
              if (currentVersion === null) {
                  return null;
              }
              
              if (typeof version === 'number') {
                  var currentMajorVersion = parseInt(currentVersion.split('.')[0], 10);
                  return currentMajorVersion < version;
              }
              
              return compareVersions(currentVersion, version) < 0;
          }
          
          // 安全的属性访问函数，替代可选链操作符
          function safeGet(obj, path, defaultValue) {
              if (!obj) return defaultValue;
              var keys = path.split('.');
              var current = obj;
              for (var i = 0; i < keys.length; i++) {
                  if (current === null || current === undefined || !current.hasOwnProperty(keys[i])) {
                      return defaultValue;
                  }
                  current = current[keys[i]];
              }
              return current;
          }
          
          // 获取Chrome推荐版本的函数
          function getChromeRecommendation(os) {
              var apiBaseUrl = window.ENV.VITE_API_BASE_URL; // 从环境变量获取的API基础URL
              var url = apiBaseUrl + '/api/version/getChromeRecommendation?os=' + encodeURIComponent(os);
              
              console.log('请求URL:', url);
              
              return fetch(url)
                  .then(function(response) {
                      console.log('响应状态:', response.status, response.statusText);
                      console.log('响应头Content-Type:', response.headers.get('content-type'));
                      
                      if (!response.ok) {
                          throw new Error('HTTP错误: ' + response.status + ' ' + response.statusText);
                      }
                      
                      // 先获取响应文本，然后尝试解析JSON
                      return response.text().then(function(text) {
                          console.log('响应原始文本:', text);
                          try {
                              var jsonData = JSON.parse(text);
                              console.log('解析后的JSON数据:', jsonData);
                              return jsonData;
                          } catch (parseError) {
                              console.error('JSON解析错误:', parseError);
                              console.error('无法解析的文本内容:', text);
                              throw new Error('服务器返回的不是有效的JSON格式: ' + parseError.message);
                          }
                      });
                  })
                  .then(function(data) {
                      console.log('API返回的完整数据:', data);
                      if (data && data.data) {
                          console.log('提取的Chrome信息:', data.data);
                          return data.data;
                      } else {
                          throw new Error('API返回数据格式不正确，缺少data字段');
                      }
                  })
                  .catch(function(error) {
                      console.error('getChromeRecommendation请求失败:', error);
                      throw error;
                  });
          }
          
          // 浏览器版本检查函数
          function checkChromeVersion() {
              console.log('开始执行浏览器版本检查');
              
              var currentVersion = getChromeVersion();
              console.log('当前Chrome版本:', currentVersion);
              
              if (!currentVersion) {
                  console.log('无法获取Chrome版本信息，跳过版本检查');
                  return;
              }
              
              var os = getOS();
              console.log('检测到的操作系统:', os);
              
              getChromeRecommendation(os)
                  .then(function(chromeInfo) {
                      console.log('成功获取Chrome推荐信息:', chromeInfo);
                      
                      var recommendedVersion = chromeInfo.version;
                      console.log('推荐版本:', recommendedVersion);
                      console.log('下载地址:', chromeInfo.downloadUrl);
                      
                      var isVersionBelow = isChromeVersionBelow(recommendedVersion);
                      console.log('当前版本是否低于推荐版本:', isVersionBelow);
                      
                      if (isVersionBelow) {
                          console.log('版本过低，显示升级提示对话框');
                          
                          // 创建提示对话框
                          var overlay = document.createElement('div');
                          overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;';
                          
                          var dialog = document.createElement('div');
                          dialog.style.cssText = 'background: white; padding: 30px; border-radius: 8px; max-width: 500px; text-align: center; box-shadow: 0 4px 12px rgba(0,0,0,0.3);';
                          
                          dialog.innerHTML = 
                              '<h3 style="margin-top: 0; color: #e6a23c;">亲爱的用户，您好！</h3>' +
                              '<div style="text-align: left; line-height: 1.6; margin: 20px 0;">' +
                              '<p>感谢您使用我们的全新智能文书辅助系统！为了给您提供更强大、更流畅的AI智能体验，我们采用了前沿的技术来构建。</p>' +
                              '<p>我们检测到您当前的浏览器版本稍旧，可能无法完全支持系统内的智能交互、实时分析和文书生成服务等高级功能。</p>' +
                              '<p>为了确保您能完整、顺畅地体验我们为您准备的AI新世界，建议您安装新版本的浏览器。</p>' +
                              '<p style="font-weight: bold;">只需一小步，拥抱智能新体验：</p>' +
                              '</div>' +
                              '<div style="margin-top: 20px;">' +
                              '<button id="downloadBtn" style="background: #409eff; color: white; border: none; padding: 12px 30px; border-radius: 4px; cursor: pointer; font-size: 16px;">👉 点击这里下载/升级浏览器</button>' +
                              '</div>' +
                              '<p style="margin-top: 15px; color: #666; font-size: 14px;">升级完成后，打开新的浏览器即可开启更智能、更高效的旅程！</p>';
                          
                          overlay.appendChild(dialog);
                          document.body.appendChild(overlay);
                          
                          // 绑定事件
                          document.getElementById('downloadBtn').onclick = function() {
                              console.log('用户点击下载/升级浏览器，打开链接:', chromeInfo.downloadUrl);
                              window.open(chromeInfo.downloadUrl, '_blank');
                              document.body.removeChild(overlay);
                          };
                      } else {
                          console.log('当前版本满足要求，无需升级');
                      }
                  })
                  .catch(function(error) {
                      console.error('检查浏览器版本时出错:', error);
                      console.error('错误详情:', {
                          message: error.message,
                          stack: error.stack,
                          name: error.name
                      });
                  });
          }
          
          // 页面加载完成后执行浏览器版本检查
          document.addEventListener('DOMContentLoaded', function() {
              checkChromeVersion();
          });
      </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
