variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  # 镜像仓库地址
  DOCKER_REGISTRY: "**************:5000"
  # 后端镜像名称
  BACKEND_IMAGE: "${DOCKER_REGISTRY}/tszj/backend"
  # 前端镜像名称
  FRONTEND_IMAGE: "${DOCKER_REGISTRY}/tszj/frontend"
  # Docker 仓库认证信息
  DOCKER_AUTH_CONFIG: '{"auths":{"**************:5000":{"username":"$HARBOR_USER","password":"$HARBOR_PASSWORD"}}}'

# 定义流水线阶段
stages:
  - build
  - package
  - push
  - deploy

# 缓存配置
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .m2/repository/
    - frontend/node_modules/

# 后端构建
build-backend:
  stage: build
  tags:
    - maven
  script:
    - cd backend
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - backend/target/*.jar
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == ""
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"

# 前端构建（测试环境）
build-frontend:
  stage: build
  tags:
    - vue
  script:
    - cd frontend
    - npm install
    - npx vite build --mode test
  artifacts:
    paths:
      - frontend/dist
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"

# 前端构建（生产环境）
build-frontend-production:
  stage: build
  tags:
    - vue
  script:
    - cd frontend
    - npm install
    - npm run build
  artifacts:
    paths:
      - frontend/dist
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual


# 构建 ARM 架构镜像并打包为 tar
build-arm-images:
  stage: package
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - |
      docker login **************:5000 -u admin -p smxz.harbor.smxz
      # 配置多平台支持
      docker run --privileged --rm tonistiigi/binfmt --install all
      # 预拉取ARM架构的基础镜像到本地
      docker pull **************:5000/base/arm/openjdk:17-jdk
      docker pull **************:5000/base/arm/nginx:latest || true
      # 使用默认构建器（支持本地镜像）
      docker buildx use default
      docker buildx ls
      # 清理旧的构建文件
      rm -f yjzs_*-x86-*.tar yjzs_*-arm64-*.tar            
  script:
    - |
      # 构建 arm64 平台镜像并保存
      docker buildx build --platform linux/arm64 \
       --build-arg ARCH_PATH="arm/" \
       -t yjzs_backend-arm64 \
       -f Dockerfiles/backend.Dockerfile \
       --load .   
      docker save yjzs_backend-arm64 > yjzs_backend-arm64-${CI_COMMIT_SHA}.tar
      
      docker buildx build --platform linux/arm64 \
       --build-arg ARCH_PATH="arm/" \
       -t yjzs_frontend-arm64 \
       -f Dockerfiles/frontend.Dockerfile \
       --load .
      docker save yjzs_frontend-arm64 > yjzs_frontend-arm64-${CI_COMMIT_SHA}.tar
  artifacts:
    paths:
      - yjzs_backend-arm64-${CI_COMMIT_SHA}.tar
      - yjzs_frontend-arm64-${CI_COMMIT_SHA}.tar
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["build-backend", "build-frontend-production"]

# 构建 X86 架构镜像并打包为 tar
build-x86-images:
  stage: package
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - |
      docker login **************:5000 -u admin -p smxz.harbor.smxz
      # 配置多平台支持
      docker run --privileged --rm tonistiigi/binfmt --install all
      # 预拉取X86架构的基础镜像到本地
      docker pull **************:5000/base/openjdk:17-jdk
      docker pull **************:5000/base/nginx:latest || true
      # 使用默认构建器（支持本地镜像）
      docker buildx use default
      docker buildx ls
      # 清理旧的构建文件
      rm -f yjzs_*-x86-*.tar yjzs_*-arm64-*.tar      
  script:
    - |
      # 构建 x86 平台镜像并保存
      docker buildx build --platform linux/amd64 \
       --build-arg ARCH_PATH="" \
       -t yjzs_backend-x86 \
       -f Dockerfiles/backend.Dockerfile \
       --load .   
      docker save yjzs_backend-x86 > yjzs_backend-x86-${CI_COMMIT_SHA}.tar
      
      docker buildx build --platform linux/amd64 \
       --build-arg ARCH_PATH="" \
       -t yjzs_frontend-x86 \
       -f Dockerfiles/frontend.Dockerfile \
       --load .
      docker save yjzs_frontend-x86 > yjzs_frontend-x86-${CI_COMMIT_SHA}.tar
  artifacts:
    paths:
      - yjzs_backend-x86-${CI_COMMIT_SHA}.tar
      - yjzs_frontend-x86-${CI_COMMIT_SHA}.tar
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["build-backend", "build-frontend-production"]




# 构建并推送后端Docker镜像
push-backend:
  stage: push
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - docker build -t ${BACKEND_IMAGE}:${CI_COMMIT_SHA} -f Dockerfiles/backend.Dockerfile .
    - docker tag ${BACKEND_IMAGE}:${CI_COMMIT_SHA} ${BACKEND_IMAGE}:latest
    - docker push ${BACKEND_IMAGE}:${CI_COMMIT_SHA}
    - docker push ${BACKEND_IMAGE}:latest
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"
  needs: ["build-backend"]

# 构建并推送前端Docker镜像
push-frontend:
  stage: push
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - docker build -t ${FRONTEND_IMAGE}:${CI_COMMIT_SHA} -f Dockerfiles/frontend.Dockerfile .
    - docker tag ${FRONTEND_IMAGE}:${CI_COMMIT_SHA} ${FRONTEND_IMAGE}:latest
    - docker push ${FRONTEND_IMAGE}:${CI_COMMIT_SHA}
    - docker push ${FRONTEND_IMAGE}:latest
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"
  needs: ["build-frontend"]

# 后端服务部署
deploy-backend:
  stage: deploy
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - cd Dockerfiles
    - docker-compose pull yjzs_backend
    - docker-compose up -d yjzs_backend --remove-orphans
    - docker-compose ps
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["push-backend"]
  environment:
    name: production-backend

# 前端服务部署
deploy-frontend:
  stage: deploy
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - **************:5000/base/docker-dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - cd Dockerfiles
    - export BACKEND_IMAGE_TAG=${CI_COMMIT_SHA}
    - export FRONTEND_IMAGE_TAG=${CI_COMMIT_SHA}
    - docker-compose pull yjzs_front
    - docker-compose up -d yjzs_front --remove-orphans
    - docker-compose ps
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.2/佛山730"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["push-frontend"]
  environment:
    name: production-frontend

# SonarQube代码检查
sonarqube:
  stage: sonar
  tags:
    - maven
  script:
    - cd backend
    - mvn sonar:sonar
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always