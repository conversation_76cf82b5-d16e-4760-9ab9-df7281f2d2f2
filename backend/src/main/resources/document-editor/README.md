# OnlyOffice 文档编辑插件

这是一个功能完整的OnlyOffice插件，支持格式化文本插入、文本替换、光标定位、书签操作等功能。

## 功能特性

1. **格式化文本插入** - 支持字体、大小、位置、加粗等格式设置
2. **文本替换** - 支持搜索和替换文档中的文本
3. **换行插入** - 插入指定数量的换行
4. **光标定位** - 定位到指定文字的开头或结尾
5. **书签操作** - 插入书签和跳转到书签位置

## 安装方法

1. 将整个 `document-editor` 目录复制到OnlyOffice服务器的插件目录
2. 重启OnlyOffice服务
3. 在OnlyOffice编辑器中，插件会自动加载

## 使用方法

### 1. 格式化文本插入

```javascript
window.docEditor.serviceCommand('PasteText', {
  text: '这是要插入的文本',
  font: '宋体',           // 字体名称
  fontSize: '三号',       // 中文字号或数字
  position: '居中',       // 对齐方式：左对齐、居中、右对齐、两端对齐
  bold: true,            // 是否加粗
  italic: false          // 是否斜体
});
```

### 2. 文本替换

```javascript
window.docEditor.serviceCommand('ReplaceText', {
  searchText: '要替换的文本',
  replaceText: '新文本',
  isReplaceAll: true,    // 是否全部替换
  isMatchCase: false     // 是否区分大小写
});
```

### 3. 插入换行

```javascript
window.docEditor.serviceCommand('InsertLineBreak', {
  count: 2  // 插入的换行数量
});
```

### 4. 光标定位

```javascript
window.docEditor.serviceCommand('SetCursorPosition', {
  target: '目标文字',     // 要定位的文字
  position: 'end'        // 'start' 或 'end'
});
```

### 5. 插入书签

```javascript
window.docEditor.serviceCommand('InsertBookmark', {
  name: 'bookmark1',     // 书签名称
  text: '书签文本'       // 书签显示的文本（可选）
});
```

### 6. 跳转到书签

```javascript
window.docEditor.serviceCommand('GotoBookmark', {
  name: 'bookmark1'      // 书签名称
});
```

## 字体大小对照表

| 中文字号 | 磅值 |
|---------|------|
| 初号    | 42   |
| 小初    | 36   |
| 一号    | 26   |
| 小一    | 24   |
| 二号    | 22   |
| 小二    | 18   |
| 三号    | 16   |
| 小三    | 15   |
| 四号    | 14   |
| 小四    | 12   |
| 五号    | 10.5 |
| 小五    | 9    |
| 六号    | 7.5  |
| 小六    | 6.5  |
| 七号    | 5.5  |

## 对齐方式

- `左对齐` - 文本左对齐
- `居中` - 文本居中对齐
- `右对齐` - 文本右对齐
- `两端对齐` - 文本两端对齐

## 注意事项

1. 插件需要OnlyOffice支持宏功能
2. 所有操作都是异步的，可能需要等待执行完成
3. 建议在操作间添加适当的延迟，避免并发问题
4. 插件会在控制台输出调试信息，便于排查问题

## 调试方法

### 使用测试命令
```javascript
// 测试API可用性
window.docEditor.serviceCommand('TestAPI', {});
```

### 查看控制台日志
插件会在浏览器控制台输出详细的调试信息，包括：
- 命令接收状态
- API调用过程
- 错误信息

### 常见问题和解决方案

#### 问题1：插件返回成功但文档无内容
**可能原因**：
- OnlyOffice API对象不可用
- 文档状态未就绪

**解决方案**：
1. 使用TestAPI命令检查API可用性
2. 确保在文档完全加载后再调用插件
3. 检查控制台是否有API相关错误

#### 问题2：格式化参数不生效
**解决方案**：
1. 使用标准字号（如14、16等数字）
2. 使用英文对齐参数（left、center、right）

#### 问题3：插件命令无响应
**解决方案**：
1. 检查插件是否正确安装
2. 重启OnlyOffice服务
3. 检查浏览器控制台错误信息

## 故障排除

如果插件无法正常工作，请检查：

1. OnlyOffice版本是否支持插件功能
2. 插件文件是否正确放置在插件目录
3. 浏览器控制台是否有错误信息
4. OnlyOffice服务是否正常运行
5. 宏功能是否已启用
6. 使用TestAPI命令检查API状态
