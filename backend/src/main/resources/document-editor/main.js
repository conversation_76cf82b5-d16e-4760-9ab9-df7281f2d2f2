(function(window, undefined) {

  var AscPlugin = window.Asc.plugin;

  // 排版调整延时常量（毫秒）
  var LAYOUT_ADJUSTMENT_DELAY = 80;

  // 命令处理器映射
  var commandHandlers = {
    'PasteText': handlePasteText,
    'ReplaceText': handleReplaceText,
    'InsertLineBreak': handleInsertLineBreak,
    'SetCursorPosition': handleSetCursorPosition,
    'InsertBookmark': handleInsertBookmark,
    'GotoBookmark': handleGotoBookmark,
    'InsertCharacter': handleInsertCharacter,
    'CheckAndAdjustLayout': handleCheckAndAdjustLayout,
    "MoveCursorToEnd": handleMoveCursorToEnd,
    'optimizeDocumentContent': handleOptimizeDocumentContent
  };

  // 监听器状态跟踪
  var listenerState = {
    bound: false,
    callCount: 0,
    lastCall: null,
    errors: []
  };

  AscPlugin.init = function(initData) {
    console.log('[DocumentEditor] 插件初始化开始');

    // 避免重复绑定监听器（关键修复）
    if(!window['boundDocumentEditorCommand']){
      console.log('[DocumentEditor] 首次绑定监听器');
      try {
        // 监听来自前端的命令
        window.parent.Common.Gateway.on('internalcommand', function(args) {
        try {
          // 确保参数有效
          if (!args) {
            console.error('[DocumentEditor] 收到空参数');
            return;
          }

          var data = args.data;
          var command = args.command;
          
          if (!command) {
            console.error('[DocumentEditor] 收到无效命令');
            return;
          }

          // 只对非InsertCharacter命令打印日志，避免流式输出时日志过多
          if (command !== 'InsertCharacter') {
            console.log('[DocumentEditor] 收到命令 #' + ':', command, data);
          }


          // 查找对应的命令处理器
          var handler = commandHandlers[command];
          if (handler) {
            try {
              // 直接同步执行，避免异步导致的监听器失效问题
              handler(data);
              // 只对非InsertCharacter命令打印日志，避免流式输出时日志过多
              if (command !== 'InsertCharacter') {
                console.log('[DocumentEditor] 命令处理完成:', command);
              }
            } catch (handlerError) {
              console.error('[DocumentEditor] 处理器执行失败:', handlerError);
            }
          } else {
            console.error('[DocumentEditor] 未知命令: ' + command);
          }
        } catch (listenerError) {
          console.error('[DocumentEditor] 监听器处理失败:', listenerError);
        }
      });
      
      listenerState.bound = true;
      console.log('[DocumentEditor] 监听器绑定成功');
    } catch (bindError) {
      console.error('[DocumentEditor] 监听器绑定失败:', bindError);
    }
    setTimeout(function() {
      console.log('[DocumentEditor] 文档内容就绪，发送消息到父窗口');
      try {
        window.parent.parent.postMessage({
          type: 'onlyoffice-document-ready',
          source: 'document-editor-plugin'
        }, '*');
        console.log('[DocumentEditor] 文档就绪消息已发送');
      } catch (error) {
        console.error('[DocumentEditor] 发送文档就绪消息失败:', error);
      }
    }, 500);


    // 设置绑定标志，避免重复绑定
    window['boundDocumentEditorCommand'] = true;
    console.log('[DocumentEditor] 设置绑定标志完成');
  } else {
    console.log('[DocumentEditor] 监听器已存在，跳过绑定');
  }

    // 暴露状态诊断方法
    window.DocumentEditorListenerState = listenerState;
    console.log('[DocumentEditor] 监听器状态已暴露到 window.DocumentEditorListenerState');
    
    // 在插件弹出窗外释放鼠标时触发
    AscPlugin.onExternalMouseUp = function() {
      var event = document.createEvent('MouseEvents');
      event.initMouseEvent('mouseup', true, true, window, 1, 0, 0, 0, 0, false, false, false, false, 0, null);
      document.dispatchEvent(event);
    };

    AscPlugin.button = function(id) {
      // 关闭插件弹出窗触发
      if (id === -1) {
        console.log('[DocumentEditor] 插件关闭，清理资源');
        this.executeCommand('close', '');
      }
    };

    console.log('[DocumentEditor] 插件初始化完成');
  };


  /**
   * 处理格式化文本插入
   */
  function handlePasteText(options) {
    console.log('[DocumentEditor] 处理文本插入开始', options);

    // 如果options是字符串，转换为对象
    if (typeof options === 'string') {
      options = { text: options };
    }

    try {
      // 将参数传递到插件作用域
      Asc.scope.textOptions = options;
      if (options.bookmark) {
        AscPlugin.executeMethod("MoveCursorToEnd", [true]);
      }
      
      // 如果有spacing参数，执行特殊逻辑
      if (options.spacing) {
        return handlePasteTextWithSpacing(options);
      }
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        // 中文字号转换为磅值
        function convertChineseFontSizeToPoints(chineseSize) {
          var sizeMap = {
            '初号': 42, '小初': 36, '一号': 26, '小一': 24, '二号': 22, '小二': 18,
            '三号': 16, '小三': 15, '四号': 14, '小四': 12, '五号': 10.5, '小五': 9,
            '六号': 7.5, '小六': 6.5, '七号': 5.5
          };
          return sizeMap[chineseSize] || 14;
        }

        // 中文对齐方式转换
        function convertChineseAlignmentToOnlyOffice(chineseAlignment) {
          var alignmentMap = {
            '左对齐': 'left', '居中': 'center', '右对齐': 'right', '两端对齐': 'both'
          };
          return alignmentMap[chineseAlignment] || 'left';
        }

        var options = Asc.scope.textOptions;
        var oDocument = Api.GetDocument();
        var oParagraph = Api.CreateParagraph();
        var oRun = Api.CreateRun();

        // 设置文本内容
        var textContent = options.text || '';
        oRun.AddText(textContent);

        // 设置文本属性
        if (options.font || options.fontSize || options.bold || options.italic) {
          var oTextPr = oRun.GetTextPr();

          // 设置字体
          if (options.font) {
            oTextPr.SetFontFamily(options.font);
          }

          // 设置字号
          if (options.fontSize) {
            var pointSize = convertChineseFontSizeToPoints(options.fontSize);
            // OnlyOffice API使用半点值，需要乘以2
            var halfPointSize = pointSize * 2;
            oTextPr.SetFontSize(halfPointSize);
          }

          // 设置加粗
          if (options.bold) {
            oTextPr.SetBold(true);
          }

          // 设置斜体
          if (options.italic) {
            oTextPr.SetItalic(true);
          }
        }

        // 添加文本运行到段落
        oParagraph.AddElement(oRun);

        // 设置段落对齐
        if (options.position) {
          var alignment = convertChineseAlignmentToOnlyOffice(options.position);
          oParagraph.SetJc(alignment);
        }

        // 设置段落缩进（正文每段开头空两格）
        if (options.indent || options.indentFirstLine) {
          // 首行缩进2字符，约等于663缇(1字符=360缇)
          oParagraph.SetIndFirstLine(663);
        }

        // 设置尾部缩进（案号、日期等后面空两格）
        if (options.tailIndent) {
          // 尾部缩进2字符，约等于663缇(1字符=360缇)
          oParagraph.SetIndRight(663);
        }

        // 插入段落到文档
        oDocument.InsertContent([oParagraph]);
        // 如果有书签参数，将书签标记添加到当前段落的开头
        if (options.bookmark) {
          try {
            let range = oParagraph.GetRange(0, 1);
            range.AddBookmark(options.bookmark);
          } catch (bookmarkError) {
            console.warn('[DocumentEditor] 书签插入失败:', bookmarkError);
          }
        }
      }, false);

      console.log('[DocumentEditor] 文本插入命令已发送');
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 文本插入失败:', error);
      return false;
    }
  }

  /**
   * 处理带字体宽度的文本插入
   */
  function handlePasteTextWithSpacing(options) {
    console.log('[DocumentEditor] 处理带字符间距的文本插入', options);

    try {
      // 将参数传递到插件作用域
      Asc.scope.textOptions = options;
      if (options.bookmark) {
        AscPlugin.executeMethod("MoveCursorToEnd", [true]);
      }
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.textOptions;
        var text = options.text || '';
        var spacing = options.spacing || 0; // 直接使用传入的spacing值
        
        // 查找<fw></fw>标签
        var fwStartIndex = text.indexOf('<fw>');
        var fwEndIndex = text.indexOf('</fw>');
        
        if (fwStartIndex !== -1 && fwEndIndex !== -1 && fwEndIndex > fwStartIndex) {
          // 分割文本
          var beforeFw = text.substring(0, fwStartIndex);
          var insideFw = text.substring(fwStartIndex + 4, fwEndIndex); // +4 是 '<fw>' 的长度
          var afterFw = text.substring(fwEndIndex + 5); // +5 是 '</fw>' 的长度
          
          console.log('分割文本:', { beforeFw, insideFw, afterFw, spacing });
          
          // 创建文档和段落
          var oDocument = Api.GetDocument();
          var oParagraph = Api.CreateParagraph();
          
          // 中文字号转换为磅值
           function convertChineseFontSizeToPoints(chineseSize) {
             var sizeMap = {
               '初号': 42, '小初': 36, '一号': 26, '小一': 24, '二号': 22, '小二': 18,
               '三号': 16, '小三': 15, '四号': 14, '小四': 12, '五号': 10.5, '小五': 9,
               '六号': 7.5, '小六': 6.5, '七号': 5.5
             };
             return sizeMap[chineseSize] || 14;
           }
           
           // 应用文本格式的通用函数
           function applyTextFormat(oRun, options) {
             if (options.font || options.fontSize || options.bold || options.italic) {
               var oTextPr = oRun.GetTextPr();
               
               if (options.font) {
                 oTextPr.SetFontFamily(options.font);
               }
               
               if (options.fontSize) {
                 var pointSize = convertChineseFontSizeToPoints(options.fontSize);
                 var halfPointSize = pointSize * 2;
                 oTextPr.SetFontSize(halfPointSize);
               }
               
               if (options.bold) {
                 oTextPr.SetBold(true);
               }
               
               if (options.italic) {
                 oTextPr.SetItalic(true);
               }
             }
           }
           
           // 插入<fw>前面的文字
           if (beforeFw) {
             var oRunBefore = Api.CreateRun();
             oRunBefore.AddText(beforeFw);
             applyTextFormat(oRunBefore, options);
             oParagraph.AddElement(oRunBefore);
           }
           
           // 处理<fw></fw>之间的数据，根据fontwidth调整spacing
           if (insideFw) {
             var oRunInside = Api.CreateRun();
             oRunInside.AddText(insideFw);
             
             // 应用基本文本格式
             applyTextFormat(oRunInside, options);
             
             // 计算spacing值：根据最长姓名长度和当前姓名长度的差值来调整
             // 直接使用传入的spacing值
             if (spacing > 0) {
               // 设置字符间距
               var oTextPr = oRunInside.GetTextPr();
               oTextPr.SetSpacing(spacing);
             }
             
             oParagraph.AddElement(oRunInside);
           }
           
           // 插入</fw>之后的数据
           if (afterFw) {
             var oRunAfter = Api.CreateRun();
             oRunAfter.AddText(afterFw);
             applyTextFormat(oRunAfter, options);
             oParagraph.AddElement(oRunAfter);
           }
          
          // 应用其他格式设置
          if (options.position) {
            var alignmentMap = {
              '左对齐': 'left', '居中': 'center', '右对齐': 'right', '两端对齐': 'both'
            };
            var alignment = alignmentMap[options.position] || 'left';
            oParagraph.SetJc(alignment);
          }
          
          // 设置段落缩进
          if (options.indent || options.indentFirstLine) {
            oParagraph.SetIndFirstLine(663);
          }
          if (options.tailIndent) {
            oParagraph.SetIndRight(663);
          }
          
          // 插入段落到文档
          oDocument.InsertContent([oParagraph]);
          
          // 处理书签
          if (options.bookmark) {
            try {
              var range = oParagraph.GetRange(0, 1);
              range.AddBookmark(options.bookmark);
            } catch (bookmarkError) {
              console.warn('[DocumentEditor] 书签插入失败:', bookmarkError);
            }
          }
        } else {
          // 如果没有找到<fw></fw>标签，按普通方式处理
          console.log('未找到<fw></fw>标签，按普通方式处理');
          // 这里可以调用原来的处理逻辑
        }
      }, false);

      console.log('[DocumentEditor] 带字体宽度的文本插入命令已发送');
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 带字体宽度的文本插入失败:', error);
      return false;
    }
  }

  /**
   * 处理文本替换
   */
  function handleReplaceText(options) {
    console.log('[DocumentEditor] 处理文本替换', options);

    try {
      // 将参数传递到插件作用域
      Asc.scope.replaceOptions = options;
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.replaceOptions;
        var oDocument = Api.GetDocument();
        var searchText = options.searchText || '';
        var replaceText = options.replaceText || '';
        var isReplaceAll = options.isReplaceAll !== false;
        var isMatchCase = options.isMatchCase || false;

        // 执行替换
        oDocument.Search(searchText, isMatchCase, isReplaceAll, replaceText);
      }, false);

      console.log('[DocumentEditor] 文本替换命令已发送');
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 文本替换失败:', error);
      return false;
    }
  }

  function handleMoveCursorToEnd(options) {

    try {
      // 使用OnlyOffice插件API将光标移动到文档末尾
      AscPlugin.executeMethod("MoveCursorToEnd", [true]);
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 换行插入失败:', error);
      return false;
    }
  }

  /**
   * 处理换行插入
   */
  function handleInsertLineBreak(options) {
    console.log('[DocumentEditor] 处理换行插入');

    try {
      // 将参数传递到插件作用域
      Asc.scope.lineBreakOptions = options;
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.lineBreakOptions;
        var oDocument = Api.GetDocument();
        var count = options.count || 1;
        var paragraphs = [];

        for (var i = 0; i < count; i++) {
          paragraphs.push(Api.CreateParagraph());
        }
      }, false);

      console.log('[DocumentEditor] 换行插入命令已发送');
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 换行插入失败:', error);
      return false;
    }
  }

  /**
   * 处理光标定位
   */
  function handleSetCursorPosition(options) {
    console.log('[DocumentEditor] 处理光标定位', options);

    try {
      // 将参数传递到插件作用域
      Asc.scope.cursorOptions = options;
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.cursorOptions;
        var oDocument = Api.GetDocument();
        var target = options.target || '';
        var position = options.position || 'end';

        if (target) {
          try {
            // OnlyOffice搜索API返回布尔值，搜索成功后会自动选中找到的文本
            var found = oDocument.Search(target, false, false);
            if (found) {
              console.log('[DocumentEditor] 成功定位到目标文本:', target);
              // 搜索成功，光标已经定位到目标位置，不需要额外操作
            } else {
              console.warn('[DocumentEditor] 未找到目标文本:', target);
            }
          } catch (searchError) {
            console.error('[DocumentEditor] 搜索过程中出错:', searchError);
          }
        }
      }, false);

      console.log('[DocumentEditor] 光标定位命令已发送');
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 光标定位失败:', error);
      return false;
    }
  }

  /**
   * 处理书签插入
   */
  function handleInsertBookmark(options) {
    console.log('[DocumentEditor] 处理书签插入', options);

    try {
      // 将参数传递到插件作用域
      Asc.scope.bookmarkOptions = options;
      // 使用OnlyOffice插件API将光标移动到文档末尾
      AscPlugin.executeMethod("MoveCursorToEnd", [true]);
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.bookmarkOptions;
        var oDocument = Api.GetDocument();
        var bookmarkName = options.name || '';
        var bookmarkText = options.text || '';

        if (bookmarkName) {
          try {
            // 获取当前光标位置的选择范围
            var oRange = oDocument.GetRangeBySelect();

            if (oRange && oRange.AddBookmark && typeof oRange.AddBookmark === 'function') {
              oRange.AddBookmark(bookmarkName);
              console.log('[DocumentEditor] 书签插入成功:', bookmarkName);
            }
          } catch (bookmarkError) {
            console.error('[DocumentEditor] 书签插入失败:', bookmarkError);
          }
        }
        return bookmarkName;
      }, false,true,function ( character){
        sendInsertCharracterMessage(character)
      });
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 书签插入失败:', error);
      return false;
    }
  }

  /**
   * 处理跳转到书签
   */
  function handleGotoBookmark(options) {
    console.log('[DocumentEditor] 处理跳转书签', options);

    try {
      // 将参数传递到插件作用域
      Asc.scope.gotoOptions = options;
      
      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.gotoOptions;
        var oDocument = Api.GetDocument();
        var bookmarkName = options.name || '';

        if (bookmarkName) {
          try {
            // 使用正确的OnlyOffice书签API跳转
            var bookmark = oDocument.GetBookmark(bookmarkName);
            if (bookmark && bookmark.GoTo && typeof bookmark.GoTo === 'function') {
              bookmark.GoTo();
            } else {
              console.warn('[DocumentEditor] 书签不存在或无法跳转:', bookmarkName);
            }
          } catch (gotoError) {
            console.error('[DocumentEditor] 跳转书签失败:', gotoError);
          }
        }
      }, false);
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 跳转书签失败:', error);
      return false;
    }
  }

  /**
   * 处理逐字符插入
   */
  function handleInsertCharacter(options) {
    try {
      // 如果options是字符串，转换为对象
      if (typeof options === 'string') {
        options = { character: options };
      }

      var character = options.character || '';

      if (character === '' || character === null || character === undefined) {
        return false;
      }

      // 将参数传递到插件作用域
      Asc.scope.characterOptions = options;

      // 使用官方推荐的callCommand方式
      AscPlugin.callCommand(function() {
        var options = Asc.scope.characterOptions;
        var character = options.character || '';
        var needIndent = options.needIndent || false;
        var fontFamily = options.fontFamily || '';
        var fontSize = options.fontSize || 0;

        // 获取当前文档
        var oDocument = Api.GetDocument();

        // 特殊处理换行符
        if (character === '\n') {
          // 插入换行符，创建新段落
          var oParagraph = Api.CreateParagraph();
          oDocument.Push(oParagraph);
        } else {
          // 处理普通字符
          // 获取最后一个段落
          var oLastParagraph = oDocument.GetElement(oDocument.GetElementsCount() - 1);

          if (oLastParagraph && oLastParagraph.GetClassType && oLastParagraph.GetClassType() === "paragraph") {
            // 尝试复用段落中最后一个Run对象
            var oRun = null;
            var elementsCount = oLastParagraph.GetElementsCount ? oLastParagraph.GetElementsCount() : 0;

            if (elementsCount > 0) {
              // 获取段落中最后一个元素
              var lastElement = oLastParagraph.GetElement(elementsCount - 1);

              // 检查是否为Run对象且可以添加文本
              if (lastElement && lastElement.GetClassType && lastElement.GetClassType() === "run") {
                oRun = lastElement;
              }
            }

            // 如果没有找到可复用的Run，创建新的
            if (!oRun) {
              oRun = Api.CreateRun();
              // 设置字体（从参数传入）
              if (fontFamily) {
                oRun.SetFontFamily(fontFamily);
              }
              if (fontSize > 0) {
                oRun.SetFontSize(fontSize);
              }
              oLastParagraph.AddElement(oRun);
            }

            // 检查是否需要设置段落缩进
            if (needIndent) {
              oLastParagraph.SetIndFirstLine(663);
            }

            // 向Run添加字符
            oRun.AddText(character);

          } else {
            // 如果最后一个元素不是段落，创建新段落
            var oParagraph = Api.CreateParagraph();

            // 检查是否需要设置段落缩进
            if (needIndent) {
              oParagraph.SetIndFirstLine(663);
            }

            var oRun = Api.CreateRun();
            // 设置字体（从参数传入）
            if (fontFamily) {
              oRun.SetFontFamily(fontFamily);
            }
            if (fontSize > 0) {
              oRun.SetFontSize(fontSize);
            }
            oRun.AddText(character);
            oParagraph.AddElement(oRun);
            oDocument.Push(oParagraph);
          }
        }
        return character;
      },false,true,function ( character){
        sendInsertCharracterMessage(character)
      });

      return true;
    } catch (error) {
      console.error('[DocumentEditor] 插入字符失败:', error);
      return false;
    }
  }

  /**
   * 检查并调整文档排版，确保审判组织成员等内容不跨页
   */
  function handleCheckAndAdjustLayout(options) {
    console.log('[DocumentEditor] 开始检查并调整文档排版');

    try {
      // 将参数传递到插件作用域
      Asc.scope.layoutOptions = options;

      // 开始执行拆分后的函数链
      executeStep1_FindRightAlignedParagraph();

    } catch (error) {
      console.error('[DocumentEditor] 排版检查和调整失败:', error);
      // 发送排版失败消息
      sendLayoutCompletedMessage('failed');
      return false;
    }
  }

  // 函数1：从后往前获取第一个右对齐段落的index（A）
  function executeStep1_FindRightAlignedParagraph() {
    console.log('[DocumentEditor] 步骤1：查找第一个右对齐段落');

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var elementsCount = oDocument.GetElementsCount();
      var rightAlignedIndex = -1;

      for (var i = elementsCount - 1; i >= 0; i--) {
        var element = oDocument.GetElement(i);
        if (element && element.GetClassType && element.GetClassType() === "paragraph") {
          try {
            var paraPr = element.GetParaPr();
            var jc = null;
            if (paraPr && paraPr.GetJc) {
              jc = paraPr.GetJc();
            }

            if (jc === "right") {
              rightAlignedIndex = i;
              console.log('[DocumentEditor] 找到右对齐段落，位置: ' + i);
              break;
            }
          } catch (error) {
            console.warn('[DocumentEditor] 检查段落对齐方式失败:', error);
          }
        }
      }

      return rightAlignedIndex;
    }, false, true, function(rightAlignedIndex) {
      if (rightAlignedIndex >= 0) {
        Asc.scope.rightAlignedIndex = rightAlignedIndex;
        console.log('[DocumentEditor] 保存rightAlignedIndex到scope，开始执行步骤2');
        // 使用setTimeout打破嵌套调用链
        setTimeout(function() {
          executeStep2_FindNonRightAlignedParagraph();
        }, LAYOUT_ADJUSTMENT_DELAY);
      } else {
        console.log('[DocumentEditor] 未找到右对齐段落，排版检查结束');
        // 发送排版完成消息（没有找到需要调整的内容）
        sendLayoutCompletedMessage('completed');
      }
    });
  }

  // 函数2：从后往前获取第一个非右对齐段落的index（B）
  function executeStep2_FindNonRightAlignedParagraph() {
    console.log('[DocumentEditor] 步骤2：查找第一个非右对齐段落');

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var elementsCount = oDocument.GetElementsCount();
      var nonRightAlignedIndex = -1;
      var rightAlignedIndex = Asc.scope.rightAlignedIndex; // 获取已找到的右对齐段落位置
      var skipLeftAlignedAfterRight = true; // 标记是否需要跳过右对齐段落后的左对齐段落

      for (var i = elementsCount - 1; i >= 0; i--) {
        var element = oDocument.GetElement(i);
        if (element && element.GetClassType && element.GetClassType() === "paragraph") {
          try {
            var paraPr = element.GetParaPr();
            var jc = null;
            if (paraPr && paraPr.GetJc) {
              jc = paraPr.GetJc();
            }

            var paragraphText = element.GetText ? element.GetText() : '';


            // 如果当前段落在右对齐段落之后且是左对齐，跳过（属于落款部分）
            if (skipLeftAlignedAfterRight && i < rightAlignedIndex && jc === "left") {
              skipLeftAlignedAfterRight = false;
              continue;
            }

            // 修改逻辑：优先查找有内容的非右对齐段落，如果没有则查找任何非右对齐段落
            if (jc !== "right") {
              if (paragraphText && paragraphText.trim() !== '') {
                nonRightAlignedIndex = i;
                console.log('[DocumentEditor] 找到非右对齐段落，位置: ' + i);
                break;
              } else if (nonRightAlignedIndex === -1) {
                // 如果还没找到任何非右对齐段落，记录这个空的非右对齐段落作为备选
                nonRightAlignedIndex = i;
              }
            }
          } catch (error) {
            console.warn('[DocumentEditor] 检查段落对齐方式失败:', error);
          }
        }
      }

      return nonRightAlignedIndex;
    }, false, true, function(nonRightAlignedIndex) {
      if (nonRightAlignedIndex >= 0) {
        Asc.scope.nonRightAlignedIndex = nonRightAlignedIndex;
        console.log('[DocumentEditor] 保存nonRightAlignedIndex到scope，开始执行步骤3');
        // 使用setTimeout打破嵌套调用链
        setTimeout(function() {
          executeStep3_MoveCursorToNonRightAligned();
        }, LAYOUT_ADJUSTMENT_DELAY);
      } else {
        console.log('[DocumentEditor] 未找到非右对齐段落，排版检查结束');
        // 发送排版完成消息（没有找到需要调整的内容）
        sendLayoutCompletedMessage('completed');
      }
    });
  }
  // 函数3：移动光标到非右对齐段落末尾
  function executeStep3_MoveCursorToNonRightAligned() {
    console.log('[DocumentEditor] 步骤3：移动光标到非右对齐段落末尾');

    moveCursorToParagraphEnd(Asc.scope.nonRightAlignedIndex, function() {
      // 使用setTimeout打破嵌套调用链
      setTimeout(function() {
        executeStep4_GetPageNumberA();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 函数4：获取当前页数（PagenumberA）
  function executeStep4_GetPageNumberA() {
    console.log('[DocumentEditor] 步骤4：获取非右对齐段落的页码');

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var currentPage = 1;

      try {
        if (oDocument.GetCurrentPage) {
          currentPage = oDocument.GetCurrentPage();
          console.log('[DocumentEditor] 非右对齐段落页码: ' + currentPage);
        }
      } catch (error) {
        console.warn('[DocumentEditor] 获取页码失败:', error);
      }

      return currentPage;
    }, false, true, function(pageNumberA) {
      Asc.scope.pageNumberA = pageNumberA;
      // 使用setTimeout打破嵌套调用链
      setTimeout(function() {
        executeStep5_MoveCursorToRightAligned();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 函数5：移动光标到右对齐段落末尾
  function executeStep5_MoveCursorToRightAligned() {
    console.log('[DocumentEditor] 步骤5：移动光标到右对齐段落末尾');

    moveCursorToParagraphEnd(Asc.scope.rightAlignedIndex, function() {
      // 使用setTimeout打破嵌套调用链
      setTimeout(function() {
        executeStep6_GetPageNumberB();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 函数6：获取当前页数（PagenumberB）
  function executeStep6_GetPageNumberB() {
    console.log('[DocumentEditor] 步骤6：获取右对齐段落的页码');

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var currentPage = 1;

      try {
        if (oDocument.GetCurrentPage) {
          currentPage = oDocument.GetCurrentPage();
          console.log('[DocumentEditor] 右对齐段落页码: ' + currentPage);
        }
      } catch (error) {
        console.warn('[DocumentEditor] 获取页码失败:', error);
      }

      return currentPage;
    }, false, true, function(pageNumberB) {
      Asc.scope.pageNumberB = pageNumberB;
      // 使用setTimeout打破嵌套调用链
      setTimeout(function() {
        executeStep7_CheckAndAdjustLayout();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 通用的光标移动函数（函数3和函数5共用）
  function moveCursorToParagraphEnd(paragraphIndex, callback) {
    console.log('[DocumentEditor] 移动光标到段落 ' + paragraphIndex + ' 的末尾');

    // 将参数传递到插件作用域
    Asc.scope.moveCursorOptions = {
      paragraphIndex: paragraphIndex,
      callback: callback
    };

    AscPlugin.callCommand(function() {
      var options = Asc.scope.moveCursorOptions;
      var paragraphIndex = options.paragraphIndex;

      var oDocument = Api.GetDocument();
      var paragraph = oDocument.GetElement(paragraphIndex);

      if (!paragraph) {
        console.warn('[DocumentEditor] 段落不存在，索引: ' + paragraphIndex);
        return false;
      }

      // 获取段落中的最后一个元素
      var elementsCount = paragraph.GetElementsCount();
      if (elementsCount > 0) {
        var lastElement = paragraph.GetElement(elementsCount - 1);
        if (lastElement && lastElement.GetClassType && lastElement.GetClassType() === "run") {
          // 移动光标到最后一个run的末尾
          var textLength = lastElement.GetText ? lastElement.GetText().length : 0;
          if (lastElement.MoveCursorToPos) {
            lastElement.MoveCursorToPos(textLength);
            console.log('[DocumentEditor] 成功移动光标到段落末尾');
            return true;
          }
        }
      }

      console.warn('[DocumentEditor] 无法移动光标到段落末尾');
      return false;
    }, false, true, function(success) {
      var callback = Asc.scope.moveCursorOptions.callback;
      if (success) {
        // 等待让光标移动生效
        setTimeout(function() {
          callback();
        }, LAYOUT_ADJUSTMENT_DELAY);
      } else {
        console.warn('[DocumentEditor] 光标移动失败，继续执行下一步');
        callback();
      }
    });
  }

  // 函数7：判断是否在同一页并修改行距、字距
  function executeStep7_CheckAndAdjustLayout() {
    console.log('[DocumentEditor] 步骤7：检查页码并调整排版');

    var pageNumberA = Asc.scope.pageNumberA;
    var pageNumberB = Asc.scope.pageNumberB;

    console.log('[DocumentEditor] 非右对齐段落页码: ' + pageNumberA);
    console.log('[DocumentEditor] 右对齐段落页码: ' + pageNumberB);

    var needsAdjustment = false;

    // 检查是否在同一页
    if (pageNumberA !== pageNumberB) {
      console.log('[DocumentEditor] 检测到段落不在同一页，需要调整排版');
      needsAdjustment = true;
    } else {
      console.log('[DocumentEditor] 段落在同一页，排版符合要求');
    }

    if (needsAdjustment) {
      adjustLayoutForSamePage();
    } else {
      sendLayoutCompletedMessage('completed');
      console.log('[DocumentEditor] 排版检查完成，无需调整');
    }
  }

  // 调整排版使内容在同一页
  function adjustLayoutForSamePage() {
    console.log('[DocumentEditor] 开始调整排版以确保内容在同一页');

    // 确保Asc.scope存在
    if (!Asc.scope) {
      Asc.scope = {};
    }

    // 在函数开始就设置调整参数到scope（模仿其他成功函数的模式）
    Asc.scope.adjustmentParams = {
      maxAttempts: 13,
      currentAttempt: 0,
      // 调整策略：第一次设为最小值，后续逐步增大
      adjustmentRatios: ['min', 1.05, 1.10, 1.15, 1.20, 1.21, 1.22, 1.23, 1.24, 1.25, 1.26, 1.27,1.28], // 第一次最小值，后续逐步增大
      minLineSpacing: 397, // 最小行间距：0.7cm ≈ 19.85磅 * 20 = 397缇
      maxLineSpacing: 800, // 最大行间距：1.2cm ≈ 34磅 * 20 = 680缇
      baseLineSpacing: {} // 记录每个段落的基础行间距
    };

    console.log('[DocumentEditor] 开始执行循环调整');

    // 直接开始循环调整，不需要callCommand
    setTimeout(function() {
      executeAdjustmentLoop();
    }, LAYOUT_ADJUSTMENT_DELAY);
  }

  // 循环调整函数
  function executeAdjustmentLoop() {

    // 安全检查：确保Asc.scope存在
    if (!Asc || !Asc.scope) {
      console.error('[DocumentEditor] Asc.scope不存在，无法继续调整');
      return;
    }

    // 安全检查：确保adjustmentParams存在
    if (!Asc.scope.adjustmentParams) {
      console.log('[DocumentEditor] adjustmentParams未初始化，重新初始化');
      Asc.scope.adjustmentParams = {
        maxAttempts: 13,
        currentAttempt: 0,
        adjustmentRatios: ['min', 1.05, 1.10, 1.15, 1.20, 1.21, 1.22, 1.23, 1.24, 1.25, 1.26, 1.27,1.28],
        minLineSpacing: 410, // 最小行间距：0.7cm ≈ 397缇
        maxLineSpacing: 800, // 最大行间距：1.2cm ≈ 680缇
        baseLineSpacing: {}
      };
    }

    var params = Asc.scope.adjustmentParams;
    console.log('[DocumentEditor] 当前尝试次数: ' + (params.currentAttempt + 1) + '/' + params.maxAttempts);

    // 检查是否达到最大尝试次数
    if (params.currentAttempt >= params.maxAttempts) {
      console.log('[DocumentEditor] 达到最大调整次数，停止调整');
      sendLayoutCompletedMessage('completed');
      return;
    }

    // 执行调整
    performSingleAdjustment(function() {
      // 调整完成后，重新检查页码
      setTimeout(function() {
        recheckPageNumbers();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 执行单次调整
  function performSingleAdjustment(callback) {
    console.log('[DocumentEditor] 执行单次调整');

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var elementsCount = oDocument.GetElementsCount();

      // 安全检查：确保adjustmentParams存在
      if (!Asc.scope.adjustmentParams) {
        console.error('[DocumentEditor] adjustmentParams在performSingleAdjustment中未找到');
        return false;
      }

      var params = Asc.scope.adjustmentParams;
      var attempt = params.currentAttempt;

      var adjustmentSuccess = false;

      // 只调整行间距，不调整字符间距
      if (attempt < params.adjustmentRatios.length) {
        var ratio = params.adjustmentRatios[attempt];
        var isMinValue = (ratio === 'min');
        var isIncreasing = (typeof ratio === 'number' && ratio > 1.0);

        if (isMinValue) {
          console.log('[DocumentEditor] 第' + (attempt + 1) + '次调整：设置为最小行间距');
        } else {
          console.log('[DocumentEditor] 第' + (attempt + 1) + '次调整：行间距' + (isIncreasing ? '增大' : '缩小') + '至' + (ratio * 100) + '%');
        }

        // 调整行间距
        try {
          var adjustedCount = 0;
          var endIndex = Asc.scope.nonRightAlignedIndex || elementsCount -1;
          for (var j = 0; j < endIndex + 1; j++) {
            var para = oDocument.GetElement(j);
            if (para && para.GetClassType && para.GetClassType() === "paragraph") {
              try {
                var oParaPr = para.GetParaPr();
                if (oParaPr && oParaPr.SetSpacingLine) {
                  var baseLineSpacing = 240; // 默认基础行间距

                  // 检查是否已经记录了基础行间距
                  if (params.baseLineSpacing[j]) {
                    // 使用已记录的基础行间距
                    baseLineSpacing = params.baseLineSpacing[j];
                  } else {
                    // 第一次处理这个段落，尝试多种方法获取真实行间距
                    try {
                      // 方法1：GetSpacingLineValue
                      if (oParaPr.GetSpacingLineValue) {
                        var spacingValue = oParaPr.GetSpacingLineValue();
                        if (spacingValue && spacingValue > 0) {
                          baseLineSpacing = spacingValue;
                        }
                      }

                      // 方法2：GetSpacingLine（如果方法1无效）
                      if (baseLineSpacing === 240 && oParaPr.GetSpacingLine) {
                        var spacingLine = oParaPr.GetSpacingLine();
                        if (spacingLine && spacingLine > 0) {
                          baseLineSpacing = spacingLine;
                        }
                      }

                      // 方法3：基于字体大小估算（如果前两种方法都无效）
                      if (baseLineSpacing === 240) {
                        // 获取段落中第一个Run的字体大小
                        var elementsCount = para.GetElementsCount();
                        if (elementsCount > 0) {
                          var firstRun = para.GetElement(0);
                          if (firstRun && firstRun.GetClassType && firstRun.GetClassType() === "run") {
                            var textPr = firstRun.GetTextPr();
                            if (textPr && textPr.GetFontSize) {
                              var fontSize = textPr.GetFontSize();
                              if (fontSize && fontSize > 0) {
                                // 1.16倍行距 = fontSize * 1.16 * 20 (转换为缇)
                                baseLineSpacing = Math.round(fontSize * 1.16 * 20);
                              }
                            }
                          }
                        }
                      }

                      // 如果仍然是默认值，使用更合理的默认值（1.16倍行距，12磅字体）
                      if (baseLineSpacing === 240) {
                        baseLineSpacing = Math.round(12 * 1.16 * 20); // 278缇
                      }

                    } catch (getError) {
                      console.warn('[DocumentEditor] 获取现有行间距失败:', getError);
                      // 使用估算的默认值
                      baseLineSpacing = Math.round(12 * 1.16 * 20); // 278缇
                    }

                    // 记录基础行间距
                    params.baseLineSpacing[j] = baseLineSpacing;
                  }

                  // 基于基础行间距计算新值
                  var newLineSpacing;
                  var originalNewSpacing;

                  if (ratio === 'min') {
                    // 第一次调整：直接设置为最小值
                    newLineSpacing = params.minLineSpacing;
                    originalNewSpacing = newLineSpacing;
                  } else {
                    // 后续调整：基于基础行间距计算
                    newLineSpacing = Math.round(baseLineSpacing * ratio);
                    originalNewSpacing = newLineSpacing;
                  }

                  // 应用最小和最大限制
                  var wasLimited = false;
                  if (newLineSpacing < params.minLineSpacing) {
                    newLineSpacing = params.minLineSpacing;
                    wasLimited = true;
                    console.log('[DocumentEditor] 段落 ' + j + ' 行间距从 ' + originalNewSpacing + ' 限制到最小值: ' + params.minLineSpacing);
                  } else if (newLineSpacing > params.maxLineSpacing) {
                    newLineSpacing = params.maxLineSpacing;
                    wasLimited = true;
                    console.log('[DocumentEditor] 段落 ' + j + ' 行间距从 ' + originalNewSpacing + ' 限制到最大值: ' + params.maxLineSpacing);
                  }

                  // 获取当前实际行间距进行比较
                  var currentSpacing = baseLineSpacing; // 默认使用基础行间距
                  try {
                    // 尝试获取当前设置的行间距
                    if (oParaPr.GetSpacingLineValue) {
                      var currentValue = oParaPr.GetSpacingLineValue();
                      if (currentValue && currentValue > 0) {
                        currentSpacing = currentValue;
                      }
                    }
                  } catch (e) {
                    // 忽略获取错误
                  }

                  // 如果新值和当前值相同，跳过设置
                  if (Math.abs(newLineSpacing - currentSpacing) < 2) { // 允许2缇的误差
                    continue; // 跳过这个段落
                  }

                  if (ratio === 'min') {
                    console.log('[DocumentEditor] 段落 ' + j + ' - 基础行间距: ' + baseLineSpacing + ', 新行间距: ' + newLineSpacing + ' (设为最小值)');
                  } else {
                    console.log('[DocumentEditor] 段落 ' + j + ' - 基础行间距: ' + baseLineSpacing + ', 新行间距: ' + newLineSpacing + ' (比例: ' + (ratio * 100) + '%)');
                  }

                  // 使用"exact"规则，精确控制行间距
                  oParaPr.SetSpacingLine(newLineSpacing, "exact");
                  if (para.SetParaPr) {
                    para.SetParaPr(oParaPr);
                  }
                  adjustedCount++;
                }
              } catch (paraError) {
                console.warn('[DocumentEditor] 调整段落行间距失败:', paraError);
              }
            }
          }
          adjustmentSuccess = adjustedCount > 0;
        } catch (error) {
          console.error('[DocumentEditor] 行间距调整失败:', error);
        }
      } else {
        // 如果超出了行间距调整的范围，说明已经尝试了所有可能的调整
        console.log('[DocumentEditor] 已尝试所有行间距调整，无法进一步优化');
        adjustmentSuccess = false;
      }

      console.log('[DocumentEditor] 单次调整完成，成功: ' + adjustmentSuccess);
      return adjustmentSuccess;
    }, false, true, function(success) {
      // 在回调中增加尝试次数（确保在外部作用域中生效）
      if (Asc.scope.adjustmentParams) {
        Asc.scope.adjustmentParams.currentAttempt++;
      }
      callback();
    });
  }

  // 重新检查页码
  function recheckPageNumbers() {
    console.log('[DocumentEditor] 重新检查页码');

    // 步骤1：移动光标到非右对齐段落并获取页码A
    recheckStep1_MoveCursorToNonRightAligned();
  }

  // 重新检查步骤1：移动光标到非右对齐段落
  function recheckStep1_MoveCursorToNonRightAligned() {

    // 将参数传递到插件作用域（只传递数据，不传递函数）
    Asc.scope.recheckCursorOptions = {
      paragraphIndex: Asc.scope.nonRightAlignedIndex
    };

    AscPlugin.callCommand(function() {
      var options = Asc.scope.recheckCursorOptions;
      var paragraphIndex = options.paragraphIndex;

      var oDocument = Api.GetDocument();
      var paragraph = oDocument.GetElement(paragraphIndex);

      if (!paragraph) {
        console.warn('[DocumentEditor] 段落不存在，索引: ' + paragraphIndex);
        return false;
      }

      // 获取段落中的最后一个元素
      var elementsCount = paragraph.GetElementsCount();
      if (elementsCount > 0) {
        var lastElement = paragraph.GetElement(elementsCount - 1);
        if (lastElement && lastElement.GetClassType && lastElement.GetClassType() === "run") {
          // 移动光标到最后一个run的末尾
          var textLength = lastElement.GetText ? lastElement.GetText().length : 0;
          if (lastElement.MoveCursorToPos) {
            lastElement.MoveCursorToPos(textLength);
            console.log('[DocumentEditor] 成功移动光标到非右对齐段落末尾');
            return true;
          }
        }
      }

      console.warn('[DocumentEditor] 无法移动光标到段落末尾');
      return false;
    }, false, true, function(success) {
      if (success) {
        // 等待光标移动生效，然后获取页码A
        setTimeout(function() {
          recheckStep2_GetPageNumberA();
        }, LAYOUT_ADJUSTMENT_DELAY);
      } else {
        console.warn('[DocumentEditor] 光标移动失败，继续执行下一步');
        recheckStep2_GetPageNumberA();
      }
    });
  }

  // 重新检查步骤2：获取页码A
  function recheckStep2_GetPageNumberA() {

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var currentPage = 1;

      try {
        if (oDocument.GetCurrentPage) {
          currentPage = oDocument.GetCurrentPage();
          console.log('[DocumentEditor] 非右对齐段落页码: ' + currentPage);
        }
      } catch (error) {
        console.warn('[DocumentEditor] 获取页码失败:', error);
      }

      return currentPage;
    }, false, true, function(pageA) {
      console.log('[DocumentEditor] 获取到页码A: ' + pageA);
      Asc.scope.pageNumberA = pageA;

      // 继续移动光标到右对齐段落
      setTimeout(function() {
        recheckStep3_MoveCursorToRightAligned();
      }, LAYOUT_ADJUSTMENT_DELAY);
    });
  }

  // 重新检查步骤3：移动光标到右对齐段落
  function recheckStep3_MoveCursorToRightAligned() {

    // 将参数传递到插件作用域（只传递数据，不传递函数）
    Asc.scope.recheckCursorOptions = {
      paragraphIndex: Asc.scope.rightAlignedIndex
    };

    AscPlugin.callCommand(function() {
      var options = Asc.scope.recheckCursorOptions;
      var paragraphIndex = options.paragraphIndex;

      var oDocument = Api.GetDocument();
      var paragraph = oDocument.GetElement(paragraphIndex);

      if (!paragraph) {
        console.warn('[DocumentEditor] 段落不存在，索引: ' + paragraphIndex);
        return false;
      }

      // 获取段落中的最后一个元素
      var elementsCount = paragraph.GetElementsCount();
      if (elementsCount > 0) {
        var lastElement = paragraph.GetElement(elementsCount - 1);
        if (lastElement && lastElement.GetClassType && lastElement.GetClassType() === "run") {
          // 移动光标到最后一个run的末尾
          var textLength = lastElement.GetText ? lastElement.GetText().length : 0;
          if (lastElement.MoveCursorToPos) {
            lastElement.MoveCursorToPos(textLength);
            console.log('[DocumentEditor] 成功移动光标到右对齐段落末尾');
            return true;
          }
        }
      }

      console.warn('[DocumentEditor] 无法移动光标到段落末尾');
      return false;
    }, false, true, function(success) {
      if (success) {
        // 等待光标移动生效，然后获取页码B
        setTimeout(function() {
          recheckStep4_GetPageNumberB();
        }, LAYOUT_ADJUSTMENT_DELAY);
      } else {
        console.warn('[DocumentEditor] 光标移动失败，继续执行下一步');
        recheckStep4_GetPageNumberB();
      }
    });
  }

  // 重新检查步骤4：获取页码B并比较
  function recheckStep4_GetPageNumberB() {

    AscPlugin.callCommand(function() {
      var oDocument = Api.GetDocument();
      var currentPage = 1;

      try {
        if (oDocument.GetCurrentPage) {
          currentPage = oDocument.GetCurrentPage();
          console.log('[DocumentEditor] 右对齐段落页码: ' + currentPage);
        }
      } catch (error) {
        console.warn('[DocumentEditor] 获取页码失败:', error);
      }

      return currentPage;
    }, false, true, function(pageB) {
      console.log('[DocumentEditor] 获取到页码B: ' + pageB);
      Asc.scope.pageNumberB = pageB;

      var pageA = Asc.scope.pageNumberA;
      console.log('[DocumentEditor] 重新检查结果 - 非右对齐段落页码: ' + pageA + ', 右对齐段落页码: ' + pageB);

      // 检查是否在同一页
      if (pageA === pageB) {
        console.log('[DocumentEditor] 调整成功！段落现在在同一页');
        // 发送排版完成消息给应用
        sendLayoutCompletedMessage('completed');
      } else {
        console.log('[DocumentEditor] 仍不在同一页，继续调整');
        setTimeout(function() {
          executeAdjustmentLoop();
        }, LAYOUT_ADJUSTMENT_DELAY);
      }
    });
  }

  // 发送排版完成消息给应用
  function sendLayoutCompletedMessage(status) {
    try {
      console.log('[DocumentEditor] 发送排版状态消息:', status);

      window.parent.parent.postMessage({
        type: 'layoutAdjustment',
        status: status, // 'completed' 或 'failed'
      }, '*');
      console.log('[DocumentEditor] 排版状态消息已发送');
    } catch (error) {
      console.error('[DocumentEditor] 发送排版状态消息失败:', error);
    }
  }

  // 发送插入成功消息给应用
  function sendInsertCharracterMessage(character) {
    // 发送字符插入成功消息
    try {
      window.parent.parent.postMessage({
        type: 'characterInserted',
        status: 'success',
        character: character
      }, '*');
    } catch (error) {
      console.error('[DocumentEditor] 发送字符插入成功消息失败:', error);
    }
  }

  /**
   * 全文内容优化处理（包括外币检查、人民币字样处理等）
   */
  function handleOptimizeDocumentContent(options) {
    console.log('[DocumentEditor] 开始全文内容优化处理');
    
    try {
      // 将参数传递到插件作用域
      Asc.scope.checkRMBOptions = options;
      
      AscPlugin.callCommand(function() {
        var oDocument = Api.GetDocument();
        var elementsCount = oDocument.GetElementsCount();
        var fullText = '';
        var hasForeignCurrency = false;
        
        // 定义外币关键词列表
        var foreignCurrencies = ['美元', '欧元', '英镑', '日元', '韩元', '港币', '澳元', '加元', '瑞士法郎', 'USD', 'EUR', 'GBP', 'JPY', 'KRW', 'HKD', 'AUD', 'CAD', 'CHF', '$', '€', '£', '¥'];
        
        console.log('[DocumentEditor] 开始获取全文内容，共' + elementsCount + '个段落');
        
        // 第一步：获取全文内容并检查是否包含外币
        for (var i = 0; i < elementsCount; i++) {
          var element = oDocument.GetElement(i);
          if (element && element.GetClassType && element.GetClassType() === "paragraph") {
            try {
              var paragraphText = element.GetText ? element.GetText() : '';
              fullText += paragraphText;
              
              // 检查当前段落是否包含外币关键词
              for (var j = 0; j < foreignCurrencies.length; j++) {
                if (paragraphText.indexOf(foreignCurrencies[j]) !== -1) {
                  hasForeignCurrency = true;
                  console.log('[DocumentEditor] 发现外币关键词: ' + foreignCurrencies[j]);
                  break;
                }
              }
              
              if (hasForeignCurrency) {
                break; // 已发现外币，无需继续检查
              }
            } catch (error) {
              console.warn('[DocumentEditor] 获取段落文本失败:', error);
            }
          }
        }
        
        console.log('[DocumentEditor] 全文检查完成，是否包含外币: ' + hasForeignCurrency);
        
        // 第二步：如果没有外币，则去除"人民币"字样
        if (!hasForeignCurrency) {
          console.log('[DocumentEditor] 未发现外币，开始去除人民币字样');
          var removedCount = 0;
          
          // 遍历所有段落，查找并替换"人民币"
          for (var i = 0; i < elementsCount; i++) {
            var element = oDocument.GetElement(i);
            if (element && element.GetClassType && element.GetClassType() === "paragraph") {
              try {
                var paragraphText = element.GetText ? element.GetText() : '';
                
                // 检查段落是否包含"人民币"
                if (paragraphText.indexOf('人民币') !== -1) {
                  
                  // 获取段落中的所有元素
                  var paraElementsCount = element.GetElementsCount ? element.GetElementsCount() : 0;
                  
                  for (var k = paraElementsCount - 1; k >= 0; k--) {
                    var paraElement = element.GetElement(k);
                    
                    if (paraElement && paraElement.GetClassType && paraElement.GetClassType() === "run") {
                      var runText = paraElement.GetText ? paraElement.GetText() : '';
                      
                      if (runText.indexOf('人民币') !== -1) {
                        
                        // 替换文本中的"人民币"
                        var newText = runText.replace(/人民币/g, '');
                        
                        // 清空run内容并重新添加文本
                        paraElement.ClearContent();
                        paraElement.AddText(newText);
                        
                        removedCount++;
                      }
                    }
                  }
                }
              } catch (error) {
                console.warn('[DocumentEditor] 处理段落失败:', error);
              }
            }
          }
          
          console.log('[DocumentEditor] 人民币字样去除完成，共处理' + removedCount + '处');
          return { status: 'completed', removedCount: removedCount, hasForeignCurrency: false };
        } else {
          console.log('[DocumentEditor] 发现外币，保留人民币字样');
          return { status: 'completed', removedCount: 0, hasForeignCurrency: true };
        }
        
      }, false, true, function(result) {
        console.log('[DocumentEditor] 全文内容优化处理完成:', result);
        
        // 发送处理完成消息给前端
        try {
          window.parent.parent.postMessage({
            type: 'optimizeContentCompleted',
            status: result.status,
            removedCount: result.removedCount,
            hasForeignCurrency: result.hasForeignCurrency
          }, '*');
        } catch (error) {
          console.error('[DocumentEditor] 发送全文内容优化完成消息失败:', error);
        }
      });
      
      return true;
    } catch (error) {
      console.error('[DocumentEditor] 全文内容优化处理失败:', error);
      
      // 发送失败消息
      try {
        window.parent.parent.postMessage({
          type: 'optimizeContentCompleted',
          status: 'failed',
          error: error.message
        }, '*');
      } catch (msgError) {
        console.error('[DocumentEditor] 发送失败消息失败:', msgError);
      }
      
      return false;
    }
  }


})(window, undefined);
