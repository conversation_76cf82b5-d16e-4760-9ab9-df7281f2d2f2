server:
  port: 8080

spring:
  application:
    name: yueju<PERSON>zhushou
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 2GB

# 线程池配置
thread-pool:
  # 核心线程数
  core-pool-size: 10
  # 最大线程数
  max-pool-size: 20
  # 队列容量
  queue-capacity: 200
  # 线程存活时间（秒）
  keep-alive-seconds: 60
  # 线程名前缀
  thread-name-prefix: yjzs-async-

# 缓存配置
cache:
  # 是否启用异步预加载树形结构缓存
  async-preload-enabled: true

# 文件预处理配置
file:
  preprocess:
    ocr:
      # OCR并发线程数，默认为4
      concurrent-threads: 4
      # OCR重试配置
      retry:
        # 最大重试次数，默认为3
        max-attempts: 3
        # 初始延迟时间（毫秒），默认为1000ms
        initial-delay: 1000
        # 延迟倍数，默认为2（每次重试延迟时间翻倍）
        multiplier: 2.0
        # 最大延迟时间（毫秒），默认为10000ms
        max-delay: 10000

minio:
  endpoint: http://**************:19000
  accessKey: root
  secretKey: smxz.minio.smxz
  bucketName: dzjz

wssc:
  stream-delay: 100
  stream-slow-delay: 100
  stream-wrod-count: 8
  generation:
    queue:
      max-concurrent-count: 3 #最多允许同时文书生成数量
      timeout-hours: 1 #任务超时时间
      cleanup-interval-minutes: 5 #定时清理任务执行间隔

smxz:
  p6spy:
    enabled: true                    # 是否启用，默认true
    multiline: true                  # 多行输出，默认false
    logging: FILE                    # 日志方式，默认SLF4J
    log-file: spy.log
    log-format: "%s [%s] [%s] p6spy - Consume Time：%d ms %s Execute SQL：%s"  # 自定义格式
    log-filter:
      pattern: ".*"                  # SQL过滤正则表达式
    exclude-categories: []           # 排除的日志类别
  console:
    enabled: true
    login-page-url: http://192.168.124.106:5173/login
    exclude-urls:
      - "/api/rpc"
      - "/api/analysis-task/task-types"
      - "/api/version/getChromeRecommendation"
      - "/api/onlyoffice/callback"
      - "/agent-config/**"
      - "/api/yaml/**"


sa-token:
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 86400
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true