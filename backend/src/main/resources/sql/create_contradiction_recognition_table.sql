-- 创建矛盾识别表
-- 执行时间: 2025年8月
-- 说明: 用于存储案件矛盾识别分析结果，包含当事人的矛盾点及相关证据定位

CREATE TABLE IF NOT EXISTS `contradiction_recognition` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `case_import_id` BIGINT NOT NULL COMMENT '关联的案件导入ID',
  `type` VARCHAR(20) NOT NULL COMMENT '当事人类型：原告/被告',
  `name` VARCHAR(100) NOT NULL COMMENT '当事人姓名',
  `contradiction_point` VARCHAR(500) NOT NULL COMMENT '矛盾点描述',
  `content` TEXT COMMENT '详细的矛盾内容描述',
  `evidence_location` VARCHAR(200) COMMENT '证据具体位置描述',
  `evidence_file_id` BIGINT COMMENT '证据文件ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_case_import_id` (`case_import_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='矛盾识别表';