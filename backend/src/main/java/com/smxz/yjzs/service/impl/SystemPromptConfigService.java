package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.SystemPromptConfig;
import com.smxz.yjzs.mapper.SystemPromptConfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SystemPromptConfigService extends ServiceImpl<SystemPromptConfigMapper, SystemPromptConfig> {

    private static final Long STAGING_VERSION = -1L;

    /**
     * 获取对应key的最新的Prompt配置
     * @return
     */
    public SystemPromptConfig getByKey(String key) {
        return this.baseMapper.getNewestConfigByKey(key);
    }

    /**
     * 获取对应key的最新的Prompt配置
     * @return
     */
    public String getMessageByKey(String key, String defaultMessage) {
        SystemPromptConfig systemPromptConfig = this.baseMapper.getNewestConfigByKey(key);

        if (systemPromptConfig == null) {
            return defaultMessage;
        }

        return systemPromptConfig.getPromptMessage();
    }

    public List<SystemPromptConfig> listAllVersion(String key) {
        return this.baseMapper.listOrderByVersion(key);
    }

    /**
     * 如果存在对应key的数据则更新一个版本，如果不存在就新增一条数据，版本号为1
     * @param key       唯一key
     * @param message   提示词
     * @return          是否成功
     */
    @Transactional
    public boolean upsert(String key, String message) {
        SystemPromptConfig systemPromptConfig = this.baseMapper.getNewestConfigByKey(key);
        LocalDateTime now = LocalDateTime.now();
        long newVersion = 1;

        // 删掉暂存的版本
        this.lambdaUpdate().eq(SystemPromptConfig::getPromptKey, key)
                .eq(SystemPromptConfig::getPromptVersion, STAGING_VERSION)
                .remove();

        if (systemPromptConfig != null) {
            // 判断一下内容是否有变化
            if (systemPromptConfig.getPromptMessage().equals(message)) {

                return true;
            }
            newVersion = systemPromptConfig.getPromptVersion() + 1;
        }

        SystemPromptConfig newConfig = new SystemPromptConfig();
        newConfig.setPromptKey(key);
        newConfig.setPromptMessage(message);
        newConfig.setPromptVersion(newVersion);
        newConfig.setCreateTime(now);
        newConfig.setUpdateTime(now);

        // 并发时候可能因为唯一性约束保存失败，试试重试或者在前端显示吧
        return this.save(newConfig);
    }

    /**
     * 如果存在对应key的数据则更新一个版本，如果不存在就新增一条数据，版本号为1
     * @param key       唯一key
     * @param message   提示词
     * @return          是否成功
     */
    public boolean staging(String key, String message) {
        SystemPromptConfig systemPromptConfig = this.lambdaQuery()
                .eq(SystemPromptConfig::getPromptKey, key)
                .eq(SystemPromptConfig::getPromptVersion, STAGING_VERSION)
                .one();

        LocalDateTime now = LocalDateTime.now();
        if (systemPromptConfig != null) {
            // 判断一下内容是否有变化
            if (systemPromptConfig.getPromptMessage().equals(message)) {
                return true;
            }
            systemPromptConfig.setPromptMessage(message);
            systemPromptConfig.setUpdateTime(now);
            return this.updateById(systemPromptConfig);
        }

        SystemPromptConfig newConfig = new SystemPromptConfig();
        newConfig.setPromptKey(key);
        newConfig.setPromptMessage(message);
        newConfig.setPromptVersion(STAGING_VERSION);
        newConfig.setCreateTime(now);
        newConfig.setUpdateTime(now);

        // 并发时候可能因为唯一性约束保存失败，试试重试或者在前端显示吧
        return this.save(newConfig);
    }


}
