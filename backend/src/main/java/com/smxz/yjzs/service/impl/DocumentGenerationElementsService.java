package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.DocumentGenerationElements;
import com.smxz.yjzs.mapper.DocumentGenerationElementsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 文书生成要素服务实现类
 * 1对1关系：一个案件对应一个文书生成要素记录
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class DocumentGenerationElementsService extends ServiceImpl<DocumentGenerationElementsMapper, DocumentGenerationElements> {

    /**
     * 根据案件ID查询文书生成要素（1对1关系）
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 文书生成要素信息
     */
    public DocumentGenerationElements getByCaseImportRecordId(Long caseImportRecordId) {
        log.info("查询案件文书生成要素信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.getByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 根据案件ID删除文书生成要素记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportRecordId(Long caseImportRecordId) {
        log.info("删除案件文书生成要素信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 保存或更新文书生成要素信息
     * 如果已存在则更新，否则新增
     *
     * @param documentGenerationElements 文书生成要素信息
     * @return 是否保存成功
     */
    public boolean saveOrUpdate(DocumentGenerationElements documentGenerationElements) {
        log.info("保存或更新文书生成要素信息，caseImportRecordId: {}", 
                documentGenerationElements.getCaseImportRecordId());
        
        // 查询是否已存在
        DocumentGenerationElements existing = getByCaseImportRecordId(
                documentGenerationElements.getCaseImportRecordId());
        
        if (existing != null) {
            // 更新现有记录
            documentGenerationElements.setId(existing.getId());
            return updateById(documentGenerationElements);
        } else {
            // 新增记录
            return save(documentGenerationElements);
        }
    }
}
