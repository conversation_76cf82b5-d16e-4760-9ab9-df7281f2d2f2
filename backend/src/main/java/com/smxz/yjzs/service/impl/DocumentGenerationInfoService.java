package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.agent.client.AgentClient;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.smxzconsole.service.CorpService;
import com.smxz.smxzconsole.service.LoginService;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.*;
import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.constant.AIConstants;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.DsrConstants;
import com.smxz.yjzs.constant.NormalCodeConsts;
import com.smxz.yjzs.dto.LegalFeesWithPartyDTO;
import com.smxz.yjzs.dto.vo.DocumentGenerationPreCheckVO;
import com.smxz.yjzs.entity.*;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.CasePartyMapper;
import com.smxz.yjzs.mapper.DocumentGenerationInfoMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.service.FilePreprocessService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文书生成信息服务实现类
 */
@Slf4j
@Service
public class DocumentGenerationInfoService extends ServiceImpl<DocumentGenerationInfoMapper, DocumentGenerationInfo> {



    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    @Lazy
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private TrialOrganizationMembersService trialOrganizationMembersService;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private DocumentGenerationElementsService documentGenerationElementsService;

    @Autowired
    private OriginalDocumentElementsService originalDocumentElementsService;

    @Autowired
    private JudgmentSituationService judgmentSituationService;

    @Autowired
    private GlajService glajService;

    @Autowired
    private LegalFeesService legalFeesService;

    @Autowired
    private DocumentExtractorService documentExtractorService;

    @Autowired
    private AnalysisTaskRecordService analysisTaskRecordService;

    @Autowired
    private CasePartyMapper casePartyMapper;


    @Autowired
    private AgentTaskUtils agentTaskUtils;

    @Autowired
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    @Autowired
    private DisputeFocuseDetailService disputeFocuseDetailService;

    @Autowired
    private CorpService corpService;

    @Autowired
    private FilePreprocessService filePreprocessService;

    @Value("${wssc.stream-delay}")
    private Integer delay;

    @Value("${wssc.stream-slow-delay}")
    private Integer slowDelay;

    @Value("${wssc.stream-wrod-count}")
    private Integer wordCount;

    /**
     * 根据案件ID查询文书生成信息
     * @param caseImportId 案件导入ID
     * @return 文书生成信息列表
     */
    public List<DocumentGenerationInfo> getByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<DocumentGenerationInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentGenerationInfo::getCaseImportId, caseImportId)
                   .orderByDesc(DocumentGenerationInfo::getCreateTime);
        
        return list(queryWrapper);
    }

    /**
     * 根据案件ID和文书类型查询文书生成信息
     * @param caseImportId 案件导入ID
     * @param documentType 文书类型
     * @return 文书生成信息列表
     */
    public List<DocumentGenerationInfo> getByCaseImportIdAndDocumentType(Long caseImportId, String documentType) {
        LambdaQueryWrapper<DocumentGenerationInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentGenerationInfo::getCaseImportId, caseImportId)
                   .eq(DocumentGenerationInfo::getDocumentType, documentType)
                   .orderByDesc(DocumentGenerationInfo::getCreateTime);
        
        return list(queryWrapper);
    }

    /**
     * 分页查询文书生成信息
     * @param page 分页参数
     * @param caseImportId 案件导入ID（可选）
     * @param documentType 文书类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    public Page<DocumentGenerationInfo> getPage(Page<DocumentGenerationInfo> page, Long caseImportId, String documentType, String startTime, String endTime) {
        LambdaQueryWrapper<DocumentGenerationInfo> queryWrapper = new LambdaQueryWrapper<>();

        if (caseImportId != null) {
            queryWrapper.eq(DocumentGenerationInfo::getCaseImportId, caseImportId);
        }

        if (documentType != null && !documentType.trim().isEmpty()) {
            queryWrapper.eq(DocumentGenerationInfo::getDocumentType, documentType);
        }

        // 添加时间范围筛选
        if (startTime != null && !startTime.trim().isEmpty()) {
            try {
                LocalDateTime startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.ge(DocumentGenerationInfo::getCreateTime, startDateTime);
            } catch (Exception e) {
                log.warn("开始时间格式解析失败: {}", startTime, e);
            }
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            try {
                LocalDateTime endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.le(DocumentGenerationInfo::getCreateTime, endDateTime);
            } catch (Exception e) {
                log.warn("结束时间格式解析失败: {}", endTime, e);
            }
        }

        queryWrapper.orderByDesc(DocumentGenerationInfo::getCreateTime);

        Page<DocumentGenerationInfo> result = page(page, queryWrapper);
        
        // 记录查询结果的时间范围
        if (!result.getRecords().isEmpty()) {
            LocalDateTime minTime = result.getRecords().stream()
                    .map(DocumentGenerationInfo::getCreateTime)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);
            LocalDateTime maxTime = result.getRecords().stream()
                    .map(DocumentGenerationInfo::getCreateTime)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
            log.info("分页查询结果时间范围: {} 到 {}, 总记录数: {}", minTime, maxTime, result.getTotal());
        }
        
        return result;
    }

    /**
     * 创建文书生成信息
     * @param documentGenerationInfo 文书生成信息
     * @return 是否创建成功
     */
    public boolean createDocumentGenerationInfo(DocumentGenerationInfo documentGenerationInfo) {
        LocalDateTime now = LocalDateTime.now();
        documentGenerationInfo.setCreateTime(now);
        documentGenerationInfo.setUpdateTime(now);
        boolean result = save(documentGenerationInfo);
        
        if (result) {
            log.info("创建文书生成信息成功，ID: {}, 案件ID: {}, 文书类型: {}", 
                    documentGenerationInfo.getId(), 
                    documentGenerationInfo.getCaseImportId(), 
                    documentGenerationInfo.getDocumentType());
        }
        return result;
    }

    /**
     * 更新文书生成信息
     * @param documentGenerationInfo 文书生成信息
     * @return 是否更新成功
     */
    public boolean updateDocumentGenerationInfo(DocumentGenerationInfo documentGenerationInfo) {
        documentGenerationInfo.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(documentGenerationInfo);
        if (result) {
            log.info("更新文书生成信息成功，ID: {}", documentGenerationInfo.getId());
        }
        return result;
    }

    /**
     * 根据ID删除文书生成信息
     * @param id 文书ID
     * @return 是否删除成功
     */
    public boolean deleteById(Long id) {
        boolean result = removeById(id);
        if (result) {
            log.info("删除文书生成信息成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 根据案件ID删除所有相关文书生成信息
     * @param caseImportId 案件导入ID
     * @return 删除的记录数
     */
    public long deleteByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<DocumentGenerationInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentGenerationInfo::getCaseImportId, caseImportId);
        
        long count = count(queryWrapper);
        boolean result = remove(queryWrapper);
        
        if (result) {
            log.info("删除案件相关文书生成信息成功，案件ID: {}, 删除记录数: {}", caseImportId, count);
        }
        
        return count;
    }

    /**
     * 批量删除文书生成信息
     * @param documentIds 要删除的文书ID列表
     * @return 删除的记录数
     */
    public long batchDeleteByIds(List<Long> documentIds) {
        if (documentIds == null || documentIds.isEmpty()) {
            log.warn("批量删除文书时，文书ID列表为空");
            return 0;
        }
        
        long count = count(new LambdaQueryWrapper<DocumentGenerationInfo>()
                .in(DocumentGenerationInfo::getId, documentIds));
        
        boolean result = remove(new LambdaQueryWrapper<DocumentGenerationInfo>()
                .in(DocumentGenerationInfo::getId, documentIds));
        
        if (result) {
            log.info("批量删除文书生成信息成功，删除记录数: {}, 文书ID列表: {}", count, documentIds);
        } else {
            log.error("批量删除文书生成信息失败，文书ID列表: {}", documentIds);
        }
        
        return count;
    }

    /**
     * 检查案件是否已有指定类型的文书
     * @param caseImportId 案件导入ID
     * @param documentType 文书类型
     * @return 是否存在
     */
    public boolean existsByCaseImportIdAndDocumentType(Long caseImportId, String documentType) {
        LambdaQueryWrapper<DocumentGenerationInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentGenerationInfo::getCaseImportId, caseImportId)
                   .eq(DocumentGenerationInfo::getDocumentType, documentType);
        
        return count(queryWrapper) > 0;
    }

    /**
     * 生成文书（异步方法，使用@Async）
     *
     * @param documentGenerationInfo 文书生成信息
     * @param caseImportId 案件导入ID
     */
    @Async
    @AnalysisTask(taskType = TaskType.DOCUMENT_GENERATION, description = "文书生成")
    public void generateDocument(DocumentGenerationInfo documentGenerationInfo, Long caseImportId) {
        boolean isSecondInstance = caseImportRecordService.isSecondInstanceCase(caseImportId);
        generateDocumentByAgentAsync(documentGenerationInfo, caseImportId,isSecondInstance);
    }

    /**
     * 异步生成文书
     */
    private void generateDocumentByAgentAsync(DocumentGenerationInfo documentGenerationInfo, Long caseImportId, boolean isSecondInstance) {
        log.info("开始执行文书生成任务，caseImportId: {}, 文书类型: {}", caseImportId, documentGenerationInfo.getDocumentType());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 准备Agent所需的属性数据
            Map<String, String> propertyMap = preparePropertyData(documentGenerationInfo);

            // 2. 使用Agent生成文书
            String agentResult = executeAgentTask(propertyMap,isSecondInstance);

            // 3. 解析JSON结果并更新文书内容
            DocumentGenerationInfo result = parseAgentResult(agentResult);

            // 生成文档key
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String wordName = caseImportId + "_"+ timestamp;

            // 4. 生成Word文件并上传到MinIO
            String wordUrl = generateAndUploadWordFile(result.getDocumentContent(),wordName);

            // 5. 更新文书信息并保存到数据库
            updateAndSaveDocument(documentGenerationInfo, result.getDocumentContent(),wordUrl);

            long duration = System.currentTimeMillis() - startTime;
            log.info("文书生成完成，ID: {}, 耗时: {}ms", documentGenerationInfo.getId(), duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("文书生成任务执行失败，caseImportId: {}, 耗时: {}ms, 错误: {}", 
                    documentGenerationInfo.getCaseImportId(), duration, e.getMessage(), e);
            throw new RuntimeException("文书生成任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行Agent任务
     * @param propertyMap Agent所需的属性数据
     * @return Agent返回的结果
     */
    private String executeAgentTask(Map<String, String> propertyMap,boolean isSecondInstance) {

        String agentName = isSecondInstance ? "星洲-二审文书生成Agent" : "星洲-一审文书生成Agent";
        String bootYmlName = isSecondInstance ? "second_document_generation_task.yml" : "document_generation_task.yml";

        String result = agentTaskUtils.executeTask(agentName, "main", bootYmlName, propertyMap);
        log.info("Agent返回结果: {}", result);
        return result;
    }

    /**
     * 解析Agent返回的结果
     * @param agentResult Agent返回的JSON结果
     * @return 解析后的文书生成信息
     */
    private DocumentGenerationInfo parseAgentResult(String agentResult) {
        DocumentGenerationInfo result = JSON.parseObject(agentResult, DocumentGenerationInfo.class);
        if (result == null) {
            log.warn("Agent返回的文书内容为空");
        }
        return result;
    }

    /**
     * 生成Word文件并上传到MinIO
     */
    private String generateAndUploadWordFile(String documentContent, String docKey) {

        byte[] wordBytes = com.smxz.yjzs.common.utils.WordUtils.jsonToWord(documentContent);

        // 上传Word到MinIO
        String wordMinioPath = "documents/" + docKey + ".docx";
        String wordUrl = uploadWordToMinio(wordBytes, wordMinioPath);
        
        return wordUrl;
    }

    /**
     * 上传Word文件到MinIO
     * @param wordBytes Word文件的字节数组
     * @param wordMinioPath MinIO中的路径
     * @return Word文件的URL
     */
    private String uploadWordToMinio(byte[] wordBytes, String wordMinioPath) {
        String wordUrl = null;
        try (InputStream wordInputStream = new ByteArrayInputStream(wordBytes)) {
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(wordMinioPath)
                    .stream(wordInputStream, wordBytes.length, -1)
                    .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                    .build()
            );
            wordUrl = minioConfig.getEndpoint() + "/" + minioConfig.getBucketName() + "/" + wordMinioPath;
            log.info("Word文件上传到MinIO成功，路径: {}", wordMinioPath);
        } catch (Exception e) {
            log.error("上传Word到MinIO失败", e);
            throw new RuntimeException("上传Word到MinIO失败: " + e.getMessage(), e);
        }
        return wordUrl;
    }

    /**
     * 更新文书信息并保存到数据库
     * @param documentGenerationInfo 文书生成信息
     * @param documentContent 文书内容
     * @param wordUrl Word文件URL
     */
    private void updateAndSaveDocument(DocumentGenerationInfo documentGenerationInfo, String documentContent, String wordUrl) {

        // 更新文书内容和URL
        documentGenerationInfo.setDocumentContent(documentContent);
        documentGenerationInfo.setDocumentUrl(wordUrl);
        documentGenerationInfo.setUpdateTime(LocalDateTime.now());

        // 保存到数据库
        boolean saveResult = saveOrUpdate(documentGenerationInfo);

        if (saveResult) {
            log.info("文书生成完成，ID: {}", documentGenerationInfo.getId());
        } else {
            log.error("文书内容保存失败，ID: {}", documentGenerationInfo.getId());
        }
    }

 
    /**
     * 保存文书内容到数据库
     * @param documentGenerationInfo 文书生成信息
     * @param documentContent 文书内容
     * @return 是否保存成功
     */
    @Transactional(rollbackFor = Exception.class)
    private boolean saveDocumentContent(DocumentGenerationInfo documentGenerationInfo, String documentContent) {
        try {
            log.info("开始保存文书内容到数据库，ID: {}, 内容长度: {}", 
                    documentGenerationInfo.getId(), 
                    documentContent != null ? documentContent.length() : 0);

            // 设置文书内容和更新时间
            documentGenerationInfo.setDocumentContent(documentContent);
            documentGenerationInfo.setUpdateTime(LocalDateTime.now());
            
            //保存文书内容到数据库
            boolean result = save(documentGenerationInfo);
            
            if (result) {
                log.info("文书内容保存成功，ID: {}, 案件ID: {}, 文书类型: {}", 
                        documentGenerationInfo.getId(),
                        documentGenerationInfo.getCaseImportId(),
                        documentGenerationInfo.getDocumentType()
                        );
            } else {
                log.error("文书内容保存失败，ID: {}", documentGenerationInfo.getId());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("保存文书内容到数据库异常，ID: {}, 错误: {}", 
                    documentGenerationInfo.getId(), e.getMessage(), e);
            throw new RuntimeException("保存文书内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 准备Agent所需的属性数据
     */
    private Map<String, String> preparePropertyData(DocumentGenerationInfo documentGenerationInfo) {
        Map<String, String> propertyMap = new HashMap<>();
        Long caseImportId = documentGenerationInfo.getCaseImportId();

        try {
            // 获取起诉状内容
            String qszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.QSZ);
            propertyMap.put("材料:QSZ", qszContent);

            // 获取答辩状内容
            String dbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ);
            propertyMap.put("材料:DBZ", dbzContent);

            // 获取庭审笔录内容
            String tsblContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.TSBL);
            propertyMap.put("材料:TSBL", tsblContent);

            // 获取法官意见
            String judgeOpinion = documentGenerationInfo.getJudgeOpinion();
            propertyMap.put("材料:judgeOpinion", judgeOpinion != null ? judgeOpinion : "");

            log.info("文书生成属性数据准备完成，caseImportId: {}, 属性数量: {}", caseImportId, propertyMap.size());
            log.info("打印入参属性:起诉状内容\n{}\n,答辩状内容\n{}\n,庭审笔录内容\n{}\n,法官意见\n{}\n",qszContent,dbzContent,tsblContent,judgeOpinion);
            return propertyMap;

        } catch (Exception e) {
            log.error("准备文书生成属性数据失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("准备文书生成属性数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于模板生成文书
     *
     * @param caseImportId 案件导入ID
     * @param ajlxdm       案件类型代码
     * @param documentType 文书类型
     * @return 生成的DocumentGenerationInfo记录
     */
    public DocumentGenerationInfo generateDocumentFromTemplate(Long caseImportId, String ajlxdm, String documentType) {
        log.info("开始基于模板生成文书，案件ID: {}, 案件类型: {}, 文书类型: {}", caseImportId, ajlxdm, documentType);

        try {
            // 1. 查询案件基本信息
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            // 2. 执行书签替换
            byte[] documentBytes = WordBookmarkUtils.replaceBookmarks("template.docx", new HashMap<>());

            // 3. 上传到MinIO
            String fileName = generateFileName(caseRecord.getCaseName(), documentType);
            String minioPath = "documents/" + fileName;
            String documentUrl = uploadDocumentToMinio(documentBytes, minioPath);

            // 4. 保存DocumentGenerationInfo记录
            DocumentGenerationInfo documentInfo = DocumentGenerationInfo.builder()
                    .caseImportId(caseImportId)
                    .documentType(documentType)
                    .documentUrl(documentUrl)
                    .documentKey(minioPath)
                    .createTime(LocalDateTime.now())
                    .build();

            // 保存到数据库
            this.save(documentInfo);

            log.info("基于模板生成文书成功，案件ID: {}, 文书URL: {}, 记录ID: {}",
                    caseImportId, documentUrl, documentInfo.getId());
            return documentInfo;

        } catch (Exception e) {
            log.error("基于模板生成文书失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("基于模板生成文书失败: " + e.getMessage(), e);
        }
    }



    /**
     * 通用字符收集处理方法：每10个字符收集后返回，遇到换行、回车则单独发送
     * @param chunk 输入的字符串块
     * @param needIndent 是否需要缩进的原子布尔值
     * @param cleanContent 是否清理内容（去除多余空行）
     * @return 处理后的字符流
     */
    private Flux<String> processCharacterCollection(String chunk, AtomicBoolean needIndent, boolean cleanContent,AtomicBoolean isLeftQuoteNext) {
        // 如果需要清理内容，去除多余空行
        String processedChunk = cleanContent ? chunk.replaceAll("\\n\\s*\\n", "\n") : chunk;
        
        // 将英文引号转换为中文引号（区分左右）
        processedChunk = convertEnglishQuotesToChinese(processedChunk,isLeftQuoteNext);
        
        // 移除所有*字符
        processedChunk = processedChunk.replace("*", "").replace("#", "").replace("-", "");

        String finalProcessedChunk = processedChunk;
        return Flux.create(sink -> {
            StringBuilder buffer = new StringBuilder();
            boolean pendingIndent = needIndent.get();

            for (char character : finalProcessedChunk.toCharArray()) {
                // 遇到换行或回车，立即发送缓冲区内容和换行符
                if (character == '\n' || character == '\r') {
                    // 先发送缓冲区内容（如果有）
                    if (buffer.length() > 0) {
                        if (pendingIndent) {
                            sink.next("<indent>");
                            pendingIndent = false;
                            needIndent.set(false);
                        }
                        sink.next(buffer.toString());
                        buffer.setLength(0);
                    }
                    // 单独发送换行符
                    sink.next(String.valueOf(character));
                    needIndent.set(true);
                    pendingIndent = true;
                } else {
                    // 普通字符加入缓冲区
                    buffer.append(character);

                    // 缓冲区达到wordCount个字符时发送
                    if (buffer.length() >= wordCount) {
                        if (pendingIndent) {
                            sink.next("<indent>");
                            pendingIndent = false;
                            needIndent.set(false);
                        }
                        sink.next(buffer.toString());
                        buffer.setLength(0);
                    }
                }
            }

            // 发送剩余的缓冲区内容
            if (buffer.length() > 0) {
                if (pendingIndent) {
                    sink.next("<indent>");
                    needIndent.set(false);
                }
                sink.next(buffer.toString());
            }

            sink.complete();
        });
    }

    /**
     * 将英文引号转换为中文引号（区分左右）
     * 支持跨文本块的引号状态追踪
     * @param text 输入文本
     * @return 转换后的文本
     */
    private String convertEnglishQuotesToChinese(String text,AtomicBoolean isLeftQuoteNext) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        StringBuilder result = new StringBuilder();
        
        for (char c : text.toCharArray()) {
            if (c == '"') {
                if (isLeftQuoteNext.get()) {
                    result.append('\u201c'); // 中文左引号
                } else {
                    result.append('\u201d'); // 中文右引号
                }
                // 切换引号状态
                isLeftQuoteNext.set(!isLeftQuoteNext.get());
            } else {
                result.append(c);
            }
        }
        
        return result.toString();
    }

    /**
     * 处理流式内容，添加书签标记
     * 在最后一个"综上所述"前添加"裁判依据"书签
     * 在"判决如下"前添加"判决主文"书签
     */
    private Flux<String> processStreamWithBookmarks(Flux<String> originalStream, String documentType) {
        StreamBookmarkFilter filter = new StreamBookmarkFilter(documentType);
        
        return originalStream
                .concatMap(chunk -> {
                    List<String> results = filter.filter(chunk);
                    return Flux.fromIterable(results);
                })
                .concatWith(Flux.defer(() -> {
                    // 流结束时处理剩余内容
                    List<String> remaining = filter.getRemaining();
                    return remaining.isEmpty() ? Flux.empty() : Flux.fromIterable(remaining);
                }));
    }


    /**
     * 处理流式输出，书签标记作为完整字符串输出，其他内容每10个字符收集后返回
     * 遇到换行、回车则单独发送，统一的SSE流处理方法，包含书签处理、缩进标记添加和SSE事件转换
     */
    public Flux<ServerSentEvent<String>> processStreamWithBookmarkAndIndent(Flux<String> streamWithBookmark, Long documentId) {
        AtomicBoolean needIndent = new AtomicBoolean(true);
        AtomicBoolean isLeftQuoteNext = new AtomicBoolean(true);

        return streamWithBookmark
                .concatMap(chunk -> {
                    // 如果是书签标记，直接返回完整字符串并保存到数据库
                    if (chunk.startsWith("<bookmark>") && chunk.endsWith("</bookmark>")) {
                        saveBookmarkAppend(documentId, chunk);
                        return Flux.just(chunk);
                    }

                    // 使用通用字符收集处理方法
                    return processCharacterCollection(chunk, needIndent, false,isLeftQuoteNext);
                })
                .delayElements(java.time.Duration.ofMillis(delay))
                .map(content -> ServerSentEvent.<String>builder()
                        .event("message")
                        .data(content)
                        .build())
                .concatWith(Flux.just(ServerSentEvent.<String>builder()
                        .event("close")
                        .build()));
    }

    /**
     * 追加保存书签信息到DocumentGenerationInfo
     */
    private void saveBookmarkAppend(Long documentId, String bookmarkContent) {
        CompletableFuture.runAsync(() -> {
            try {
                DocumentGenerationInfo documentInfo = this.getById(documentId);
                if (documentInfo != null) {
                    String currentBookmark = documentInfo.getBookmark();
                    String newBookmark;
                    
                    if (StringUtils.isNotBlank(currentBookmark)) {
                        // 追加模式：现有书签 + 分号 + 新书签
                        newBookmark = currentBookmark + "；" + bookmarkContent;
                    } else {
                        // 首次保存
                        newBookmark = bookmarkContent;
                    }
                    
                    documentInfo.setBookmark(newBookmark);
                    this.updateById(documentInfo);
                    
                    log.info("书签信息已追加保存，文书ID: {}, 书签内容: {}", documentId, newBookmark);
                } else {
                    log.warn("未找到文书生成信息，无法保存书签，文书ID: {}", documentId);
                }
            } catch (Exception e) {
                log.error("保存书签信息失败，文书ID: {}, 书签内容: {}", documentId, bookmarkContent, e);
            }
        });
    }

    /**
     * 处理流式输出，仅包含缩进标记添加和SSE事件转换（无书签处理）
     */
    private Flux<ServerSentEvent<String>> processStreamWithIndentOnly(Flux<String> originalStream) {
        AtomicBoolean needIndent = new AtomicBoolean(true);

        AtomicBoolean isLeftQuoteNext = new AtomicBoolean(true);
        
        return originalStream
                .concatMap(chunk -> processCharacterCollection(chunk, needIndent, false,isLeftQuoteNext))
                .delayElements(java.time.Duration.ofMillis(delay))
                .map(character -> ServerSentEvent.<String>builder()
                        .event("message")
                        .data(character)
                        .build())
                .concatWith(Flux.just(ServerSentEvent.<String>builder()
                        .event("close")
                        .build()));
    }

    /**
     * 处理流式输出，包含内容清理、书签处理、缩进标记添加和SSE事件转换
     */
    private Flux<ServerSentEvent<String>> processStreamWithBookmarkAndCleanup(Flux<String> streamWithBookmark, Long documentId) {
        AtomicBoolean needIndent = new AtomicBoolean(true);
        AtomicBoolean isLeftQuoteNext = new AtomicBoolean(true);
        
        return streamWithBookmark
                .concatMap(chunk -> {
                    // 如果是书签标记，直接返回完整字符串并保存到数据库
                    if (chunk.startsWith("<bookmark>") && chunk.endsWith("</bookmark>")) {
                        saveBookmarkAppend(documentId, chunk);
                        // 决策理由：书签输出后等待1秒再输出后续内容，避免书签位置错乱
                        return Flux.just(chunk)
                                .delayElements(java.time.Duration.ofSeconds(1));
                    }

                    // 使用通用字符收集处理方法（启用内容清理）
                    return processCharacterCollection(chunk, needIndent, true,isLeftQuoteNext);
                })
                .delayElements(java.time.Duration.ofMillis(delay))
                .map(content -> ServerSentEvent.<String>builder()
                        .event("message")
                        .data(content)
                        .build())
                .concatWith(Flux.just(ServerSentEvent.<String>builder()
                        .event("close")
                        .build()));
    }

    /**
     * 1. 流式生成当事人基本情况 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_DSRXX, description = "文书生成")
    public Flux<ServerSentEvent<String>> streamPartyBasicInfoSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentGenerationElements(caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetPartyBasicInfo(caseImportId, elements);

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>诉讼参与人基本情况</bookmark>")
                    .concatWith(originalStream);

            // 使用专门的当事人信息流处理方法（包含字符数控制延迟）
            return processPartyInfoStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成当事人基本情况失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 专门用于当事人信息的流处理方法，包含字符数控制的延迟逻辑
     */
    public Flux<ServerSentEvent<String>> processPartyInfoStreamWithBookmarkAndIndent(Flux<String> streamWithBookmark, Long documentId) {
        AtomicBoolean needIndent = new AtomicBoolean(true);
        AtomicBoolean isLeftQuoteNext = new AtomicBoolean(true);
        AtomicInteger characterCount = new AtomicInteger(0);

        return streamWithBookmark
                .concatMap(chunk -> {
                    // 如果是书签标记，直接返回完整字符串并保存到数据库
                    if (chunk.startsWith("<bookmark>") && chunk.endsWith("</bookmark>")) {
                        saveBookmarkAppend(documentId, chunk);
                        return Flux.just(chunk);
                    }

                    // 使用通用字符收集处理方法
                    return processCharacterCollection(chunk, needIndent, false,isLeftQuoteNext);
                })
                .concatMap(content -> {
                    // 统计字符数（不包括书签标记）
                    if (!content.startsWith("<bookmark>")) {
                        characterCount.addAndGet(content.length());
                    }

                    int currentCount = characterCount.get();
                    // 当输出字符数在60-100之间时使用slowDelay
                    if (currentCount >= 55 && currentCount <= 100) {
                        return reactor.core.publisher.Mono.delay(java.time.Duration.ofMillis(slowDelay))
                                .then(reactor.core.publisher.Mono.just(content));
                    } else {
                        return reactor.core.publisher.Mono.delay(java.time.Duration.ofMillis(delay))
                                .then(reactor.core.publisher.Mono.just(content));
                    }
                })
                .map(content -> ServerSentEvent.<String>builder()
                        .event("message")
                        .data(content)
                        .build())
                .concatWith(Flux.just(ServerSentEvent.<String>builder()
                        .event("close")
                        .build()));
    }

    /**
     * 2. 流式生成审理经过 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_SLGC, description = "文书生成-审理经过")
    public Flux<ServerSentEvent<String>> streamTrialProcessSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentGenerationElements(caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetTrialProcess(caseImportId, elements);

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>案件由来和审理经过</bookmark>")
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成审理经过失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 3. 流式生成诉辩信息 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_SBXX, description = "文书生成-诉辩信息")
    public Flux<ServerSentEvent<String>> streamLitigationDefenseInfoSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentGenerationElements(caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetLitigationDefenseInfo(caseImportId, elements);
            String bookmark  = "<bookmark>当事人诉辩意见</bookmark>";
            if(!StringUtils.equals(CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1,documentInfo.getDocumentType())){
                bookmark = "<bookmark>二审当事人诉辩意见</bookmark>";
            }
            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just(bookmark)
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成诉辩信息失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 10. 流式生成总结 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_ZJ, description = "文书生成-总结")
    public Flux<ServerSentEvent<String>> streamSummarySSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            String documentType = documentInfo.getDocumentType();


            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetSummary(caseImportId, documentType);

            // 获取文书结尾信息（诉讼费用和上诉提示）
            Flux<String> wsjwStream = generateWsjw(caseImportId, documentId, documentInfo);

            // 拼接原始流和文书结尾信息
            Flux<String> combinedStream = originalStream.concatWith(wsjwStream);


            // 使用统一的流处理方法（带内容清理版本）
            return processStreamWithBookmarkAndCleanup(combinedStream, documentId);
        } catch (Exception e) {
            log.error("流式生成总结失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 使用规则生成文书结尾
     */
    public Flux<String> generateWsjw(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo){
        try {
            // 修正参数使用：用caseImportId查询CaseImportRecord
            CaseImportRecord caseImportRecord = caseImportRecordService.getById(caseImportId);

            // 获取诉讼费数据
            List<LegalFeesWithPartyDTO> legalFeesList = legalFeesService.listWithPartyInfoByCaseImportId(caseImportId);

            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("\n");
            // 1. 生成诉讼费用文本
            String legalFeesText = generateLegalFeesText(legalFeesList);
            if (StringUtils.isNotBlank(legalFeesText)) {
                contentBuilder.append(legalFeesText).append("\n");
            }

            // 2. 生成上诉提示文本
            String appealText = generateAppealText(caseImportRecord.getCorpName(),caseImportRecord.getAjlxdm());
            contentBuilder.append(appealText);

            log.info("生成文书结尾信息完成，案件ID: {}", caseImportId);
            return createFluxFromExisting(contentBuilder.toString());

        } catch (Exception e) {
            log.error("生成文书结尾信息失败，案件ID: {}", caseImportId, e);
            return createFluxFromExisting("生成文书结尾信息失败：" + e.getMessage());
        }
    }

    /**
     * 4. 流式生成原审诉讼请求 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_YSSQQ, description = "文书生成-原审诉讼请求")
    public Flux<ServerSentEvent<String>> streamOriginalLitigationRequestSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            OriginalDocumentElements originalElements = getOrCreateOriginalDocumentElements(caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetOriginalLitigationRequest(caseImportId, originalElements);

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>一审诉讼请求</bookmark>")
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成原审诉讼请求失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 5. 流式生成原审认定事实 (SSE版本)
     */
    public Flux<ServerSentEvent<String>> streamOriginalRecognizedFactsSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            String documentType = documentInfo.getDocumentType();
            OriginalDocumentElements originalElements = getOrCreateOriginalDocumentElements(caseImportId);

            // 获取原始流式数据，传递documentType参数
            Flux<String> originalStream = generateOrGetOriginalRecognizedFacts(caseImportId, originalElements, documentType);

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>一审法院查明</bookmark>")
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成原审认定事实失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 6. 流式生成原审本院认为+原审裁判结果 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_YSBYCP, description = "文书生成-原审本院认为+原审裁判结果")
    public Flux<ServerSentEvent<String>> streamOriginalCourtOpinionAndJudgmentSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            String documentType = documentInfo.getDocumentType();
            OriginalDocumentElements originalElements = getOrCreateOriginalDocumentElements(caseImportId);

            // 获取原始流式数据，传递documentType参数
            Flux<String> originalStream = generateOrGetOriginalCourtOpinionAndJudgment(caseImportId, originalElements, documentType);

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>一审法院认为与裁判</bookmark>")
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成原审本院认为+原审裁判结果失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }


    /**
     * 7. 流式生成原审裁判结果 (SSE版本) - 独立接口
     */
    @AnalysisTask(taskType = TaskType.WSSC_YSCPJG, description = "文书生成-原审裁判结果")
    public Flux<ServerSentEvent<String>> streamOriginalJudgmentResultSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            OriginalDocumentElements originalElements = getOrCreateOriginalDocumentElements(caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = generateOrGetOriginalJudgmentResult(caseImportId, originalElements);

            // 使用统一的流处理方法（无书签版本）
            return processStreamWithIndentOnly(originalStream);
        } catch (Exception e) {
            log.error("流式生成原审裁判结果失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }

    /**
     * 8. 流式生成认定事实 (SSE版本)
     */
    @AnalysisTask(taskType = TaskType.WSSC_RDSJ, description = "文书生成-认定事实")
    public Flux<ServerSentEvent<String>> streamRecognizedFactsSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        try {
            // 先检查证据摘要是否存在，不存在则先生成
//        if (!hasValue(elements.getEvidenceSummary())) {
//            log.info("证据摘要不存在，先生成证据摘要，案件ID: {}", caseImportId);
//            try {
//
//                // 重新获取elements
//                elements = getOrCreateDocumentGenerationElements(caseImportId);
//            } catch (Exception e) {
//                log.error("生成证据摘要失败，案件ID: {}", caseImportId, e);
//            }
//        }
            generateEvidenceSummary(caseImportId);
            // 如果determineFacts字段没有值，则调用AI生成
            log.info("开始生成认定事实，案件ID: {}", caseImportId);

            // 获取原始流式数据
            Flux<String> originalStream = addSeparatorToFlux(generateRecognizedFacts(caseImportId));

            // 在流开始前添加书签标记
            Flux<String> streamWithBookmark = Flux.just("<bookmark>证据和事实认定</bookmark>")
                    .concatWith(originalStream);

            // 使用统一的流处理方法
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } catch (Exception e) {
            log.error("流式生成认定事实失败，文书ID: {}", documentId, e);
            return Flux.error(e);
        }
    }


    /**
     * 生成文件名
     */
    private String generateFileName(String caseName, String documentType) {
        // 将documentType转换为中文
        String chineseDocumentType = getChineseDocumentType(documentType);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String cleanCaseName = caseName != null ? caseName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_") : "案件";
        return cleanCaseName + "_" + chineseDocumentType + "_" + timestamp + ".docx";
    }

    /**
     * 将英文documentType转换为中文
     */
    private String getChineseDocumentType(String documentType) {
        if (documentType == null) {
            return "判决书";
        }
        switch (documentType) {
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1:
                return "民事判决书（一审普通程序用）";
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT:
                return "民事判决书(驳回上诉，维持原判)";
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE:
                return "民事判决书(二审改判)";
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE:
                return "民事判决书(部分改判)";
            default:
                return documentType; // 如果没有匹配的类型，返回原始值
        }
    }

    /**
     * 上传文档到MinIO
     */
    private String uploadDocumentToMinio(byte[] documentBytes, String minioPath) {
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(minioPath)
                            .stream(new ByteArrayInputStream(documentBytes), documentBytes.length, -1)
                            .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                            .build()
            );

            // 返回MinIO文件访问URL，与uploadWordToMinio保持一致
            String documentUrl = minioConfig.getEndpoint() + "/" + minioConfig.getBucketName() + "/" + minioPath;
            log.info("文档上传到MinIO成功，路径: {}", minioPath);
            return documentUrl;

        } catch (Exception e) {
            log.error("上传文档到MinIO失败，路径: {}", minioPath, e);
            throw new RuntimeException("上传文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成证据摘要
     *
     * @param caseImportId 案件导入ID
     * @return 证据摘要内容
     */
    public String generateEvidenceSummary(Long caseImportId) {
        log.info("开始生成证据摘要，案件ID: {}", caseImportId);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName = isSecondInstance ? "mses-wssc-evidence_summary.yml" : "wssc-evidence_summary.yml";

            // 2. 获取所有证据文件（DocumentType.ZJ）
            List<FileUploadRecord> evidenceFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.ZJ));

            if (evidenceFiles.isEmpty()) {
                log.warn("未找到证据文件，案件ID: {}", caseImportId);
                return "未找到相关证据文件";
            }
            
            List<FileUploadRecord> zjmlFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.ZJML));
            String zjmlText = zjmlFiles.isEmpty() ? "" : zjmlFiles.stream().map(FileUploadRecord::getExtractedText).collect(Collectors.joining("\n\n"));
            
            // 3. 并发处理每个证据文件，生成摘要
            List<CompletableFuture<String>> futures = evidenceFiles.stream()
                    .map(evidenceFile -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return generateSingleEvidenceSummary(evidenceFile, bootYmlName, zjmlText);
                        } catch (Exception e) {
                            log.error("并发处理证据文件失败，文件: {}", evidenceFile.getFileName(), e);
                            return "证据摘要生成失败：" + e.getMessage();
                        }
                    }))
                    .collect(Collectors.toList());

            // 4. 等待所有异步任务完成并收集结果
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 收集所有摘要结果
            List<String> summaries = new ArrayList<>();
            for (CompletableFuture<String> future : futures) {
                try {
                    String summary = future.get();
                    if (StringUtils.isNotBlank(summary)) {
                        summaries.add(summary);
                    }
                } catch (Exception e) {
                    log.error("获取证据摘要结果失败", e);
                }
            }

            // 5. 拼接所有摘要结果
            String finalSummary = String.join("\n\n", summaries);

            // 6. 保存到数据库
            saveEvidenceSummaryToDatabase(caseImportId, finalSummary);

            long duration = System.currentTimeMillis() - startTime;
            log.info("证据摘要生成完成，案件ID: {}, 证据文件数量: {}, 摘要长度: {}, 并发处理耗时: {}ms",
                    caseImportId, evidenceFiles.size(), finalSummary.length(), duration);

            return finalSummary;

        } catch (Exception e) {
            log.error("生成证据摘要失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("生成证据摘要失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为单个证据文件生成摘要
     */
    private String generateSingleEvidenceSummary(FileUploadRecord evidenceFile, String bootYmlName, String zjml) {
        try {
            // 准备AI调用参数
            Map<String, String> propertyMap = new HashMap<>();

            propertyMap.put("证据目录", zjml);

            // 证据名称：获取DocumentType.ZJ的单个文件名称
            propertyMap.put("证据名称", evidenceFile.getFileName());

            // 证据内容：单个文件的内容
            propertyMap.put("证据内容", evidenceFile.getExtractedText() != null ? evidenceFile.getExtractedText() : "");

            // 创建AI任务
            String result = agentTaskUtils.executeTask("星洲-证据摘要Agent", "main", bootYmlName, propertyMap);

            log.info("单个证据摘要生成完成，文件: {}, 摘要长度: {}",
                    evidenceFile.getFileName(), result != null ? result.length() : 0);

            return result != null ? result.trim() : "";

        } catch (Exception e) {
            log.error("生成单个证据摘要失败，文件: {}", evidenceFile.getFileName(), e);
            return "证据摘要生成失败：" + e.getMessage();
        }
    }

    /**
     * 生成当事人基本情况
     *
     * @param caseImportId 案件导入ID
     * @return 当事人基本情况内容流
     */
    public Flux<String> generatePartyBasicInfo(Long caseImportId) {
        log.info("开始生成当事人基本情况，案件ID: {}", caseImportId);

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName = isSecondInstance ? "mses-wssc-party_basic_info.yml" : "wssc-party_basic_info.yml";
            String taskName = "main"; // 默认任务名称

            // 2. 获取相关文件内容
            Map<String, String> propertyMap = new HashMap<>();
            
            // 第一步：先判断本案是否有庭审笔录
            List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.TSBL));

            String tsblContent = extractFileContent(tsblFiles);
            if(!StringUtils.equals(caseRecord.getCorpName(),"广州互联网法院")){
                // 决策理由：如果有庭审笔录且包含"申请回避"，则截取"申请回避"之前的内容作为输入
                if (StringUtils.isNotBlank(tsblContent) && tsblContent.contains("申请回避")) {
                    log.info("发现庭审笔录包含申请回避，案件ID: {}", caseImportId);
                    // 使用正则找到第一个"申请回避"的位置
                    Pattern pattern = Pattern.compile("申请回避");
                    Matcher matcher = pattern.matcher(tsblContent);
                    if (matcher.find()) {
                        // 截取"申请回避"之前的所有内容
                        String contentBeforeAvoidance = tsblContent.substring(0, matcher.start()).trim();
                        // 一审判决书
                        List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                                caseImportId, List.of(DocumentType.YSPJS));
                        String yspjsContent = extractFileContent(yspjsFiles);
                        propertyMap.put("一审判决书", yspjsContent);
                        propertyMap.put("庭审笔录", contentBeforeAvoidance);
                        taskName = AIConstants.TASK_NAME_TSBL;
                    }else {
                        propertyMap.put("庭审笔录", "");
                    }

                }else{
                    propertyMap.put("庭审笔录", "");
                }
            }else{
                propertyMap.put("庭审笔录", tsblContent);
            }

            if(!StringUtils.equals(taskName,AIConstants.TASK_NAME_TSBL)){
                if (isSecondInstance){
                    // 一审判决书
                    List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.YSPJS));
                    String yspjsContent = extractFileContent(yspjsFiles);
                    propertyMap.put("一审判决书", yspjsContent);

                    // 上诉状
                    List<FileUploadRecord> sszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.SSZ));
                    String sszContent = extractFileContent(sszFiles);
                    propertyMap.put("上诉状", sszContent);
                }else{
                    List<FileUploadRecord> qszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.QSZ, DocumentType.BCQSYJ));
                    String qszContent = extractFileContent(qszFiles);
                    propertyMap.put("起诉书", qszContent);

                    List<FileUploadRecord> dbzFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.DBZ, DocumentType.BCDBYJ));
                    String dbzContent = extractFileContent(dbzFiles);
                    propertyMap.put("答辩状", dbzContent);

                }
            }
            // 3. 执行AI任务 - 返回流式输出
            log.info("执行AI任务，taskName: {}, 案件ID: {}", taskName, caseImportId);
            return agentTaskUtils.executeStreamTaskWithResult("星洲-当事人基本情况Agent", taskName, bootYmlName, propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                savePartyBasicInfoToDatabase(caseImportId, finalResult);

                            } catch (Exception e) {
                                log.error("保存当事人基本情况到数据库失败，案件ID: {}", caseImportId, e);
                            }
                        });
                    });

        } catch (Exception e) {
            log.error("生成当事人基本情况失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成当事人基本情况失败: " + e.getMessage(), e));
        }
    }

    /**
     * 提取文件内容的辅助方法
     */
    private String extractFileContent(List<FileUploadRecord> files) {
        if (files == null || files.isEmpty()) {
            return "";
        }

        StringBuilder contentBuilder = new StringBuilder();
        for (FileUploadRecord file : files) {
            if (StringUtils.isNotBlank(file.getExtractedText())) {
                contentBuilder.append(file.getExtractedText()).append("\n\n");
            }
        }

        return contentBuilder.toString().trim();
    }

    /**
     * 生成审理经过
     *
     * @param caseImportId 案件导入ID
     * @return 审理经过内容流
     */
    public Flux<String> generateTrialProcess(Long caseImportId) {
        log.info("开始生成审理经过，案件ID: {}", caseImportId);

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName = isSecondInstance ? "mses-wssc-trial_process.yml" : "wssc-trial_process.yml";

            // 2. 获取相关文件内容
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("立案日期", StringUtils.defaultIfBlank(extractAndGetLarq(caseImportId,caseRecord),StringUtils.EMPTY));
            if(isSecondInstance){
                // 一审判决书
                List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.YSPJS));
                String yspjsContent = extractFileContent(yspjsFiles);
                propertyMap.put("一审判决书", yspjsContent);

                // 上诉状
                List<FileUploadRecord> sszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.SSZ));
                String sszContent = extractFileContent(sszFiles);
                propertyMap.put("上诉状", sszContent);
            }else{
                List<FileUploadRecord> qszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.QSZ, DocumentType.BCQSYJ));
                String qszContent = extractFileContent(qszFiles);
                propertyMap.put("起诉书", qszContent);

                List<FileUploadRecord> dbzFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.DBZ, DocumentType.BCDBYJ));
                String dbzContent = extractFileContent(dbzFiles);
                propertyMap.put("答辩状", dbzContent);
            }

            // 庭审笔录
            List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.TSBL));
            String tsblContent = extractFileContent(tsblFiles);
            propertyMap.put("庭审笔录", tsblContent);

            // 3. 执行AI任务 - 返回流式输出
            return agentTaskUtils.executeStreamTaskWithResult("星洲-审理经过Agent", "main", bootYmlName, propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                saveTrialProcessToDatabase(caseImportId, finalResult);

                            } catch (Exception e) {
                                log.error("保存审理经过到数据库失败，案件ID: {}", caseImportId, e);
                            }
                        });
                    });

        } catch (Exception e) {
            log.error("生成审理经过失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成审理经过失败: " + e.getMessage(), e));
        }
    }

    /**
     * 生成诉辩信息
     *
     * @param caseImportId 案件导入ID
     * @return 诉辩信息内容流
     */
    public Flux<String> generateLitigationDefenseInfo(Long caseImportId) {
        log.info("开始生成诉辩信息，案件ID: {}", caseImportId);

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName = isSecondInstance ? "mses-wssc-litigation_defense_info.yml" : "wssc-litigation_defense_info.yml";

            // 2. 获取相关文件内容
            Map<String, String> propertyMap = new HashMap<>();
            if(isSecondInstance){
                // 上诉状
                List<FileUploadRecord> sszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.SSZ));
                String sszContent = extractFileContent(sszFiles);
                propertyMap.put("上诉状", sszContent);
            }else{
                List<FileUploadRecord> qszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.QSZ, DocumentType.BCQSYJ));
                String qszContent = extractFileContent(qszFiles);
                propertyMap.put("起诉状", qszContent);
            }
            // 答辩状（包含DBZ和BCDBYJ）
            List<FileUploadRecord> dbzFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.DBZ, DocumentType.BCDBYJ));
            String dbzContent = extractFileContent(dbzFiles);
            propertyMap.put("答辩状", dbzContent);
            // 庭审笔录
            List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.TSBL));
            String tsblContent = extractFileContent(tsblFiles);
            propertyMap.put("庭审笔录", tsblContent);

            // 3. 执行AI任务 - 返回流式输出
            return agentTaskUtils.executeStreamTaskWithResult("星洲-诉辩信息Agent", "main", bootYmlName, propertyMap,
                    fullResult -> {
                        try {
                            // 清理AI返回结果（移除markdown代码块标记）
                            String result = AiOutputUtils.removeCodeBlock(fullResult);
                            String finalResult = result != null ? result.trim() : "";

                            // 保存到数据库
                            saveLitigationDefenseInfoToDatabase(caseImportId, finalResult);

                        } catch (Exception e) {
                            log.error("保存诉辩信息到数据库失败，案件ID: {}", caseImportId, e);
                        }
                    });

        } catch (Exception e) {
            log.error("生成诉辩信息失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成诉辩信息失败: " + e.getMessage(), e));
        }
    }

    /**
     * 生成认定事实
     *
     * @param caseImportId 案件导入ID
     * @return 认定事实内容流
     */
    public Flux<String> generateRecognizedFacts(Long caseImportId) {
        log.info("开始生成认定事实，案件ID: {}", caseImportId);

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName = isSecondInstance ? "mses-wssc-recognized_facts.yml" : "wssc-recognized_facts.yml";

            // 2. 获取相关文件内容
            Map<String, String> propertyMap = new HashMap<>();

            if(isSecondInstance){
                // 一审判决书
                List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.YSPJS));
                String yspjsContent = extractFileContent(yspjsFiles);
                propertyMap.put("一审判决书", yspjsContent);

                // 上诉状
                List<FileUploadRecord> sszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.SSZ));
                String sszContent = extractFileContent(sszFiles);
                propertyMap.put("上诉状", sszContent);
            }else{
                List<FileUploadRecord> qszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.QSZ, DocumentType.BCQSYJ));
                String qszContent = extractFileContent(qszFiles);
                propertyMap.put("起诉状", qszContent);
            }
            // 庭审笔录
            List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.TSBL));
            String tsblContent = extractFileContent(tsblFiles);
            propertyMap.put("庭审笔录", tsblContent);

            // 答辩状（包含DBZ和BCDBYJ）
            List<FileUploadRecord> dbzFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.DBZ, DocumentType.BCDBYJ));
            String dbzContent = extractFileContent(dbzFiles);
            propertyMap.put("答辩状", dbzContent);

            // 证据名称和内容：从库里查询证据摘要AI生成的结果
            DocumentGenerationElements elements = documentGenerationElementsService.getByCaseImportRecordId(caseImportId);
            String evidenceSummary = (elements != null && elements.getEvidenceSummary() != null) ?
                    elements.getEvidenceSummary() : "";
            propertyMap.put("证据名称和内容", evidenceSummary);

            // 3. 执行AI任务 - 返回流式输出
            return agentTaskUtils.executeStreamTaskWithResult("星洲-认定事实Agent", "main", bootYmlName, propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                saveRecognizedFactsToDatabase(caseImportId, finalResult);

                            } catch (Exception e) {
                                log.error("保存认定事实到数据库失败，案件ID: {}", caseImportId, e);
                            }
                        });
                    });

        } catch (Exception e) {
            log.error("生成认定事实失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成认定事实失败: " + e.getMessage(), e));
        }
    }

    /**
     * 生成总结
     *
     * @param caseImportId 案件导入ID
     * @param documentType 文书类型
     * @return 总结内容流
     */
    public Flux<String> generateSummary(Long caseImportId, String documentType) {
        log.info("开始生成总结，案件ID: {}, documentType: {}", caseImportId, documentType);

        try {
            // 1. 判断案件类型（一审/二审）
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
            }

            boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
            String bootYmlName;

            // 2. 根据documentType确定taskName
            String taskName;
            if (documentType != null) {
                switch (documentType) {
                    case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1:
                    case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT:
                        taskName = "main";
                        break;
                    case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE:
                        taskName = "second";
                        break;
                    case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE:
                        taskName = "three";
                        break;
                    default:
                        taskName = "main";
                        break;
                }
            } else {
                taskName = "main";
            }
            String task;;
            // 3. 获取相关文件内容
            Map<String, String> propertyMap = new HashMap<>();
            // 诉讼费用：从LegalFees表获取
            String legalFeesContent = generateLegalFeesContent(caseImportId);
            propertyMap.put("诉讼费用", legalFeesContent);

            // 判决情况：根据documentType处理
            String judgmentSituationContent = generateJudgmentSituationContent(caseImportId, documentType, caseRecord);
            propertyMap.put("判决情况", judgmentSituationContent);
            DisputeFocuseDetail disputeDetail = disputeFocuseDetailService.getByCaseImportId(caseImportId);
            boolean bzd = disputeDetail != null && StringUtils.isNotBlank(disputeDetail.getDisputeFocuseReasoning());
            if(isSecondInstance){
                bootYmlName = "mses-wssc-summary.yml";
                // 一审判决书
                List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.YSPJS));
                String yspjsContent = extractFileContent(yspjsFiles);
                propertyMap.put("一审判决书", yspjsContent);

                // 上诉状
                List<FileUploadRecord> sszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.SSZ));
                String sszContent = extractFileContent(sszFiles);
                propertyMap.put("上诉状", sszContent);

                // 庭审笔录
                List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.TSBL));
                String tsblContent = extractFileContent(tsblFiles);
                propertyMap.put("庭审笔录", tsblContent);

                if(bzd){
                    propertyMap.put("裁判理由", disputeDetail.getDisputeFocuseReasoning());
                    bootYmlName = "mses-wssc-bzd_summary.yml";
                    task = "综上所述+裁判结果+诉讼费";
                }else{
                    task = "裁判理由+综上所述+裁判结果+诉讼费";
                    DocumentGenerationElements elements = documentGenerationElementsService.getByCaseImportRecordId(caseImportId);
                    String evidenceSummary = (elements != null && elements.getEvidenceSummary() != null) ?
                            elements.getEvidenceSummary() : "";
                    propertyMap.put("证据名称和内容", evidenceSummary);

                    // 二审认定事实：优先从EvidenceFactsDetails获取determineFacts，参考streamRecognizedFacts逻辑
                    String secondInstanceRecognizedFacts = "";
                    EvidenceFactsDetails evidenceDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
                    if (evidenceDetails != null && StringUtils.isNotBlank(evidenceDetails.getDetermineFacts())) {
                        secondInstanceRecognizedFacts = evidenceDetails.getDetermineFacts();
                    } else {
                        secondInstanceRecognizedFacts = elements.getRecognizedFacts() != null ? elements.getRecognizedFacts() : "";
                    }
                    propertyMap.put("二审认定事实", secondInstanceRecognizedFacts);
                }
            }else{
                bootYmlName = "wssc-summary.yml";
                List<FileUploadRecord> qszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                        caseImportId, List.of(DocumentType.QSZ, DocumentType.BCQSYJ));
                String qszContent = extractFileContent(qszFiles);
                propertyMap.put("起诉状", qszContent);
                if(bzd){
                    propertyMap.put("裁判理由", disputeDetail.getDisputeFocuseReasoning());
                    bootYmlName = "wssc-bzd_summary.yml";
                    task = "综上所述+裁判结果+诉讼费";
                }else{
                    task = "裁判理由+综上所述+裁判结果+诉讼费";
                    DocumentGenerationElements elements = documentGenerationElementsService.getByCaseImportRecordId(caseImportId);
                    String evidenceSummary = (elements != null && elements.getEvidenceSummary() != null) ?
                            elements.getEvidenceSummary() : "";
                    propertyMap.put("证据名称和内容", evidenceSummary);

                    // 认定事实：优先从EvidenceFactsDetails获取determineFacts，参考streamRecognizedFacts逻辑
                    String recognizedFacts = "";
                    EvidenceFactsDetails evidenceDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
                    if (evidenceDetails != null && StringUtils.isNotBlank(evidenceDetails.getDetermineFacts())) {
                        recognizedFacts = evidenceDetails.getDetermineFacts();
                    } else {
                        recognizedFacts = elements.getRecognizedFacts() != null ? elements.getRecognizedFacts() : "";
                    }
                    propertyMap.put("认定事实", recognizedFacts);
                    // 庭审笔录
                    List<FileUploadRecord> tsblFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.TSBL));
                    String tsblContent = extractFileContent(tsblFiles);
                    propertyMap.put("庭审笔录", tsblContent);
                    List<FileUploadRecord> dbzFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                            caseImportId, List.of(DocumentType.DBZ, DocumentType.BCDBYJ));
                    String dbzContent = extractFileContent(dbzFiles);
                    propertyMap.put("答辩状", dbzContent);
                }
            }

            // 4. 执行AI任务 - 返回流式输出
            Flux<String> originalStream = agentTaskUtils.executeStreamTaskWithResult("星洲-总结Agent", taskName, bootYmlName, propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                saveSummaryToDatabase(caseImportId, finalResult, documentType);

                            } catch (Exception e) {
                                log.error("保存总结到数据库失败，案件ID: {}, documentType: {}", caseImportId, documentType, e);
                            }
                        });
                    });
            
            // 5. 处理流式内容，添加书签标记
            return processStreamWithBookmarks(originalStream, documentType);

        } catch (Exception e) {
            log.error("生成总结失败，案件ID: {}, documentType: {}", caseImportId, documentType, e);
            return Flux.error(new RuntimeException("生成总结失败: " + e.getMessage(), e));
        }
    }

    /**
     * 生成原审诉讼请求
     *
     * @param caseImportId 案件导入ID
     * @return 原审诉讼请求内容流
     */
    public Flux<String> generateOriginalLitigationRequest(Long caseImportId) {
        log.info("开始生成原审诉讼请求，案件ID: {}", caseImportId);

        try {
            // 1. 获取一审判决书内容
            List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.YSPJS));

            if (yspjsFiles.isEmpty()) {
                log.warn("未找到一审判决书文件，案件ID: {}", caseImportId);
                return Flux.just("未找到一审判决书文件");
            }

            String yspjsContent = extractFileContent(yspjsFiles);
            if (StringUtils.isBlank(yspjsContent)) {
                log.warn("一审判决书内容为空，案件ID: {}", caseImportId);
                return Flux.just("一审判决书内容为空");
            }

            // 2. 准备AI调用参数
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("一审判决书", yspjsContent);

            // 3. 执行AI任务 - 返回流式输出
            return agentTaskUtils.executeStreamTaskWithResult("星洲-原审诉讼请求Agent", "main", "mses-wssc-ys_litigation_request.yml", propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                saveOriginalLitigationRequestToDatabase(caseImportId, finalResult);

                            } catch (Exception e) {
                                log.error("保存原审诉讼请求到数据库失败，案件ID: {}", caseImportId, e);
                            }
                        });
                    });

        } catch (Exception e) {
            log.error("生成原审诉讼请求失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成原审诉讼请求失败: " + e.getMessage(), e));
        }
    }

    /**
     * 生成原审裁判结果
     *
     * @param caseImportId 案件导入ID
     * @return 原审裁判结果内容
     */


    public Flux<String> generateOriginalJudgmentResult(Long caseImportId) {
        log.info("开始生成原审裁判结果，案件ID: {}", caseImportId);

        try {
            // 1. 获取一审判决书内容
            List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.YSPJS));

            if (yspjsFiles.isEmpty()) {
                log.warn("未找到一审判决书文件，案件ID: {}", caseImportId);
                return Flux.just("未找到一审判决书文件");
            }

            String yspjsContent = extractFileContent(yspjsFiles);
            if (StringUtils.isBlank(yspjsContent)) {
                log.warn("一审判决书内容为空，案件ID: {}", caseImportId);
                return Flux.just("一审判决书内容为空");
            }

            // 2. 准备AI调用参数
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("一审判决书", yspjsContent);

            // 3. 执行AI任务 - 返回流式输出
            Flux<String> originalStream = agentTaskUtils.executeStreamTaskWithResult("星洲-原审裁判结果Agent", "main", "mses-wssc-ys_judgment_result.yml", propertyMap,
                    fullResult -> {
                        // 异步保存到数据库，不阻塞流式输出
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 清理AI返回结果（移除markdown代码块标记）
                                String result = AiOutputUtils.removeCodeBlock(fullResult);
                                String finalResult = result != null ? result.trim() : "";

                                // 保存到数据库
                                saveOriginalJudgmentResultToDatabase(caseImportId, finalResult);

                            } catch (Exception e) {
                                log.error("保存原审裁判结果到数据库失败，案件ID: {}", caseImportId, e);
                            }
                        });
                    });

            // 对流式输出去除开头的回车换行
            return trimStreamStartingNewlines(originalStream);

        } catch (Exception e) {
            log.error("生成原审裁判结果失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("生成原审裁判结果失败: " + e.getMessage(), e));
        }
    }


    public Map<String, Object> saveYsRdssAndByrw(Long caseImportId){
        Map<String, Object> stringObjectMap = new HashMap<>();
        List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                caseImportId, List.of(DocumentType.YSPJS));
        String ysRdss = StringUtils.EMPTY;
        String ysFyrw = StringUtils.EMPTY;
        String ysah = StringUtils.EMPTY;
        String ysFymc = StringUtils.EMPTY;
        for(FileUploadRecord fileUploadRecord : yspjsFiles){
            if(fileUploadRecord.getExtract_element_result() != null && fileUploadRecord.getExtract_element_result().getElements() != null){
                // 处理裁判理由（可能是String或ArrayList）
                ysFyrw  = StringUtils.defaultIfBlank(convertToString(fileUploadRecord.getExtract_element_result().getElements().get("裁判理由")),ysFyrw);
                ysRdss  = StringUtils.defaultIfBlank(convertToString(fileUploadRecord.getExtract_element_result().getElements().get("认定事实")),ysRdss);
                ysah = StringUtils.defaultIfBlank((String)fileUploadRecord.getExtract_element_result().getElements().get("案号"),ysah);
                ysFymc = StringUtils.defaultIfBlank((String)fileUploadRecord.getExtract_element_result().getElements().get("法院名称"),ysFymc);
                saveYsRdssAndByrwToDatabase(caseImportId,ysRdss,ysFyrw);
                saveYsAjxxDatabase(caseImportId,ysah,ysFymc);
                stringObjectMap.put("裁判理由",ysFyrw);
                stringObjectMap.put("认定事实",ysRdss);
                stringObjectMap.put("案号",ysah);
                stringObjectMap.put("法院名称",ysFymc);
            }
        }
        return stringObjectMap;
    }
    @Async
    @AnalysisTask(taskType = TaskType.TQ_YSXX_FROM_YSPJS, description = "提取原审信息")
    public void ysxxExtractAndSave(Long caseImportId){
        Map<String, Object> stringObjectMap = saveYsRdssAndByrw(caseImportId);
        String ysAh = (String)stringObjectMap.get("案号");
        String ysFymc = (String)stringObjectMap.get("法院名称");
        // 创建JudgmentSituation记录
        createJudgmentSituationForOverrule(caseImportId, ysFymc, ysAh);
    }


    /**
     * 保存证据摘要到数据库
     */
    private void saveEvidenceSummaryToDatabase(Long caseImportId, String evidenceSummary) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setEvidenceSummary(evidenceSummary);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("证据摘要已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存证据摘要到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存当事人基本情况到数据库
     */
    private void savePartyBasicInfoToDatabase(Long caseImportId, String partyBasicInfo) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setPartyBasicInfo(partyBasicInfo);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("当事人基本情况已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存当事人基本情况到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存审理经过到数据库
     */
    private void saveTrialProcessToDatabase(Long caseImportId, String trialProcess) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setTrialProcess(trialProcess);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("审理经过已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存审理经过到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存诉辩信息到数据库
     */
    private void saveLitigationDefenseInfoToDatabase(Long caseImportId, String litigationDefenseInfo) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setLitigationDefenseInfo(litigationDefenseInfo);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("诉辩信息已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存诉辩信息到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存认定事实到数据库
     */
    private void saveRecognizedFactsToDatabase(Long caseImportId, String recognizedFacts) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setRecognizedFacts(recognizedFacts);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("认定事实已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存认定事实到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存裁判理由到数据库
     */
    private void saveJudgmentReasonToDatabase(Long caseImportId, String judgmentReason) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);
            elements.setJudgmentReason(judgmentReason);
            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("裁判理由已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存裁判理由到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存总结到数据库
     */
    private void saveSummaryToDatabase(Long caseImportId, String summary, String documentType) {
        try {
            DocumentGenerationElements elements = getOrCreateDocumentElements(caseImportId);

            // 根据documentType保存到不同字段
            switch (documentType) {
                case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT:
                    elements.setSummary(summary);
                    break;
                case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE:
                    elements.setSummaryQg(summary);
                    break;
                case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE:
                    elements.setSummaryBfg(summary);
                    break;
                default:
                    // 默认保存到summary字段（一审等其他情况）
                    elements.setSummary(summary);
                    break;
            }

            documentGenerationElementsService.saveOrUpdate(elements);
            log.info("总结已保存到数据库，案件ID: {}, documentType: {}", caseImportId, documentType);
        } catch (Exception e) {
            log.error("保存总结到数据库失败，案件ID: {}, documentType: {}", caseImportId, documentType, e);
        }
    }

    /**
     * 获取或创建DocumentGenerationElements记录
     */
    private DocumentGenerationElements getOrCreateDocumentElements(Long caseImportId) {
        DocumentGenerationElements elements = documentGenerationElementsService.getByCaseImportRecordId(caseImportId);
        if (elements == null) {
            elements = DocumentGenerationElements.builder()
                    .caseImportRecordId(caseImportId)
                    .build();
        }
        return elements;
    }

    /**
     * 保存原审诉讼请求到数据库
     */
    private void saveOriginalLitigationRequestToDatabase(Long caseImportId, String litigationRequest) {
        try {
            OriginalDocumentElements elements = getOrCreateOriginalDocumentElements(caseImportId);
            elements.setLitigationRequest(litigationRequest);
            originalDocumentElementsService.saveOrUpdate(elements);
            log.info("原审诉讼请求已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存原审诉讼请求到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 保存原审裁判结果到数据库
     */
    private void saveOriginalJudgmentResultToDatabase(Long caseImportId, String judgmentResult) {
        try {
            OriginalDocumentElements elements = getOrCreateOriginalDocumentElements(caseImportId);
            elements.setJudgmentResult(judgmentResult);
            originalDocumentElementsService.saveOrUpdate(elements);
            log.info("原审裁判结果已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存原审裁判结果到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 解析裁判结果并保存到JudgmentSituation表
     */
    private void parseAndSaveJudgmentSituations(Long caseImportId, String judgmentResult) {
        try {
            // 1. 解析裁判结果
            List<String> judgmentItems = parseJudgmentResult(judgmentResult);
            if (judgmentItems.isEmpty()) {
                log.warn("解析裁判结果为空，案件ID: {}", caseImportId);
                return;
            }

            // 2. 先删除已有的相同类型记录
            judgmentSituationService.deleteByCaseImportIdAndDocumentCaseType(caseImportId, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE);

            // 3. 创建JudgmentSituation记录列表
            List<JudgmentSituation> judgmentSituationList = new ArrayList<>();
            int order = 1;
            for (String item : judgmentItems) {
                JudgmentSituation judgmentSituation = JudgmentSituation.builder()
                        .caseImportRecordId(caseImportId)
                        .judgment(item)
                        .documentCaseType(CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE)
                        .xh(order++)
                        .dataType(CaseTypeConstants.DataType.TQ)
                        .build();
                judgmentSituationList.add(judgmentSituation);
            }

            // 4. 批量保存记录
            boolean result = judgmentSituationService.saveBatch(judgmentSituationList);
            if (result) {
                log.info("裁判结果已保存到JudgmentSituation表，案件ID: {}, 记录数: {}",
                        caseImportId, judgmentSituationList.size());
            } else {
                log.error("保存裁判结果到JudgmentSituation表失败，案件ID: {}", caseImportId);
            }
        } catch (Exception e) {
            log.error("解析和保存裁判结果失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 解析裁判结果文本
     * 格式为"一、xxx 二、xxx"
     */
    private List<String> parseJudgmentResult(String judgmentResult) {
        if (StringUtils.isBlank(judgmentResult)) {
            return Collections.emptyList();
        }

        List<String> result = new ArrayList<>();

        // 使用正则表达式匹配中文数字开头的段落
        Pattern pattern = Pattern.compile("[一二三四五六七八九十]、(.*?)(?=\\s*[一二三四五六七八九十]、|$)");
        Matcher matcher = pattern.matcher(judgmentResult);

        while (matcher.find()) {
            String content = matcher.group(1).trim();
            if (StringUtils.isNotBlank(content)) {
                result.add(content);
            }
        }

        // 如果没有匹配到任何内容，将整个文本作为一项
        if (result.isEmpty() && StringUtils.isNotBlank(judgmentResult)) {
            result.add(judgmentResult.trim());
        }

        return result;
    }

    private void saveYsAjxxDatabase(Long caseImportId, String ysah, String ysFymc) {
        try {
            Glaj glaj = getOrCreateGlaj(caseImportId);
            glaj.setAh(ysah);
            glaj.setGlgx(NormalCodeConsts.GLAJ_YS);
            glaj.setCorpName(ysFymc);
            glajService.saveOrUpdate(glaj);
            log.info("原审信息已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存原审信息到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    private Glaj getOrCreateGlaj(Long caseImportId) {
        List<Glaj> existingElements = glajService.getOriginalTrialCases(caseImportId);

        if (!existingElements.isEmpty()) {
            // 如果存在记录，返回第一个
            return existingElements.get(0);
        } else {
            // 如果不存在，创建新记录
            return Glaj.builder()
                    .caseImportRecordId(caseImportId)
                    .build();
        }
    }

    /**
     * 保存原审认定事实和法院认为到数据库
     */
    private void saveYsRdssAndByrwToDatabase(Long caseImportId, String ysRdss, String ysfyrw) {
        try {
            OriginalDocumentElements elements = getOrCreateOriginalDocumentElements(caseImportId);
            elements.setRecognizedFacts("一审法院认定事实：" + ysRdss);
            elements.setCourtOpinion("一审法院认为：" + ysfyrw);
            originalDocumentElementsService.saveOrUpdate(elements);
            log.info("原审 settledSituation已保存到数据库，案件ID: {}", caseImportId);
        } catch (Exception e) {
            log.error("保存原审 settledSituation到数据库失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 获取或创建OriginalDocumentElements记录
     */
    private OriginalDocumentElements getOrCreateOriginalDocumentElements(Long caseImportId) {
        List<OriginalDocumentElements> existingElements = originalDocumentElementsService.listByCaseImportRecordId(caseImportId);

        if (!existingElements.isEmpty()) {
            // 如果存在记录，返回第一个
            return existingElements.get(0);
        } else {
            // 如果不存在，创建新记录
            return OriginalDocumentElements.builder()
                    .caseImportRecordId(caseImportId)
                    .build();
        }
    }

    /**
     * 获取或创建DocumentGenerationElements记录
     */
    public DocumentGenerationElements getOrCreateDocumentGenerationElements(Long caseImportId) {
        DocumentGenerationElements elements = documentGenerationElementsService.getByCaseImportRecordId(caseImportId);
        if (elements == null) {
            elements = DocumentGenerationElements.builder()
                    .caseImportRecordId(caseImportId)
                    .build();
        }
        return elements;
    }

    /**
     * 检查字段是否有值
     */
    private boolean hasValue(String field) {
        return StringUtils.isNotBlank(field);
    }



    /**
     * 创建包含内容的流（用于已有内容）
     */
    public Flux<String> createFluxFromExisting(String content) {
        return Flux.just(content + "\n");  // 只添加一个换行符，减少空行
    }

    /**
     * 创建包含内容的流（用于已有内容，不添加换行符）
     */
    private Flux<String> createFluxFromExistingNoBreak(String content) {
        return Flux.just(content);
    }


    /**
     * 去除流式输出开头的回车换行
     */
    private Flux<String> trimStreamStartingNewlines(Flux<String> sourceStream) {
        return sourceStream
                .scan("", (accumulated, current) -> {
                    // 如果累积内容为空（第一次），去除开头的回车换行
                    if (accumulated.isEmpty()) {
                        return current.replaceAll("^[\\r\\n]+", "");
                    }
                    // 后续内容保持不变
                    return current;
                })
                .skip(1); // 跳过第一个空字符串
    }

    /**
     * 为流式输出添加换行分隔
     */
    public Flux<String> addSeparatorToFlux(Flux<String> contentFlux) {
        return Flux.concat(
                contentFlux,
                Flux.just("\n\n")  // 在不同AI模型之间添加两个换行符进行分隔
        );
    }

    public Flux<String> addSeparatorToFrontFlux(Flux<String> contentFlux) {
        return Flux.concat(
                Flux.just("\n\n"),contentFlux  // 在不同AI模型之间添加两个换行符进行分隔
        );
    }

    /**
     * 将Object转换为String，支持String和ArrayList类型
     */
    private String convertToString(Object obj) {
        if (obj == null) {
            return "";
        }

        if (obj instanceof String) {
            return (String) obj;
        }

        if (obj instanceof ArrayList) {
            @SuppressWarnings("unchecked")
            ArrayList<String> list = (ArrayList<String>) obj;
            return String.join("\n", list);
        }

        // 其他类型直接转换为字符串
        return obj.toString();
    }

    /**
     * 生成或获取当事人基本情况
     */
    @AnalysisTask(taskType = TaskType.WSSC_DSRXX, description = "文书生成")
    public Flux<String> generateOrGetPartyBasicInfo(Long caseImportId, DocumentGenerationElements elements) {
        // 注释掉数据库检查逻辑，始终调用AI生成
        // if (hasValue(elements.getPartyBasicInfo())) {
        //     log.info("当事人基本情况已存在，直接返回，案件ID: {}", caseImportId);
        //     return createFluxFromExisting(elements.getPartyBasicInfo());
        // } else {
            log.info("开始生成当事人基本情况，案件ID: {}", caseImportId);
            return addSeparatorToFlux(generatePartyBasicInfo(caseImportId));
        // }
    }

    /**
     * 生成或获取审理经过
     */
    private Flux<String> generateOrGetTrialProcess(Long caseImportId, DocumentGenerationElements elements) {
        // 注释掉数据库检查逻辑，始终调用AI生成
        // if (hasValue(elements.getTrialProcess())) {
        //     log.info("审理经过已存在，直接返回，案件ID: {}", caseImportId);
        //     return createFluxFromExisting(elements.getTrialProcess());
        // } else {
            log.info("开始生成审理经过，案件ID: {}", caseImportId);
            return addSeparatorToFlux(generateTrialProcess(caseImportId));
        // }
    }

    /**
     * 生成或获取诉辩信息
     */
    private Flux<String> generateOrGetLitigationDefenseInfo(Long caseImportId, DocumentGenerationElements elements) {
        // 注释掉数据库检查逻辑，始终调用AI生成
        // if (hasValue(elements.getLitigationDefenseInfo())) {
        //     log.info("诉辩信息已存在，直接返回，案件ID: {}", caseImportId);
        //     return createFluxFromExisting(elements.getLitigationDefenseInfo());
        // } else {
            log.info("开始生成诉辩信息，案件ID: {}", caseImportId);
            return addSeparatorToFlux(generateLitigationDefenseInfo(caseImportId));
        // }
    }

    /**
     * 生成或获取原审诉讼请求
     */
    private Flux<String> generateOrGetOriginalLitigationRequest(Long caseImportId, OriginalDocumentElements originalElements) {
        // 注释掉数据库检查逻辑，始终调用AI生成
        // if (hasValue(originalElements.getLitigationRequest())) {
        //     log.info("原审诉讼请求已存在，直接返回，案件ID: {}", caseImportId);
        //     return createFluxFromExisting(originalElements.getLitigationRequest());
        // } else {
            log.info("开始生成原审诉讼请求，案件ID: {}", caseImportId);
            return addSeparatorToFlux(generateOriginalLitigationRequest(caseImportId));
        // }
    }

    /**
     * 生成或获取原审认定事实
     */
    private Flux<String> generateOrGetOriginalRecognizedFacts(Long caseImportId, OriginalDocumentElements originalElements, String documentType) {
        // 如果是二审全部改判，直接取数据库的值
        if (CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE.equals(documentType)) {
            if (hasValue(originalElements.getRecognizedFacts())) {
                log.info("二审全部改判，原审认定事实直接取数据库值，案件ID: {}", caseImportId);
                return createFluxFromExisting(originalElements.getRecognizedFacts());
            }
        }

        // 其他情况始终调用AI生成
        log.info("开始调用saveYsRdssAndByrw生成原审认定事实，案件ID: {}, documentType: {}", caseImportId, documentType);
        try {
            // 调用saveYsRdssAndByrw方法获取数据
            saveYsRdssAndByrw(caseImportId);

            // 重新获取数据
            List<OriginalDocumentElements> updatedElements = originalDocumentElementsService.listByCaseImportRecordId(caseImportId);
            if (!updatedElements.isEmpty()) {
                OriginalDocumentElements updated = updatedElements.get(0);
                String content = updated.getRecognizedFacts() != null ? updated.getRecognizedFacts() : "";
                return createFluxFromExisting(content);
            } else {
                return createFluxFromExisting("数据获取失败");
            }
        } catch (Exception e) {
            log.error("调用saveYsRdssAndByrw失败，案件ID: {}", caseImportId, e);
            return createFluxFromExisting("数据获取失败：" + e.getMessage());
        }
    }

    /**
     * 生成或获取原审本院认为+原审裁判结果
     */
    private Flux<String> generateOrGetOriginalCourtOpinionAndJudgment(Long caseImportId, OriginalDocumentElements originalElements, String documentType) {
        log.info("开始生成原审本院认为+原审裁判结果，案件ID: {}, documentType: {}", caseImportId, documentType);

        // 先获取本院认为
        Flux<String> courtOpinionFlux;
        try {
            List<OriginalDocumentElements> updatedElements = originalDocumentElementsService.listByCaseImportRecordId(caseImportId);
            if (!updatedElements.isEmpty()) {
                OriginalDocumentElements updated = updatedElements.get(0);
                String courtOpinion = updated.getCourtOpinion() != null ? updated.getCourtOpinion() : "";
                courtOpinionFlux = Flux.just(courtOpinion);
            } else {
                courtOpinionFlux = Flux.just("本院认为数据获取失败");
            }
        } catch (Exception e) {
            log.error("获取本院认为失败，案件ID: {}", caseImportId, e);
            courtOpinionFlux = Flux.just("本院认为数据获取失败：" + e.getMessage());
        }

        // 然后连接原审裁判结果
        Flux<String> judgmentResultFlux;

        // 如果是二审部分改判，原审裁判结果直接取数据库的值
        if (CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE.equals(documentType)) {
            if (hasValue(originalElements.getJudgmentResult())) {
                log.info("二审部分改判，原审裁判结果直接取数据库值，案件ID: {}", caseImportId);
                judgmentResultFlux = createFluxFromExisting(originalElements.getJudgmentResult());
            } else {
                log.info("二审部分改判但数据库无值，调用AI生成原审裁判结果，案件ID: {}", caseImportId);
                judgmentResultFlux = addSeparatorToFlux(generateOriginalJudgmentResult(caseImportId));
            }
        } else {
            // 其他情况调用AI生成
            log.info("非二审部分改判，调用AI生成原审裁判结果，案件ID: {}", caseImportId);
            judgmentResultFlux = addSeparatorToFlux(generateOriginalJudgmentResult(caseImportId));
        }

        return courtOpinionFlux.concatWith(judgmentResultFlux);
    }

    /**
     * 生成或获取原审裁判结果
     */
    private Flux<String> generateOrGetOriginalJudgmentResult(Long caseImportId, OriginalDocumentElements originalElements) {
        // 注释掉数据库检查逻辑，始终调用AI生成
        // if (hasValue(originalElements.getJudgmentResult())) {
        //     log.info("原审裁判结果已存在，直接返回，案件ID: {}", caseImportId);
        //     return createFluxFromExisting(originalElements.getJudgmentResult());
        // } else {
            log.info("开始生成原审裁判结果，案件ID: {}", caseImportId);
            return addSeparatorToFlux(generateOriginalJudgmentResult(caseImportId));
        // }
    }


    /**
     * 生成或获取总结
     */
    private Flux<String> generateOrGetSummary(Long caseImportId, String documentType) {
        // 检查DisputeFocuseDetail#disputeFocuseReasoning字段是否有值
        DisputeFocuseDetail disputeDetail = disputeFocuseDetailService.getByCaseImportId(caseImportId);
        if (disputeDetail != null && hasValue(disputeDetail.getDisputeFocuseReasoning())) {
            log.info("争议焦点说理已存在，先流式输出该内容再调用AI生成，案件ID: {}", caseImportId);

            // 创建第一个流：输出争议焦点说理内容
            String reasoning = disputeDetail.getDisputeFocuseReasoning();
            // 如果争议焦点说理已经以"本院认为"开头，则不再添加
            String reasoningContent = reasoning.startsWith("本院认为") ? reasoning : "本院认为" + reasoning;
            Flux<String> reasoningFlux = Flux.just("<bookmark>裁判理由</bookmark>").concatWith(createFluxFromExisting(reasoningContent));

            // 创建第二个流：调用AI生成总结
            Flux<String> aiFlux = generateSummary(caseImportId, documentType);

            // 组合两个流：先输出争议焦点说理，再输出AI生成内容
            return reasoningFlux.concatWith(addSeparatorToFlux(aiFlux));
        }

        generateEvidenceSummary(caseImportId);
        // 如果disputeFocuseReasoning字段没有值，直接调用AI生成
        log.info("开始生成总结，案件ID: {}, documentType: {}", caseImportId, documentType);
        // 不使用addSeparatorToFlux，避免多余的空行
        return Flux.just("<bookmark>裁判理由</bookmark>").concatWith(generateSummary(caseImportId, documentType));
    }

    /**
     * 根据documentType获取对应的总结字段内容
     */
    private String getSummaryByDocumentType(DocumentGenerationElements elements, String documentType) {
        switch (documentType) {
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT:
                return elements.getSummary();
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE:
                return elements.getSummaryQg();
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE:
                return elements.getSummaryBfg();
            default:
                // 默认返回summary字段（一审等其他情况）
                return elements.getSummary();
        }
    }


    /**
     * 生成格式化的判决结果（wslx=3时使用）
     */
    private String generateFormattedJudgmentResult(Long caseImportId, CaseImportRecord caseRecord, String documentCaseType) {
        // 根据documentCaseType过滤JudgmentSituation数据
        List<JudgmentSituation> judgmentList = judgmentSituationService.listByCaseImportIdAndDocumentCaseType(
                caseImportId, documentCaseType);
        if (judgmentList.isEmpty()) {
            return "";
        }

        judgmentList.sort(Comparator.comparing(JudgmentSituation::getOpinion, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(JudgmentSituation::getXh, Comparator.nullsLast(Comparator.naturalOrder())));

        StringBuilder result = new StringBuilder();

        // 获取原审法院名称和案号
        Glaj originalCase = getOriginalTrialCase(caseImportId);
        String originalCourtName = originalCase != null && originalCase.getCorpName() != null ?
                originalCase.getCorpName() : "【原审法院名称】";
        String originalCaseNumber = originalCase != null && originalCase.getAh() != null ?
                originalCase.getAh() : "【原审案号】";

        for (JudgmentSituation situation : judgmentList) {
            if (situation.getOpinion() == null || situation.getXh() == null) {
                continue;
            }

            String orderText = convertNumberToChinese(situation.getXh());

            switch (situation.getOpinion()) {
                case 1:
                    // 维持
                    result.append("维持").append(originalCourtName).append(originalCaseNumber)
                          .append("民事判决第").append(orderText).append("项；\n");
                    break;

                case 2:
                    // 撤销
                    result.append("撤销").append(originalCourtName).append(originalCaseNumber)
                          .append("民事判决第").append(orderText).append("项；\n");
                    break;

                case 3:
                    // 变更
                    String newJudgment = situation.getNewJudgment() != null ? situation.getNewJudgment() : "";
                    result.append("变更").append(originalCourtName).append(originalCaseNumber)
                          .append("民事判决第").append(orderText).append("项为").append(newJudgment).append("；\n");
                    break;

                default:
                    break;
            }
        }

        return result.toString().trim();
    }

    /**
     * 获取原审案件信息
     */
    private Glaj getOriginalTrialCase(Long caseImportId) {
        // 从glaj表中获取原审案件信息（glgx=1表示原审）
        List<Glaj> originalCases = glajService.getOriginalTrialCases(caseImportId);
        if (!originalCases.isEmpty()) {
            return originalCases.get(0);
        }
        return null;
    }

    /**
     * 将数字转换为中文数字
     */
    private String convertNumberToChinese(Integer number) {
        if (number == null) {
            return "";
        }

        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

        if (number <= 0 || number > 10) {
            return String.valueOf(number);
        }

        return chineseNumbers[number];
    }

    /**
     * 将日期转换为大写中文格式
     * 例如：2023年5月1日 -> 二〇二三年五月一日
     */
    private String convertDateToChinese(LocalDateTime dateTime) {
        if (dateTime == null) {
            dateTime = LocalDateTime.now();
        }

        int year = dateTime.getYear();
        int month = dateTime.getMonthValue();
        int day = dateTime.getDayOfMonth();

        // 年份转换为大写中文（每个数字单独转换）
        String yearStr = String.valueOf(year);
        StringBuilder chineseYear = new StringBuilder();
        for (char digit : yearStr.toCharArray()) {
            chineseYear.append(convertDigitToChinese(Character.getNumericValue(digit)));
        }

        // 月份和日期转换为大写中文
        String chineseMonth = convertNumberToChineseFull(month);
        String chineseDay = convertNumberToChineseFull(day);

        return chineseYear + "年" + chineseMonth + "月" + chineseDay + "日";
    }

    /**
     * 将单个数字转换为大写中文数字
     */
    private String convertDigitToChinese(int digit) {
        String[] chineseDigits = {"〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        if (digit >= 0 && digit <= 9) {
            return chineseDigits[digit];
        }
        return String.valueOf(digit);
    }

    /**
     * 将数字转换为大写中文数字（完整版，支持更大的数字）
     */
    private String convertNumberToChineseFull(int number) {
        if (number == 0) {
            return "〇";
        }

        String[] chineseDigits = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] chineseUnits = {"", "十", "百", "千", "万", "十", "百", "千", "亿"};

        String numStr = String.valueOf(number);
        StringBuilder result = new StringBuilder();

        // 处理十几的特殊情况
        if (number >= 10 && number < 20) {
            if (number == 10) {
                return "十";
            } else {
                return "十" + chineseDigits[number % 10];
            }
        }

        for (int i = 0; i < numStr.length(); i++) {
            int digit = Character.getNumericValue(numStr.charAt(i));
            int position = numStr.length() - i - 1;

            if (digit != 0) {
                result.append(chineseDigits[digit]).append(chineseUnits[position]);
            } else if (i < numStr.length() - 1 && Character.getNumericValue(numStr.charAt(i + 1)) != 0) {
                // 当前位是0，但下一位不是0，需要加上"零"
                result.append("零");
            }
        }

        return result.toString();
    }

    /**
     * 生成诉讼费用内容
     */
    private String generateLegalFeesContent(Long caseImportId) {
        List<LegalFeesWithPartyDTO> legalFeesList = legalFeesService.listWithPartyInfoByCaseImportId(caseImportId);
        if (legalFeesList.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (LegalFeesWithPartyDTO legalFees : legalFeesList) {
            result.append("姓名：").append(legalFees.getPartyName() != null ? legalFees.getPartyName() : "")
                  .append("，费用类型：").append(legalFees.getFeeType() != null ? legalFees.getFeeType() : "")
                  .append("，已缴金额：").append(legalFees.getAmountPaid() != null ? legalFees.getAmountPaid() : "0")
                  .append("，承担金额：").append(legalFees.getAmountToBear() != null ? legalFees.getAmountToBear() : "0")
                  .append("\n");
        }

        return result.toString().trim();
    }

    /**
     * 生成判决情况内容
     */
    private String generateJudgmentSituationContent(Long caseImportId, String documentType, CaseImportRecord caseRecord) {
        if (documentType == null) {
            return "";
        }

        switch (documentType) {
            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1:
                // 一审案件的特殊处理逻辑
                return generateCivilJudgement1Content(caseImportId);

            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT:
                return "驳回上诉，维持原判。";

            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE:
                // 二审改判时取JudgmentSituation的judgment字段，按"一、xxx 二、xxx"格式输出
                List<JudgmentSituation> judgmentList = judgmentSituationService.listByCaseImportIdAndDocumentCaseType(
                        caseImportId, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE);
                if (!judgmentList.isEmpty()) {
                    StringBuilder result = new StringBuilder();
                    for (int i = 0; i < judgmentList.size(); i++) {
                        JudgmentSituation js = judgmentList.get(i);
                        if (js.getJudgment() != null && !js.getJudgment().trim().isEmpty()) {
                            String orderText = convertNumberToChinese(i + 1);
                            result.append(orderText).append("、").append(js.getJudgment().trim());
                            if (i < judgmentList.size() - 1) {
                                result.append("\n");
                            }
                        }
                    }
                    return result.toString();
                }
                return "";

            case CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE:
                // 二审部分改判时根据JudgmentSituation的opinion字段生成格式化文本
                return generateFormattedJudgmentResult(caseImportId, caseRecord, documentType);

            default:
                return "";
        }
    }

    /**
     * 生成一审案件判决情况内容
     * 根据当事人的opinion值生成不同的输出格式
     */
    private String generateCivilJudgement1Content(Long caseImportId) {
        // 获取documentCaseType=civil_judgement_1的JudgmentSituation数据
        List<JudgmentSituation> judgmentList = judgmentSituationService.listByCaseImportIdAndDocumentCaseType(
                caseImportId, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1);

        if (judgmentList.isEmpty()) {
            return "";
        }

        List<FileUploadRecord> fszFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                caseImportId, List.of(DocumentType.FSZ));

        if(fszFiles.isEmpty()){
            String ygName = casePartyMapper.listByCaseImportId(caseImportId).stream().filter(cp -> StringUtils.equals(cp.getPartyType(),DsrConstants.DSR_SSDW_YG_NAME))
                    .map(CaseParty::getPartyName).collect(Collectors.joining("、"));
            for (JudgmentSituation judgmentSituation : judgmentList) {
                judgmentSituation.setDsrxm(ygName);
                judgmentSituation.setSsdw(DsrConstants.DSR_SSDW_YG_NAME);
            }
        }
        // 按dsrxm(当事人姓名)和ssdw(诉讼地位)分组
        Map<String, List<JudgmentSituation>> groupedByPartyNameAndType = judgmentList.stream()
                .filter(js -> js.getDsrxm() != null && js.getSsdw() != null)
                .collect(Collectors.groupingBy(js -> {
                    String partyName = js.getDsrxm() != null ? js.getDsrxm() : "";
                    String partyType = js.getSsdw() != null ? js.getSsdw() : "";
                    return partyName + "|" + partyType; // 使用"|"作为分隔符组合姓名和诉讼地位
                }));

        StringBuilder result = new StringBuilder();
        List<String> supportedRequests = new ArrayList<>();
        List<String> rejectedRequests = new ArrayList<>();
        boolean hasMixedOpinions = false;

        // 处理每个当事人的判项
        for (Map.Entry<String, List<JudgmentSituation>> entry : groupedByPartyNameAndType.entrySet()) {
            String partyKey = entry.getKey();
            List<JudgmentSituation> partyJudgments = entry.getValue();

            // 解析当事人姓名和诉讼地位
            String[] parts = partyKey.split("\\|");
            if (parts.length != 2) {
                continue;
            }
            String partyName = parts[0];
            String partyType = parts[1];

            // 检查该当事人的所有opinion值
            Set<Integer> opinions = partyJudgments.stream()
                    .map(JudgmentSituation::getOpinion)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (opinions.isEmpty()) {
                continue;
            }

            // 判断opinion的情况
            if (opinions.size() == 1) {
                Integer opinion = opinions.iterator().next();
                if (opinion == 2) {
                    // 全部为2：支持
                    supportedRequests.add("支持" + partyType + partyName + "的全部诉讼请求。");
                } else if (opinion == 1) {
                    // 全部为2：驳回
                    rejectedRequests.add("驳回" + partyType + partyName + "的全部诉讼请求。");
                }
            } else {
                // 既有1又有2：混合情况
                hasMixedOpinions = true;

                // 输出opinion=2的记录
                List<JudgmentSituation> supportedJudgments = partyJudgments.stream()
                        .filter(js -> js.getOpinion() != null && js.getOpinion() == 2)
                        .sorted(Comparator.comparing(JudgmentSituation::getXh, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());

                for (int i = 0; i < supportedJudgments.size(); i++) {
                    JudgmentSituation js = supportedJudgments.get(i);
                    String orderText = convertNumberToChinese(i + 1);
                    result.append(orderText).append("、").append(js.getJudgment()).append("；\n");
                }
            }
        }

        // 根据情况生成最终结果
        if (hasMixedOpinions) {
            // 有混合情况，已经在上面处理了，最后加上"驳回其他诉讼请求"
            result.append("驳回其他诉讼请求。");
        } else {
            // 没有混合情况，输出支持和驳回的汇总
            supportedRequests.forEach(req -> result.append(req).append("\n"));
            rejectedRequests.forEach(req -> result.append(req).append("\n"));
        }

        return result.toString().trim();
    }

    /**
     * 格式化参数Map为日志输出格式
     * @param propertyMap 参数Map
     * @return 格式化后的字符串，单行JSON格式
     */
    private String formatPropertyMapForLog(Map<String, String> propertyMap) {
        if (propertyMap == null || propertyMap.isEmpty()) {
            return "{}";
        }

        try {
            // 使用FastJSON生成紧凑的JSON格式，便于日志复制
            return JSON.toJSONString(propertyMap);
        } catch (Exception e) {
            return propertyMap.toString();
        }
    }

    /**
     * 测试SSE流式输出 - 一个字一个字返回，无限循环直到客户端断开
     * 用于测试OnlyOffice在接收高频率SSE数据时的连接稳定性
     */
    public Flux<ServerSentEvent<String>> testStreamCharByChar() {
        log.info("开始测试SSE流式输出 - 一个字一个字返回（无限循环）");

        // 基础测试文本内容
        String baseText = "这是一个字一个字返回的SSE测试，用于检测OnlyOffice在接收高频率流式数据时的连接稳定性。每个字符都会单独发送一次SSE事件，以模拟最极端的流式输出场景。如果在某个字符处断开连接，我们就能确定问题的根本原因。测试内容包含中文字符、标点符号、数字和英文字母ABC123。这个测试将帮助我们理解OnlyOffice的SSE处理能力和限制。\n\n";

        // 创建无限循环的字符流
        return Flux.generate(() -> 0L, (charCount, sink) -> {
            // 计算当前字符在基础文本中的位置
            int textIndex = (int) (charCount % baseText.length());
            char character = baseText.charAt(textIndex);

            // 发送字符
            sink.next(character);

            return charCount + 1;
        })
        .cast(Character.class)
        .index() // 添加全局索引
        .delayElements(Duration.ofMillis(50)) // 每个字符间隔50ms，模拟真实的AI生成速度
        .map(tuple -> {
            long globalIndex = tuple.getT1();
            char character = tuple.getT2();

            // 每100个字符输出一次进度日志
            if ((globalIndex + 1) % 100 == 0) {
                log.info("SSE测试进度: 已发送{}个字符，当前字符: '{}'", globalIndex + 1, character);
            }

            // 每500个字符输出详细状态
            if ((globalIndex + 1) % 500 == 0) {
                log.info("=== SSE测试状态: 已发送{}个字符，连接正常，继续循环输出 ===", globalIndex + 1);
            }

            // 每1000个字符输出一次循环信息
            if ((globalIndex + 1) % 1000 == 0) {
                long cycleCount = (globalIndex + 1) / baseText.length();
                log.info("*** SSE测试循环信息: 已完成{}轮循环，总计{}个字符 ***", cycleCount, globalIndex + 1);
            }

            return ServerSentEvent.<String>builder()
                    .data(String.valueOf(character))
                    .build();
        })
        .doOnCancel(() -> {
            log.info("SSE测试被客户端取消");
        })
        .doOnError(error -> {
            log.error("SSE测试过程中发生错误", error);
        });
    }

    /**
     * 通过DocumentGenerationInfo ID流式生成文书内容
     * 按顺序生成各个部分的内容，如果数据库中已有内容则直接返回，否则调用AI生成
     *
     * @param documentId 文书生成信息ID
     * @return 流式文书内容
     */
    public Flux<String> generateDocumentStreamContentByDocumentId(Long documentId) {
        log.info("开始流式生成文书内容，文书ID: {}", documentId);

        try {
            // 1. 获取DocumentGenerationInfo记录
            DocumentGenerationInfo documentInfo = this.getById(documentId);
            if (documentInfo == null) {
                return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
            }

            Long caseImportId = documentInfo.getCaseImportId();
            log.info("文书对应的案件ID: {}", caseImportId);

            return generateDocumentStreamContent(caseImportId);

        } catch (Exception e) {
            log.error("流式生成文书内容失败，文书ID: {}", documentId, e);
            return Flux.error(new RuntimeException("流式生成文书内容失败: " + e.getMessage(), e));
        }
    }

    /**
     * 流式生成文书内容
     * 按顺序生成各个部分的内容，如果数据库中已有内容则直接返回，否则调用AI生成
     *
     * @param caseImportId 案件导入ID
     * @return 流式文书内容
     */
    public Flux<String> generateDocumentStreamContent(Long caseImportId) {
        log.info("开始流式生成文书内容，案件ID: {}", caseImportId);

        try {
            // 获取案件信息
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            if (caseRecord == null) {
                return Flux.error(new RuntimeException("案件信息不存在，案件ID: " + caseImportId));
            }

            // 获取或创建DocumentGenerationElements记录
            DocumentGenerationElements elements = getOrCreateDocumentGenerationElements(caseImportId);

            // 获取或创建OriginalDocumentElements记录
            OriginalDocumentElements originalElements = getOrCreateOriginalDocumentElements(caseImportId);

            // 简单返回提示信息，实际生成由单独的流式接口处理
            return Flux.just("请使用单独的流式接口按顺序生成各个部分的内容");

        } catch (Exception e) {
            log.error("流式生成文书内容失败，案件ID: {}", caseImportId, e);
            return Flux.error(new RuntimeException("流式生成文书内容失败: " + e.getMessage(), e));
        }
    }

    /**
     * 文书生成前置检查
     *
     * @param caseImportId 案件导入ID
     * @param documentType 文书类型
     * @return 前置检查结果
     */
    public DocumentGenerationPreCheckVO preCheckDocumentGeneration(Long caseImportId, String documentType) {
        log.info("开始文书生成前置检查，案件ID: {}, 文书类型: {}", caseImportId, documentType);

        // 1. 获取案件信息，确认案件类型
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        if (caseRecord == null) {
            throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
        }

        checkAndUpdateCorpName(caseImportId, caseRecord);

        String ajlxdm = caseRecord.getAjlxdm();

        // 3. 只处理案件类型为0302的情况
        if (!CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)) {
            // 非0302案件类型，返回默认值
            return DocumentGenerationPreCheckVO.builder()
                    .loading(false)
                    .taskType("")
                    .build();
        }

        // 4. 根据文书类型进行不同的检查
        if (CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_REJECT.equals(documentType)) {
            // 一审或二审驳回：直接返回loading:false
            return DocumentGenerationPreCheckVO.builder()
                    .loading(false)
                    .taskType("")
                    .build();
        } else if (CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE.equals(documentType)) {
            // 二审改判的检查逻辑
            // 1. 检查是否有未完成的TQ_YSXX_FROM_YSPJS任务
            boolean hasRunningTask = analysisTaskRecordService.isTaskRunning(caseImportId, TaskType.TQ_YSXX_FROM_YSPJS.getCode());
            if (hasRunningTask) {
                return DocumentGenerationPreCheckVO.builder()
                        .loading(true)
                        .taskType(TaskType.TQ_YSXX_FROM_YSPJS.getCode())
                        .build();
            }

            // 2. 检查JudgmentSituation表中是否有documentCaseType=CIVIL_JUDGEMENT_2_OVERRULE且xh=1的记录
            LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportId)
                    .eq(JudgmentSituation::getDocumentCaseType, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE)
                    .eq(JudgmentSituation::getXh, 1)
                    .orderByAsc(JudgmentSituation::getXh);

            JudgmentSituation existingRecord = judgmentSituationService.getOne(wrapper);
            if (existingRecord != null) {
                return DocumentGenerationPreCheckVO.builder()
                        .loading(false)
                        .taskType("")
                        .build();
            }

            // 3. 如果都没有，需要调用extractYsRdssAndByrw函数
            return DocumentGenerationPreCheckVO.builder()
                    .loading(true)
                    .taskType(TaskType.TQ_YSXX_FROM_YSPJS.getCode())
                    .needsAsyncCall("ysxxExtractAndSave") // 标记需要异步调用
                    .build();
        } else if (CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE.equals(documentType)) {
            // 二审部分改判的检查逻辑
            // 1. 检查是否有未完成的TQ_YSCPJG_FROM_YSPJS任务
            boolean hasRunningTask = analysisTaskRecordService.isTaskRunning(caseImportId, TaskType.TQ_YSCPJG_FROM_YSPJS.getCode());
            if (hasRunningTask) {
                return DocumentGenerationPreCheckVO.builder()
                        .loading(true)
                        .taskType(TaskType.TQ_YSCPJG_FROM_YSPJS.getCode())
                        .build();
            }

            // 2. 检查JudgmentSituation表中是否有documentCaseType=CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE的记录
            LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportId)
                    .eq(JudgmentSituation::getDocumentCaseType, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE)
                    .orderByAsc(JudgmentSituation::getXh);

            long existingRecord = judgmentSituationService.count(wrapper);
            if (existingRecord > 0) {
                return DocumentGenerationPreCheckVO.builder()
                        .loading(false)
                        .taskType("")
                        .build();
            }

            // 3. 如果都没有，需要调用generateOriginalJudgmentResult函数
            return DocumentGenerationPreCheckVO.builder()
                    .loading(true)
                    .taskType(TaskType.TQ_YSCPJG_FROM_YSPJS.getCode())
                    .needsAsyncCall("generateOriginalJudgmentResultForQd") // 标记需要异步调用
                    .build();
        }

        // 默认情况
        return DocumentGenerationPreCheckVO.builder()
                .loading(false)
                .taskType("")
                .build();
    }

    @Async
    @AnalysisTask(taskType = TaskType.TQ_YSCPJG_FROM_YSPJS, description = "提取原审判决结果信息")
    public void generateOriginalJudgmentResultForQd(Long caseImportId){
        log.info("开始生成原审裁判结果，案件ID: {}", caseImportId);

        try {
            // 1. 获取一审判决书内容
            List<FileUploadRecord> yspjsFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                    caseImportId, List.of(DocumentType.YSPJS));

            if (yspjsFiles.isEmpty()) {
                log.warn("未找到一审判决书文件，案件ID: {}", caseImportId);
                return ;
            }

            String yspjsContent = extractFileContent(yspjsFiles);
            if (StringUtils.isBlank(yspjsContent)) {
                log.warn("一审判决书内容为空，案件ID: {}", caseImportId);
                return ;
            }

            // 2. 准备AI调用参数
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("一审判决书", yspjsContent);

            // 3. 执行AI任务 - 等待完成后保存到数据库
            agentTaskUtils.executeStreamTaskWithResult("星洲-原审裁判结果Agent", "main", "mses-wssc-ys_judgment_result.yml", propertyMap,
                    fullResult -> {
                        // 流式处理完成，保存到数据库
                        try {
                            // 清理AI返回结果（移除markdown代码块标记）
                            String result = AiOutputUtils.removeCodeBlock(fullResult);
                            String finalResult = result != null ? result.trim() : "";

                            // 保存到数据库
                            saveOriginalJudgmentResultToDatabase(caseImportId, finalResult);

                            // 解析结果并保存到JudgmentSituation表
                            parseAndSaveJudgmentSituations(caseImportId, finalResult);

                        } catch (Exception e) {
                            log.error("保存原审裁判结果到数据库失败，案件ID: {}", caseImportId, e);
                        }
                    })
                    .blockLast(); // 等待流式处理完全结束后再返回

            log.info("原审裁判结果流式处理已完成，案件ID: {}", caseImportId);

        } catch (Exception e) {
            log.error("生成原审裁判结果失败，案件ID: {}", caseImportId, e);
            return;
        }
    }

    /**
     * 为改判案件创建JudgmentSituation记录
     *
     * @param caseImportId 案件导入ID
     * @param ysFymc 原审法院名称
     * @param ysAh 原审案号
     */
    private void createJudgmentSituationForOverrule(Long caseImportId, String ysFymc, String ysAh) {
        try {
            // 构造判决内容
            String judgment = String.format("撤销%s%s民事判决",
                    ysFymc != null ? ysFymc : "",
                    ysAh != null ? ysAh : "");

            // 创建JudgmentSituation记录
            JudgmentSituation judgmentSituation = JudgmentSituation.builder()
                    .caseImportRecordId(caseImportId)
                    .judgment(judgment)
                    .xh(1)
                    .dataType(CaseTypeConstants.DataType.TQ)
                    .documentCaseType(CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_2_OVERRULE)
                    .build();

            // 保存到数据库
            judgmentSituationService.save(judgmentSituation);

            log.info("已创建改判JudgmentSituation记录，案件ID: {}, 判决内容: {}", caseImportId, judgment);
        } catch (Exception e) {
            log.error("创建改判JudgmentSituation记录失败，案件ID: {}", caseImportId, e);
        }
    }

    /**
     * 生成诉讼费用文本
     * 根据承担人数量选择不同的格式模板
     * 按照案件受理费、保全费、公告费的顺序排序，不同费用在同一行显示
     */
    private String generateLegalFeesText(List<LegalFeesWithPartyDTO> legalFeesList) {
        if (CollectionUtils.isEmpty(legalFeesList)) {
            return "案件受理费xxxx元，由xxxx承担。";
        }

        // 按费用类型分组处理
        Map<String, List<LegalFeesWithPartyDTO>> feesByType = legalFeesList.stream()
                .collect(Collectors.groupingBy(fee ->
                    StringUtils.isNotBlank(fee.getFeeType()) ? fee.getFeeType() : "案件受理费"));

        List<FileUploadRecord> counterClaimFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                legalFeesList.get(0).getCaseImportRecordId(), List.of(DocumentType.FSZ));
        boolean hasFsz = !counterClaimFiles.isEmpty();
        
        // 定义费用类型排序顺序
        List<String> feeTypeOrder = Arrays.asList("案件受理费", "保全费", "公告费");
        
        // 按照指定顺序排序费用类型
        List<String> sortedFeeTypes = feesByType.keySet().stream()
                .sorted((type1, type2) -> {
                    int index1 = feeTypeOrder.indexOf(type1);
                    int index2 = feeTypeOrder.indexOf(type2);
                    // 如果费用类型不在预定义列表中，放到最后
                    if (index1 == -1) index1 = Integer.MAX_VALUE;
                    if (index2 == -1) index2 = Integer.MAX_VALUE;
                    return Integer.compare(index1, index2);
                })
                .collect(Collectors.toList());
        
        List<String> feeTexts = new ArrayList<>();

        for (String feeType : sortedFeeTypes) {
            List<LegalFeesWithPartyDTO> fees = feesByType.get(feeType);

            // 计算总承担金额
            BigDecimal totalAmount = fees.stream()
                    .filter(fee -> fee.getAmountToBear() != null)
                    .map(LegalFeesWithPartyDTO::getAmountToBear)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 格式化金额（去掉小数点后的零）
            String totalAmountStr = formatAmount(totalAmount);

            if (fees.size() == 1) {
                // 情形一：只有一个人承担诉讼费
                LegalFeesWithPartyDTO fee = fees.get(0);
                String partyType = normalizePartyType(fee.getPartyType());
                String partyName = StringUtils.isNotBlank(fee.getPartyName()) ? fee.getPartyName() : "";

                // 处理单人承担费用的情况
                String partyInfo = processMultiPartyInfo(partyType, partyName, hasFsz, false, null);
                feeTexts.add(feeType + totalAmountStr + "元，由" + partyInfo + "承担");
            } else {
                // 情形二：有两个及以上的人承担诉讼费
                StringBuilder feeText = new StringBuilder();
                feeText.append(feeType).append(totalAmountStr).append("元，由");

                for (int i = 0; i < fees.size(); i++) {
                    LegalFeesWithPartyDTO fee = fees.get(i);
                    String partyType = normalizePartyType(fee.getPartyType());
                    String partyName = StringUtils.isNotBlank(fee.getPartyName()) ? fee.getPartyName() : "";
                    String amountStr = formatAmount(fee.getAmountToBear());

                    // 处理多人承担费用的情况
                    String partyInfo = processMultiPartyInfo(partyType, partyName, hasFsz, true, amountStr);
                    feeText.append(partyInfo);

                    if (i < fees.size() - 1) {
                        feeText.append("，");
                    }
                }
                feeTexts.add(feeText.toString());
            }
        }

        // 决策理由：将所有费用文本用分号连接在同一行显示
        return String.join("。", feeTexts) + "。";
    }

    private String getXmAndSsdw(String xm, String ssdw) {
        if(StringUtils.isBlank(xm) && StringUtils.isBlank(ssdw)) {
            return "xxxx";
        }
        // 决策理由：修正顺序为先诉讼地位再姓名
        return ssdw + xm;
    }

    /**
     * 处理多选当事人信息（支持逗号分隔的诉讼地位和姓名）
     * @param partyType 诉讼地位（可能包含逗号分隔的多个值）
     * @param partyName 当事人姓名（可能包含逗号分隔的多个值）
     * @param hasFsz 是否有反诉状
     * @param includeAmount 是否包含金额信息
     * @param amount 金额字符串
     * @return 格式化后的当事人信息字符串
     */
    private String processMultiPartyInfo(String partyType, String partyName, boolean hasFsz, boolean includeAmount, String amount) {
        if (!partyType.contains(",")) {
            String singleResult = formatSinglePartyInfo(partyType, partyName, hasFsz, includeAmount, amount);
            if (includeAmount) {
                return singleResult + "承担" + amount + "元";
            }
            return singleResult;
        }
        
        String[] partyTypes = partyType.split(",");
        String[] partyNames = partyName.split(",");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < partyTypes.length; i++) {
            String currentPartyType = normalizePartyType(partyTypes[i].trim());
            String currentPartyName = i < partyNames.length ? partyNames[i].trim() : "";
            
            // 决策理由：多人时不在单个格式化中添加"承担"，统一在最后添加
            result.append(formatSinglePartyInfo(currentPartyType, currentPartyName, hasFsz, false, amount));
            if (i < partyTypes.length - 1) {
                result.append("、");
            }
        }
        
        // 决策理由：多人承担时统一在最后添加"承担xx元"
        if (includeAmount) {
            result.append("承担").append(amount).append("元");
        }
        
        return result.toString();
    }

    /**
     * 格式化单个当事人信息
     * @param partyType 诉讼地位
     * @param partyName 当事人姓名
     * @param hasFsz 是否有反诉状
     * @param includeAmount 是否包含金额信息
     * @param amount 金额字符串
     * @return 格式化后的当事人信息
     */
    private String formatSinglePartyInfo(String partyType, String partyName, boolean hasFsz, boolean includeAmount, String amount) {
        String normalizedType = normalizePartyType(partyType);
        String ssdw = getSsdw(hasFsz, normalizedType);
        
        if (includeAmount) {
            // 决策理由：多人承担时不显示"承担"，由调用方统一处理
            return getXmAndSsdw(partyName, ssdw);
        } else {
            return ssdw + partyName;
        }
    }

    /**
     * 标准化诉讼地位，去除序号和后缀
     * 例如："原告(张三)" -> "原告"，"原告-共同" -> "原告"
     */
    private String normalizePartyType(String partyType) {
        if (StringUtils.isBlank(partyType)) {
            return "";
        }
        
        // 处理"原告(张三)"、"原告(1)"等格式，提取基础类型"原告"
        String normalized = partyType.replaceAll("\\([^)]*\\)", "").trim();
        
        // 处理"原告-共同"等格式，统一为"原告"
        if (normalized.contains("-")) {
            normalized = normalized.split("-")[0].trim();
        }
        
        return normalized;
    }

    private String getSsdw(boolean hasFsz,String ySsdw){
        if(!hasFsz){
            return ySsdw;
        }

        if(DsrConstants.DSR_SSDW_YG_NAME.equals(ySsdw)){
            return ySsdw + "（"  + DsrConstants.DSR_SSDW_FSBG_NAME + "）";
        }

        if(DsrConstants.DSR_SSDW_BG_NAME.equals(ySsdw)){
            return ySsdw + "（"  + DsrConstants.DSR_SSDW_FSYG_NAME + "）";
        }

        return ySsdw;
    }


    /**
     * 生成上诉提示文本
     * 通过corpName查询corp表的parentId，查出上级法院名称赋值给courtName
     */
    private String generateAppealText(String corpName,String ajlxdm) {
        if(CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)){
            return "本判决为终审判决。";
        }

        String courtName = "xxxx人民法院";

        if (StringUtils.isNotBlank(corpName)) {
            // 通过当前法院名称查询上级法院名称
            String parentCourtName = corpService.getParentCourtName(corpName);
            courtName = StringUtils.isNotBlank(parentCourtName) ? parentCourtName : courtName;
        }

        return "如不服本判决，可以在判决书送达之日起十五日内，向本院递交上诉状，并按照对方当事人或者代表人的人数提出副本，上诉于" + courtName + "。";
    }

    /**
     * 格式化金额，去掉小数点后的零
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return "xxxx";
        }
        // 去掉小数点后的零，保持整数格式显示
        return amount.stripTrailingZeros().toPlainString();
    }

    /**
     * 统一流式生成认定事实处理 - 包含controller中的逻辑判断
     */
    public Flux<ServerSentEvent<String>> streamUnifiedRecognizedFactsSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        log.info("开始统一流式生成认定事实，案件ID: {}, 文书ID: {}", caseImportId, documentId);
        
        // 检查认定事实是否已存在（复制controller中的逻辑）
        EvidenceFactsDetails evidenceDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
        
        if (evidenceDetails != null && StringUtils.isNotBlank(evidenceDetails.getDetermineFacts())) {
            // 如果有现有数据，直接返回现有数据
            log.info("认定事实已存在，直接返回现有数据，案件ID: {}", caseImportId);
            
            Flux<String> existingData = addSeparatorToFrontFlux(addSeparatorToFlux(createFluxFromExisting(evidenceDetails.getDetermineFacts())));
            Flux<String> streamWithBookmark = Flux.just("<bookmark>证据和事实认定</bookmark>")
                    .concatWith(existingData);
            return processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } else {
            // 如果没有现有数据，调用AI生成
            log.info("认定事实不存在，调用AI生成，案件ID: {}", caseImportId);
            return streamRecognizedFactsSSE(caseImportId, documentId, documentInfo);
        }
    }

    /**
     * 统一流式生成接口 - 并发执行多个生成任务，按顺序返回结果
     * 除了文书头和文书尾，其他接口统一在后端处理
     * 修改：只有stepNameMap中有的才并发执行，没有的就不执行
     */
    public Flux<ServerSentEvent<String>> streamUnifiedGenerationSSE(Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        log.info("开始统一流式生成，案件ID: {}, 文书ID: {}", caseImportId, documentId);

        return Flux.create(sink -> {
            try {
                // 定义步骤名称映射 - 只有这里定义的步骤才会执行
                Map<String, String> stepNameMap = new HashMap<>();
                stepNameMap.put("partyBasicInfo", "当事人基本情况");
//                stepNameMap.put("trialProcess", "审理经过");
//                stepNameMap.put("litigationDefenseInfo", "诉辩信息");
//                stepNameMap.put("originalLitigationRequest", "原审诉讼请求");
//                stepNameMap.put("originalRecognizedFacts", "原审认定事实");
//                stepNameMap.put("originalCourtOpinionAndJudgment", "原审本院认为+原审裁判结果");
                stepNameMap.put("recognizedFacts", "认定事实");
//                stepNameMap.put("summary", "总结");

                // 定义严格的输出顺序
                String[] outputOrder = {"partyBasicInfo", "trialProcess", "litigationDefenseInfo", 
                                       "originalLitigationRequest", "originalRecognizedFacts", 
                                       "originalCourtOpinionAndJudgment", "recognizedFacts", "summary"};

                // 存储各步骤的结果
                Map<String, List<String>> stepResults = new ConcurrentHashMap<>();
                Map<String, Boolean> stepCompleted = new ConcurrentHashMap<>();
                AtomicInteger outputIndex = new AtomicInteger(0);
                
                // 初始化步骤状态
                for (String step : stepNameMap.keySet()) {
                    stepResults.put(step, new ArrayList<>());
                    stepCompleted.put(step, false);
                }

                // 创建并发任务列表
                List<CompletableFuture<Void>> concurrentTasks = new ArrayList<>();
                
                // 并发执行所有步骤
                for (String stepName : stepNameMap.keySet()) {
                    concurrentTasks.add(executeStepConcurrentlyWithOrderedOutput(stepName, caseImportId, documentId, 
                        documentInfo, stepResults, stepCompleted, sink, outputOrder, outputIndex));
                }
                
                // 等待所有并发任务完成
                CompletableFuture.allOf(concurrentTasks.toArray(new CompletableFuture[0]))
                    .thenRun(() -> {
                        // 发送完成事件
                        sink.next(ServerSentEvent.<String>builder()
                            .event("complete")
                            .data("")
                            .build());
                        sink.complete();
                        log.info("统一流式生成完成，案件ID: {}, 文书ID: {}", caseImportId, documentId);
                    }).exceptionally(throwable -> {
                        log.error("统一流式生成失败，案件ID: {}, 文书ID: {}", caseImportId, documentId, throwable);
                        sink.error(throwable);
                        return null;
                    });

            } catch (Exception e) {
                log.error("统一流式生成初始化失败，案件ID: {}, 文书ID: {}", caseImportId, documentId, e);
                sink.error(e);
            }
        });
    }

    /**
     * 并发执行单个步骤，收集结果后按顺序输出
     */
    private CompletableFuture<Void> executeStepConcurrentlyWithOrderedOutput(String stepName, Long caseImportId, Long documentId, 
                                                                            DocumentGenerationInfo documentInfo,
                                                                            Map<String, List<String>> stepResults,
                                                                            Map<String, Boolean> stepCompleted,
                                                                            FluxSink<ServerSentEvent<String>> sink,
                                                                            String[] outputOrder,
                                                                            AtomicInteger outputIndex) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始并发执行步骤: {}", stepName);
                
                Flux<ServerSentEvent<String>> stepFlux = getStepFlux(stepName, caseImportId, documentId, documentInfo);
                
                if (stepFlux != null) {
                    List<String> results = new ArrayList<>();
                    
                    // 收集步骤结果
                    stepFlux
                        .doOnNext(event -> {
                            if ("message".equals(event.event())) {
                                results.add(event.data());
                            }
                        })
                        .doOnComplete(() -> {
                            // 步骤完成后，存储结果并尝试按顺序输出
                            stepResults.put(stepName, results);
                            stepCompleted.put(stepName, true);
                            log.info("步骤执行完成: {}, 结果数量: {}", stepName, results.size());
                            
                            // 尝试按顺序输出已完成的步骤
                            outputCompletedStepsInOrder(stepResults, stepCompleted, sink, outputOrder, outputIndex);
                        })
                        .doOnError(error -> {
                            log.error("步骤执行失败: {}", stepName, error);
                        })
                        .blockLast(); // 等待Flux完成
                }
                    
                return null;
            } catch (Exception e) {
                log.error("执行步骤异常: {}", stepName, e);
                throw new RuntimeException("步骤执行失败: " + stepName, e);
            }
        }).thenApply(v -> null);
    }
    
    /**
     * 按顺序输出已完成的步骤结果
     */
    private synchronized void outputCompletedStepsInOrder(Map<String, List<String>> stepResults,
                                                         Map<String, Boolean> stepCompleted,
                                                         FluxSink<ServerSentEvent<String>> sink,
                                                         String[] outputOrder,
                                                         AtomicInteger outputIndex) {
        // 从当前输出索引开始，检查是否有连续完成的步骤可以输出
        while (outputIndex.get() < outputOrder.length) {
            String currentStep = outputOrder[outputIndex.get()];
            
            if (stepCompleted.getOrDefault(currentStep, false)) {
                // 该步骤已完成，流式输出其结果
                List<String> results = stepResults.get(currentStep);
                if (results != null) {
                    for (String result : results) {
                        sink.next(ServerSentEvent.<String>builder()
                            .event("message")
                            .data(result)
                            .build());
                    }
                    log.info("已按顺序输出步骤: {}, 内容长度: {}", currentStep, results.size());
                }
                outputIndex.incrementAndGet();
            } else {
                // 该步骤还未完成，停止输出
                break;
            }
        }
    }
    
    /**
     * 根据步骤名称获取对应的Flux
     */
    private Flux<ServerSentEvent<String>> getStepFlux(String stepName, Long caseImportId, Long documentId, DocumentGenerationInfo documentInfo) {
        switch (stepName) {
            case "partyBasicInfo":
                return streamPartyBasicInfoSSE(caseImportId, documentId, documentInfo);
            case "trialProcess":
                return streamTrialProcessSSE(caseImportId, documentId, documentInfo);
            case "litigationDefenseInfo":
                return streamLitigationDefenseInfoSSE(caseImportId, documentId, documentInfo);
            case "originalLitigationRequest":
                return streamOriginalLitigationRequestSSE(caseImportId, documentId, documentInfo);
            case "originalRecognizedFacts":
                return streamOriginalRecognizedFactsSSE(caseImportId, documentId, documentInfo);
            case "originalCourtOpinionAndJudgment":
                return streamOriginalCourtOpinionAndJudgmentSSE(caseImportId, documentId, documentInfo);
            case "recognizedFacts":
                return streamUnifiedRecognizedFactsSSE(caseImportId, documentId, documentInfo);
            case "summary":
                return streamSummarySSE(caseImportId, documentId, documentInfo);
            default:
                log.warn("未知的步骤名称: {}", stepName);
                return null;
        }
    }


    /**
     *
     * @param caseImportId 案件导入ID
     * @param caseRecord 案件记录
     */
    private void checkAndUpdateCorpName(Long caseImportId, CaseImportRecord caseRecord) {
        try {
            // 收集结果并批量更新
            String newCorpName = extractAndGetCorpName(caseRecord);
            // 批量更新数据库（避免并发更新冲突）
            boolean needUpdate = false;
            CaseImportRecord updateRecord = new CaseImportRecord();
            updateRecord.setId(caseImportId);

            if (StringUtils.isNotBlank(newCorpName) && StringUtils.isBlank(caseRecord.getCorpName())) {
                updateRecord.setCorpName(newCorpName);
                needUpdate = true;
                log.info("准备更新法院名称，案件ID: {}, 法院名称: {}", caseImportId, newCorpName);
            }
            if (needUpdate) {
                boolean success = caseImportRecordService.updateById(updateRecord);
                if (success) {
                    if (StringUtils.isNotBlank(newCorpName)) {
                        caseRecord.setCorpName(newCorpName);
                    }
                } else {
                    log.warn("并发更新案件信息失败，案件ID: {}", caseImportId);
                }
            }

        } catch (Exception e) {
            log.error("并发执行法院名称检查和立案日期提取失败，案件ID: {}, 回退到顺序执行", caseImportId, e);
        }
    }

    /**
     * 提取并获取法院名称（不直接更新数据库）
     *
     * @param caseRecord 案件记录
     * @return 提取到的法院名称，如果提取失败或已存在则返回null
     */
    private String extractAndGetCorpName(CaseImportRecord caseRecord) {
        // 检查corpName是否为空
        if (StringUtils.isNotBlank(caseRecord.getCorpName())) {
            log.debug("案件已有法院名称: {}, 跳过提取", caseRecord.getCorpName());
            return null;
        }
        Object fy = null;

        boolean isSecondInstance = CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
        if (isSecondInstance) {
            // 获取上诉状文件
            fy = fileUploadRecordMapper.getFileRecordElementByDocumentTypes(
                    caseRecord.getId(), List.of(DocumentType.SSZ),"上诉法院");
        }else{
            //获取庭审笔录
            fy = fileUploadRecordMapper.getFileRecordElementByDocumentTypes(
                    caseRecord.getId(), List.of(DocumentType.TSBL),"开庭法院");
        }
        if (fy != null) {
            return fy.toString();
        }

        // 从caseCode中提取法院代码
        String courtCode = CaseNumberExtractUtil.extractCourtCodeFromCaseCode(caseRecord.getCaseName());

        if (StringUtils.isNotBlank(courtCode)) {
            log.debug("从caseCode: {} 中提取到法院代码: {}", caseRecord.getCaseName(), courtCode);

            // 根据法院代码查询法院名称
            String corpName = corpService.getCorpNameByCode(courtCode);

            if (StringUtils.isNotBlank(corpName)) {
                log.debug("根据法院代码: {} 查询到法院名称: {}", courtCode, corpName);
                return corpName;
            } else {
                log.warn("根据法院代码: {} 未查询到对应的法院名称", courtCode);
            }
        } else {
            log.warn("无法从caseCode: {} 中提取法院代码", caseRecord.getCaseCode());
        }

        return null;
    }

    /**
     * 提取并获取立案日期（不直接更新数据库）
     *
     * @param caseImportId 案件导入ID
     * @param caseRecord 案件记录
     * @return 提取到的立案日期，如果提取失败或已存在则返回null
     */
    private String extractAndGetLarq(Long caseImportId, CaseImportRecord caseRecord) {
        // 检查是否已有立案日期
        if (StringUtils.isNotBlank(caseRecord.getLarq())) {
            log.debug("案件已有立案日期: {}, 跳过提取", caseRecord.getLarq());
            return caseRecord.getLarq();
        }

        log.info("案件缺少立案日期，开始从文档中提取，案件ID: {}", caseImportId);

        // 查询案件的相关文档（立案信息表、审批流程管理信息表等）
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                .in(FileUploadRecord::getDocumentType,
                        DocumentType.LADJB, DocumentType.JAXXB, DocumentType.SPLCGLXXB)
                .isNotNull(FileUploadRecord::getExtractedText)
                .ne(FileUploadRecord::getExtractedText, "");

        List<FileUploadRecord> relevantFiles = fileUploadRecordService.list(queryWrapper);

        if (CollectionUtils.isEmpty(relevantFiles)) {
            log.info("未找到包含立案信息的相关文档，案件ID: {}", caseImportId);
            return null;
        }
        // 从案件名称中提取案号
        String caseNameAh = CaseNumberExtractUtil.extractCaseNumberFromCaseName(caseRecord.getCaseName());
        // 尝试从每个相关文档中提取立案日期
        for (FileUploadRecord fileRecord : relevantFiles) {
            try {
                log.info("尝试从文档中提取立案日期，文件: {}", fileRecord.getFileName());
                String larq = extractLarqFromFile(fileRecord,caseNameAh);
                if (StringUtils.isNotBlank(larq)) {
                    log.info("成功从文档中提取立案日期: {}, 案件ID: {}, 文件: {}",
                            larq, caseImportId, fileRecord.getFileName());
                    return larq;
                }
            } catch (Exception e) {
                log.warn("从文档中提取立案日期失败，文件: {}, 错误: {}",
                        fileRecord.getFileName(), e.getMessage());
                // 继续尝试下一个文档
            }
        }

        log.info("所有相关文档都未能成功提取立案日期，案件ID: {}", caseImportId);
        return null;
    }

    /**
     * 从单个文件中提取立案日期
     *
     * @param fileRecord 文件记录
     * @return 提取到的立案日期
     */
    private String extractLarqFromFile(FileUploadRecord fileRecord, String caseNameAh) {
        try {
            // 直接调用FilePreprocessService的extractLaxxFromDocument方法
            // 该方法会提取立案日期、更新数据库并返回结果，避免重复查询
            return filePreprocessService.extractLaxxFromDocument(fileRecord, caseNameAh);
        } catch (Exception e) {
            log.error("从文档中提取立案信息失败，文件ID: {}, 错误: {}", fileRecord.getId(), e.getMessage(), e);
            return null;
        }
    }



}