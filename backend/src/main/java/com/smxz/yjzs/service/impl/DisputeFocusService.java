package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.agent.client.AgentClient;
import com.smxz.agent.dto.AgentSubTaskResult;
import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.agent.dto.AgentTaskTokenInfo;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.ragflow.model.assistant.ChatAssistantCreateRequest;
import com.smxz.ragflow.service.RagflowFacadeService;
import com.smxz.ragflow.service.SessionService;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskContext;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.common.utils.ThreadPoolUtils;
import com.smxz.yjzs.config.SessionConfig;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.DisputeConstants;
import com.smxz.yjzs.dto.response.LawInfoResponse;
import com.smxz.yjzs.entity.*;
import com.smxz.yjzs.enums.TaskCompletionStrategy;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.CasePartyMapper;
import com.smxz.yjzs.mapper.DisputeFocusMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.service.TaskStatusManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 争议焦点服务实现类
 */
@Slf4j
@Service
public class DisputeFocusService extends ServiceImpl<DisputeFocusMapper, DisputeFocus> {

    @Autowired
    @Lazy
    private LitigationPointService litigationPointService;

    @Autowired
    private LitigationRelationService litigationRelationService;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Autowired
    @Lazy
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private SessionConfig sessionConfig;

    @Autowired
    private SystemPromptConfigService systemPromptConfigService;

    @Autowired
    private AnalysisTaskRecordService analysisTaskRecordService;

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    private CasePartyMapper casePartyMapper;

    @Autowired
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    @Autowired
    private LegalProvisionService legalProvisionService;

    @Autowired
    private TaskStatusManager taskStatusManager;

    @Autowired
    private RagflowFacadeService ragflowFacadeService;

    @Autowired
    private KnowledgeBaseConfigService knowledgeBaseConfigService;

    /**
     * 测试AOP是否工作的方法
     */
    @AnalysisTask(taskType = TaskType.DISPUTE_FOCUS, description = "AOP测试")
    public String testAOP(Long caseImportId) {
        log.info("测试AOP方法被调用，caseImportId: {}", caseImportId);
        return "AOP测试成功";
    }

    /**
     * 根据案件ID获取争议焦点列表
     */
    public List<DisputeFocus> getDisputeFocusByCaseId(Long caseImportId) {
        log.info("获取争议焦点数据，caseImportId: {}", caseImportId);

        List<DisputeFocus> disputeFocusList = this.list(
                new LambdaQueryWrapper<DisputeFocus>()
                        .eq(DisputeFocus::getCaseImportId, caseImportId)
                        .orderBy(true, true, DisputeFocus::getCreateTime)
        );

        log.info("获取争议焦点数据完成，数量: {}", disputeFocusList.size());
        return disputeFocusList;
    }

    /**
     * 根据案件ID获取争议焦点列表
     *
     * @param caseImportId 案件ID
     * @return 争议焦点列表
     */
    public List<DisputeFocus> getByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<DisputeFocus> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DisputeFocus::getCaseImportId, caseImportId);
        wrapper.orderByAsc(DisputeFocus::getCreateTime, DisputeFocus::getId);
        return list(wrapper);
    }

    /**
     * 根据案件ID删除争议焦点
     *
     * @param caseImportId 案件ID
     * @return 是否删除成功
     */
    public boolean removeByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<DisputeFocus> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DisputeFocus::getCaseImportId, caseImportId);
        return remove(wrapper);
    }

    /**
     * 保存或更新争议焦点
     *
     * @param disputeFocus 争议焦点数据
     * @return 保存或更新后的争议焦点
     */
    @Transactional
    public DisputeFocus saveDisputeFocus(DisputeFocus disputeFocus) {
        // 验证数据
        validateDisputeFocus(disputeFocus);

        // 保存或更新到数据库
        saveOrUpdate(disputeFocus);
        
        return disputeFocus;
    }

    /**
     * 删除争议焦点
     *
     * @param focusId 争议焦点ID
     * @return 是否删除成功
     */
    @Transactional
    public boolean deleteDisputeFocus(Long focusId) {
        // 删除争议焦点
        return removeById(focusId);
    }

    /**
     * 重新生成时删除相关数据（内部方法，依赖外层事务）
     *
     * @param caseImportId 案件导入ID
     */
    public void removeDataForRegenerate(Long caseImportId) {
        log.info("开始清理案件 {} 的争议焦点相关数据", caseImportId);

        try {
            // 只删除争议焦点，不删除诉辩观点和诉辩关系
            removeByCaseImportId(caseImportId);

            log.info("案件 {} 的争议焦点相关数据清理完成", caseImportId);
        } catch (Exception e) {
            log.error("清理案件 {} 的争议焦点相关数据失败", caseImportId, e);
            throw new RuntimeException("数据清理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除案件下的所有争议焦点
     *
     * @param caseImportId 案件ID
     * @return 删除的数量
     */
    public int deleteAllByCaseImportId(Long caseImportId) {
        log.info("开始删除案件 {} 下的所有争议焦点", caseImportId);

        // 先查询要删除的争议焦点数量
        LambdaQueryWrapper<DisputeFocus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisputeFocus::getCaseImportId, caseImportId);
        int count = Math.toIntExact(count(queryWrapper));

        if (count == 0) {
            log.info("案件 {} 下没有争议焦点，无需删除", caseImportId);
            return 0;
        }

        // 执行删除
        LambdaQueryWrapper<DisputeFocus> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(DisputeFocus::getCaseImportId, caseImportId);
        boolean result = remove(deleteWrapper);

        if (result) {
            log.info("成功删除案件 {} 下的 {} 个争议焦点", caseImportId, count);
            return count;
        } else {
            log.error("删除案件 {} 下的争议焦点失败", caseImportId);
            throw new RuntimeException("删除争议焦点失败");
        }
    }

    /**
     * 验证争议焦点数据
     *
     * @param disputeFocus 争议焦点数据
     */
    private void validateDisputeFocus(DisputeFocus disputeFocus) {
        // 基本字段校验
        if (disputeFocus.getDescription() == null || disputeFocus.getDescription().trim().isEmpty()) {
            throw new RuntimeException("争议焦点描述不能为空");
        }

        // 新增时才校验 caseImportId
        if (disputeFocus.getId() == null && disputeFocus.getCaseImportId() == null) {
            throw new RuntimeException("新增争议焦点时案件ID不能为空");
        }

        // plaintiffClaim 和 defendantClaim 改为可选字段，不强制校验
        // 这些字段在新的设计中可能不是必需的
    }

    /**
     * 重新生成争议焦点（异步方法，使用@Async）
     * @param caseImportId 案件导入ID
     */
    @Async
    @AnalysisTask(taskType = TaskType.DISPUTE_FOCUS, completionStrategy = TaskCompletionStrategy.FIRST_SUCCESS, description = "争议焦点分析（重新生成）")
    public void generate(Long caseImportId) {
        // 执行分析任务核心逻辑
        executeAnalysisTaskInternal(caseImportId);
        log.info("争议焦点分析任务异步执行完成，caseImportId: {}", caseImportId);
    }

    /**
     * 重新生成争议焦点标签的所有模型任务
     * 包括：诉辩关系、争议焦点
     * @param caseImportId 案件导入ID
     */
    @Async
    public void regenerateAllFocusTasks(Long caseImportId) {
        log.info("开始重新生成争议焦点标签所有任务，caseImportId: {}", caseImportId);

        try {
            // 1. 重新生成诉辩关系
            log.info("步骤1：重新生成诉辩关系，caseImportId: {}", caseImportId);
            litigationPointService.removeDataForRegenerate(caseImportId);
            litigationPointService.generate(caseImportId);
            log.info("诉辩关系重新生成完成，caseImportId: {}", caseImportId);

            // 2. 重新生成争议焦点
            log.info("步骤2：重新生成争议焦点，caseImportId: {}", caseImportId);
            this.removeDataForRegenerate(caseImportId);
            this.generate(caseImportId);
            log.info("争议焦点重新生成完成，caseImportId: {}", caseImportId);

            log.info("争议焦点标签所有任务重新生成完成，caseImportId: {}", caseImportId);

        } catch (Exception e) {
            log.error("争议焦点标签任务重新生成失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("争议焦点标签任务重新生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行争议焦点分析任务（内部实现，纯业务逻辑）
     * @param caseImportId 案件导入ID
     */
    @Transactional(rollbackFor = Exception.class)
    protected List<DisputeFocus> executeAnalysisTaskInternal(Long caseImportId) {
        log.info("开始执行争议焦点分析任务核心逻辑，caseImportId: {}", caseImportId);

        // 生成争议焦点（包含保存和法条信息获取）
        List<DisputeFocus> disputeFocusList = generateDisputePoints(caseImportId);

        log.info("争议焦点分析任务核心逻辑执行完成，caseImportId: {}", caseImportId);
        return disputeFocusList;
    }



    /**
     * 生成争议焦点
     */
    private List<DisputeFocus> generateDisputePoints(Long caseImportId) {
        log.info("开始生成争议焦点");

        // 1. 调用Agent模型生成争议焦点并解析
        List<DisputeFocus> disputeFocusList = generateDisputePointsByAgent(caseImportId);

        // 原AI调用方式（已注释）
        // String prompt = systemPromptConfigService.getMessageByKey("DISPUTE_FOCUS_PROMPT", null);
        // String cleanResponse = aiModelService.callForDisputePoints(fileContents, prompt);
        // List<DisputeFocus> disputeFocusList = JSON.parseArray(cleanResponse, DisputeFocus.class);

        // 2. 设置案件ID
        setDisputeFocusCaseId(disputeFocusList, caseImportId);

        // 3. 保存争议焦点到数据库
        saveDisputeFocusData(disputeFocusList, caseImportId);

        // 4. 调用知识库获取法条信息并立即更新数据库
        getLawInfoFromKnowledgeBase(caseImportId, disputeFocusList);

        return disputeFocusList;
    }

    /**
     * 使用Agent生成争议焦点
     */
    private List<DisputeFocus> generateDisputePointsByAgent(Long caseImportId) {
        log.info("开始使用Agent生成争议焦点，caseImportId: {}", caseImportId);

        try {
            // 获取案件类型代码
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            String ajlxdm = caseRecord != null ? caseRecord.getAjlxdm() : "";

            // 根据案件类型准备不同的属性数据
            String taskName;
            String bootYmlName;
            Map<String, String> propertyMap = new HashMap<>();
            // 获取起诉状内容
            String qszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.QSZ);
            String bcQszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.BCQSYJ);
            propertyMap.put("material:QSZ", qszContent + "\n" + bcQszContent);

            // 获取答辩状内容
            String dbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ);
            String bcDbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.BCDBYJ);
            propertyMap.put("material:DBZ", dbzContent + "\n" + bcDbzContent);

            // 获取庭审笔录内容
            String tsblContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.TSBL);
            propertyMap.put("material:TSBL", tsblContent);

            String caseType = getCaseCause(caseRecord);
            propertyMap.put("case:cause_of_action", caseType != null ? caseType : "");

            // 获取当事人信息
            List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);
            if (CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)) {
                // 获取二审文件内容（新增）
                String firstInstanceJudgment = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.YSPJS);
                String appealContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.SSZ);

                propertyMap.put("一审判决书", firstInstanceJudgment != null ? firstInstanceJudgment : "");
                propertyMap.put("上诉状", appealContent != null ? appealContent : "");
                propertyMap.put("当事人信息", buildPartiesInfo(caseParties));

                taskName =  "main";
                bootYmlName = "mses-dispute-focuses.yml";
                log.info("二审案件，使用配置文件: {}, 任务名称: {}", bootYmlName, taskName);
            } else {
                // 获取原告信息
                String plaintiffInfo = caseParties.stream()
                        .filter(party -> "原告".equals(party.getPartyType()))
                        .map(party -> party.getPartyName())
                        .collect(Collectors.joining("、"));
                propertyMap.put("party:plaintiff_position_name", plaintiffInfo);

                // 获取被告信息
                String defendantInfo = caseParties.stream()
                        .filter(party -> "被告".equals(party.getPartyType()))
                        .map(party -> party.getPartyName())
                        .collect(Collectors.joining("、"));
                propertyMap.put("party:defendant_position_name", defendantInfo);
                boolean hasCourtTranscript = hasCourtTranscriptFile(caseImportId);
                taskName = hasCourtTranscript ? "有庭审笔录" : "无庭审笔录";
                bootYmlName = "dispute-focuses.yml";
                log.info("一审案件，使用配置文件: {}, 任务名称: {}", bootYmlName, taskName);
            }

            String res = agentTaskUtils.executeTask("星洲-争议焦点Agent", taskName, bootYmlName, propertyMap);

            List<DisputeFocus> disputeFocusList = JSON.parseArray(res, DisputeFocus.class);
            log.info("Agent争议焦点分析完成，caseImportId: {}, 争议焦点数量: {}",
                    caseImportId,
                    disputeFocusList != null ? disputeFocusList.size() : 0);

            return disputeFocusList;

        } catch (Exception e) {
            log.error("Agent争议焦点分析失败，caseImportId: {}, 错误: {}", caseImportId, e.getMessage(), e);
            throw new RuntimeException("争议焦点分析失败: " + e.getMessage(), e);
        }
    }
    /**
     * 构建当事人信息
     */
    private String buildPartiesInfo(List<CaseParty> caseParties) {
        if (caseParties == null || caseParties.isEmpty()) {
            return "";
        }

        StringBuilder partiesInfo = new StringBuilder();

        String appellantInfo = caseParties.stream()
                .map(party -> party.getPartyType() + party.getPartyName())
                .collect(Collectors.joining("、"));

        partiesInfo.append(appellantInfo);

        return partiesInfo.toString();
    }

    /**
     * 准备二审案件相关数据
     */
    private void prepareSecondInstanceData(Long caseImportId, Map<String, String> propertyMap) {
        try {
            log.info("开始准备二审案件数据，caseImportId: {}", caseImportId);

            // 获取一审诉讼请求（从起诉状中提取）
            String firstInstanceLawsuitRequest = extractFirstInstanceLawsuitRequest(caseImportId);
            propertyMap.put("一审诉讼请求", firstInstanceLawsuitRequest);

            // 获取一审认定事实（从一审判决书中提取）
            String firstInstanceDeterminedFacts = extractFirstInstanceDeterminedFacts(caseImportId);
            propertyMap.put("一审认定事实", firstInstanceDeterminedFacts);

            // 获取一审裁判理由（从一审判决书中提取）
            String firstInstanceJudgmentReason = extractFirstInstanceJudgmentReason(caseImportId);
            propertyMap.put("一审裁判理由", firstInstanceJudgmentReason);

            // 获取一审裁判结果（从一审判决书中提取）
            String firstInstanceJudgmentResult = extractFirstInstanceJudgmentResult(caseImportId);
            propertyMap.put("一审裁判结果", firstInstanceJudgmentResult);

            // 获取上诉意见（从上诉状中提取）
            String appealOpinion = extractAppealOpinion(caseImportId);
            propertyMap.put("上诉意见", appealOpinion);

            log.info("二审案件数据准备完成，caseImportId: {}", caseImportId);

        } catch (Exception e) {
            log.error("准备二审案件数据失败，caseImportId: {}", caseImportId, e);
            // 不抛出异常，使用空值继续执行
            propertyMap.put("一审诉讼请求", "");
            propertyMap.put("一审认定事实", "");
            propertyMap.put("一审裁判理由", "");
            propertyMap.put("一审裁判结果", "");
            propertyMap.put("上诉意见", "");
        }
    }

    /**
     * 提取一审诉讼请求
     */
    private String extractFirstInstanceLawsuitRequest(Long caseImportId) {
        return "";
    }

    /**
     * 提取一审认定事实
     */
    private String extractFirstInstanceDeterminedFacts(Long caseImportId) {
        // 从一审判决书中提取认定事实部分
        // TODO: 需要根据实际文档类型来获取一审判决书内容
        return "";
    }

    /**
     * 提取一审裁判理由
     */
    private String extractFirstInstanceJudgmentReason(Long caseImportId) {
        // 从一审判决书中提取裁判理由部分
        // TODO: 需要根据实际文档类型来获取一审判决书内容
        return "";
    }

    /**
     * 提取一审裁判结果
     */
    private String extractFirstInstanceJudgmentResult(Long caseImportId) {
        // 从一审判决书中提取裁判结果部分
        // TODO: 需要根据实际文档类型来获取一审判决书内容
        return "";
    }

    /**
     * 提取上诉意见
     */
    private String extractAppealOpinion(Long caseImportId) {
        // 从起诉状或一审判决书中提取诉讼请求
        String qszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.QSZ);
        // TODO: 这里可以添加更精确的提取逻辑，比如使用正则表达式或AI提取
        return qszContent != null ? qszContent : "无";
    }



    /**
     * 调用知识库获取法条信息并立即更新数据库
     */
    private void getLawInfoFromKnowledgeBase(Long caseImportId, List<DisputeFocus> disputeFocusList) {
        try {
            log.info("开始为 {} 个争议焦点查询法条信息", disputeFocusList.size());

            // 获取知识库配置
            KnowledgeBaseConfig config = knowledgeBaseConfigService.getEnabledConfig(DisputeConstants.KnowledgeBaseConfigKey.LAW_INFO_QUERY);

            // 对每个争议焦点单独查询法条
            for (DisputeFocus disputeFocus : disputeFocusList) {
                processSingleDisputeFocus(caseImportId, disputeFocus, config);
            }

            log.info("所有争议焦点法条查询和更新完成");

        } catch (Exception e) {
            log.error("调用知识库获取法条信息失败", e);
        }
    }

    /**
     * 处理单个争议焦点的法条查询
     */
    private void processSingleDisputeFocus(Long caseImportId, DisputeFocus disputeFocus, KnowledgeBaseConfig config) {
        try {
            log.info("开始为争议焦点ID: {} 查询法条信息", disputeFocus.getId());

            long startTime = System.currentTimeMillis();

            String lawInfo;
            String keywords = null;

            // 检查是否启用知识库查询
            if (config.getIsEnabled() != null && config.getIsEnabled()) {
                log.info("使用知识库查询法条信息");

                // 为单个争议焦点构建查询问题
                keywords = buildSingleDisputeFocusLawQuery(caseImportId, disputeFocus);

                // 获取案件材料
                String caseMaterial = getCaseMaterial(caseImportId);

                // 查询法条信息
                lawInfo = queryLawInfoFromKnowledgeBase(keywords, caseMaterial, disputeFocus.getDescription(), config);

                // 记录任务执行信息
                recordTaskExecution(startTime, keywords, lawInfo);
            } else {
                log.info("使用Agent模型直接提取法条编号");

                // 使用Agent模型直接提取法条编号
                lawInfo = queryLawInfoFromAgent(caseImportId, disputeFocus);
            }


            // 转换为JSON格式
            String convertedLawInfo = convertLawInfoToJson(lawInfo, disputeFocus.getId());

            // 更新数据库
            updateDisputeFocusLawInfo(disputeFocus.getId(), convertedLawInfo);

        } catch (Exception e) {
            log.error("争议焦点ID: {} 查询法条信息失败", disputeFocus.getId(), e);
            // 单个争议焦点失败不影响其他争议焦点的处理
        }
    }

    /**
     * 从知识库查询法条信息
     */
    private String queryLawInfoFromKnowledgeBase(String keywords, String caseMaterial, String disputeDescription, KnowledgeBaseConfig config) {
        try {
            // 构建ChatAssistantCreateRequest对象
            ChatAssistantCreateRequest createRequest = buildChatAssistantRequest(config, caseMaterial, disputeDescription);

            return ragflowFacadeService.queryKnowledgeBase(createRequest, keywords);
        } catch (Exception e) {
            log.error("知识库查询失败，关键词: {}", keywords, e);
            throw new RuntimeException("知识库查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Agent模型直接提取法条编号
     */
    private String queryLawInfoFromAgent(Long caseImportId, DisputeFocus disputeFocus) {
        try {
            log.info("使用Agent模型为争议焦点ID: {} 提取法条编号", disputeFocus.getId());

            // 获取案件材料
            String caseMaterial = getCaseMaterial(caseImportId);

            // 构建Agent输入参数
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("CASE_MATERIALS", caseMaterial);
            propertyMap.put("DISPUTE", disputeFocus.getDescription());

            // 调用Agent提取法条编号
            String lawInfo = agentTaskUtils.executeTask("星洲-法条提取Agent", "main", "dispute-laws.yml", propertyMap);

            log.info("Agent模型提取法条编号完成，争议焦点ID: {}", disputeFocus.getId());
            return lawInfo;

        } catch (Exception e) {
            log.error("Agent模型提取法条编号失败，争议焦点ID: {}", disputeFocus.getId(), e);
            throw new RuntimeException("Agent模型提取法条编号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建ChatAssistantCreateRequest对象
     */
    private ChatAssistantCreateRequest buildChatAssistantRequest(KnowledgeBaseConfig config, String caseMaterial, String disputeDescription) {
        ChatAssistantCreateRequest.ChatAssistantCreateRequestBuilder builder = ChatAssistantCreateRequest.builder()
                .description("法条信息查询助手")
                .datasetIds(config.getDatasetIds());

        ChatAssistantCreateRequest request = builder.build();

        // 设置LLM配置
        if (config.getLlmConfig() != null) {
            KnowledgeBaseConfig.LlmConfig llmConfig = config.getLlmConfig();

            request.addLlmSetting("model_name", llmConfig.getModelName())
                   .addLlmSetting("temperature", llmConfig.getTemperature())
                   .addLlmSetting("top_p", llmConfig.getTopP())
                   .addLlmSetting("presence_penalty", llmConfig.getPresencePenalty())
                   .addLlmSetting("frequency_penalty", llmConfig.getFrequencyPenalty());
        }

        // 设置提示词配置
        if (config.getPromptConfig() != null) {
            KnowledgeBaseConfig.PromptConfig promptConfig = config.getPromptConfig();

            // 构建包含案件材料的提示词
            String knowledgePrompt = buildKnowledgePrompt(caseMaterial, disputeDescription, promptConfig.getPrompt());

            request.addPromptSetting("similarity_threshold", promptConfig.getSimilarityThreshold())
                   .addPromptSetting("keywords_similarity_weight", promptConfig.getKeywordsSimilarityWeight())
                   .addPromptSetting("top_n", promptConfig.getTopN())
                   .addPromptSetting("top_k", promptConfig.getTopK())
                   .addPromptSetting("empty_response", promptConfig.getEmptyResponse())
                   .addPromptSetting("opener", promptConfig.getOpener())
                   .addPromptSetting("show_quote", promptConfig.getShowQuote())
                   .addPromptSetting("prompt", knowledgePrompt);

            // 设置变量配置，如果没有配置则使用默认值
            if (promptConfig.getVariables() != null) {
                request.addPromptSetting("variables", promptConfig.getVariables());
            } else {
                // 使用默认的变量配置
                request.addPromptSetting("variables", java.util.List.of(
                    java.util.Map.of("key", "knowledge", "optional", true)
                ));
            }
        }

        return request;
    }

    /**
     * 构建知识库查询提示词
     */
    private String buildKnowledgePrompt(String caseMaterial, String disputeDescription, String promptTemplate) {
        // 将案件材料替换到提示词模板中
        return promptTemplate.replace("case_material", caseMaterial != null ? caseMaterial : "")
                .replace("dispute_description", disputeDescription != null ? disputeDescription : "");
    }

    /**
     * 获取案件材料
     */
    private String getCaseMaterial(Long caseImportId) {
        try {
            // 判断是否二审案件
            boolean isSecondInstance = isSecondInstanceCase(caseImportId);

            // 根据案件类型获取相应的文档内容
            if (isSecondInstance) {
                // 二审：一审判决书/上诉状
                return fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId,
                    DocumentType.YSPJS, DocumentType.SSZ);
            } else {
                // 一审：起诉状/答辩状
                return fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId,
                    DocumentType.QSZ, DocumentType.DBZ);
            }
        } catch (Exception e) {
            log.error("获取案件材料失败，案件ID: {}", caseImportId, e);
            return "";
        }
    }

    /**
     * 记录任务执行信息
     */
    private void recordTaskExecution(long startTime, String keywords, String lawInfo) {
        try {
            Long taskId = AgentTaskContext.getCurrentTaskId();
            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;

            taskStatusManager.updateTaskResult(taskId, new AgentTaskInfo(
                "知识库法条查询",
                "done",
                (double) startTime,
                durationSeconds,
                new AgentTaskTokenInfo(0, 0, 0),
                "",
                Collections.singletonList(new AgentSubTaskResult("智推法条", "智推法条", keywords, (double) startTime, durationSeconds, lawInfo)),
                lawInfo,
                null,
                null
            ));
        } catch (Exception e) {
            log.warn("记录任务执行信息失败", e);
            // 不影响主流程
        }
    }

    /**
     * 将法条信息转换为JSON格式
     */
    private String convertLawInfoToJson(String lawInfo, Long disputeFocusId) {
        try {
            String jsonSchema = """
                      laws没有第X则不显示，例如输入中华人民共和国民法典第一千零八十四条第三款，则laws为["中华人民共和国民法典第一千零八十四条第三款"]
                      {
                        "laws": ["法律名称第X条第X款第X项"]
                      }
                    """;

            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("INPUT", lawInfo);
            propertyMap.put("JSON_SCHEMA", jsonSchema);

            String convertedLawInfo = agentTaskUtils.executeTask("星洲-JSON处理Agent", "main", "json-convert.yml", propertyMap);
            log.info("争议焦点ID: {} 法条信息转换完成，响应: {}", disputeFocusId, convertedLawInfo);

            return convertedLawInfo;
        } catch (Exception e) {
            log.error("争议焦点ID: {} 法条信息JSON转换失败", disputeFocusId, e);
            throw new RuntimeException("法条信息JSON转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新争议焦点的法条信息到数据库
     */
    private void updateDisputeFocusLawInfo(Long disputeFocusId, String convertedLawInfo) {
        try {
            // 解析法条信息
            LawInfoResponse singleLawInfo = JSON.parseObject(convertedLawInfo, LawInfoResponse.class);
            if (singleLawInfo != null) {
                // 立即更新当前争议焦点的法条信息到数据库
                updateSingleDisputeFocusLaws(disputeFocusId, singleLawInfo);
                log.info("争议焦点ID: {} 成功获取并更新法条信息", disputeFocusId);
            } else {
                log.warn("争议焦点ID: {} 未获取到法条信息", disputeFocusId);
            }
        } catch (Exception e) {
            log.error("争议焦点ID: {} 更新法条信息失败", disputeFocusId, e);
            throw new RuntimeException("更新法条信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据ID更新争议焦点的法条信息
     */
    private void updateDisputeFocusLaws(List<LawInfoResponse> lawInfoList) {
        List<DisputeFocus> disputeFocusList = lawInfoList.stream().map(lawInfo -> {
            DisputeFocus disputeFocus = new DisputeFocus();
            disputeFocus.setId(Long.valueOf(lawInfo.getId()));
            disputeFocus.setLaws(lawInfo.getLaws());
            disputeFocus.setLawSummaries(lawInfo.getLawSummaries());
            return disputeFocus;
        }).toList();

        this.updateBatchById(disputeFocusList);
    }

    /**
     * 更新单个争议焦点的法条信息
     */
    private void updateSingleDisputeFocusLaws(Long disputeFocusId, LawInfoResponse lawInfo) {
        try {
            // 获取法条信息
            List<String> laws = lawInfo.getLaws() != null ? lawInfo.getLaws() : new ArrayList<>();

            // 根据 laws 查询数据库获取法条内容
            List<String> lawSummaries = new ArrayList<>();
            if (!laws.isEmpty()) {
                log.info("开始查询数据库获取法条内容，法条数量: {}", laws.size());
                LegalProvisionService.LawQueryResult queryResult = legalProvisionService.getValidProvisionContentsByLawReferences(laws);

                // 更新为只包含有效的法条
                laws = queryResult.getValidLaws();
                lawSummaries = queryResult.getLawSummaries();

                log.info("查询完成，有效法条数量: {}, 法条详情数量: {}", laws.size(), lawSummaries.size());
            }

            // 创建更新对象
            DisputeFocus disputeFocus = new DisputeFocus();
            disputeFocus.setId(disputeFocusId);
            disputeFocus.setLaws(laws);
            disputeFocus.setLawSummaries(lawSummaries);

            // 更新数据库
            this.updateById(disputeFocus);
            log.info("成功更新争议焦点ID: {} 的法条信息，法条数量: {}, 法条详情数量: {}",
                    disputeFocusId, laws.size(), lawSummaries.size());

        } catch (Exception e) {
            log.error("更新争议焦点ID: {} 的法条信息失败", disputeFocusId, e);
            throw new RuntimeException("更新争议焦点法条信息失败: " + e.getMessage(), e);
        }
    }


    /**
     * 设置争议焦点数据的案件ID
     */
    private void setDisputeFocusCaseId(List<DisputeFocus> disputeFocusList, Long caseImportId) {
        if (disputeFocusList != null) {
            disputeFocusList.forEach(dispute -> {
                dispute.setCaseImportId(caseImportId);
                dispute.setCreateTime(java.time.LocalDateTime.now());
                dispute.setUpdateTime(java.time.LocalDateTime.now());
            });
        }
    }

    /**
     * 保存争议焦点数据（内部方法，依赖外层事务）
     */
    private void saveDisputeFocusData(List<DisputeFocus> disputeFocusList, Long caseImportId) {
        try {
            log.info("开始保存争议焦点数据，caseImportId: {}", caseImportId);

            // 只保存争议焦点，不保存诉辩观点和诉辩关系
            if (disputeFocusList != null && !disputeFocusList.isEmpty()) {
                disputeFocusList.forEach(dispute -> dispute.setId(null));
                this.saveBatch(disputeFocusList);
                log.info("保存争议焦点 {} 条", disputeFocusList.size());
            }

            log.info("争议焦点数据保存完成");
        } catch (Exception e) {
            log.error("争议焦点数据保存失败", e);
            throw new RuntimeException("争议焦点数据保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为单个争议焦点构建法条查询问题
     * @param caseImportId 案件ID
     * @param disputeFocus 单个争议焦点
     * @return 构建好的查询问题
     */
    private String buildSingleDisputeFocusLawQuery(Long caseImportId, DisputeFocus disputeFocus) {
        try {
            // 案由
            String caseCause = getCaseCause(caseImportId);

            // 判断是否二审
            boolean isSecondInstance = isSecondInstanceCase(caseImportId);

            Map<String, String> inputPropertyMap = new HashMap<>();
            inputPropertyMap.put("CASE_CAUSE", caseCause);
            inputPropertyMap.put("CASE_CONTENT", isSecondInstance ? fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.YSPJS, DocumentType.SSZ) : fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.QSZ, DocumentType.DBZ));
            inputPropertyMap.put("DISPUTE_FOCUS", disputeFocus.getDescription());

            // 一审：答辩状/起诉状
            // 二审：一审判决书/上诉状
            String caseKeywords = agentTaskUtils.executeTask("星洲-提取关键字", "main", "extract_keywords.yml", inputPropertyMap);

            log.info("为争议焦点ID: {} 构建法条查询问题成功，案件ID: {}, 问题: {}",
                    disputeFocus.getId(), caseImportId, caseKeywords);
            return caseKeywords;

        } catch (Exception e) {
            log.error("为争议焦点ID: {} 构建法条查询问题失败，案件ID: {}", disputeFocus.getId(), caseImportId, e);
            throw new RuntimeException("构建法条查询问题失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为二审案件
     */
    private boolean isSecondInstanceCase(Long caseImportId) {
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        return CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
    }

    /**
     * 构建法条查询问题（保留原方法，用于兼容性）
     * @param caseImportId 案件ID
     * @param disputeFocusList 争议焦点列表
     * @return 构建好的查询问题
     */
    private String buildLawQueryQuestion(Long caseImportId, List<DisputeFocus> disputeFocusList) {
        try {
            // 从数据库获取提示词模板
            String promptTemplate = systemPromptConfigService.getMessageByKey("DISPUTE_FOCUS_LAW_QUERY",
                "请根据争议焦点查询相关法条信息");

            // 获取各个字段的数据
            String caseCause = getCaseCause(caseImportId);
            String disputeFocus = buildDisputeFocusInfo(disputeFocusList);

            // 使用String.format替换占位符
            String question = String.format(promptTemplate, caseCause, disputeFocus);

            return question;

        } catch (Exception e) {
            log.error("构建法条查询问题失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("构建法条查询问题失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取案由信息
     */
    private String getCaseCause(Long caseImportId) {
        return getCaseCause(caseImportId);
    }

    private String getCaseCause(CaseImportRecord caseRecord) {
        try {
            String caseCause = (caseRecord != null && caseRecord.getCaseCause() != null) ?
                    caseRecord.getCaseCause() : "";
            if(StringUtils.isBlank(caseCause)){
                Object trialOrgInfo = fileUploadRecordMapper.getFileRecordElementByDocumentTypes(
                        caseRecord.getId(), List.of(DocumentType.AJPDTZS),"案由");
                if(trialOrgInfo != null){
                    return trialOrgInfo.toString();
                }
            }
            log.info("获取案由成功，案件ID: {}, 案由: {}", caseRecord.getId(), caseCause);
            return caseCause;
        } catch (Exception e) {
            log.error("获取案由失败，案件ID: {}", caseRecord.getId(), e);
            return "";
        }
    }

    /**
     * 获取诉讼请求（从答辩状文件）
     */
    private String getLitigationRequest(Long caseImportId) {
        try {
            String dbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ);
            log.info("获取诉讼请求成功，案件ID: {}, 内容长度: {}", caseImportId, dbzContent.length());
            return dbzContent;
        } catch (Exception e) {
            log.error("获取诉讼请求失败，案件ID: {}", caseImportId, e);
            return "";
        }
    }

    /**
     * 获取认定事实（从无争议事实表）
     */
    private String getDeterminedFacts(Long caseImportId) {
        try {
            EvidenceFactsDetails evidenceDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
            String determinedFacts = "";

            if (evidenceDetails != null) {
                // 最后使用模型生成的认定事实
                if (evidenceDetails.getDetermineFacts() != null && !evidenceDetails.getDetermineFacts().trim().isEmpty()) {
                    determinedFacts = evidenceDetails.getDetermineFacts();
                }
                // 如果无争议事实为空，使用认定事实提取内容
                else if (evidenceDetails.getDetermineFactsExtract() != null && !evidenceDetails.getDetermineFactsExtract().trim().isEmpty()) {
                    determinedFacts = evidenceDetails.getDetermineFactsExtract();
                }
            }

            log.info("获取认定事实成功，案件ID: {}, 内容长度: {}", caseImportId, determinedFacts.length());
            return determinedFacts;
        } catch (Exception e) {
            log.error("获取认定事实失败，案件ID: {}", caseImportId, e);
            return "";
        }
    }

    /**
     * 构建单个争议焦点信息
     */
    private String buildSingleDisputeFocusInfo(DisputeFocus disputeFocus) {
        return "争议焦点（ID：" + disputeFocus.getId() + "）：" + disputeFocus.getDescription();
    }

    /**
     * 构建争议焦点信息
     */
    private String buildDisputeFocusInfo(List<DisputeFocus> disputeFocusList) {
        StringBuilder focusInfo = new StringBuilder();

        for (int i = 0; i < disputeFocusList.size(); i++) {
            DisputeFocus dispute = disputeFocusList.get(i);
            focusInfo.append("争议焦点").append(i + 1)
                    .append("（ID：").append(dispute.getId()).append("）")
                    .append("：").append(dispute.getDescription());

            if (i < disputeFocusList.size() - 1) {
                focusInfo.append("；");
            }
        }

        return focusInfo.toString();
    }

    /**
     * 构建简化的法条查询内容（备用方案）
     */
    private String buildSimpleLawQuery(List<DisputeFocus> disputeFocusList) {
        StringBuilder query = new StringBuilder();
        query.append("争议焦点详情:\n");

        for (int i = 0; i < disputeFocusList.size(); i++) {
            DisputeFocus dispute = disputeFocusList.get(i);
            query.append("ID: ").append(dispute.getId()).append("\n");
            query.append("争议焦点").append(i + 1).append(": ").append(dispute.getDescription()).append("\n");

            if (dispute.getPlaintiffClaim() != null && !dispute.getPlaintiffClaim().trim().isEmpty()) {
                query.append("原告主张: ").append(dispute.getPlaintiffClaim()).append("\n");
            }

            if (dispute.getDefendantClaim() != null && !dispute.getDefendantClaim().trim().isEmpty()) {
                query.append("被告主张: ").append(dispute.getDefendantClaim()).append("\n");
            }

            query.append("---\n");
        }

        query.append("\n请根据以上争议焦点，提供相关的法律条文和法律依据。");
        return query.toString();
    }

    /**
     * 根据是否有庭审笔录确定使用的任务名称
     */
    private String determineTaskName(Long caseImportId) {
        // 检查是否有庭审笔录文件
        boolean hasCourtTranscript = hasCourtTranscriptFile(caseImportId);

        if (hasCourtTranscript) {
            log.info("检测到庭审笔录，使用有庭审笔录的争议焦点分析任务");
            return "有庭审笔录";
        } else {
            log.info("未检测到庭审笔录，使用无庭审笔录的争议焦点分析任务");
            return "无庭审笔录";
        }
    }

    /**
     * 检查是否有庭审笔录文件
     */
    private boolean hasCourtTranscriptFile(Long caseImportId) {
        try {
            List<FileUploadRecord> courtTranscripts = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                caseImportId, List.of(DocumentType.TSBL));

            boolean hasTranscript = !courtTranscripts.isEmpty();
            log.info("庭审笔录检查结果，caseImportId: {}, 是否有庭审笔录: {}, 文件数量: {}",
                    caseImportId, hasTranscript, courtTranscripts.size());

            return hasTranscript;
        } catch (Exception e) {
            log.error("检查庭审笔录文件失败，caseImportId: {}, 默认返回false", caseImportId, e);
            return false;
        }
    }

    /**
     * 获取争议焦点法条完成进度
     * 基于争议焦点数据中的laws字段判断是否有法条
     */
    public Map<String, Integer> getLawProgress(Long caseImportId) {
        log.info("获取争议焦点法条进度，caseImportId: {}", caseImportId);

        try {
            // 查询该案件的所有争议焦点
            List<DisputeFocus> disputeFocusList = getDisputeFocusByCaseId(caseImportId);

            int total = disputeFocusList.size();
            int completed = 0;

            // 统计有法条数据的争议焦点数量
            for (DisputeFocus focus : disputeFocusList) {
                if (focus.getLaws() != null && !focus.getLaws().isEmpty()) {
                    completed++;
                }
            }

            Map<String, Integer> progress = new HashMap<>();
            progress.put("total", total);
            progress.put("completed", completed);

            log.info("争议焦点法条进度统计完成，总数: {}, 已完成: {}", total, completed);
            return progress;

        } catch (Exception e) {
            log.error("获取争议焦点法条进度失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("获取法条进度失败: " + e.getMessage(), e);
        }
    }

}