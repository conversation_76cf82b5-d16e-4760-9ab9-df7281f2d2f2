package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.smxz.agent.client.AgentClient;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.common.utils.AiOutputUtils;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.CaseImportRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 案由提取服务
 * 从庭审笔录中提取案件的案由信息
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class CaseCauseExtractionService {

    @Autowired
    private CaseImportRecordMapper caseImportRecordMapper;

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    /**
     * 案由提取结果数据类
     */
    public static class CaseCauseResult {
        private String caseCause;

        public String getCaseCause() {
            return caseCause;
        }

        public void setCaseCause(String caseCause) {
            this.caseCause = caseCause;
        }
    }

    /**
     * 提取案由（异步方法，使用@Async）
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.CASE_CAUSE_EXTRACTION, description = "案由提取")
    public void extractCaseCause(Long caseImportId) {
        log.info("开始执行案由提取任务，caseImportId: {}", caseImportId);

        // 执行案由提取核心逻辑
        executeExtractionInternal(caseImportId);

        log.info("案由提取任务执行完成，caseImportId: {}", caseImportId);
    }

    /**
     * 执行案由提取任务（内部实现，纯业务逻辑）
     * @param caseImportId 案件导入ID
     */
    protected void executeExtractionInternal(Long caseImportId) {
        log.info("开始执行案由提取任务核心逻辑，caseImportId: {}", caseImportId);

        // 1. 获取庭审笔录文件内容
        String courtRecordContent = fileUploadRecordService.getExtractedTextByDocumentTypes(
                caseImportId, DocumentType.TSBL);
        if (StringUtils.isBlank(courtRecordContent)) {
            throw new IllegalStateException("未找到庭审笔录文件内容");
        }

        // 2. 调用AI模型提取案由
        String caseCause = extractCaseCauseByAgent(caseImportId, courtRecordContent);
        if (StringUtils.isBlank(caseCause)) {
            throw new IllegalStateException("案由提取失败，返回结果为空");
        }

        // 3. 更新案件记录中的案由字段
        updateCaseCauseInRecord(caseImportId, caseCause);

        log.info("案由提取任务核心逻辑执行完成，caseImportId: {}, 提取的案由: {}", caseImportId, caseCause);
    }



    /**
     * 通过Agent调用AI模型提取案由
     */
    private String extractCaseCauseByAgent(Long caseImportId, String courtRecordContent) {
        log.info("开始调用Agent提取案由，caseImportId: {}", caseImportId);

        try {
            String cleanResponse = agentTaskUtils.executeTask("星洲-案由提取Agent", "main", "extract_case_cause_task.yml", courtRecordContent);

            // 解析JSON响应
            CaseCauseResult result = JSON.parseObject(cleanResponse, CaseCauseResult.class);
            if (result != null && StringUtils.isNotBlank(result.getCaseCause())) {
                log.info("案由提取成功，案由: {}", result.getCaseCause());
                return result.getCaseCause();
            } else {
                log.warn("案由提取结果解析失败或为空");
                return null;
            }

        } catch (Exception e) {
            log.error("调用Agent提取案由失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("案由提取失败", e);
        }
    }



    /**
     * 更新案件记录中的案由字段
     */
    @Transactional(rollbackFor = Exception.class)
    protected void updateCaseCauseInRecord(Long caseImportId, String caseCause) {
        log.info("开始更新案件记录中的案由，caseImportId: {}, 案由: {}", caseImportId, caseCause);

        LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CaseImportRecord::getId, caseImportId)
                    .set(CaseImportRecord::getCaseCause, caseCause);

        int updated = caseImportRecordMapper.update(null, updateWrapper);
        if (updated > 0) {
            log.info("案由更新成功，caseImportId: {}, 案由: {}", caseImportId, caseCause);
        } else {
            log.error("案由更新失败，caseImportId: {}, 案由: {}", caseImportId, caseCause);
            throw new RuntimeException("案由更新失败");
        }
    }
}
