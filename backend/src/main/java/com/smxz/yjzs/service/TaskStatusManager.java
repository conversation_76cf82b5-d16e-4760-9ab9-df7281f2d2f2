package com.smxz.yjzs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.yjzs.dto.TaskStatusDTO;
import com.smxz.yjzs.entity.AnalysisTaskRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.service.impl.AnalysisTaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务状态管理服务
 * 统一管理所有分析任务的状态
 */
@Slf4j
@Service
public class TaskStatusManager {
    
    @Autowired
    private AnalysisTaskRecordService analysisTaskRecordService;

    /**
     * 开始新的任务执行（每次都创建新记录）
     * @param caseId 案件ID
     * @param taskType 任务类型
     * @return 任务ID
     */
    public Long startTask(Long caseId, TaskType taskType) {
        // 直接创建"执行中"状态的任务记录，一次数据库操作完成
        AnalysisTaskRecord task = analysisTaskRecordService.createRunningAnalysisTask(
            caseId, taskType.getCode(), taskType.getName());

        log.info("新任务开始执行，caseId: {}, taskType: {}, taskId: {}",
                 caseId, taskType.getCode(), task.getId());
        return task.getId();
    }

    /**
     * 终止指定案件和任务类型的执行中任务
     * @param caseId 案件ID
     * @param taskType 任务类型
     */
    public void terminateRunningTask(Long caseId, TaskType taskType) {
        analysisTaskRecordService.terminateRunningTasksByType(caseId, taskType.getCode(), "重新生成中止");
        log.info("终止执行中任务，caseId: {}, taskType: {}", caseId, taskType.getCode());
    }
    
    /**
     * 更新任务状态为成功（基于任务ID）
     * @param taskId 任务ID
     */
    public void markTaskSuccess(Long taskId) {
        analysisTaskRecordService.updateTaskSuccess(taskId);
        log.info("任务执行成功，taskId: {}", taskId);
    }

    /**
     * 更新任务完成时间和耗时（用于流式任务完成时）
     * @param taskId 任务ID
     */
    public void updateTaskCompletionTime(Long taskId) {
        if (taskId == null) {
            log.warn("任务ID为空，无法更新完成时间");
            return;
        }

        try {
            // 调用不带参数的updateTaskSuccess方法，该方法会自动计算完成时间和耗时
            analysisTaskRecordService.updateTaskSuccess(taskId);
            log.info("任务完成时间更新成功，taskId: {}", taskId);
        } catch (Exception e) {
            log.error("更新任务完成时间失败，taskId: {}, error: {}", taskId, e.getMessage(), e);
        }
    }


    /**
     * 更新任务状态为失败（基于任务ID）
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    public void markTaskFailed(Long taskId, String errorMessage) {
        analysisTaskRecordService.updateTaskFailure(taskId, errorMessage);
        log.error("任务执行失败，taskId: {}, error: {}", taskId, errorMessage);
    }

    /**
     * 更新任务状态为失败（基于任务ID，包含子任务结果）
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param agentTaskInfoList 任务信息列表
     */
    public void markTaskFailed(Long taskId, String errorMessage, List<AgentTaskInfo> agentTaskInfoList) {
        analysisTaskRecordService.updateTaskFailure(taskId, errorMessage, agentTaskInfoList);
        log.error("任务执行失败，taskId: {}, error: {}, 任务信息列表: {}", taskId, errorMessage,
                agentTaskInfoList);
    }

    /**
     * 更新任务结果（在finalProcess中调用）
     * 累积更新：先获取现有的任务信息列表，然后添加新的任务信息
     * @param taskId 任务ID
     * @param agentTaskInfo 新的任务信息
     */
    public synchronized void updateTaskResult(Long taskId, AgentTaskInfo agentTaskInfo) {
        if (taskId == null || agentTaskInfo == null) {
            log.warn("任务结果更新失败，参数为空，taskId: {}, agentTaskInfo: {}", taskId, agentTaskInfo);
            return;
        }

        try {
            // 1. 获取现有的任务记录
            AnalysisTaskRecord existingTask = analysisTaskRecordService.getById(taskId);
            if (existingTask == null) {
                log.error("任务记录不存在，taskId: {}", taskId);
                return;
            }

            // 2. 获取现有的任务信息列表，如果为空则创建新列表
            List<AgentTaskInfo> existingTaskInfoList = existingTask.getAgentTaskInfo();
            List<AgentTaskInfo> updatedTaskInfoList = new ArrayList<>();
            if (existingTaskInfoList != null) {
                updatedTaskInfoList.addAll(existingTaskInfoList);
            }

            // 3. 添加新的任务信息
            updatedTaskInfoList.add(agentTaskInfo);

            // 4. 更新任务记录
            analysisTaskRecordService.updateTaskSuccess(taskId, updatedTaskInfoList);

            log.info("任务结果累积更新成功，taskId: {}, 新增任务信息: {}, 总任务信息数量: {}",
                    taskId, agentTaskInfo.name(), updatedTaskInfoList.size());

        } catch (Exception e) {
            log.error("任务结果更新失败，taskId: {}, error: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("任务结果更新失败", e);
        }
    }

    /**
     * 仅更新任务结果，不更新完成时间（用于流式任务的finalProcess）
     * 累积更新：先获取现有的任务信息列表，然后添加新的任务信息
     * @param taskId 任务ID
     * @param agentTaskInfo 新的任务信息
     */
    public synchronized void updateTaskResultOnly(Long taskId, AgentTaskInfo agentTaskInfo) {
        if (taskId == null || agentTaskInfo == null) {
            log.warn("任务结果更新失败，参数为空，taskId: {}, agentTaskInfo: {}", taskId, agentTaskInfo);
            return;
        }

        try {
            // 1. 获取现有的任务记录
            AnalysisTaskRecord existingTask = analysisTaskRecordService.getById(taskId);
            if (existingTask == null) {
                log.error("任务记录不存在，taskId: {}", taskId);
                return;
            }

            // 2. 获取现有的任务信息列表，如果为空则创建新列表
            List<AgentTaskInfo> existingTaskInfoList = existingTask.getAgentTaskInfo();
            List<AgentTaskInfo> updatedTaskInfoList = new ArrayList<>();
            if (existingTaskInfoList != null) {
                updatedTaskInfoList.addAll(existingTaskInfoList);
            }

            // 3. 添加新的任务信息
            updatedTaskInfoList.add(agentTaskInfo);

            // 4. 仅更新任务信息，不更新完成时间
            analysisTaskRecordService.updateTaskResultOnly(taskId, updatedTaskInfoList);

            log.info("任务结果累积更新成功（不更新完成时间），taskId: {}, 新增任务信息: {}, 总任务信息数量: {}",
                    taskId, agentTaskInfo.name(), updatedTaskInfoList.size());

        } catch (Exception e) {
            log.error("任务结果更新失败，taskId: {}, error: {}", taskId, e.getMessage(), e);
            throw new RuntimeException("任务结果更新失败", e);
        }
    }

    /**
     * 获取指定案件的所有任务状态
     * @param caseId 案件ID
     * @return 任务状态列表
     */
    public List<TaskStatusDTO> getTaskStatuses(Long caseId) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseId)
                   .orderByDesc(AnalysisTaskRecord::getCreateTime);

        List<AnalysisTaskRecord> records = analysisTaskRecordService.list(queryWrapper);

        return records.stream()
                .collect(Collectors.groupingBy(AnalysisTaskRecord::getTaskType))
                .entrySet()
                .stream()
                .map(entry -> {
                    String taskType = entry.getKey();
                    // 获取最新的任务记录
                    AnalysisTaskRecord latestRecord = entry.getValue().get(0);

                    TaskStatusDTO dto = new TaskStatusDTO();
                    dto.setTaskType(taskType);
                    dto.setStatus(latestRecord.getStatus());
                    dto.setStartTime(latestRecord.getStartTime());
                    dto.setEndTime(latestRecord.getEndTime());
                    dto.setErrorMessage(latestRecord.getErrorMessage());

                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取指定案件和任务类型的最新任务状态
     * @param caseId 案件ID
     * @param taskType 任务类型
     * @return 任务状态DTO，如果没有找到则返回null
     */
    public TaskStatusDTO getTaskStatus(Long caseId, TaskType taskType) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseId)
                   .eq(AnalysisTaskRecord::getTaskType, taskType.getCode())
                   .orderByDesc(AnalysisTaskRecord::getCreateTime)
                   .last("LIMIT 1");

        AnalysisTaskRecord record = analysisTaskRecordService.getOne(queryWrapper);
        if (record == null) {
            return null;
        }

        TaskStatusDTO dto = new TaskStatusDTO();
        dto.setTaskType(record.getTaskType());
        dto.setStatus(record.getStatus());
        dto.setStartTime(record.getStartTime());
        dto.setEndTime(record.getEndTime());
        dto.setErrorMessage(record.getErrorMessage());

        return dto;
    }

}
