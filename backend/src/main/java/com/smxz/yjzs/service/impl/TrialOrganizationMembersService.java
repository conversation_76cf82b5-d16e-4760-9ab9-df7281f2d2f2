package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.entity.TrialOrganizationMembers;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.mapper.TrialOrganizationMembersMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 审判组织成员服务实现类
 * 1对多关系：一个案件可以对应多个审判组织成员记录
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class TrialOrganizationMembersService extends ServiceImpl<TrialOrganizationMembersMapper, TrialOrganizationMembers> {

    @Autowired
    private DocumentExtractorService documentExtractorService;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    /**
     * 根据案件ID查询审判组织成员列表（1对多关系）
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 审判组织成员列表
     */
    public List<TrialOrganizationMembers> listByCaseImportRecordId(Long caseImportRecordId) {
        log.info("查询案件审判组织成员信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 根据案件ID删除审判组织成员记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportRecordId(Long caseImportRecordId) {
        log.info("删除案件审判组织成员信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 根据案件ID删除审判组织成员记录（别名方法）
     *
     * @param caseImportId 案件导入记录ID
     */
    public void deleteByCaseImportId(Long caseImportId) {
        deleteByCaseImportRecordId(caseImportId);
    }

    /**
     * 批量保存审判组织成员记录
     *
     * @param trialOrganizationMembersList 审判组织成员列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<TrialOrganizationMembers> trialOrganizationMembersList) {
        log.info("批量保存审判组织成员信息，数量: {}", trialOrganizationMembersList.size());
        return super.saveBatch(trialOrganizationMembersList);
    }

    /**
     * 先删除后批量保存审判组织成员记录
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param trialOrganizationMembersList 审判组织成员列表
     * @return 是否保存成功
     */
    public boolean replaceAllByCaseImportRecordId(Long caseImportRecordId, 
                                                  List<TrialOrganizationMembers> trialOrganizationMembersList) {
        log.info("替换案件审判组织成员信息，caseImportRecordId: {}, 数量: {}", 
                caseImportRecordId, trialOrganizationMembersList.size());
        
        // 先删除现有记录
        deleteByCaseImportRecordId(caseImportRecordId);
        
        // 批量保存新记录
        if (!trialOrganizationMembersList.isEmpty()) {
            return saveBatch(trialOrganizationMembersList);
        }
        
        return true;
    }

    /**
     * 智能获取审判组织成员信息
     * 如果数据库中已有数据则直接返回，如果没有则从庭审笔录提取并保存
     *
     * @param caseImportId 案件导入记录ID
     * @return 审判组织成员列表
     */
    public List<TrialOrganizationMembers> getOrExtractTrialOrganizationMembers(Long caseImportId) {
        log.info("智能获取审判组织成员信息，caseImportId: {}", caseImportId);

        // 先查询数据库中是否已有数据
        List<TrialOrganizationMembers> existingMembers = listByCaseImportRecordId(caseImportId);
        if (!existingMembers.isEmpty()) {
            log.info("数据库中已有审判组织成员信息，直接返回，数量: {}", existingMembers.size());
            return existingMembers;
        }

        // 数据库中没有数据，从庭审笔录提取
        log.info("数据库中无审判组织成员信息，开始从庭审笔录提取");
        List<TrialOrganizationMembers> extractedMembers = extractTrialOrganizationMembersFromCourtRecord(caseImportId);

        if (!extractedMembers.isEmpty()) {
            // 保存到数据库
            saveBatch(extractedMembers);
            log.info("从庭审笔录提取并保存审判组织成员信息成功，数量: {}", extractedMembers.size());
        } else {
            log.warn("从庭审笔录提取审判组织成员信息失败或无数据，caseImportId: {}", caseImportId);
        }

        return extractedMembers;
    }

    /**
     * 从《案件排定通知书》和《合议庭组成人员通知书》提取审判组织成员信息
     *
     * @param caseImportId 案件导入记录ID
     * @return 审判组织成员列表
     */
    private List<TrialOrganizationMembers> extractTrialOrganizationMembersFromCourtRecord(Long caseImportId) {
        log.info("开始提取审判组织成员信息，案件ID: {}", caseImportId);
        
        // 获取《案件排定通知书》和《合议庭组成人员通知书》文件
        List<FileUploadRecord> fileUploadRecordList = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                caseImportId, List.of(DocumentType.HYTZCRYTZS, DocumentType.AJPDTZS));

        if (CollectionUtils.isEmpty(fileUploadRecordList)) {
            log.warn("未找到《案件排定通知书》或《合议庭组成人员通知书》文件，案件ID: {}", caseImportId);
            return extractTrialOrganizationMembersFromTsbl(caseImportId);
        }
        
        // 存储所有提取的成员信息，用于去重
        Map<String, Set<String>> allMembersMap = new HashMap<>();
        
        // 遍历每个文件，根据文件类型提取对应要素
        for (FileUploadRecord file : fileUploadRecordList) {
            try {
                if (file.getExtract_element_result() == null || 
                    file.getExtract_element_result().getElements()== null) {
                    log.warn("文件要素提取结果为空，跳过处理，文件: {}", file.getFileName());
                    continue;
                }
                
                Map<String, Object> elements = file.getExtract_element_result().getElements();
                DocumentType docType = file.getDocumentType();
                
                if (docType == DocumentType.AJPDTZS) {
                    // 《案件排定通知书》：提取审判长、合议庭成员（审判员）、书记员、法官助理
                    extractMembersFromAjpdtzs(elements, allMembersMap);
                    log.debug("从《案件排定通知书》提取成员信息，文件: {}", file.getFileName());
                } else if (docType == DocumentType.HYTZCRYTZS) {
                    // 《合议庭组成人员通知书》：提取审判长、审判员
                    extractMembersFromHytzcrytzs(elements, allMembersMap);
                    log.debug("从《合议庭组成人员通知书》提取成员信息，文件: {}", file.getFileName());
                }
                
            } catch (Exception e) {
                log.error("处理文件时发生异常，文件: {}", file.getFileName(), e);
            }
        }
        
        // 去重处理：确保审判长的名字不出现在其他角色中
        removeDuplicatesAndCleanup(allMembersMap);
        
        // 转换为TrialOrganizationMembers列表
        return convertToTrialOrganizationMembers(caseImportId, allMembersMap);
    }

    /**
     * 从庭审笔录提取审判组织成员信息
     *
     * @param caseImportId 案件导入记录ID
     * @return 审判组织成员列表
     */
    private List<TrialOrganizationMembers> extractTrialOrganizationMembersFromTsbl(Long caseImportId) {
        // 获取庭审笔录文件
        Object trialOrgInfo = fileUploadRecordMapper.getFileRecordElementByDocumentTypes(
                caseImportId, List.of(DocumentType.TSBL),"审判组信息");

        if (trialOrgInfo == null) {
            log.warn("未提取到审判组信息，案件ID: {}", caseImportId);
            return new ArrayList<>();
        }

        // 解析审判组信息
        return parseTrialOrganizationInfo(caseImportId, trialOrgInfo);
    }

    /**
     * 解析审判组信息
     * 输入格式：["审判员:张三","书记员:李四"] 或 "审判员:张三,书记员:李四"
     */
    private List<TrialOrganizationMembers> parseTrialOrganizationInfo(Long caseImportId, Object trialOrgInfo) {
        List<TrialOrganizationMembers> members = new ArrayList<>();

        try {
            List<String> infoList = new ArrayList<>();

            // 处理不同的输入格式
            if (trialOrgInfo instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) trialOrgInfo;
                infoList = list.stream().map(Object::toString).collect(Collectors.toList());
            } else if (trialOrgInfo instanceof String) {
                String infoStr = (String) trialOrgInfo;
                // 按逗号分割
                infoList = Arrays.asList(infoStr.split(","));
            }
            int xh = 1;
            // 解析每个成员信息
            for (String info : infoList) {
                if (info == null || info.trim().isEmpty()) {
                    continue;
                }

                String cleanInfo = info.trim();
                if (cleanInfo.contains(":")) {
                    String[] parts = cleanInfo.split(":", 2);
                    if (parts.length == 2) {
                        String role = parts[0].trim();
                        String name = parts[1].trim();

                        if (!role.isEmpty() && !name.isEmpty()) {
                            TrialOrganizationMembers member = new TrialOrganizationMembers();
                            member.setCaseImportRecordId(caseImportId);
                            member.setRole(role);
                            member.setName(name);
                            member.setXh(xh++);
                            members.add(member);

                            log.debug("解析审判组成员：{} - {}", role, name);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("解析审判组信息失败，caseImportId: {}", caseImportId, e);
        }

        return members;
    }

    /**
     * 提取文件内容
     */
    private String extractFileContent(List<FileUploadRecord> files) {
        if (files.isEmpty()) {
            return "";
        }

        return files.stream()
                .filter(file -> file.getExtractedText() != null && !file.getExtractedText().trim().isEmpty())
                .map(FileUploadRecord::getExtractedText)
                .collect(Collectors.joining("\n"));
    }

    /**
     * 从《案件排定通知书》中提取审判组织成员信息
     * 提取要素：审判长、合议庭成员（审判员）、书记员、法官助理
     */
    private void extractMembersFromAjpdtzs(Map<String, Object> elements, Map<String, Set<String>> allMembersMap) {
        // 提取审判长
        extractMembersByRole(elements, "审判长", "审判长", allMembersMap);
        
        // 提取合议庭成员（审判员）
        extractMembersByRole(elements, "合议庭成员", "审判员", allMembersMap);
        extractMembersByRole(elements, "审判员", "审判员", allMembersMap);
        
        // 提取书记员
        extractMembersByRole(elements, "书记员", "书记员", allMembersMap);
        
        // 提取法官助理
        extractMembersByRole(elements, "法官助理", "法官助理", allMembersMap);
    }
    
    /**
     * 从《合议庭组成人员通知书》中提取审判组织成员信息
     * 提取要素：审判长、审判员
     */
    private void extractMembersFromHytzcrytzs(Map<String, Object> elements, Map<String, Set<String>> allMembersMap) {
        // 提取审判长
        extractMembersByRole(elements, "审判长", "审判长", allMembersMap);
        
        // 提取审判员
        extractMembersByRole(elements, "审判员", "审判员", allMembersMap);
    }
    
    /**
     * 根据角色从要素中提取成员信息
     */
    private void extractMembersByRole(Map<String, Object> elements, String elementKey, String targetRole, Map<String, Set<String>> allMembersMap) {
        Object roleData = elements.get(elementKey);
        if (roleData == null) {
            return;
        }
        
        Set<String> names = parseNamesFromElement(roleData);
        if (!names.isEmpty()) {
            allMembersMap.computeIfAbsent(targetRole, k -> new HashSet<>()).addAll(names);
            log.debug("提取{}：{}", targetRole, names);
        }
    }
    
    /**
     * 从要素数据中解析姓名列表
     */
    private Set<String> parseNamesFromElement(Object elementData) {
        Set<String> names = new HashSet<>();
        
        try {
            if (elementData instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) elementData;
                for (Object item : list) {
                    String name = item.toString();
                    if (name != null && !name.isEmpty()) {
                        names.add(name);
                    }
                }
            } else if (elementData instanceof String) {
                String dataStr = (String) elementData;
                // 按常见分隔符分割：逗号、分号、顿号等
                String[] parts = dataStr.split("[,，;；、\\s]+");
                for (String part : parts) {
                    String name = part;
                    if (name != null && !name.isEmpty()) {
                        names.add(name);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析姓名时发生异常：{}", elementData, e);
        }
        
        return names;
    }
    

    
    /**
     * 去重处理：确保审判长的名字不出现在其他角色中，各角色内部去重
     */
    private void removeDuplicatesAndCleanup(Map<String, Set<String>> allMembersMap) {
        Set<String> presidentNames = allMembersMap.get("审判长");
        
        if (presidentNames != null && !presidentNames.isEmpty()) {
            // 从其他角色中移除审判长的姓名
            for (Map.Entry<String, Set<String>> entry : allMembersMap.entrySet()) {
                if (!"审判长".equals(entry.getKey())) {
                    entry.getValue().removeAll(presidentNames);
                }
            }
            log.debug("已从其他角色中移除审判长姓名：{}", presidentNames);
        }
        
        // 移除空的角色集合
        allMembersMap.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }
    
    /**
     * 将Map格式的成员信息转换为TrialOrganizationMembers列表
     */
    private List<TrialOrganizationMembers> convertToTrialOrganizationMembers(Long caseImportId, Map<String, Set<String>> allMembersMap) {
        List<TrialOrganizationMembers> members = new ArrayList<>();
        int xh = 1;
        
        // 按角色优先级排序：审判长 > 审判员 > 书记员 > 法官助理
        String[] roleOrder = {"审判长", "审判员", "人民陪审员","书记员", "法官助理"};
        
        for (String role : roleOrder) {
            Set<String> names = allMembersMap.get(role);
            if (names != null && !names.isEmpty()) {
                for (String name : names) {
                    TrialOrganizationMembers member = new TrialOrganizationMembers();
                    member.setCaseImportRecordId(caseImportId);
                    member.setRole(role);
                    member.setName(name);
                    member.setXh(xh++);
                    members.add(member);
                    
                    log.debug("添加审判组织成员：{} - {}", role, name);
                }
            }
        }
        
        log.info("审判组织成员信息提取完成，案件ID: {}, 成员数量: {}", caseImportId, members.size());
        return members;
    }
}
