package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.dto.VerificationEvidenceDTO;
import com.smxz.yjzs.entity.CaseParty;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.entity.VerificationEvidenceInfo;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.CasePartyMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.mapper.VerificationEvidenceInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.smxz.yjzs.common.utils.AiOutputUtils.removeCodeBlock;

/**
 * 质证情况信息表Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
public class VerificationEvidenceInfoService extends ServiceImpl<VerificationEvidenceInfoMapper, VerificationEvidenceInfo> {

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    @Lazy
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private CasePartyMapper casePartyMapper;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    /**
     * 匹配“证据+数字”，用于提取排序数字
     */
    private static final Pattern EVIDENCE_NUM_PATTERN = Pattern.compile("证据\\s*(\\d+)");

    /**
     * 根据案件ID查询质证情况信息列表
     *
     * @param caseImportId 案件导入ID
     * @return 质证情况信息列表
     */
    public List<VerificationEvidenceInfo> listByCaseImportId(Long caseImportId) {
        log.info("查询案件质证情况信息，caseImportId: {}", caseImportId);
        LambdaQueryWrapper<VerificationEvidenceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId)
                   .orderByDesc(VerificationEvidenceInfo::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据当事人类型和案件ID查询质证情况信息列表
     *
     * @param caseImportId 案件导入ID
     * @param partyType 当事人类型
     * @return 质证情况信息列表
     */
    public List<VerificationEvidenceInfo> listByPartyType(Long caseImportId, String partyType) {
        log.info("查询指定当事人类型的质证情况信息，caseImportId: {}, partyType: {}", caseImportId, partyType);
        LambdaQueryWrapper<VerificationEvidenceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId)
                   .eq(VerificationEvidenceInfo::getPartyType, partyType)
                   .orderByDesc(VerificationEvidenceInfo::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据案件ID和当事人姓名列表查询质证情况信息列表
     * 当VerificationEvidenceInfo中的partyName包含入参partyNames中的任何一个名字时，返回该数据
     *
     * @param caseImportId 案件导入ID
     * @param partyNames 当事人姓名列表
     * @return 质证情况信息列表
     */
    public List<VerificationEvidenceInfo> listByCaseImportIdAndPartyNames(Long caseImportId, List<String> partyNames) {

        if (caseImportId == null || partyNames == null || CollectionUtils.isEmpty(partyNames)) {
            return new ArrayList<>();
        }

        // 先查询该案件的所有质证情况信息
        LambdaQueryWrapper<VerificationEvidenceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId)
                   .orderByDesc(VerificationEvidenceInfo::getCreateTime);

        List<VerificationEvidenceInfo> allVerificationInfos = this.list(queryWrapper);

        // 过滤出partyName包含入参partyNames中任何一个名字的数据，并按“证据+数字”升序排序（无数字的排在后面，保持原相对顺序）
        return allVerificationInfos.stream()
                .filter(info -> {
                    if (StringUtils.isBlank(info.getPartyName())) {
                        return false;
                    }
                    // 检查VerificationEvidenceInfo对象中的partyName 是否 包含partyNames中的任何一个名字
                    return partyNames.stream().anyMatch(partyName -> info.getPartyName().contains(partyName));
                })
                .sorted(Comparator.comparing(
                        (VerificationEvidenceInfo info) -> extractEvidenceNumber(info.getVerificationContent()),
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
    }

    /**
     * 根据是否采纳和案件ID查询质证情况信息列表
     *
     * @param caseImportId 案件导入ID
     * @param isAdopt 是否采纳
     * @return 质证情况信息列表
     */
    public List<VerificationEvidenceInfo> listByAdoptStatus(Long caseImportId, Integer isAdopt) {
        log.info("查询指定采纳状态的质证情况信息，caseImportId: {}, isAdopt: {}", caseImportId, isAdopt);
        LambdaQueryWrapper<VerificationEvidenceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId)
                   .eq(VerificationEvidenceInfo::getIsAdopt, isAdopt)
                   .orderByDesc(VerificationEvidenceInfo::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 创建质证情况信息
     *
     * @param verificationInfo 质证情况信息
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createVerificationInfo(VerificationEvidenceInfo verificationInfo) {
        log.info("创建质证情况信息，案件ID: {}, 当事人: {}", 
                verificationInfo.getCaseImportId(), verificationInfo.getPartyName());
        
        // 设置创建时间
        verificationInfo.setCreateTime(LocalDateTime.now());
        verificationInfo.setUpdateTime(LocalDateTime.now());
        
        boolean result = this.save(verificationInfo);
        
        if (result) {
            log.info("质证情况信息创建成功，ID: {}", verificationInfo.getId());
        } else {
            log.error("质证情况信息创建失败");
        }
        
        return result;
    }

    /**
     * 更新质证情况信息/质证情况的采纳状态
     * @param verificationInfo 质证情况信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVerificationInfo(VerificationEvidenceInfo verificationInfo) {
        log.info("更新质证情况信息，ID: {}", verificationInfo.getId());
        
        // 设置更新时间
        verificationInfo.setUpdateTime(LocalDateTime.now());
        
        boolean result = this.updateById(verificationInfo);
        
        if (result) {
            log.info("质证情况信息更新成功，ID: {}", verificationInfo.getId());
        } else {
            log.error("质证情况信息更新失败，ID: {}", verificationInfo.getId());
        }
        
        return result;
    }

    /**
     * 根据ID删除质证情况信息（逻辑删除）
     *
     * @param id 质证情况信息ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVerificationInfo(Long id) {
        log.info("删除质证情况信息，ID: {}", id);
        
        boolean result = this.removeById(id);
        
        if (result) {
            log.info("质证情况信息删除成功，ID: {}", id);
        } else {
            log.error("质证情况信息删除失败，ID: {}", id);
        }
        
        return result;
    }

    /**
     * 批量删除案件相关的质证情况信息
     *
     * @param caseImportId 案件导入ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCaseImportId(Long caseImportId) {
        log.info("批量删除案件相关的质证情况信息，caseImportId: {}", caseImportId);
        
        LambdaQueryWrapper<VerificationEvidenceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId);
        
        boolean result = this.remove(queryWrapper);
        
        if (result) {
            log.info("案件相关质证情况信息批量删除成功，caseImportId: {}", caseImportId);
        } else {
            log.error("案件相关质证情况信息批量删除失败，caseImportId: {}", caseImportId);
        }
        
        return result;
    }

    /**
     * 更新质证情况的采纳状态
     *
     * @param id 质证情况信息ID
     * @param isAdopt 是否采纳
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdoptStatus(Long id, Integer isAdopt) {
        log.info("更新质证情况采纳状态，ID: {}, isAdopt: {}", id, isAdopt);

        VerificationEvidenceInfo verificationInfo = new VerificationEvidenceInfo();
        verificationInfo.setId(id);
        verificationInfo.setIsAdopt(isAdopt);
        verificationInfo.setUpdateTime(LocalDateTime.now());

        boolean result = this.updateById(verificationInfo);

        if (result) {
            log.info("质证情况采纳状态更新成功，ID: {}, isAdopt: {}", id, isAdopt);
        } else {
            log.error("质证情况采纳状态更新失败，ID: {}, isAdopt: {}", id, isAdopt);
        }

        return result;
    }

    /**
     * 生成质证情况信息（异步方法，使用@Async）
     * 该任务不依赖其他模型返回的结果
     * @param caseImportId 案件导入ID
     */
    @Async
    @AnalysisTask(taskType = TaskType.VERIFICATION_EVIDENCE, description = "质证情况分析")
    public void generate(Long caseImportId) {
        boolean isSecondInstance = caseImportRecordService.isSecondInstanceCase(caseImportId);
        generateVerificationEvidencesByAgent(caseImportId,isSecondInstance);
    }


    /**
     * 生成质证情况信息
     */
    private void generateVerificationEvidencesByAgent(Long caseImportId, boolean isSecondInstance) {
        log.info("开始执行质证情况分析任务，caseImportId: {}", caseImportId);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 获取并验证文件记录{庭审笔录/询问笔录 等}
            List<FileUploadRecord> fileRecords = getAndValidateFileRecords(caseImportId);

            // 2. 获取当事人信息
            List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);

            List<String> firstStepResListStr = new ArrayList<>();
            // 收集第一步Agent的结果Map，key为质证意见1、质证意见2等
            Map<String, List<VerificationEvidenceDTO>> firstStepResultMap = new HashMap<>();

            // 每份庭审笔录都需要进行跑 第一步的agent，然后根据第一步的结果跑第二步的agent
            for (FileUploadRecord fileRecord : fileRecords) {
                if (fileRecord != null && StringUtils.isNotBlank(fileRecord.getExtractedText())) {

                    String oneStepInputTSBL = fileRecord.getExtractedText();
                    String fileName = fileRecord.getFileName();
                    Long fileId = fileRecord.getId();

                    // 3.构建第一步agent的参数
                    Map<String, String> propertyMapFirst = buildPropertyMapFirst(oneStepInputTSBL, caseParties);

                    // 4.执行第一步Agent处理
                    String resOneStr = executeFirstStepAgent(propertyMapFirst, isSecondInstance);
                    List<VerificationEvidenceDTO> verificationInfo = JSON.parseArray(resOneStr, VerificationEvidenceDTO.class);

                    // 5.收集第一步agent返回数据
                    if (null != verificationInfo && !verificationInfo.isEmpty()) {

                        // 空的，不作为第二步的参数
                        firstStepResListStr.add(resOneStr);

                        // 设置fileName到每个DTO对象
                        verificationInfo.stream()
                                .filter(Objects::nonNull)
                                .forEach(dto -> {
                                    dto.setFileName(fileName);
                                    dto.setFileId(fileId);
                                });

                        // 将不为空的数据存到map中，key为质证意见1、质证意见2等
                        String opinionKey = "质证意见" + (firstStepResultMap.size() + 1);
                        firstStepResultMap.put(opinionKey, verificationInfo);
                    }
                }
            }
//            log.info("firstStepResListStr:{}", firstStepResListStr);
//            log.info("firstStepResultMap:{}", JSON.toJSONString(firstStepResultMap));

            // 注意：如果task1只有一份质证意见的输出，则不需要跑task2.
            if (firstStepResultMap.size() == 1) {
                log.info("只有一个质证意见，直接保存第一步Agent结果");
                // 直接保存第一步Agent结果
                for (Map.Entry<String, List<VerificationEvidenceDTO>> entry : firstStepResultMap.entrySet()) {
                    clearVerificationEvidenceData(caseImportId);
                    processAndSaveAgentResult(entry.getValue(), caseImportId, caseParties, startTime, "第一步Agent分析");
                }

            } else if (firstStepResultMap.size() > 1) {
                // 构建质证意见map
                log.info("收集到{}个,第一步的结果。", firstStepResultMap.size());

                // 原始输出构建第二步agent参数
                Map<String, String> secondStepPropertyMap = buildSecondStepPropertyMap(firstStepResListStr);
                // 执行第二步Agent处理
                String resTwoStep = executeSecondStepAgent(secondStepPropertyMap, isSecondInstance);

                // 处理第二步Agent返回结果并保存到数据库
                if (StringUtils.isNotBlank(resTwoStep)) {

                    // 如果resTwoStep不为空，需要增加去重逻辑，如果第二步返回的结果(resTwoStep) 包含或者等于 firstStepResultMap中的某个key,需要去掉firstStepResultMap中那对key-value
                    removeProcessedOpinionsFromMap(firstStepResultMap, resTwoStep);

                    // 保存剩余的第一步Agent结果（未被第二步处理的）
                    if (!firstStepResultMap.isEmpty()) {
                        log.info("保存剩余的第一步Agent结果完成，剩余{}个质证意见", firstStepResultMap.size());
                        clearVerificationEvidenceData(caseImportId);
                        for (Map.Entry<String, List<VerificationEvidenceDTO>> entry : firstStepResultMap.entrySet()) {
                            processAndSaveAgentResult(entry.getValue(), caseImportId, caseParties, startTime, "第二步Agent分析");
                        }
                    }
                }
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("质证情况分析任务执行失败，caseImportId: {}, 耗时: {}ms, 错误: {}",
                    caseImportId, duration, e.getMessage(), e);
            throw new RuntimeException("质证情况分析任务执行失败: " + e.getMessage(), e);
        }
    }


    /**
     * 获取并验证文件记录
     */
    private List<FileUploadRecord> getAndValidateFileRecords(Long caseImportId) {
        List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, List.of(DocumentType.TSBL));
        log.info("获取文件记录数量: {}", fileRecords.size());
        if (fileRecords.isEmpty()) {
            throw new IllegalStateException("未找到庭审笔录文件记录");
        }
        return fileRecords;
    }



    /**
     * 执行第一步Agent处理
     * 期望agent结果：这段话 “针对本案的有关事实询问各方当事人"
     */
    private String executeFirstStepAgent(Map<String, String> propertyMapFirst, boolean isSecondInstance) {
        String agentNameOne = isSecondInstance ? "星洲-二审-生成质证情况第一步Agent" : "星洲-一审-生成质证情况第一步Agent";
        String bootYmlNameOne = isSecondInstance ? "second_verification_evidence_task_0.yml" : "verification_evidence_task_0.yml";
        String resOne = agentTaskUtils.executeTask(agentNameOne, "main", bootYmlNameOne, propertyMapFirst);
        resOne = removeCodeBlock(resOne);
        log.info("打印第一步Agent返回结果: {}", resOne);
        return resOne;
    }

    /**
     * 执行第二步Agent处理
     */
    private String executeSecondStepAgent(Map<String, String> propertyMap, boolean isSecondInstance) {
        String agentNameTwo = isSecondInstance ? "星洲-二审-生成质证情况第二步Agent" : "星洲-一审-生成质证情况第二步Agent";
        String bootYmlNameTwo = isSecondInstance ? "second_verification_evidence_task.yml" : "verification_evidence_task.yml";

        String resTwo = agentTaskUtils.executeTask(agentNameTwo, "main", bootYmlNameTwo, propertyMap);
        log.info("第二步Agent返回结果: {}", resTwo);
        return resTwo;
    }

    /**
     * 质证情况第一步---构建Agent属性映射
     */
    private Map<String, String> buildPropertyMapFirst(String oneStepInputTSBL, List<CaseParty> caseParties) {
        Map<String, String> propertyMap = new HashMap<>();

        propertyMap.put("材料:庭审笔录", oneStepInputTSBL);

        String oneStepInputPartyName = caseParties.stream()
                .map(CaseParty::getPartyName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .distinct()
                .collect(Collectors.joining(","));
        propertyMap.put("材料:当事人姓名", oneStepInputPartyName);

        log.info("构建第一步属性映射完成，属性映射: {}", propertyMap);
        return propertyMap;
    }


    /**
     * 清除质证情况旧数据（逻辑删除）
     */
    private void clearVerificationEvidenceData(Long caseImportId) {
        try {
            log.info("开始清除质证情况旧数据，caseImportId: {}", caseImportId);
            
            // 先查询现有数据数量（只查询未删除的记录）
            LambdaQueryWrapper<VerificationEvidenceInfo> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId);
            long oldCount = this.count(countWrapper);
            log.info("案件现有质证情况记录数量: {}", oldCount);
            
            if (oldCount > 0) {
                // 使用批量更新方式执行逻辑删除
                LambdaUpdateWrapper<VerificationEvidenceInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(VerificationEvidenceInfo::getCaseImportId, caseImportId)
                           .set(VerificationEvidenceInfo::getDeleted, 1)
                           .set(VerificationEvidenceInfo::getUpdateTime, LocalDateTime.now());
                
                boolean updateResult = this.update(updateWrapper);
                
                if (updateResult) {
                    log.info("逻辑删除质证情况旧数据完成，caseImportId: {}, 影响记录数: {}", caseImportId, oldCount);
                } else {
                    log.warn("逻辑删除质证情况旧数据可能失败，caseImportId: {}", caseImportId);
                }
            } else {
                log.info("案件没有旧的质证情况数据，无需清除，caseImportId: {}", caseImportId);
            }
            
        } catch (Exception e) {
            log.error("清除质证情况旧数据失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("清除质证情况旧数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存质证情况数据
     */
    @Transactional(rollbackFor = Exception.class)
    protected void saveVerificationEvidenceData(List<VerificationEvidenceInfo> verificationInfos, Long caseImportId) {
        try {
            log.info("开始保存质证情况数据，caseImportId: {}", caseImportId);

            // 保存新数据
            if (!verificationInfos.isEmpty()) {
                this.saveBatch(verificationInfos);
            }

            log.info("质证情况数据保存完成，质证情况数: {}", verificationInfos.size());
        } catch (Exception e) {
            log.error("质证情况数据保存失败", e);
            throw new RuntimeException("质证情况数据保存失败: " + e.getMessage(), e);
        }
    }


    /**
     * 构建第二步Agent属性映射
     * 将第一步的质证结果格式化为第二步Agent需要的参数
     */
    private Map<String, String> buildSecondStepPropertyMap(List<String> firstStepResults) {
        Map<String, String> propertyMap = new HashMap<>();

        if (firstStepResults.isEmpty()) {
            throw new IllegalStateException("第一步Agent结果为空，无法构建第二步参数!");
        }

        // 构建格式化的质证意见字符串：质证意见1 : resOne,质证意见2 : resOne
        StringBuilder formattedResults = new StringBuilder();
        for (int i = 0; i < firstStepResults.size(); i++) {
            String resOne = firstStepResults.get(i);
            if (StringUtils.isNotBlank(resOne)) {
                if (formattedResults.length() > 0) {
                    formattedResults.append("\n");
                }
                formattedResults.append("质证意见").append(i + 1).append(" : ").append(resOne).append("\n");
            }
        }

        // 将格式化的结果作为第二步Agent的输入参数
        propertyMap.put("材料:第一步的质证结果", formattedResults.toString());

        log.info("构建第二步Agent参数完成，格式化结果长度: {}", formattedResults.length());
        return propertyMap;
    }

    /**
     * 根据第二步Agent输出从firstStepResultMap中剔除已处理的质证意见
     *
     * @param firstStepResultMap 第一步Agent结果Map
     * @param resTwoStep 第二步Agent输出结果
     */
    private void removeProcessedOpinionsFromMap(Map<String, List<VerificationEvidenceDTO>> firstStepResultMap, String resTwoStep) {
        if (StringUtils.isBlank(resTwoStep) || firstStepResultMap.isEmpty()) {
            return;
        }

        // 记录原始大小
        int originalSize = firstStepResultMap.size();

        // 检查第二步输出是否包含或等于firstStepResultMap中的某个key，如果包含则剔除
        Iterator<Map.Entry<String, List<VerificationEvidenceDTO>>> iterator = firstStepResultMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<VerificationEvidenceDTO>> entry = iterator.next();
            String opinionKey = entry.getKey(); // 如"质证意见1"、"质证意见2"

            // 检查第二步输出是否 等于或包含 某个质证意见的key
            if ( resTwoStep.equals(opinionKey) || resTwoStep.contains(opinionKey)) {
                iterator.remove();
                log.info("从firstStepResultMap中剔除已处理的质证意见: {}", opinionKey);
            }
        }

        int removedCount = originalSize - firstStepResultMap.size();
        if (removedCount > 0) {
            log.info("第二步Agent处理了{}个质证意见，剩余{}个未处理", removedCount, firstStepResultMap.size());
        } else {
            log.info("第二步Agent未处理任何质证意见，所有{}个质证意见保持不变", originalSize);
        }
    }


    /**
     * 通用的Agent结果处理和保存方法
     *
     * @param dtoList Agent返回的DTO列表
     * @param caseImportId 案件导入ID
     * @param caseParties 当事人列表
     * @param startTime 开始时间
     * @param createBy 创建人标识
     */
    private void processAndSaveAgentResult(List<VerificationEvidenceDTO>  dtoList,
                                           Long caseImportId,
                                           List<CaseParty> caseParties,
                                           long startTime,
                                           String createBy) {
        try {
            if (dtoList == null || dtoList.isEmpty()) {
                log.warn("Agent返回结果为空，caseImportId: {}, createBy: {}", caseImportId, createBy);
                return;
            }

            // 转换为实体对象
            List<VerificationEvidenceInfo> verificationInfos = convertDtoToEntity(dtoList, caseImportId, caseParties, startTime, createBy);

            // 批量保存到数据库
            if (!verificationInfos.isEmpty()) {
                this.saveBatch(verificationInfos);
                log.info("{}结果保存成功，caseImportId: {}, 保存{}条记录", createBy, caseImportId, verificationInfos.size());
            }

        } catch (Exception e) {
            log.error("处理{}结果失败，caseImportId: {}, 错误: {}", createBy, caseImportId, e.getMessage(), e);
            throw new RuntimeException("处理" + createBy + "结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将DTO转换为VerificationEvidenceInfo实体对象（通用方法）
     */
    private List<VerificationEvidenceInfo> convertDtoToEntity(List<VerificationEvidenceDTO> dtoList,
                                                              Long caseImportId,
                                                              List<CaseParty> caseParties,
                                                              long startTime,
                                                              String createBy) {
        List<VerificationEvidenceInfo> result = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        long duration = System.currentTimeMillis() - startTime;

        // 构建当事人姓名到类型的映射
        Map<String, String> partyNameToTypeMap = caseParties.stream()
                .collect(Collectors.toMap(
                    CaseParty::getPartyName,
                    CaseParty::getPartyType,
                    (existing, replacement) -> existing // 如果有重复姓名，保留第一个
                ));

        for (VerificationEvidenceDTO dto : dtoList) {
            // 直接使用partyName字段作为当事人姓名，不进行分割
            String partyNames = dto.getPartyName();
            if (StringUtils.isBlank(partyNames)) {
                log.warn("当事人姓名为空，跳过处理，caseImportId: {}", caseImportId);
                continue;
            }

            // 尝试从当事人映射中获取当事人类型（使用第一个匹配的姓名）
            String partyType = null;
            for (String singleName : partyNames.split(",")) {
                String trimmedName = singleName.trim();
                if (partyNameToTypeMap.containsKey(trimmedName)) {
                    partyType = partyNameToTypeMap.get(trimmedName);
                    break;
                }
            }

            // 如果没有找到匹配的当事人类型，使用默认值
            if (StringUtils.isBlank(partyType)) {
                log.warn("未找到当事人类型，姓名: {}, caseImportId: {}", partyNames, caseImportId);
                partyType = ""; // 使用默认值而不是跳过
            }

            VerificationEvidenceInfo info = new VerificationEvidenceInfo();
            info.setCaseImportId(caseImportId);
            info.setPartyType(partyType);
            info.setPartyName(partyNames); // 直接存储完整的姓名字符串
            info.setIsAdopt(1); // 使用传入的采纳状态
            info.setCreateTime(now);
            info.setUpdateTime(now);
            info.setCreateBy("系统" + createBy); // 使用传入的创建人标识
            info.setDurationMs(duration);
            info.setTrialInvestigationContent(JSON.toJSONString(dto));

            // 1、证据1.结婚证。证据2.出生医学证明（2份）。证据3.户口本。证据1-3共同质证：三性均认可。
            // 用正则匹配 如果 VerificationContent字段以数字开头， 需要去除前面的数字和符号(有可能是、.)，例如1、, 2、 或者是1.，2.
            String verificationContent = dto.getVerificationContent();
            if (StringUtils.isNotBlank(verificationContent)) {
                verificationContent = verificationContent.replaceAll("^\\d+、", "");
                verificationContent = verificationContent.replaceAll("^\\d+\\.", "");
            }
            info.setVerificationContent(verificationContent);
            // 构建evidence数组（基于highlight内容和当前文件信息）
            info.setEvidence(buildEvidenceFromHighlight(dto.getHighlight(), dto.getFileId(), dto.getFileName()));

            result.add(info);
        }

        return result;
    }

    /**
     * 根据highlight内容构建evidence数组
     *
     * @param highlight 高亮内容
     * @param fileId 文件ID
     * @param fileName 文件名称
     */
    private List<com.smxz.yjzs.entity.Evidence> buildEvidenceFromHighlight(String highlight, Long fileId, String fileName) {
        List<com.smxz.yjzs.entity.Evidence> evidenceList = new ArrayList<>();

        try {
            com.smxz.yjzs.entity.Evidence evidence = new com.smxz.yjzs.entity.Evidence();
            evidence.setFileId(fileId);
            evidence.setFileName(fileName);
            evidence.setHighlight(highlight);
            evidenceList.add(evidence);

        } catch (Exception e) {
            log.error("构建evidence数组失败，fileId: {}, fileName: {}, 错误: {}", fileId, fileName, e.getMessage());
        }

        return evidenceList;
    }

    /**
     * 从 verificationContent 中提取“证据+数字”的数字部分用于排序；未匹配返回 null
     */
    private Integer extractEvidenceNumber(String verificationContent) {
        if (StringUtils.isBlank(verificationContent)) {
            return null;
        }
        Matcher matcher = EVIDENCE_NUM_PATTERN.matcher(verificationContent);
        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }


}