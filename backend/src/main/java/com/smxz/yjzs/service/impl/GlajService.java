package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.constant.NormalCodeConsts;
import com.smxz.yjzs.entity.Glaj;
import com.smxz.yjzs.mapper.GlajMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 关联案件服务实现类
 * 1对多关系：一个案件可以对应多个关联案件记录
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class GlajService extends ServiceImpl<GlajMapper, Glaj> {

    /**
     * 根据案件ID查询关联案件列表（1对多关系）
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 关联案件列表
     */
    public List<Glaj> listByCaseImportRecordId(Long caseImportRecordId) {
        log.info("查询案件关联案件信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 根据案件ID和关联关系查询关联案件列表
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param glgx 关联关系（1-原审，2-后继）
     * @return 关联案件列表
     */
    public List<Glaj> listByCaseImportRecordIdAndGlgx(Long caseImportRecordId, Integer glgx) {
        log.info("查询案件关联案件信息，caseImportRecordId: {}, glgx: {}", caseImportRecordId, glgx);
        return baseMapper.listByCaseImportRecordIdAndGlgx(caseImportRecordId, glgx);
    }

    /**
     * 根据案件ID删除关联案件记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportRecordId(Long caseImportRecordId) {
        log.info("删除案件关联案件信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 批量保存关联案件记录
     *
     * @param glajList 关联案件列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<Glaj> glajList) {
        log.info("批量保存关联案件信息，数量: {}", glajList.size());
        return super.saveBatch(glajList);
    }

    /**
     * 先删除后批量保存关联案件记录
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param glajList 关联案件列表
     * @return 是否保存成功
     */
    public boolean replaceAllByCaseImportRecordId(Long caseImportRecordId, List<Glaj> glajList) {
        log.info("替换案件关联案件信息，caseImportRecordId: {}, 数量: {}", 
                caseImportRecordId, glajList.size());
        
        // 先删除现有记录
        deleteByCaseImportRecordId(caseImportRecordId);
        
        // 批量保存新记录
        if (!glajList.isEmpty()) {
            return saveBatch(glajList);
        }
        
        return true;
    }

    /**
     * 根据关联关系获取原审案件列表
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 原审案件列表
     */
    public List<Glaj> getOriginalTrialCases(Long caseImportRecordId) {
        log.info("查询原审案件信息，caseImportRecordId: {}", caseImportRecordId);
        return listByCaseImportRecordIdAndGlgx(caseImportRecordId, NormalCodeConsts.GLAJ_YS);
    }

    /**
     * 根据关联关系获取后继案件列表
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 后继案件列表
     */
    public List<Glaj> getSubsequentCases(Long caseImportRecordId) {
        log.info("查询后继案件信息，caseImportRecordId: {}", caseImportRecordId);
        return listByCaseImportRecordIdAndGlgx(caseImportRecordId, NormalCodeConsts.GLAJ_HJ);
    }
}
