package com.smxz.yjzs.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.document.convert.DocumentConvertService;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.ocr.model.OcrPreview;
import com.smxz.ocr.model.OcrResponseData;
import com.smxz.ocr.model.enums.FileType;
import com.smxz.ocr.service.OcrService;
import com.smxz.smxzconsole.entity.User;
import com.smxz.smxzconsole.service.CorpService;
import com.smxz.smxzconsole.service.UserService;
import com.smxz.yjzs.common.utils.FileExtractUtil;
import com.smxz.yjzs.common.utils.ThreadPoolUtils;
import com.smxz.yjzs.common.utils.UUIDHelper;
import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.RightConstants;
import com.smxz.yjzs.dto.CaseImportSearchDTO;
import com.smxz.yjzs.dto.PageResult;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.entity.PdfPageContent;
import com.smxz.yjzs.entity.TrialOrganizationMembers;
import com.smxz.yjzs.exception.FilePreprocessException;
import com.smxz.yjzs.mapper.CaseImportRecordMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.service.FilePreprocessService;
import com.smxz.yjzs.service.ContradictionRecognitionService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import org.apache.commons.io.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 诚
 * @since 2025-05-09 14:24
 **/
@Service
@Slf4j
public class CaseImportRecordService extends ServiceImpl<CaseImportRecordMapper, CaseImportRecord> {

    @Autowired
    private FilePreprocessService filePreprocessService;

    @Autowired
    @Lazy
    private DisputeFocusService disputeFocusService;

    @Autowired
    @Lazy
    private LitigationPointService litigationPointService;

    @Autowired
    @Lazy
    private ContradictionRecognitionService contradictionRecognitionService;

    @Autowired
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    @Autowired
    private TrialOrganizationMembersService trialOrganizationMembersService;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private OcrService ocrService;

    @Autowired
    private DocumentConvertService documentConvertService;

    @Autowired
    private EvidenceOverviewService evidenceOverviewService;

    @Autowired
    private CasePartyService casePartyService;

    @Autowired
    private VerificationEvidenceInfoService verificationEvidenceInfoService;

    @Autowired
    private CaseCauseExtractionService caseCauseExtractionService;

    @Autowired
    private DocumentExtractorService documentExtractorService;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private UserService userService;


    /**
     * 查询案件导入记录列表
     */
    public PageResult<CaseImportRecord> listCaseImportRecords(CaseImportSearchDTO searchDTO) {
        // 设置分页参数默认值
        Integer page = searchDTO.getPage() != null ? searchDTO.getPage() : 1;
        Integer pageSize = searchDTO.getPageSize() != null ? searchDTO.getPageSize() : 12;

        // 使用 MyBatis Plus 的分页功能
        Page<CaseImportRecord> pageParam = new Page<>(page, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<CaseImportRecord> queryWrapper = buildQueryWrapper(searchDTO);
        if(!StpUtil.hasPermission(RightConstants.RIGHT_ALL_DATA)){
            queryWrapper.eq(CaseImportRecord::getImporterId, StpUtil.getLoginIdAsLong());
        }
        // 执行分页查询
        Page<CaseImportRecord> result = this.page(pageParam, queryWrapper);

        // 填充importerName字段
        fillImporterNames(result.getRecords());

        // 转换为自定义的 PageResult
        return convertToPageResult(result);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<CaseImportRecord> buildQueryWrapper(CaseImportSearchDTO searchDTO) {
        LambdaQueryWrapper<CaseImportRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：未删除
        queryWrapper.eq(CaseImportRecord::getDeleted, 0);

        // 案件名称模糊查询
        if (StringUtils.isNotBlank(searchDTO.getCaseName())) {
            queryWrapper.like(CaseImportRecord::getCaseName, searchDTO.getCaseName());
        }

        // 导入人员模糊查询 - 通过用户姓名匹配用户ID
        if (StringUtils.isNotBlank(searchDTO.getOperator())) {
            // 获取所有用户缓存数据
            List<User> allUsers = userService.getAllUsersFromCache();
            List<Long> matchedUserIds = new ArrayList<>();

            if (allUsers != null && !allUsers.isEmpty()) {
                for (User userObj : allUsers) {
                    Long id = userObj.getId();
                    String xm = userObj.getXm();

                    if (StringUtils.contains(xm, searchDTO.getOperator())) {
                        matchedUserIds.add(id);
                    }
                }
            }
            if (!matchedUserIds.isEmpty()) {
                // 使用 in importerId 的方式查询
                queryWrapper.in(CaseImportRecord::getImporterId, matchedUserIds);
            } else {
                // 如果没有匹配的用户ID，则使用原来的模糊查询方式作为兜底
                queryWrapper.like(CaseImportRecord::getImporterName, searchDTO.getOperator());
            }
        }

        // 状态查询
        if (StringUtils.isNotBlank(searchDTO.getStatus())) {
            try {
                Integer status = Integer.valueOf(searchDTO.getStatus());
                queryWrapper.eq(CaseImportRecord::getImportStatus, status);
            } catch (NumberFormatException e) {
                log.warn("状态参数格式错误: {}", searchDTO.getStatus());
            }
        }

        // 时间范围查询
        if (StringUtils.isNotBlank(searchDTO.getStartTime())) {
            queryWrapper.ge(CaseImportRecord::getImportTime, searchDTO.getStartTime());
        }

        if (StringUtils.isNotBlank(searchDTO.getEndTime())) {
            queryWrapper.le(CaseImportRecord::getImportTime, searchDTO.getEndTime());
        }

        // 按导入时间倒序排列
        queryWrapper.orderByDesc(CaseImportRecord::getImportTime);

        return queryWrapper;
    }

    /**
     * 填充导入人员姓名
     * 通过用户ID从缓存中获取用户姓名并填充到importerName字段
     */
    private void fillImporterNames(List<CaseImportRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // 获取所有用户缓存数据
        List<User> allUsers = userService.getAllUsersFromCache();
        if (allUsers == null || allUsers.isEmpty()) {
            return;
        }

        // 构建用户ID到姓名的映射
        Map<Long, String> userIdToNameMap = new HashMap<>();
        for (User user : allUsers) {
            Long userId = user.getId();
            String xm = user.getXm();

            if (userId != null && StringUtils.isNotBlank(xm)) {
                userIdToNameMap.put(userId, xm);
            }
        }

        // 填充importerName字段
        for (CaseImportRecord record : records) {
            if (record.getImporterId() != null) {
                String importerName = userIdToNameMap.get(record.getImporterId());
                if (StringUtils.isNotBlank(importerName)) {
                    record.setImporterName(importerName);
                }
            }
        }
    }

    /**
     * 转换 MyBatis Plus 分页结果为自定义 PageResult
     */
    private PageResult<CaseImportRecord> convertToPageResult(Page<CaseImportRecord> result) {
        PageResult<CaseImportRecord> pageResult = new PageResult<>();
        pageResult.setCurrent(result.getCurrent())
                  .setSize(result.getSize())
                  .setTotal(result.getTotal())
                  .setPages(result.getPages())
                  .setRecords(result.getRecords())
                  .setHasPrevious(result.getCurrent() > 1)
                  .setHasNext(result.getCurrent() < result.getPages());

        return pageResult;
    }

    /**
     * 批量上传案件压缩包并处理
     * 业务流程：
     * 1. 创建案件记录
     * 2. 调用文件服务处理压缩包（解压、上传、转换、文本提取）
     * 3. 启动AI分析任务
     */
    public void uploadAndProcessArchives(List<MultipartFile> archives,int caseType) {
        for (MultipartFile archive : archives) {
            log.info("开始处理案件压缩包，文件名: {}", archive.getOriginalFilename());

            // 1. 创建案件导入记录
            String caseName = FilenameUtils.getBaseName(archive.getOriginalFilename());
            CaseImportRecord caseImportRecord = createCaseImportRecord(caseName,caseType);

            // 2. 异步处理文件和分析任务
            processArchiveAsync(caseImportRecord.getId(), archive);

            log.info("案件压缩包处理完成，caseImportId: {}", caseImportRecord.getId());
        }
    }

    /**
     * 创建单个案件导入记录
     */
    @Transactional(rollbackFor = Exception.class)
    public CaseImportRecord createCaseImportRecord(String caseName,int caseType) {
        log.info("创建案件导入记录，案件名: {}", caseName);

        // 根据案件名称确定案件类型代码
        String ajlxdm = CaseTypeConstants.determineAjlxdmByCaseName(caseName);
        log.info("根据案件名称 '{}' 确定案件类型代码: {}", caseName, ajlxdm);

        CaseImportRecord caseImportRecord = CaseImportRecord.builder()
                .importTime(LocalDateTime.now())
                .caseName(caseName)
                .caseType(caseType)
                .ajlxdm(ajlxdm)
                .importStatus(0) // 设置为处理中
                .fileStatus(0)
                .build();
        caseImportRecord.setImporterId(StpUtil.getLoginIdAsLong());
        getBaseMapper().insert(caseImportRecord);

        log.info("案件导入记录创建成功，caseImportId: {}", caseImportRecord.getId());
        return caseImportRecord;
    }

    /**
     * 同步处理单个文件上传（新方法）
     * 逻辑：先转为PDF → 原文件、PDF上传到MinIO → 保存基本信息到数据库 → 异步提取文本并更新数据库
     */
    @Transactional(rollbackFor = Exception.class)
    public void processSingleFileSync(Long caseImportId, MultipartFile file) {
        log.info("开始同步处理单个文件上传，caseImportId: {}, 文件: {}", caseImportId, file.getOriginalFilename());

        String fileName = file.getOriginalFilename();
        try {
            String fileExtension = FilenameUtils.getExtension(fileName);

            // 第一步：转为PDF
            byte[] originalFileBytes = file.getBytes();
            byte[] pdfBytes = convertToPdf(originalFileBytes, fileName, fileExtension);

            // 第二步：上传原文件和PDF到MinIO
            FileUploadRecord fileRecord = uploadFilesToMinIO(caseImportId, file, originalFileBytes, pdfBytes, fileName, fileExtension);

            // 调用基础文档类型分析方法（不包含判决书案号识别）
            CaseImportRecord caseRecord = getById(caseImportId);
            filePreprocessService.analyzeBasicDocumentType(Arrays.asList(fileRecord), caseRecord.getAjlxdm());
            fileUploadRecordService.updateById(fileRecord);


            // 第三步：先保存基本文件信息到数据库
            fileRecord.setStatus(1); // 处理中
            fileUploadRecordService.save(fileRecord);

            // 第四步：异步提取文本、OCR识别和更新数据库
            Long fileRecordId = fileRecord.getId();
            byte[] finalFileBytes = pdfBytes.clone(); // 复制字节数组避免异步任务中的引用问题
            String finalFileExtension = fileExtension;
            threadPoolUtils.execute(() -> {
                try {
                    log.info("开始异步处理文件: {}", fileName);

                    String extractedText = null;
                    OcrResponseData ocrResult = null;

                    // 检查文件类型，如果是QT（其他类型），跳过OCR和文本提取
                    if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                        log.info("文件类型为QT（其他类型），跳过OCR和文本提取: {}", fileName);
                        // 直接跳到更新文件记录部分
                    } else {
                        String base64Content = java.util.Base64.getEncoder().encodeToString(finalFileBytes);

                        // 根据文件类型进行不同的处理
                        if ("png".equalsIgnoreCase(finalFileExtension) || "jpg".equalsIgnoreCase(finalFileExtension) || "jpeg".equalsIgnoreCase(finalFileExtension)) {
                            // 图片文件：只进行OCR识别，不提取文本
                            log.info("图片文件，进行OCR识别: {}", fileName);
                            ocrResult = ocrService.getLayoutData(base64Content, FileType.IMAGE, true);
                        } else {
                            // 非图片文件：提取文本内容和OCR识别
                            log.info("非图片文件，提取文本和OCR识别: {}", fileName);
                            extractedText = extractTextFromPdf(finalFileBytes, fileRecord);
                            ocrResult = ocrService.getLayoutData(base64Content, FileType.PDF, true);
                        }
                    }

                    // 更新文件记录
                    FileUploadRecord updateRecord = new FileUploadRecord();
                    updateRecord.setId(fileRecordId);
                    updateRecord.setExtractedText(extractedText);
                    updateRecord.setFileName(fileName);
                    if (ocrResult != null) {
                        updateRecord.setOcrResult(ocrResult.getResults());
                        updateRecord.setOcrPreviews(ocrResult.getPreviews());
                    }
                    updateRecord.setStatus(2); // 处理成功

                    // 获取当前文件记录的文档类型，用于判决书案号识别
                    FileUploadRecord currentRecord = fileUploadRecordService.getById(fileRecordId);
                    if (currentRecord != null) {
                        updateRecord.setDocumentType(currentRecord.getDocumentType());
                    }

                    fileUploadRecordService.updateById(updateRecord);

                    // 如果有提取的文本内容，且是判决书文件，进行案号识别
                    if (extractedText != null && !extractedText.trim().isEmpty()){
                        if(currentRecord != null && DocumentType.PJS.equals(currentRecord.getDocumentType())) {

                            // 重新获取更新后的记录进行判决书案号识别
                            FileUploadRecord updatedRecord = fileUploadRecordService.getById(fileRecordId);
                            if (updatedRecord != null) {
                                updatedRecord.setExtractedText(extractedText);
                                filePreprocessService.analyzeJudgmentDocumentType(Arrays.asList(updatedRecord));
                                fileUploadRecordService.updateById(updatedRecord);
                                log.info("判决书案号识别完成: {}", fileName);
                            }
                        }
                    }

//                    if(fileName != null &&(fileName.contains("裁定书") || fileName.contains("判决书"))){
//                        if(CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm()) && !DocumentType.YSPJS.equals(updateRecord.getDocumentType())){
//                            evidenceFactsDetailsService.generate(caseImportId);
//                        }
//                    }
                    log.info("异步文件处理完成: {}", fileName);

                } catch (Exception e) {
                    log.error("异步文件处理失败: {}", fileName, e);
                    // 更新状态为失败
                    FileUploadRecord failRecord = new FileUploadRecord();
                    failRecord.setId(fileRecordId);
                    failRecord.setStatus(3); // 处理失败
                    fileUploadRecordService.updateById(failRecord);
                }
            });

            log.info("单个文件同步处理完成，异步文本提取已启动，caseImportId: {}, 文件: {}", caseImportId, fileName);

        } catch (Exception e) {
            log.error("单个文件同步处理失败，caseImportId: {}, 文件: {}", caseImportId, fileName, e);
            throw FilePreprocessException.processingFailed(fileName, e);
        }
    }

    /**
     * 启动AI分析任务（异步方法，使用@Async）
     */
    public void startAnalysisTasks(Long caseImportId) {
        log.info("开始启动AI分析任务，caseImportId: {}", caseImportId);


        // 2. 生成当事人分析（同步执行）
        runSilently(() -> casePartyService.analyseCasePartySync(caseImportId));

        // 3. 案由提取（同步执行）
        runSilently(() -> caseCauseExtractionService.extractCaseCause(caseImportId));

        // 4. 质证情况生成（异步执行）
        verificationEvidenceInfoService.generate(caseImportId);

        // 5. 诉辩关系分析（异步执行）
        litigationPointService.generate(caseImportId);

        // 6. 争议焦点分析（异步执行）
        disputeFocusService.generate(caseImportId);

        // 7. 矛盾识别分析（异步执行）
        contradictionRecognitionService.generate(caseImportId);

        // 8. 证据情况分析（同步执行）
        runSilently(() -> evidenceOverviewService.syncAnalyzeEvidence(caseImportId));

        // 9. 生成认定事实（同步执行）
        runSilently(() -> evidenceFactsDetailsService.generateDetermineFacts(caseImportId));


        log.info("核心AI分析完成，caseImportId: {}", caseImportId);
    }

    private void runSilently(Runnable runnable) {
        try {
            runnable.run();
        } catch (Throwable throwable) {
            log.error("静默运行出错", throwable);
        }
    }





    /**
     * 提取文件内容
     *
     * @param files 文件记录列表
     * @return 合并的文件内容
     */
    private String extractFileContent(List<FileUploadRecord> files) {
        if (files == null || files.isEmpty()) {
            return "";
        }

        return files.stream()
                .filter(file -> StringUtils.isNotBlank(file.getExtractedText()))
                .map(FileUploadRecord::getExtractedText)
                .collect(Collectors.joining("\n"));
    }

    /**
     * 异步处理压缩包文件
     * 包括：解压、转换、上传、保存到数据库、启动AI分析
     */
    private void processArchiveAsync(Long caseImportId, MultipartFile archive) {
        // 先将 MultipartFile 转换为字节数组，避免异步任务中访问已关闭的流
        byte[] archiveBytes;
        String originalFilename = archive.getOriginalFilename();
        String contentType = archive.getContentType();

        try {
            archiveBytes = archive.getBytes();
        } catch (IOException e) {
            log.error("读取压缩包文件失败，caseImportId: {}", caseImportId, e);
            return;
        }

        threadPoolUtils.execute(() -> {
            try {
                log.info("开始异步处理压缩包，caseImportId: {}", caseImportId);

                // 创建新的 MultipartFile 对象用于异步处理
                MultipartFile asyncArchive = new ByteArrayMultipartFile(
                        archiveBytes, originalFilename, contentType);

                // 调用文件预处理服务处理压缩包
                // 这里会处理：解压缩包 → 上传文件到MinIO → 转换为PDF → 提取文本 → 保存到数据库
                filePreprocessService.processArchive(caseImportId, asyncArchive);

                // 启动AI分析任务（各服务内部已异步）
                startAnalysisTasks(caseImportId);

                log.info("异步处理压缩包完成，caseImportId: {}", caseImportId);

            } catch (Exception e) {
                log.error("异步处理压缩包失败，caseImportId: {}", caseImportId, e);
            }
        });
    }


    /**
     * 重新处理案件（重新执行AI分析任务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void reprocessFiles(Long caseImportId) {
        log.info("开始重新分析案件，caseImportId: {}", caseImportId);

        // 调用文件预处理服务执行重新分析
        filePreprocessService.reprocessFiles(caseImportId);

        log.info("案件重新分析完成，caseImportId: {}", caseImportId);
    }



    // ==================== 案件状态管理方法 ====================

    /**
     * 检查并更新案件总体状态
     * 当所有分析任务都完成时，将案件状态更新为"已完成"
     *
     * @param caseImportId 案件导入ID
     */
    public void checkAndUpdateCaseStatus(Long caseImportId) {
        try {
            log.info("开始检查案件状态，caseImportId: {}", caseImportId);

            // 获取案件记录
            CaseImportRecord caseRecord = this.getById(caseImportId);
            if (caseRecord == null) {
                log.warn("案件记录不存在，caseImportId: {}", caseImportId);
                return;
            }

            // 如果已经是完成状态，不需要重复更新
            if (Integer.valueOf(1).equals(caseRecord.getImportStatus())) {
                log.debug("案件已经是完成状态，无需更新，caseImportId: {}", caseImportId);
                return;
            }

//            // 检查所有分析任务是否都已完成
//            boolean allTasksCompleted = isAllAnalysisTasksCompleted(caseRecord);
//
//            if (allTasksCompleted) {
//                // 更新案件状态为已完成
//                updateCaseStatusToCompleted(caseImportId);
//                log.info("案件状态已更新为已完成，caseImportId: {}", caseImportId);
//            } else {
//                log.debug("还有分析任务未完成，caseImportId: {}", caseImportId);
//            }

        } catch (Exception e) {
            log.error("检查案件状态失败，caseImportId: {}", caseImportId, e);
        }
    }

    /**
     * 更新案件状态为已完成
     *
     * @param caseImportId 案件导入ID
     */
    private void updateCaseStatusToCompleted(Long caseImportId) {
        LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CaseImportRecord::getId, caseImportId)
                    .set(CaseImportRecord::getImportStatus, 1);

        boolean updated = this.update(updateWrapper);
        if (updated) {
            log.info("案件状态更新成功，caseImportId: {}", caseImportId);
        } else {
            log.error("案件状态更新失败，caseImportId: {}", caseImportId);
        }
    }

    /**
     * 获取案件状态显示文本
     *
     * @param importStatus 导入状态
     * @return 状态文本
     */
    public String getCaseStatusText(Integer importStatus) {
        if (importStatus == null) {
            return "未知";
        }
        return importStatus == 1 ? "已完成" : "分析中";
    }

    /**
     * 获取案件状态类型（用于前端样式）
     *
     * @param importStatus 导入状态
     * @return 状态类型
     */
    public String getCaseStatusType(Integer importStatus) {
        if (importStatus == null) {
            return "info";
        }
        return importStatus == 1 ? "success" : "warning";
    }

    /**
     * 设置案件为分析中状态
     * 在开始分析任务时调用
     *
     * @param caseImportId 案件导入ID
     */
    public void setCaseStatusToAnalyzing(Long caseImportId) {
        try {
            LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CaseImportRecord::getId, caseImportId)
                        .set(CaseImportRecord::getImportStatus, 0);

            boolean updated = this.update(updateWrapper);
            if (updated) {
                log.info("案件状态设置为分析中，caseImportId: {}", caseImportId);
            } else {
                log.error("设置案件状态为分析中失败，caseImportId: {}", caseImportId);
            }
        } catch (Exception e) {
            log.error("设置案件状态为分析中失败，caseImportId: {}", caseImportId, e);
        }
    }

    /**
     * 逻辑删除案件记录
     *
     * @param caseImportId 案件导入ID
     * @return 删除结果
     */
    public boolean deleteCaseRecord(Long caseImportId) {
        try {
            LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CaseImportRecord::getId, caseImportId)
                        .set(CaseImportRecord::getDeleted, 1);

            boolean updated = this.update(updateWrapper);
            if (updated) {
                log.info("案件记录逻辑删除成功，caseImportId: {}", caseImportId);
            } else {
                log.error("案件记录逻辑删除失败，caseImportId: {}", caseImportId);
            }
            return updated;
        } catch (Exception e) {
            log.error("案件记录逻辑删除失败，caseImportId: {}", caseImportId, e);
            return false;
        }
    }

    // ==================== 单文件同步处理辅助方法 ====================

    /**
     * 第一步：转为PDF
     * 根据文件类型决定是否需要转换
     */
    private byte[] convertToPdf(byte[] originalFileBytes, String fileName, String fileExtension) throws Exception {
        log.info("开始转换文件为PDF: {}", fileName);

        if ("pdf".equalsIgnoreCase(fileExtension)) {
            // 已经是PDF文件，直接返回
            log.info("文件已是PDF格式，无需转换: {}", fileName);
            return originalFileBytes;
        } else if ("doc".equalsIgnoreCase(fileExtension) || "docx".equalsIgnoreCase(fileExtension)) {
            // Word文档转PDF
            try (InputStream inputStream = new ByteArrayInputStream(originalFileBytes)) {
                byte[] pdfBytes = documentConvertService.wordToPdfBytes(inputStream);
                log.info("Word文档转换为PDF成功: {}", fileName);
                return pdfBytes;
            }
        } else if ("png".equalsIgnoreCase(fileExtension) || "jpg".equalsIgnoreCase(fileExtension) || "jpeg".equalsIgnoreCase(fileExtension)) {
            // 图片文件不再转换为PDF，直接返回原始字节
            log.info("图片文件无需转换，直接使用原始文件: {}", fileName);
            return originalFileBytes;
        } else {
            throw FilePreprocessException.unsupportedFileType(fileExtension);
        }
    }

    /**
     * 第二步：上传原文件和PDF到MinIO
     */
    private FileUploadRecord uploadFilesToMinIO(Long caseImportId, MultipartFile file, byte[] originalFileBytes,
                                               byte[] pdfBytes, String fileName, String fileExtension) throws Exception {
        log.info("开始上传文件到MinIO: {}", fileName);

        // 生成存储文件名
        String originalStoredName = UUIDHelper.generateUUID() + "." + fileExtension;

        // 上传原文件到MinIO
        ObjectWriteResponse originalResponse = minioClient.putObject(PutObjectArgs.builder()
                .bucket(minioConfig.getBucketName())
                .object(originalStoredName)
                .stream(new ByteArrayInputStream(originalFileBytes), originalFileBytes.length, -1)
                .contentType(file.getContentType())
                .build());

        String pdfPath = null;

        // 只有非图片文件才上传PDF
        if (!"png".equalsIgnoreCase(fileExtension) && !"jpg".equalsIgnoreCase(fileExtension) && !"jpeg".equalsIgnoreCase(fileExtension)) {
            String pdfStoredName = UUIDHelper.generateUUID() + ".pdf";

            // 上传PDF文件到MinIO
            ObjectWriteResponse pdfResponse = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(pdfStoredName)
                    .stream(new ByteArrayInputStream(pdfBytes), pdfBytes.length, -1)
                    .contentType("application/pdf")
                    .build());

            pdfPath = pdfResponse.object();
            log.info("PDF文件上传成功: {}", pdfStoredName);
        } else {
            log.info("图片文件无需上传PDF版本: {}", fileName);
        }

        // 创建文件记录
        FileUploadRecord fileRecord = FileUploadRecord.builder()
                .caseImportId(caseImportId)
                .fileName(fileName)
                .filePath(originalResponse.object())
                .pdfPath(pdfPath) // 图片文件的pdfPath为null
                .fileSize(file.getSize())
                .fileType(fileExtension)
                .uploadTime(new Date())
                .status(1) // 处理中
                .build();

        log.info("文件上传到MinIO成功: 原文件={}, PDF={}", originalStoredName, pdfPath != null ? pdfPath : "无");
        return fileRecord;
    }

    /**
     * 第三步：提取文本
     */
    private String extractTextFromPdf(byte[] pdfBytes, FileUploadRecord fileRecord) throws Exception {
        log.info("开始提取PDF文本: {}", fileRecord.getFileName());

        try (InputStream pdfStream = new ByteArrayInputStream(pdfBytes)) {
            // 提取文本内容
            List<PdfPageContent> pageContents = FileExtractUtil.pdf2StringByPages(pdfStream);

            // 合并所有页面的文本，包含文件ID、文件名和页码信息
            String fullText = pageContents.stream()
                    .map(pageContent -> String.format("=== 文件ID：%d | 文件：%s | 第%d页 ===\n%s",
                            fileRecord.getId() != null ? fileRecord.getId() : 0L,
                            fileRecord.getFileName(),
                            pageContent.getPageNumber(),
                            pageContent.getContent()))
                    .collect(Collectors.joining("\n\n"));

            log.info("PDF文本提取成功: {}, 文本长度: {}", fileRecord.getFileName(), fullText.length());
            return fullText;
        }
    }



    /**
     * 字节数组 MultipartFile 实现
     * 用于异步任务中传递文件数据
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String originalFilename, String contentType) {
            this.content = content;
            this.name = "file";
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content.clone();
        }

        @Override
        public InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }


    /**
     * 判断是否为二审案件
     */
    public boolean isSecondInstanceCase(Long caseImportId) {
        CaseImportRecord caseRecord = this.getById(caseImportId);
        return CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
    }



    /**
     * 重新OCR识别文件
     */
    public void retryFileOcr(Long fileId) {
        log.info("开始重新OCR识别文件，fileId: {}", fileId);

        // 先设置OCR处理中状态 (ocrPreviews = 0 表示处理中)
        FileUploadRecord processingRecord = new FileUploadRecord();
        processingRecord.setId(fileId);
        // 创建一个空的OcrPreview对象表示处理中状态
        OcrPreview processingPreview = new OcrPreview();
        processingRecord.setOcrPreviews(List.of(processingPreview));
        fileUploadRecordService.updateById(processingRecord);

        threadPoolUtils.execute(() -> {
            try {
                FileUploadRecord fileRecord = fileUploadRecordService.getById(fileId);
                if (fileRecord == null) {
                    log.error("文件不存在，无法重新OCR，fileId: {}", fileId);
                    // 设置失败状态
                    setOcrFailedStatus(fileId);
                    return;
                }

                // 检查文件类型，如果是QT（其他类型），跳过OCR识别
                if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                    log.info("文件类型为QT（其他类型），跳过OCR识别: {}", fileRecord.getFileName());
                    return;
                }

                log.info("开始重新OCR识别: {}", fileRecord.getFileName());

                String fileType = fileRecord.getFileType().toLowerCase();

                // 根据文件类型选择不同的处理方式
                boolean isImage = isImageFile(fileType);
                String filePath = isImage ? fileRecord.getFilePath() : fileRecord.getPdfPath();
                FileType ocrFileType = isImage ? FileType.IMAGE : FileType.PDF;

                log.info("{}，进行重新OCR识别: {}", isImage ? "图片文件" : "非图片文件", fileRecord.getFileName());

                try (InputStream fileStream = minioClient.getObject(
                        GetObjectArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .object(filePath)
                                .build())) {

                    byte[] fileBytes = IOUtils.toByteArray(fileStream);
                    String base64Content = Base64.getEncoder().encodeToString(fileBytes);

                    OcrResponseData ocrResult = ocrService.getLayoutData(base64Content, ocrFileType, true);

                    // 更新文件记录
                    FileUploadRecord updateRecord = new FileUploadRecord();
                    updateRecord.setId(fileId);
                    if (ocrResult != null && ocrResult.getPreviews() != null && !ocrResult.getPreviews().isEmpty()) {
                        // OCR成功，设置正常的预览数据
                        updateRecord.setOcrResult(ocrResult.getResults());
                        updateRecord.setOcrPreviews(ocrResult.getPreviews());
                        log.info("重新OCR识别成功: {}", fileRecord.getFileName());
                    } else {
                        // OCR失败，设置失败状态 (ocrPreviews = -1)
                        log.warn("重新OCR识别失败，结果为空: {}", fileRecord.getFileName());
                        setOcrFailedStatus(fileId);
                        return;
                    }

                    fileUploadRecordService.updateById(updateRecord);

                } catch (Exception e) {
                    log.error("重新OCR识别过程中发生错误，文件: {}", fileRecord.getFileName(), e);
                    // 设置失败状态
                    setOcrFailedStatus(fileId);
                }

            } catch (Exception e) {
                log.error("重新OCR识别失败，fileId: {}", fileId, e);
                // 设置失败状态
                setOcrFailedStatus(fileId);
            }
        });
    }

    /**
     * 设置OCR失败状态
     */
    private void setOcrFailedStatus(Long fileId) {
        try {
            FileUploadRecord failedRecord = new FileUploadRecord();
            failedRecord.setId(fileId);
            // 创建一个特殊的OcrPreview对象表示失败状态 (-1)
            OcrPreview failedPreview = new OcrPreview();
            // 这里可以设置一个特殊标记，比如在预览对象中设置错误标识
            failedRecord.setOcrPreviews(new ArrayList<>()); // 设置为空列表表示失败
            fileUploadRecordService.updateById(failedRecord);
            log.info("已设置OCR失败状态，fileId: {}", fileId);
        } catch (Exception e) {
            log.error("设置OCR失败状态时发生错误，fileId: {}", fileId, e);
        }
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String fileType) {
        return "png".equalsIgnoreCase(fileType) ||
               "jpg".equalsIgnoreCase(fileType) ||
               "jpeg".equalsIgnoreCase(fileType);
    }

}
 