package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.dto.response.FileUploadRecordDTO;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 诚
 * @since 2025-05-12 10:10
 **/
@Slf4j
@Service
public class FileUploadRecordService extends ServiceImpl<FileUploadRecordMapper, FileUploadRecord> {

    @Autowired
    private TrialOrganizationMembersService trialOrganizationMembersService;

    public void updatePdfPath(FileUploadRecord fileUploadRecord) {
        updateById(FileUploadRecord.builder().pdfPath(fileUploadRecord.getPdfPath()).id(fileUploadRecord.getId()).build());
    }

    /**
     * 获取回收站文件列表
     */
    public List<FileUploadRecord> getRecycleBinFiles(Long caseImportId) {
        return baseMapper.selectRecycleBinFiles(caseImportId);
    }

    /**
     * 逻辑删除文件（移入回收站）
     */
    public boolean deleteFileToRecycleBin(Long fileId, String deletedBy) {
        try {
            // 先获取文件信息，检查是否是庭审笔录
            FileUploadRecord fileRecord = getById(fileId);
            if (fileRecord != null && DocumentType.TSBL.equals(fileRecord.getDocumentType())) {
                log.info("检测到庭审笔录文件删除，删除案件审判组织成员记录，案件ID: {}", fileRecord.getCaseImportId());
                trialOrganizationMembersService.deleteByCaseImportId(fileRecord.getCaseImportId());
            }

            int result = baseMapper.logicalDeleteFile(fileId, LocalDateTime.now(), deletedBy);
            if (result > 0) {
                log.info("文件移入回收站成功，fileId: {}, deletedBy: {}", fileId, deletedBy);
                return true;
            } else {
                log.warn("文件移入回收站失败，文件可能不存在或已删除，fileId: {}", fileId);
                return false;
            }
        } catch (Exception e) {
            log.error("文件移入回收站失败，fileId: {}", fileId, e);
            return false;
        }
    }

    /**
     * 从回收站恢复文件
     */
    public boolean restoreFileFromRecycleBin(Long fileId) {
        try {
            int result = baseMapper.restoreFile(fileId);
            if (result > 0) {
                log.info("文件从回收站恢复成功，fileId: {}", fileId);
                return true;
            } else {
                log.warn("文件从回收站恢复失败，文件可能不存在或未删除，fileId: {}", fileId);
                return false;
            }
        } catch (Exception e) {
            log.error("文件从回收站恢复失败，fileId: {}", fileId, e);
            return false;
        }
    }



    /**
     * 永久删除文件（真正删除数据库记录）
     * 注意：这会绕过@TableLogic，真正删除记录
     */
    public boolean permanentDeleteFile(Long fileId) {
        try {
            // 查询回收站中的文件（deleted=1）
            FileUploadRecord file = baseMapper.selectDeletedFileById(fileId);
            if (file == null) {
                log.warn("永久删除失败，文件不在回收站中，fileId: {}", fileId);
                return false;
            }

            // 物理删除记录
            int result = baseMapper.physicalDeleteById(fileId);
            if (result > 0) {
                log.info("文件永久删除成功，fileId: {}", fileId);
                return true;
            } else {
                log.warn("文件永久删除失败，fileId: {}", fileId);
                return false;
            }
        } catch (Exception e) {
            log.error("文件永久删除失败，fileId: {}", fileId, e);
            return false;
        }
    }

    /**
     * 清空回收站（批量永久删除案件的所有回收站文件）
     */
    public int clearRecycleBin(Long caseImportId) {
        try {
            // 批量物理删除回收站中的文件
            int deletedCount = baseMapper.batchPhysicalDeleteByCaseId(caseImportId);
            log.info("清空回收站成功，caseImportId: {}, 删除文件数: {}", caseImportId, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("清空回收站失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("清空回收站失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据多个DocumentType获取案件文档的提取文本内容
     * @param caseImportId 案件导入ID
     * @param documentTypes 文档类型（可变参数）
     * @return 合并后的文本内容
     */
    public String getExtractedTextByDocumentTypes(Long caseImportId, DocumentType... documentTypes) {
        if (caseImportId == null || documentTypes == null || documentTypes.length == 0) {
            log.warn("参数无效，caseImportId: {}, documentTypes: {}", caseImportId, Arrays.toString(documentTypes));
            return "";
        }

        try {
            // 转换为List用于查询
            List<DocumentType> documentTypeList = Arrays.asList(documentTypes);

            // 获取文件记录
            List<FileUploadRecord> fileRecords = baseMapper.getFileRecordsByDocumentTypes(caseImportId, documentTypeList);

            if (fileRecords.isEmpty()) {
                log.info("未找到指定文档类型的文件，caseImportId: {}, documentTypes: {}",
                        caseImportId, Arrays.toString(documentTypes));
                return "";
            }

            // 合并文本内容
            StringBuilder allContents = new StringBuilder();
            for (FileUploadRecord record : fileRecords) {
                if (StringUtils.isNotBlank(record.getExtractedText())) {
                    allContents.append("=== 文件: ").append(record.getFileName())
                              .append(" | 类型: ").append(record.getDocumentType())
                              .append(" ===\n");
                    allContents.append(record.getExtractedText()).append("\n\n");
                }
            }

            String result = allContents.toString().trim();
            log.info("根据DocumentType获取文本内容成功，caseImportId: {}, documentTypes: {}, 文件数量: {}, 内容长度: {}",
                    caseImportId, Arrays.toString(documentTypes), fileRecords.size(), result.length());

            return result;

        } catch (Exception e) {
            log.error("根据DocumentType获取文本内容失败，caseImportId: {}, documentTypes: {}",
                    caseImportId, Arrays.toString(documentTypes), e);
            return "";
        }
    }

    /**
     * 获取文件上传记录列表（不包含OCR字段）
     */
    public List<FileUploadRecordDTO> listFileUploadRecordsWithoutOcr(Integer caseImportId) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId);
        List<FileUploadRecord> records = this.list(queryWrapper);

        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将FileUploadRecord实体转换为FileUploadRecordDTO
     */
    private FileUploadRecordDTO convertToDTO(FileUploadRecord record) {
        return FileUploadRecordDTO.builder()
                .id(record.getId())
                .caseImportId(record.getCaseImportId())
                .fileName(record.getFileName())
                .filePath(record.getFilePath())
                .pdfPath(record.getPdfPath())
                .fileSize(record.getFileSize())
                .fileType(record.getFileType())
                .uploaderId(record.getUploaderId())
                .uploaderName(record.getUploaderName())
                .uploadTime(record.getUploadTime())
                .status(record.getStatus())
                .md5Hash(record.getMd5Hash())
                .description(record.getDescription())
                .downloadCount(record.getDownloadCount())
                .extractedText(record.getExtractedText())
                .deleted(record.getDeleted())
                .deletedTime(record.getDeletedTime())
                .deletedBy(record.getDeletedBy())
                .documentType(record.getDocumentType())
                .build();
    }
}
