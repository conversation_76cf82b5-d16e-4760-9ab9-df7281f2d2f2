package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.entity.JudgmentSituation;
import com.smxz.yjzs.mapper.JudgmentSituationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 判项情况服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class JudgmentSituationService extends ServiceImpl<JudgmentSituationMapper, JudgmentSituation> {

    /**
     * 根据案件ID查询判项情况列表
     * 按xh字段升序排序
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 判项情况列表
     */
    public List<JudgmentSituation> listByCaseImportId(Long caseImportRecordId) {
        log.info("查询案件判项情况信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listByCaseImportId(caseImportRecordId);
    }

    /**
     * 根据案件ID和文书类型查询判项情况列表
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param documentCaseType 文书类型
     * @return 判项情况列表
     */
    public List<JudgmentSituation> listByCaseImportIdAndDocumentCaseType(Long caseImportRecordId, String documentCaseType) {
        return baseMapper.listByCaseImportIdAndDocumentCaseType(caseImportRecordId, documentCaseType);
    }

    /**
     * 根据案件ID删除判项情况记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportId(Long caseImportRecordId) {
        log.info("删除案件判项情况信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportId(caseImportRecordId);
    }

    /**
     * 根据案件ID和文书类型删除判项情况记录
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param documentCaseType 文书类型
     */
    public void deleteByCaseImportIdAndDocumentCaseType(Long caseImportRecordId, String documentCaseType) {
        log.info("根据案件ID和文书类型删除判项情况信息，caseImportRecordId: {}, documentCaseType: {}",
                caseImportRecordId, documentCaseType);
        baseMapper.deleteByCaseImportIdAndDocumentCaseType(caseImportRecordId, documentCaseType);
    }

    /**
     * 保存判项情况记录
     * 如果是新记录，自动设置序号为相同documentCaseType下的最大序号+1
     *
     * @param entity 判项情况实体
     * @return 是否保存成功
     */
    @Override
    public boolean save(JudgmentSituation entity) {
        // 如果是新记录（ID为空）且序号为空，自动设置序号
        if (entity.getId() == null && entity.getXh() == null && entity.getDocumentCaseType() != null) {
            Integer maxXh = baseMapper.getMaxXhByDocumentCaseType(entity.getDocumentCaseType());
            entity.setXh(maxXh + 1);
            entity.setDataType(CaseTypeConstants.DataType.NEW);
            log.info("为新记录自动设置序号，documentCaseType: {}, 新序号: {}",
                    entity.getDocumentCaseType(), entity.getXh());
        }

        return super.save(entity);
    }

    /**
     * 批量保存判项情况记录
     *
     * @param judgmentSituationList 判项情况列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<JudgmentSituation> judgmentSituationList) {
        log.info("批量保存判项情况信息，数量: {}", judgmentSituationList.size());
        return super.saveBatch(judgmentSituationList);
    }

    /**
     * 批量保存或更新判项情况记录
     * 根据ID是否存在自动判断是新增还是更新
     *
     * @param judgmentSituationList 判项情况列表
     * @return 是否操作成功
     */
    public boolean saveOrUpdateBatch(List<JudgmentSituation> judgmentSituationList) {
        log.info("批量保存或更新判项情况信息，数量: {}", judgmentSituationList.size());
        
        for (JudgmentSituation judgmentSituation : judgmentSituationList) {
            if (judgmentSituation.getId() != null) {
                // 有ID，执行更新
                updateById(judgmentSituation);
            } else {
                // 无ID，执行新增
                save(judgmentSituation);
            }
        }
        
        return true;
    }
}
