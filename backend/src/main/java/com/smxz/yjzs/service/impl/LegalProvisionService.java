package com.smxz.yjzs.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.common.utils.LegalProvisionParseUtil;
import com.smxz.yjzs.dto.LegalProvisionExcelDTO;
import com.smxz.yjzs.dto.response.LawNameResponse;
import com.smxz.yjzs.dto.response.LegalProvisionTreeNode;
import com.smxz.yjzs.entity.LegalProvision;
import com.smxz.yjzs.mapper.LegalProvisionMapper;
import com.smxz.yjzs.config.CacheConfig;
import com.smxz.yjzs.common.utils.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 法条服务实现类
 */
@Slf4j
@Service
public class LegalProvisionService extends ServiceImpl<LegalProvisionMapper, LegalProvision> {

    @Autowired
    private LegalProvisionMapper legalProvisionMapper;

    @Autowired
    private CacheConfig cacheConfig;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    /**
     * 法律名称缓存
     */
    private final Map<Long, LawNameResponse> lawNameCache = new ConcurrentHashMap<>();

    /**
     * 应用启动时初始化缓存
     */
    @PostConstruct
    public void initCache() {
        refreshLawNameCache();
        preloadProvisionTreeCache();
    }

    /**
     * 刷新法律名称缓存
     */
    public void refreshLawNameCache() {
        try {
            List<LegalProvision> laws = legalProvisionMapper.selectAllLaws();
            lawNameCache.clear();
            for (LegalProvision law : laws) {
                lawNameCache.put(law.getId(), LawNameResponse.builder()
                        .lawId(law.getId())
                        .lawName(law.getLawName())
                        .displayName(law.getLawName())
                        .build());
            }
            log.info("法律名称缓存刷新完成，共缓存 {} 个法律", lawNameCache.size());
        } catch (Exception e) {
            log.error("刷新法律名称缓存失败", e);
        }
    }

    /**
     * 预加载法条树形结构缓存
     */
    public void preloadProvisionTreeCache() {
        if (cacheConfig.isAsyncPreloadEnabled()) {
            // 异步预加载
            threadPoolUtils.execute(() -> {
                log.info("开始异步预加载法条树形结构缓存");
                doPreloadProvisionTreeCache();
            });
        } else {
            // 同步预加载
            doPreloadProvisionTreeCache();
        }
    }

    /**
     * 执行法条树形结构缓存预加载的具体逻辑
     */
    private void doPreloadProvisionTreeCache() {
        try {
            List<LegalProvision> laws = legalProvisionMapper.selectAllLaws();
            int cachedCount = 0;

            for (LegalProvision law : laws) {
                try {
                    // 调用getProvisionTree方法，这会触发@Cacheable注解，将结果缓存
                    List<LegalProvisionTreeNode> tree = getProvisionTree(law.getId());
                    cachedCount++;
                    log.debug("已缓存法律树形结构: {} (ID: {}), 节点数: {}",
                             law.getLawName(), law.getId(), tree.size());
                } catch (Exception e) {
                    log.warn("缓存法律树形结构失败: {} (ID: {}), 错误: {}",
                            law.getLawName(), law.getId(), e.getMessage());
                }
            }

            log.info("法条树形结构缓存预加载完成，共缓存 {} 个法律的树形结构", cachedCount);
        } catch (Exception e) {
            log.error("预加载法条树形结构缓存失败", e);
        }
    }

    /**
     * 获取所有法律名称（从缓存）
     */
    public List<LawNameResponse> getAllLawNames() {
        return new ArrayList<>(lawNameCache.values());
    }

    /**
     * 刷新所有缓存（法律名称 + 树形结构）
     */
    public void refreshAllCache() {
        refreshLawNameCache();
        preloadProvisionTreeCache();
    }

    /**
     * 导入Excel文件
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "legalProvisionTree", allEntries = true)
    public void importFromExcel(MultipartFile file) throws IOException {
        log.info("开始导入法条Excel文件: {}", file.getOriginalFilename());
        
        List<LegalProvisionExcelDTO> excelData = EasyExcel.read(file.getInputStream())
                .head(LegalProvisionExcelDTO.class)
                .sheet()
                .doReadSync();

        if (excelData.isEmpty()) {
            throw new RuntimeException("Excel文件为空");
        }

        // 按法律名称分组处理
        Map<String, List<LegalProvisionExcelDTO>> lawGroups = excelData.stream()
                .filter(dto -> dto.getLawName() != null && dto.getArticleNumber() != null && dto.getContent() != null)
                .collect(Collectors.groupingBy(dto -> dto.getLawName().trim()));

        List<LegalProvision> allProvisions = new ArrayList<>();

        for (Map.Entry<String, List<LegalProvisionExcelDTO>> entry : lawGroups.entrySet()) {
            String lawName = entry.getKey();
            List<LegalProvisionExcelDTO> lawData = entry.getValue();

            // 1. 先创建法律记录（level=0）
            LegalProvision lawRecord = LegalProvision.builder()
                    .lawName(lawName)
                    .content(lawName) // 法律记录的内容就是法律名称
                    .level(0)
                    .build();
            allProvisions.add(lawRecord);

            // 2. 处理该法律下的条款项，并自动创建缺失的父级节点
            Set<String> createdNodes = new HashSet<>(); // 记录已创建的节点，避免重复
            List<LegalProvision> provisions = new ArrayList<>();

            for (LegalProvisionExcelDTO dto : lawData) {
                LegalProvisionParseUtil.ParseResult parseResult = LegalProvisionParseUtil.parseArticleNumber(dto.getArticleNumber());
                if (parseResult == null) {
                    log.warn("无法解析法条编号，跳过: {}", dto.getArticleNumber());
                    continue;
                }

                // 自动创建缺失的父级节点
                createMissingParentNodes(lawName, parseResult, provisions, createdNodes);

                // 创建当前节点
                LegalProvision provision = LegalProvision.builder()
                        .lawName(lawName)
                        .articleNo(parseResult.getArticleNo())
                        .paragraphNo(parseResult.getParagraphNo())
                        .subparagraphNo(parseResult.getSubparagraphNo())
                        .content(dto.getContent().trim())
                        .level(parseResult.getLevel())
                        .build();

                provisions.add(provision);
            }
            allProvisions.addAll(provisions);
        }

        // 批量保存
        if (!allProvisions.isEmpty()) {
            // 先删除同名法律的现有数据
            Set<String> lawNames = allProvisions.stream().map(LegalProvision::getLawName).collect(Collectors.toSet());
            for (String lawName : lawNames) {
                LambdaQueryWrapper<LegalProvision> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(LegalProvision::getLawName, lawName);
                remove(deleteWrapper);
            }

            // 分批保存并更新父节点关系
            saveBatchWithParentRelation(allProvisions);

            // 刷新缓存
            refreshLawNameCache();

            // 预加载树形结构缓存
            preloadProvisionTreeCache();

            log.info("成功导入 {} 条法条数据", allProvisions.size());
        }
    }

    /**
     * 导入多个Excel文件
     */

    @CacheEvict(value = "legalProvisionTree", allEntries = true)
    public String importFromExcelFiles(List<MultipartFile> files) throws IOException {
        log.info("开始批量导入 {} 个法条Excel文件", files.size());

        int totalImported = 0;
        int successCount = 0;
        int failureCount = 0;
        StringBuilder resultMessage = new StringBuilder();
        List<String> failedFiles = new ArrayList<>();

        for (MultipartFile file : files) {
            try {
                String fileName = file.getOriginalFilename();
                log.info("正在处理文件: {}", fileName);

                List<LegalProvisionExcelDTO> excelData = EasyExcel.read(file.getInputStream())
                        .head(LegalProvisionExcelDTO.class)
                        .sheet()
                        .doReadSync();

                if (excelData.isEmpty()) {
                    log.warn("文件为空，跳过: {}", fileName);
                    failedFiles.add(fileName + " (文件为空)");
                    failureCount++;
                    continue;
                }

                // 处理单个文件的数据
                int fileImportCount = processSingleFileData(excelData, fileName);
                totalImported += fileImportCount;
                successCount++;

                log.info("文件 {} 导入成功，导入 {} 条数据", fileName, fileImportCount);

            } catch (Exception e) {
                String fileName = file.getOriginalFilename();
                log.error("导入文件失败: {}", fileName, e);
                failedFiles.add(fileName + " (" + e.getMessage() + ")");
                failureCount++;
            }
        }

        // 构建结果消息
        resultMessage.append(String.format("批量导入完成！成功: %d 个文件，失败: %d 个文件，总计导入: %d 条法条数据",
                successCount, failureCount, totalImported));

        if (!failedFiles.isEmpty()) {
            resultMessage.append("\n失败文件: ").append(String.join(", ", failedFiles));
        }

        // 如果有成功导入的文件，刷新缓存
        if (successCount > 0) {
            refreshLawNameCache();
            preloadProvisionTreeCache();
        }

        log.info("批量导入法条文件完成，成功: {}, 失败: {}, 总计: {} 条", successCount, failureCount, totalImported);
        return resultMessage.toString();
    }

    /**
     * 处理单个文件的数据
     */
    private int processSingleFileData(List<LegalProvisionExcelDTO> excelData, String fileName) {
        // 按法律名称分组处理
        Map<String, List<LegalProvisionExcelDTO>> lawGroups = excelData.stream()
                .filter(dto -> dto.getLawName() != null && dto.getArticleNumber() != null && dto.getContent() != null)
                .collect(Collectors.groupingBy(dto -> dto.getLawName().trim()));

        List<LegalProvision> allProvisions = new ArrayList<>();

        for (Map.Entry<String, List<LegalProvisionExcelDTO>> entry : lawGroups.entrySet()) {
            String lawName = entry.getKey();
            List<LegalProvisionExcelDTO> lawData = entry.getValue();

            // 1. 先创建法律记录（level=0）
            LegalProvision lawRecord = LegalProvision.builder()
                    .lawName(lawName)
                    .content(lawName) // 法律记录的内容就是法律名称
                    .level(0)
                    .build();
            allProvisions.add(lawRecord);

            // 2. 处理该法律下的条款项，并自动创建缺失的父级节点
            Set<String> createdNodes = new HashSet<>(); // 记录已创建的节点，避免重复
            List<LegalProvision> provisions = new ArrayList<>();

            for (LegalProvisionExcelDTO dto : lawData) {
                LegalProvisionParseUtil.ParseResult parseResult = LegalProvisionParseUtil.parseArticleNumber(dto.getArticleNumber());
                if (parseResult == null) {
                    log.warn("无法解析法条编号，跳过: {}", dto.getArticleNumber());
                    continue;
                }
                if(StringUtils.isBlank(dto.getContent())){
                    log.warn("法条内容为空，跳过: {}", dto.getArticleNumber());
                    continue;
                }

                // 自动创建缺失的父级节点
                createMissingParentNodes(lawName, parseResult, provisions, createdNodes);

                // 创建当前节点
                LegalProvision provision = LegalProvision.builder()
                        .lawName(lawName)
                        .articleNo(parseResult.getArticleNo())
                        .paragraphNo(parseResult.getParagraphNo())
                        .subparagraphNo(parseResult.getSubparagraphNo())
                        .content(dto.getContent().trim())
                        .level(parseResult.getLevel())
                        .build();

                provisions.add(provision);
            }
            allProvisions.addAll(provisions);
        }

        // 批量保存
        if (!allProvisions.isEmpty()) {
            // 先删除同名法律的现有数据
            Set<String> lawNames = allProvisions.stream().map(LegalProvision::getLawName).collect(Collectors.toSet());
            for (String lawName : lawNames) {
                LambdaQueryWrapper<LegalProvision> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(LegalProvision::getLawName, lawName);
                remove(deleteWrapper);
            }

            // 分批保存并更新父节点关系
            saveBatchWithParentRelation(allProvisions);
        }

        return allProvisions.size();
    }

    /**
     * 分批保存并建立父子关系
     */
    private void saveBatchWithParentRelation(List<LegalProvision> provisions) {
        // 按层级排序，先保存法律(level=0)，再保存条，再保存款，最后保存项
        provisions.sort(Comparator.comparing(LegalProvision::getLevel)
                .thenComparing(LegalProvision::getArticleNo, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(LegalProvision::getParagraphNo, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(LegalProvision::getSubparagraphNo, Comparator.nullsFirst(Integer::compareTo)));

        // 按法律分组处理
        Map<String, List<LegalProvision>> lawGroups = provisions.stream()
                .collect(Collectors.groupingBy(LegalProvision::getLawName));

        for (Map.Entry<String, List<LegalProvision>> entry : lawGroups.entrySet()) {
            String lawName = entry.getKey();
            List<LegalProvision> lawProvisions = entry.getValue();

            // 再次按层级排序
            lawProvisions.sort(Comparator.comparing(LegalProvision::getLevel)
                    .thenComparing(LegalProvision::getArticleNo, Comparator.nullsFirst(Integer::compareTo))
                    .thenComparing(LegalProvision::getParagraphNo, Comparator.nullsFirst(Integer::compareTo))
                    .thenComparing(LegalProvision::getSubparagraphNo, Comparator.nullsFirst(Integer::compareTo)));

            // 用于存储节点映射：key -> nodeId
            Map<String, Long> nodeMap = new HashMap<>();
            Long lawId = null;

            for (LegalProvision provision : lawProvisions) {
                if (provision.getLevel() == 0) {
                    // 保存法律记录
                    save(provision);
                    lawId = provision.getId();
                    nodeMap.put("LAW", lawId);
                } else {
                    // 设置父节点ID
                    Long parentId = findParentIdNew(provision, nodeMap, lawId);
                    provision.setParentId(parentId);

                    // 保存条款项记录
                    save(provision);

                    // 更新节点映射
                    String nodeKey = buildNodeKey(provision);
                    nodeMap.put(nodeKey, provision.getId());
                }
            }
        }
    }

    /**
     * 查找父节点ID（新版本）
     */
    private Long findParentIdNew(LegalProvision provision, Map<String, Long> nodeMap, Long lawId) {
        if (provision.getLevel() == 1) {
            // 条的父节点是法律
            return lawId;
        } else if (provision.getLevel() == 2) {
            // 款的父节点是条
            String parentKey = "ARTICLE_" + provision.getArticleNo();
            return nodeMap.get(parentKey);
        } else if (provision.getLevel() == 3) {
            // 项的父节点是款
            String parentKey = "PARAGRAPH_" + provision.getArticleNo() + "_" + provision.getParagraphNo();
            return nodeMap.get(parentKey);
        }

        return null;
    }

    /**
     * 构建节点映射的key
     */
    private String buildNodeKey(LegalProvision provision) {
        if (provision.getLevel() == 1) {
            return "ARTICLE_" + provision.getArticleNo();
        } else if (provision.getLevel() == 2) {
            return "PARAGRAPH_" + provision.getArticleNo() + "_" + provision.getParagraphNo();
        } else if (provision.getLevel() == 3) {
            return "SUBPARAGRAPH_" + provision.getArticleNo() + "_" + provision.getParagraphNo() + "_" + provision.getSubparagraphNo();
        }
        return "";
    }



    /**
     * 根据法律ID获取树形结构
     */
    @Cacheable(value = "legalProvisionTree", key = "#lawId")
    public List<LegalProvisionTreeNode> getProvisionTree(Long lawId) {
        List<LegalProvision> provisions = legalProvisionMapper.selectByLawIdOrderByHierarchy(lawId);
        return buildTree(provisions);
    }

    /**
     * 构建树形结构
     */
    private List<LegalProvisionTreeNode> buildTree(List<LegalProvision> provisions) {
        Map<Long, LegalProvisionTreeNode> nodeMap = new HashMap<>();
        List<LegalProvisionTreeNode> rootNodes = new ArrayList<>();

        // 创建所有节点
        for (LegalProvision provision : provisions) {
            LegalProvisionTreeNode node = LegalProvisionTreeNode.builder()
                    .id(provision.getId())
                    .title(buildTitle(provision))
                    .content(provision.getContent())
                    .level(provision.getLevel())
                    .articleNo(provision.getArticleNo())
                    .paragraphNo(provision.getParagraphNo())
                    .subparagraphNo(provision.getSubparagraphNo())
                    .children(new ArrayList<>())
                    .build();
            
            nodeMap.put(provision.getId(), node);
        }

        // 建立父子关系
        for (LegalProvision provision : provisions) {
            LegalProvisionTreeNode node = nodeMap.get(provision.getId());
            
            if (provision.getParentId() == null) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                LegalProvisionTreeNode parent = nodeMap.get(provision.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }

        return rootNodes;
    }

    /**
     * 构建节点标题
     */
    private String buildTitle(LegalProvision provision) {
        StringBuilder title = new StringBuilder();

        if (provision.getLevel() == 0) {
            title.append(provision.getLawName());
        } else if (provision.getLevel() == 1) {
            title.append("第").append(convertToChineseNumber(provision.getArticleNo())).append("条");
        } else if (provision.getLevel() == 2) {
            title.append("第").append(convertToChineseNumber(provision.getParagraphNo())).append("款");
        } else if (provision.getLevel() == 3) {
            title.append("第（").append(convertToChineseNumber(provision.getSubparagraphNo())).append("）项");
        }

        return title.toString();
    }

    /**
     * 批量查询法条内容，只返回能从数据库查到的法条
     * 输入格式：["中华人民共和国民法典第一千零六十二条", "中华人民共和国民法典第六百五十七条"]
     * 返回：{validLaws: [...], lawSummaries: [...]} 数量一致且都是有效的
     */
    public LawQueryResult getValidProvisionContentsByLawReferences(List<String> lawReferences) {
        if (lawReferences == null || lawReferences.isEmpty()) {
            return new LawQueryResult(new ArrayList<>(), new ArrayList<>());
        }

        // 去重处理，使用LinkedHashSet保持原有顺序
        List<String> uniqueLawReferences = new ArrayList<>(new LinkedHashSet<>(lawReferences));

        // 记录去重信息
        if (uniqueLawReferences.size() != lawReferences.size()) {
            log.info("法条引用去重处理：原始数量={}, 去重后数量={}", lawReferences.size(), uniqueLawReferences.size());
        }

        List<String> validLaws = new ArrayList<>();
        List<String> lawSummaries = new ArrayList<>();

        for (String lawReference : uniqueLawReferences) {
            try {

                LegalProvisionParseUtil.LegalProvisionParseResult legalProvisionParseResult = LegalProvisionParseUtil.parseLegalProvision(lawReference);

                // 添加调试日志
                log.info("解析法条引用: {}", lawReference);
                log.info("解析结果: 法律名称={}, 条号={}, 款号={}, 项号={}, 是否有效={}",
                        legalProvisionParseResult.getLawName(),
                        legalProvisionParseResult.getArticleNo(),
                        legalProvisionParseResult.getParagraphNo(),
                        legalProvisionParseResult.getItemNo(),
                        legalProvisionParseResult.isValid());

                // 3. 根据解析结果查询数据库
                List<LegalProvision> provisions = queryProvisionsByArticleInfo(
                        legalProvisionParseResult.getLawName(),
                        legalProvisionParseResult.getArticleNo(),
                        legalProvisionParseResult.getParagraphNo(),
                        legalProvisionParseResult.getItemNo()
                );

                // 4. 只有找到法条内容才添加到结果中
                if (provisions != null && !provisions.isEmpty()) {
                    validLaws.add(lawReference);
                    // 将多个条文内容合并，按顺序连接，并添加款、项标识
                    String combinedContent = formatProvisionContent(provisions);
                    lawSummaries.add(combinedContent);
                } else {
                    log.warn("未找到法条内容，跳过: {}", lawReference);
                }

            } catch (Exception e) {
                log.error("查询法条内容失败，跳过: {}", lawReference, e);
                log.error("查询法条内容失败，跳过: {}", lawReference, e);
            }
        }

        log.info("法条查询完成，原始输入数量: {}, 去重后数量: {}, 有效数量: {}",
                lawReferences.size(), uniqueLawReferences.size(), validLaws.size());
        return new LawQueryResult(validLaws, lawSummaries);
    }

    /**
     * 根据法律名称和条款信息查询法条内容
     * 当没有明确到款、项时，返回该条下的所有款、项内容
     *
     * @param lawName 法律名称
     * @param articleNo 条号
     * @param paragraphNo 款号（可为null）
     * @param subparagraphNo 项号（可为null）
     * @return 法条内容列表
     */
    public List<LegalProvision> queryProvisionsByArticleInfo(String lawName, Integer articleNo,
                                                           Integer paragraphNo, Integer subparagraphNo) {
        LambdaQueryWrapper<LegalProvision> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LegalProvision::getLawName, lawName)
               .eq(LegalProvision::getArticleNo, articleNo)
               .gt(LegalProvision::getLevel, 0);

        // 如果指定了款号，则精确匹配款号
        if (paragraphNo != null) {
            wrapper.eq(LegalProvision::getParagraphNo, paragraphNo);
        }

        // 如果指定了项号，则精确匹配项号
        if (subparagraphNo != null) {
            wrapper.eq(LegalProvision::getSubparagraphNo, subparagraphNo);
        }

        // 按层级、款号、项号排序，确保返回完整的条文结构
        wrapper.orderByAsc(LegalProvision::getLevel, LegalProvision::getParagraphNo, LegalProvision::getSubparagraphNo);

        List<LegalProvision> provisions = this.list(wrapper);

        log.debug("查询法条内容: 法律={}, 条={}, 款={}, 项={}, 结果数量={}",
                 lawName, articleNo, paragraphNo, subparagraphNo, provisions.size());

        return provisions;
    }

    /**
     * 格式化法条内容，添加款、项标识
     *
     * @param provisions 法条列表
     * @return 格式化后的内容
     */
    private String formatProvisionContent(List<LegalProvision> provisions) {
        if (provisions == null || provisions.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        // 按条号分组
        Map<Integer, List<LegalProvision>> articleGroups = provisions.stream()
                .collect(Collectors.groupingBy(LegalProvision::getArticleNo));

        for (Map.Entry<Integer, List<LegalProvision>> articleEntry : articleGroups.entrySet()) {
            Integer articleNo = articleEntry.getKey();
            List<LegalProvision> articleProvisions = articleEntry.getValue();

            // 按层级和款号、项号排序
            articleProvisions.sort((p1, p2) -> {
                int levelCompare = Integer.compare(p1.getLevel(), p2.getLevel());
                if (levelCompare != 0) return levelCompare;

                int paragraphCompare = Integer.compare(
                    p1.getParagraphNo() != null ? p1.getParagraphNo() : 0,
                    p2.getParagraphNo() != null ? p2.getParagraphNo() : 0
                );
                if (paragraphCompare != 0) return paragraphCompare;

                return Integer.compare(
                    p1.getSubparagraphNo() != null ? p1.getSubparagraphNo() : 0,
                    p2.getSubparagraphNo() != null ? p2.getSubparagraphNo() : 0
                );
            });

            // 判断查询类型：是查询整条还是特定款/项
            boolean isFullArticleQuery = isFullArticleQuery(articleProvisions);

            if (isFullArticleQuery) {
                // 查询整条：显示所有款和项
                result.append(buildCompleteArticleStructure(articleProvisions));
            } else {
                // 查询特定款或项：只显示相关内容
                result.append(buildSpecificParagraphStructure(articleProvisions));
            }
        }

        return result.toString().trim();
    }

    /**
     * 构建完整的条文结构
     */
    private String buildCompleteArticleStructure(List<LegalProvision> provisions) {
        StringBuilder result = new StringBuilder();

        // 首先处理条级别内容（level=1且paragraph_no为null的记录）
        provisions.stream()
                .filter(p -> p.getLevel() == 1 && p.getParagraphNo() == null)
                .filter(p -> !isArticleTitle(p, p.getArticleNo())) // 过滤掉纯标题
                .forEach(provision -> {
                    result.append(provision.getContent()).append("\n");
                });

        // 然后处理款和项级别内容
        List<LegalProvision> contentProvisions = provisions.stream()
                .filter(p -> !(p.getLevel() == 1 && p.getParagraphNo() == null))
                .collect(Collectors.toList());

        // 按paragraph_no分组
        Map<Integer, List<LegalProvision>> paragraphGroups = contentProvisions.stream()
                .collect(Collectors.groupingBy(LegalProvision::getParagraphNo));

        // 按款号顺序处理（1, 2, 3, 4, 5）
        for (int paragraphNo = 1; paragraphNo <= 5; paragraphNo++) {
            if (paragraphGroups.containsKey(paragraphNo)) {
                final int currentParagraphNo = paragraphNo; // 创建final变量供lambda使用
                List<LegalProvision> paragraphProvisions = paragraphGroups.get(paragraphNo);

                // 处理款级别内容（level=2）
                paragraphProvisions.stream()
                        .filter(p -> p.getLevel() == 2)
                        .forEach(provision -> {
                            if (!provision.getContent().trim().equals("第" + convertToChineseNumber(currentParagraphNo) + "款")) {
                                // 有具体内容的款，直接显示内容，不加款标识
                                result.append(provision.getContent()).append("\n");
                            }
                        });

                // 处理该款下的项级别内容（level=3）
                List<LegalProvision> itemProvisions = paragraphProvisions.stream()
                        .filter(p -> p.getLevel() == 3)
                        .sorted(Comparator.comparing(LegalProvision::getSubparagraphNo))
                        .collect(Collectors.toList());

                if (itemProvisions.size() > 1) {
                    // 多个项，检查是否有公共前缀
                    String mergedContent = mergeItemContentsWithPrefix(itemProvisions);
                    result.append(mergedContent);
                } else {
                    // 单个项或无项，直接显示
                    itemProvisions.forEach(provision -> {
                        result.append(provision.getContent()).append("\n");
                    });
                }
            }
        }

        return result.toString();
    }

    /**
     * 判断是否为查询整条的情况
     */
    private boolean isFullArticleQuery(List<LegalProvision> provisions) {
        // 如果包含多个不同的paragraph_no，说明是查询整条
        Set<Integer> paragraphNos = provisions.stream()
                .map(LegalProvision::getParagraphNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        return paragraphNos.size() > 1;
    }

    /**
     * 构建特定款或项的结构
     */
    private String buildSpecificParagraphStructure(List<LegalProvision> provisions) {
        StringBuilder result = new StringBuilder();

        // 首先处理条级别内容（level=1且paragraph_no为null的记录）
        provisions.stream()
                .filter(p -> p.getLevel() == 1 && p.getParagraphNo() == null)
                .filter(p -> !isArticleTitle(p, p.getArticleNo())) // 过滤掉纯标题
                .forEach(provision -> {
                    result.append(provision.getContent()).append("\n");
                });

        // 然后处理款和项级别内容
        List<LegalProvision> contentProvisions = provisions.stream()
                .filter(p -> !(p.getLevel() == 1 && p.getParagraphNo() == null))
                .collect(Collectors.toList());

        // 按paragraph_no分组
        Map<Integer, List<LegalProvision>> paragraphGroups = contentProvisions.stream()
                .collect(Collectors.groupingBy(LegalProvision::getParagraphNo));

        // 按款号顺序处理
        paragraphGroups.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    Integer paragraphNo = entry.getKey();
                    List<LegalProvision> paragraphProvisions = entry.getValue();

                    // 处理款级别内容（level=2）
                    paragraphProvisions.stream()
                            .filter(p -> p.getLevel() == 2)
                            .forEach(provision -> {
                                String paragraphLabel = "第" + convertToChineseNumber(paragraphNo) + "款";
                                if (!provision.getContent().trim().equals(paragraphLabel)) {
                                    // 有具体内容的款，直接显示内容，不加款标识
                                    result.append(provision.getContent()).append("\n");
                                }
                            });

                    // 处理该款下的项级别内容（level=3）
                    List<LegalProvision> itemProvisions = paragraphProvisions.stream()
                            .filter(p -> p.getLevel() == 3)
                            .sorted(Comparator.comparing(LegalProvision::getSubparagraphNo))
                            .collect(Collectors.toList());

                    if (itemProvisions.size() > 1) {
                        // 多个项，检查是否有公共前缀
                        String mergedContent = mergeItemContentsWithPrefix(itemProvisions);
                        result.append(mergedContent);
                    } else {
                        // 单个项或无项，直接显示
                        itemProvisions.forEach(provision -> {
                            result.append(provision.getContent()).append("\n");
                        });
                    }
                });

        return result.toString();
    }

    /**
     * 判断是否为条标题内容
     */
    private boolean isArticleTitle(LegalProvision provision, Integer articleNo) {
        if (provision.getLevel() != 1 || provision.getParagraphNo() != null) {
            return false;
        }

        String content = provision.getContent();
        if (content == null) {
            return false;
        }

        // 如果内容就是"第X条"格式，则认为是条标题
        String expectedTitle = "第" + convertToChineseNumber(articleNo) + "条";
        return content.trim().equals(expectedTitle);
    }

    /**
     * 合并项级别内容，提取公共前缀并使用简洁编号
     */
    private String mergeItemContentsWithPrefix(List<LegalProvision> itemProvisions) {
        if (itemProvisions == null || itemProvisions.isEmpty()) {
            return "";
        }

        if (itemProvisions.size() == 1) {
            return itemProvisions.get(0).getContent() + "\n";
        }

        // 获取所有项的内容
        List<String> contents = itemProvisions.stream()
                .map(LegalProvision::getContent)
                .collect(Collectors.toList());

        // 查找公共前缀
        String commonPrefix = findCommonPrefix(contents);

        StringBuilder result = new StringBuilder();

        if (commonPrefix.length() > 0) {
            // 有公共前缀，先显示公共前缀
            result.append(commonPrefix).append("\n");

            // 然后显示各项的剩余内容
            for (int i = 0; i < itemProvisions.size(); i++) {
                LegalProvision provision = itemProvisions.get(i);
                String remainingContent = provision.getContent().substring(commonPrefix.length()).trim();
                String itemNumber = convertToChineseNumber(provision.getSubparagraphNo());
                result.append("（").append(itemNumber).append("）").append(remainingContent).append("\n");
            }
        } else {
            // 没有公共前缀，直接显示各项内容
            for (LegalProvision provision : itemProvisions) {
                result.append(provision.getContent()).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 查找字符串列表的公共前缀
     */
    private String findCommonPrefix(List<String> contents) {
        if (contents == null || contents.isEmpty()) {
            return "";
        }

        if (contents.size() == 1) {
            return "";
        }

        String first = contents.get(0);
        int minLength = contents.stream().mapToInt(String::length).min().orElse(0);

        StringBuilder commonPrefix = new StringBuilder();
        for (int i = 0; i < minLength; i++) {
            final int index = i; // 创建final变量供lambda使用
            char c = first.charAt(i);
            boolean allMatch = contents.stream().allMatch(s -> s.charAt(index) == c);
            if (allMatch) {
                commonPrefix.append(c);
            } else {
                break;
            }
        }

        String prefix = commonPrefix.toString();

        // 寻找最后一个冒号作为前缀结束点
        int lastColonIndex = prefix.lastIndexOf('：');
        if (lastColonIndex > 0) {
            return prefix.substring(0, lastColonIndex + 1);
        }

        // 如果没有找到冒号，且前缀长度小于10，不使用前缀
        if (prefix.length() < 10) {
            return "";
        }

        return prefix;
    }



    /**
     * 法条查询结果封装类
     */
    public static class LawQueryResult {
        private final List<String> validLaws;
        private final List<String> lawSummaries;

        public LawQueryResult(List<String> validLaws, List<String> lawSummaries) {
            this.validLaws = validLaws;
            this.lawSummaries = lawSummaries;
        }

        public List<String> getValidLaws() {
            return validLaws;
        }

        public List<String> getLawSummaries() {
            return lawSummaries;
        }
    }

    /**
     * 从法条引用中提取法律名称
     * 输入：中华人民共和国民法典第一千零六十二条
     * 输出：中华人民共和国民法典
     */
    private String extractLawName(String lawReference) {
        if (lawReference == null || lawReference.trim().isEmpty()) {
            return null;
        }

        try {
            // 匹配格式：法律名称第...条
            Pattern pattern = Pattern.compile("^(.+?)第.+条");
            Matcher matcher = pattern.matcher(lawReference.trim());

            if (matcher.find()) {
                return matcher.group(1).trim();
            }

            return null;
        } catch (Exception e) {
            log.error("提取法律名称失败: {}", lawReference, e);
            return null;
        }
    }





    /**
     * 阿拉伯数字转中文数字（完整版）
     */
    private String convertToChineseNumber(Integer number) {
        if (number == null) {
            return "";
        }

        if (number == 0) {
            return "零";
        }

        String[] units = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

        if (number < 10) {
            return units[number];
        } else if (number == 10) {
            return "十";
        } else if (number < 20) {
            return "十" + units[number - 10];
        } else if (number < 100) {
            int ten = number / 10;
            int unit = number % 10;
            return units[ten] + "十" + (unit == 0 ? "" : units[unit]);
        } else if (number < 1000) {
            // 处理百位数
            int hundred = number / 100;
            int remainder = number % 100;
            StringBuilder result = new StringBuilder(units[hundred] + "百");

            if (remainder == 0) {
                // 整百数
                return result.toString();
            } else if (remainder < 10) {
                // 百零几
                result.append("零").append(units[remainder]);
            } else {
                // 百几十几
                result.append(convertToChineseNumber(remainder));
            }
            return result.toString();
        } else if (number < 10000) {
            // 处理千位数
            int thousand = number / 1000;
            int remainder = number % 1000;
            StringBuilder result = new StringBuilder(units[thousand] + "千");

            if (remainder == 0) {
                // 整千数
                return result.toString();
            } else if (remainder < 100) {
                // 千零几十几或千零几
                result.append("零").append(convertToChineseNumber(remainder));
            } else {
                // 千几百几十几
                result.append(convertToChineseNumber(remainder));
            }
            return result.toString();
        }

        return number.toString(); // 超出范围直接返回数字
    }





    /**
     * 自动创建缺失的父级节点
     */
    private void createMissingParentNodes(String lawName, LegalProvisionParseUtil.ParseResult parseResult,
                                        List<LegalProvision> provisions, Set<String> createdNodes) {

        // 如果是款或项，需要确保条存在
        if (parseResult.getLevel() >= 2) {
            String articleKey = lawName + "_ARTICLE_" + parseResult.getArticleNo();
            if (!createdNodes.contains(articleKey)) {
                LegalProvision article = LegalProvision.builder()
                        .lawName(lawName)
                        .articleNo(parseResult.getArticleNo())
                        .content("第" + convertToChineseNumber(parseResult.getArticleNo()) + "条")
                        .level(1)
                        .build();
                provisions.add(article);
                createdNodes.add(articleKey);
                log.info("自动创建条: {}", article.getContent());
            }
        }

        // 如果是项，需要确保款存在
        if (parseResult.getLevel() == 3) {
            String paragraphKey = lawName + "_PARAGRAPH_" + parseResult.getArticleNo() + "_" + parseResult.getParagraphNo();
            if (!createdNodes.contains(paragraphKey)) {
                LegalProvision paragraph = LegalProvision.builder()
                        .lawName(lawName)
                        .articleNo(parseResult.getArticleNo())
                        .paragraphNo(parseResult.getParagraphNo())
                        .content("第" + convertToChineseNumber(parseResult.getParagraphNo()) + "款")
                        .level(2)
                        .build();
                provisions.add(paragraph);
                createdNodes.add(paragraphKey);
                log.info("自动创建款: 第{}条{}", parseResult.getArticleNo(), paragraph.getContent());
            }
        }
    }


}
