package com.smxz.yjzs.service;

import com.smxz.yjzs.entity.ModuleUpdateRecord;

import java.util.List;
import java.util.Map;

/**
 * 模块更新服务接口
 */
public interface ModuleUpdateService {

    /**
     * 标记多个模块需要更新
     * @param caseImportId 案件导入ID
     * @param moduleCodes 模块代码列表
     */
    void markModulesNeedUpdate(Long caseImportId, List<String> moduleCodes);

    /**
     * 标记单个模块需要更新
     * @param caseImportId 案件导入ID
     * @param moduleCode 模块代码
     */
    void markModuleNeedUpdate(Long caseImportId, String moduleCode);

    /**
     * 检查模块是否需要更新（原方法，基于has_update字段）
     * @param caseImportId 案件导入ID
     * @param moduleCode 模块代码
     * @return true表示需要更新，false表示不需要更新
     */
    boolean checkModuleNeedUpdate(Long caseImportId, String moduleCode);

    /**
     * 检查特定模块是否需要更新（新方法，基于operate_module字段）
     * @param caseImportId 案件导入ID
     * @param sourceModule 源模块代码（要查询的记录）
     * @param operatedModule 目标操作模块代码（检查是否已在operate_module中）
     * @return true表示需要更新，false表示不需要更新
     */
    boolean checkModuleNeedUpdateNew(Long caseImportId, String sourceModule, String operatedModule);

    /**
     * 标记模块已操作（添加到operate_module字段）
     * @param caseImportId 案件导入ID
     * @param sourceModule 源模块代码（触发更新的模块）
     * @param operatedModule 已操作的模块代码（被点击的模块）
     */
    void markModuleAsOperated(Long caseImportId, String sourceModule, String operatedModule);

    /**
     * 重置模块更新状态（原方法，设置has_update为0）
     * @param caseImportId 案件导入ID
     * @param moduleCode 模块代码
     */
    void resetModuleStatus(Long caseImportId, String moduleCode);

    /**
     * 批量重置模块更新状态
     * @param caseImportId 案件导入ID
     * @param moduleCodes 模块代码列表，如果为空则重置所有模块
     */
    void batchResetModuleStatus(Long caseImportId, List<String> moduleCodes);

    /**
     * 获取案件所有模块的更新状态（原方法）
     * @param caseImportId 案件导入ID
     * @return 模块代码到更新状态的映射，true表示需要更新
     */
    Map<String, Boolean> getAllModuleStatus(Long caseImportId);

    /**
     * 获取案件特定模块的更新状态（新方法，基于operate_module字段）
     * @param caseImportId 案件导入ID
     * @param currentModule 当前查询的模块代码
     * @return 模块代码到更新状态的映射，true表示需要更新
     */
    Map<String, Boolean> getAllModuleStatusNew(Long caseImportId, String currentModule);

    /**
     * 获取需要更新的模块列表
     * @param caseImportId 案件导入ID
     * @return 需要更新的模块代码列表
     */
    List<String> getNeedUpdateModules(Long caseImportId);

    /**
     * 获取案件的所有模块更新记录
     * @param caseImportId 案件导入ID
     * @return 模块更新记录列表
     */
    List<ModuleUpdateRecord> getModuleUpdateRecords(Long caseImportId);
}
