package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.common.utils.FileRecordUtils;
import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.EvidenceFactsDetails;
import com.smxz.yjzs.entity.EvidenceOverview;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.EvidenceFactsDetailsMapper;
import com.smxz.yjzs.mapper.EvidenceOverviewMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 证据详情表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
public class EvidenceFactsDetailsService extends ServiceImpl<EvidenceFactsDetailsMapper, EvidenceFactsDetails> {

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private FileRecordUtils fileRecordUtils;

    @Autowired
    @Lazy
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private EvidenceOverviewMapper evidenceOverviewMapper;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    // 提取规则内部类
    private static class ExtractionRule {
        private final String startPattern;
        private final String endPattern;
        private final String description;

        public ExtractionRule(String startPattern, String endPattern, String description) {
            this.startPattern = startPattern;
            this.endPattern = endPattern;
            this.description = description;
        }

        public String getStartPattern() { return startPattern; }
        public String getEndPattern() { return endPattern; }
        public String getDescription() { return description; }
    }

    // 案件类型与提取规则的映射
    private static final Map<String, ExtractionRule> CASE_TYPE_RULES = new HashMap<>();

    // 文件分隔符模式，用于清理文本 - 匹配所有 ===xxx=== 格式的内容
    private static final Pattern FILE_SEPARATOR_PATTERN = Pattern.compile("===.*?===");

    // 开头冒号模式，用于去除开头的中文或英文冒号
    private static final Pattern LEADING_COLON_PATTERN = Pattern.compile("^[：:]+\\s*");

    static {
        // 一审案件提取规则
        CASE_TYPE_RULES.put(CaseTypeConstants.AjlxdmCode.MSYS, new ExtractionRule(
            "本院.*?事实如下",
            "本院认为",
            "一审案件事实认定提取"
        ));

        // 二审案件提取规则（支持不同Unicode编码的"一"字）
        CASE_TYPE_RULES.put(CaseTypeConstants.AjlxdmCode.MSES, new ExtractionRule(
            "(?:.*法院查明|一审法院查明|一审法院认定事实)",
            "本院认为",
            "二审案件事实认定提取"
        ));
    }


    /**
     * 生成案件事实认定（异步方法，使用@Async）
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.CASE_FACT, description = "案件事实认定提取")
    public void generate(Long caseImportId) {
        executeAnalysisTaskInternal(caseImportId);
    }

    /**
     * 执行案件事实认定分析任务（内部实现，纯业务逻辑）
     * @param caseImportId 案件导入ID
     */
    private void executeAnalysisTaskInternal(Long caseImportId) {
        // 1. 获取文件记录
        List<FileUploadRecord> fileRecords = getFileRecords(caseImportId);
        if (fileRecords.isEmpty()) {
            return;
        }

        // 2. 构建文本内容
        String allFileContents = buildFormattedFileContents(fileRecords,caseImportId);
        if (allFileContents.isEmpty()) {
            return;
        }

        // 3. 提取事实认定内容
        String extractedFacts = extractFactsFromText(caseImportId, allFileContents);

        // 4. 保存到evidence_facts_details表的determineFacts字段
        if (StringUtils.isNotBlank(extractedFacts)) {
            saveOrUpdateDetermineFacts(caseImportId, extractedFacts);
            log.info("案件事实认定提取并保存成功，内容长度: {}", extractedFacts.length());
        } else {
            log.warn("未能提取到事实认定内容");
        }
    }

    /**
     * 根据案件ID查询证据详情信息
     *
     * @param caseImportId 案件导入ID
     * @return 证据详情信息
     */
    public EvidenceFactsDetails getByCaseImportId(Long caseImportId) {
        log.info("查询案件证据详情信息，caseImportId: {}", caseImportId);
        LambdaQueryWrapper<EvidenceFactsDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvidenceFactsDetails::getCaseImportId, caseImportId)
                   .orderByDesc(EvidenceFactsDetails::getCreateTime)
                   .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }



    /**
     * 创建证据详情信息
     *
     * @param EvidenceFactsDetails 证据详情信息
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createEvidenceFactsDetails(EvidenceFactsDetails EvidenceFactsDetails) {
        log.info("创建证据详情信息，案件ID: {}", EvidenceFactsDetails.getCaseImportId());

        // 设置创建时间
        EvidenceFactsDetails.setCreateTime(LocalDateTime.now());
        EvidenceFactsDetails.setUpdateTime(LocalDateTime.now());

        boolean result = this.save(EvidenceFactsDetails);

        if (result) {
            log.info("证据详情信息创建成功，ID: {}", EvidenceFactsDetails.getId());
        } else {
            log.error("证据详情信息创建失败");
        }

        return result;
    }

    /**
     * 更新证据详情信息
     *
     * @param EvidenceFactsDetails 证据详情信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEvidenceFactsDetails(EvidenceFactsDetails EvidenceFactsDetails) {
        log.info("更新证据详情信息，ID: {}", EvidenceFactsDetails.getId());

        // 设置更新时间
        EvidenceFactsDetails.setUpdateTime(LocalDateTime.now());

        boolean result = this.updateById(EvidenceFactsDetails);

        if (result) {
            log.info("证据详情信息更新成功，ID: {}", EvidenceFactsDetails.getId());
        } else {
            log.error("证据详情信息更新失败，ID: {}", EvidenceFactsDetails.getId());
        }

        return result;
    }

    /**
     * 根据ID删除证据详情信息（逻辑删除）
     *
     * @param id 证据详情信息ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEvidenceFactsDetails(Long id) {
        log.info("删除证据详情信息，ID: {}", id);

        boolean result = this.removeById(id);

        if (result) {
            log.info("证据详情信息删除成功，ID: {}", id);
        } else {
            log.error("证据详情信息删除失败，ID: {}", id);
        }

        return result;
    }

    /**
     * 批量删除案件相关的证据详情信息
     *
     * @param caseImportId 案件导入ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCaseImportId(Long caseImportId) {
        log.info("批量删除案件相关的证据详情信息，caseImportId: {}", caseImportId);

        LambdaQueryWrapper<EvidenceFactsDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvidenceFactsDetails::getCaseImportId, caseImportId);

        boolean result = this.remove(queryWrapper);

        if (result) {
            log.info("案件相关证据详情信息批量删除成功，caseImportId: {}", caseImportId);
        } else {
            log.error("案件相关证据详情信息批量删除失败，caseImportId: {}", caseImportId);
        }

        return result;
    }

    /**
     * 清除证据详情旧数据（逻辑删除）
     */
    public void clearEvidenceFactsDetailsData(Long caseImportId) {
        try {
            log.info("开始清除证据详情旧数据，caseImportId: {}", caseImportId);

            // 先查询现有数据数量（只查询未删除的记录）
            LambdaQueryWrapper<EvidenceFactsDetails> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(EvidenceFactsDetails::getCaseImportId, caseImportId);
            long oldCount = this.count(countWrapper);
            log.info("案件现有证据详情记录数量: {}", oldCount);

            if (oldCount > 0) {
                // 使用批量更新方式执行逻辑删除
                LambdaUpdateWrapper<EvidenceFactsDetails> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EvidenceFactsDetails::getCaseImportId, caseImportId)
                           .set(EvidenceFactsDetails::getDeleted, 1)
                           .set(EvidenceFactsDetails::getUpdateTime, LocalDateTime.now());

                boolean updateResult = this.update(updateWrapper);

                if (updateResult) {
                    log.info("逻辑删除证据详情旧数据完成，caseImportId: {}, 影响记录数: {}", caseImportId, oldCount);
                } else {
                    log.warn("逻辑删除证据详情旧数据可能失败，caseImportId: {}", caseImportId);
                }
            } else {
                log.info("案件没有旧的证据详情数据，无需清除，caseImportId: {}", caseImportId);
            }

        } catch (Exception e) {
            log.error("清除证据详情旧数据失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("清除证据详情旧数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存或更新认定事实到evidence_facts_details表
     */
    private EvidenceFactsDetails saveOrUpdateDetermineFacts(Long caseImportId, String determineFacts) {
        try {
            // 查询是否已存在记录
            EvidenceFactsDetails existingDetails = getByCaseImportId(caseImportId);

            if (existingDetails != null) {
                // 更新现有记录的认定事实字段
                existingDetails.setDetermineFactsExtract(determineFacts);
                existingDetails.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetails);
                log.info("更新证据详情记录的认定事实成功，案件ID: {}, 内容长度: {}", caseImportId, determineFacts.length());
                return existingDetails;
            } else {
                // 新增记录，只设置认定事实字段
                EvidenceFactsDetails newDetails = EvidenceFactsDetails.builder()
                        .caseImportId(caseImportId)
                        .determineFactsExtract(determineFacts)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                this.save(newDetails);
                log.info("新增证据详情记录的认定事实成功，案件ID: {}, 内容长度: {}", caseImportId, determineFacts.length());
                return newDetails;
            }
        } catch (Exception e) {
            log.error("保存认定事实记录失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("保存认定事实记录失败", e);
        }
    }

    /**
     * 获取案件文件记录（过滤逻辑删除的记录）
     */
    private List<FileUploadRecord> getFileRecords(Long caseImportId) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .eq(FileUploadRecord::getDeleted, 0); // 过滤逻辑删除的记录
        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.debug("获取案件文件记录，caseImportId: {}, 有效文件数量: {}", caseImportId, records.size());
        return records;
    }

    /**
     * 构建格式化的文件内容
     */
    private String buildFormattedFileContents(List<FileUploadRecord> fileRecords,Long caseImportId) {
        List<String> formattedContents = new ArrayList<>();
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        for (FileUploadRecord file : fileRecords) {
            // 只处理文件名包含"裁定书"或"判决书"的文件
            if (file.getFileName() != null &&
                (file.getFileName().contains("裁定书") || file.getFileName().contains("判决书"))) {

                log.info("找到裁定书文件: {}", file.getFileName());

                // 优先从OCR结果提取文本
                String extractedText = extractTextFromOcrResult(file);

                // 如果OCR结果为空，尝试使用POI读取Word文档
                if (StringUtils.isBlank(extractedText)) {
                    extractedText = extractTextFromWordDocument(file);
                }

                // 如果还是为空，使用原有的extractedText字段
                if (StringUtils.isBlank(extractedText) &&
                    file.getExtractedText() != null && !file.getExtractedText().trim().isEmpty()) {
                    extractedText = file.getExtractedText();
                }

                if (StringUtils.isNotBlank(extractedText)) {
                    formattedContents.add(extractedText);
                    log.info("成功提取文件内容，文件: {}, 内容长度: {}", file.getFileName(), extractedText.length());
                } else {
                    log.warn("无法提取文件内容，文件: {}", file.getFileName());
                }
            }
        }

        if (formattedContents.isEmpty()) {
            log.warn("未找到任何裁定书或判决书文件内容");
            return "";
        }

        return String.join("\n\n=== 文件分隔符 ===\n\n", formattedContents);
    }

    /**
     * 从OCR结果中提取文本内容
     */
    private String extractTextFromOcrResult(FileUploadRecord file) {
        try {
            if (file.getOcrResult() == null || file.getOcrResult().isEmpty()) {
                log.debug("文件 {} 的OCR结果为空", file.getFileName());
                return null;
            }

            StringBuilder allContent = new StringBuilder();

            // 遍历所有OCR结果页面
            for (Object ocrResultObj : file.getOcrResult()) {
                try {
                    // 将OcrResult对象转换为JsonNode进行处理
                    JsonNode pageNode = objectMapper.valueToTree(ocrResultObj);

                    JsonNode blocksNode = pageNode.get("blocks");
                    if (blocksNode != null && blocksNode.isArray()) {
                        // 遍历页面中的所有块
                        for (JsonNode blockNode : blocksNode) {
                            JsonNode blockContentNode = blockNode.get("blockContent");
                            if (blockContentNode != null && !blockContentNode.isNull()) {
                                String blockContent = blockContentNode.asText();
                                if (StringUtils.isNotBlank(blockContent)) {
                                    allContent.append(blockContent).append("\n");
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理OCR结果页面失败，文件: {}, 错误: {}", file.getFileName(), e.getMessage());
                    continue;
                }
            }

            String result = allContent.toString().trim();
            if (StringUtils.isNotBlank(result)) {
                log.info("从OCR结果提取文本成功，文件: {}, 内容长度: {}", file.getFileName(), result.length());
                return result;
            } else {
                log.debug("OCR结果中未找到有效的文本内容，文件: {}", file.getFileName());
                return null;
            }

        } catch (Exception e) {
            log.error("解析OCR结果失败，文件: {}", file.getFileName(), e);
            return null;
        }
    }

    /**
     * 从Word文档中提取文本内容
     */
    private String extractTextFromWordDocument(FileUploadRecord file) {
        if (file.getFilePath() == null || !file.getFilePath().toLowerCase().endsWith(".docx")) {
            log.debug("文件不是Word文档或路径为空，跳过POI提取，文件: {}", file.getFileName());
            return null;
        }

        StringBuilder content = new StringBuilder();

        try {
            log.debug("尝试使用POI从MinIO提取Word文档文本，文件: {}", file.getFileName());

            // 从MinIO下载文件并使用POI处理
            try (GetObjectResponse minioObject = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(file.getFilePath())
                            .build());
                 InputStream inputStream = minioObject;
                 XWPFDocument document = new XWPFDocument(inputStream)) {

                // 提取所有段落的文本
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    String paragraphText = paragraph.getText();
                    if (StringUtils.isNotBlank(paragraphText)) {
                        content.append(paragraphText).append("\n");
                    }
                }

                String result = content.toString().trim();
                if (StringUtils.isNotBlank(result)) {
                    log.info("使用POI从MinIO提取Word文档文本成功，文件: {}, 内容长度: {}", file.getFileName(), result.length());
                    return result;
                } else {
                    log.debug("Word文档中未找到有效的文本内容，文件: {}", file.getFileName());
                    return null;
                }

            }
        } catch (IOException e) {
            log.warn("使用POI提取Word文档文本失败，文件: {}, 错误: {}", file.getFileName(), e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("提取Word文档文本时发生未知错误，文件: {}", file.getFileName(), e);
            return null;
        }
    }

    /**
     * 从文本中提取事实认定内容
     */
    private String extractFactsFromText(Long caseImportId, String allFileContents) {
        try {
            CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
            // 2. 获取提取规则
            ExtractionRule rule = CASE_TYPE_RULES.get(caseRecord.getAjlxdm());
            if (rule == null) {
                log.warn("未找到案件类型 {} 对应的提取规则", caseRecord.getAjlxdm());
                return null;
            }

            // 3. 检查是否有裁定书内容
            if (StringUtils.isBlank(allFileContents)) {
                log.warn("未找到裁定书内容");
                return null;
            }

            // 4. 清理文本格式
            String cleanedContent = cleanTextContent(allFileContents);

            // 5. 使用规则提取事实认定
            return extractFactsUsingRule(cleanedContent, rule);

        } catch (Exception e) {
            log.error("从文本中提取事实认定内容失败", e);
            return null;
        }
    }
    /**
     * 清理文本内容，去除文件分隔符等格式信息
     */
    private String cleanTextContent(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }

        // 去除文件ID、文件名、页码等格式信息
        String cleaned = FILE_SEPARATOR_PATTERN.matcher(content).replaceAll("");

        // 去除多余的空行
        cleaned = cleaned.replaceAll("\n{3,}", "\n\n");

        return cleaned.trim();
    }

    /**
     * 去除文本开头的中文或英文冒号
     * @param text 待处理的文本
     * @return 去除开头冒号后的文本
     */
    private String removeLeadingColons(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        // 使用正则表达式去除开头的中文冒号（：）或英文冒号（:），可能有多个，后面可能跟空格
        String result = LEADING_COLON_PATTERN.matcher(text).replaceFirst("");

        log.debug("去除开头冒号：原文本长度={}, 处理后长度={}", text.length(), result.length());
        return result;
    }

    /**
     * 获取一审判决书内容
     */
    private String getFirstInstanceJudgmentContent(Long caseImportId) {
        List<String> contents = new ArrayList<>();

        // 根据DocumentType获取一审判决书
        List<FileUploadRecord> documentTypeRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId,
                List.of(DocumentType.YSPJS));

        for (FileUploadRecord file : documentTypeRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                contents.add(content);
                log.info("根据DocumentType提取一审判决书内容成功，文件: {}, 内容长度: {}", file.getFileName(), content.length());
            }
        }

        if (contents.isEmpty()) {
            log.warn("未找到一审判决书文件，caseImportId: {}", caseImportId);
            return "";
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("一审判决书内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }

    /**
     * 获取上诉状内容
     */
    private String getAppealContent(Long caseImportId) {
        List<String> contents = new ArrayList<>();

        // 根据DocumentType获取上诉状
        List<FileUploadRecord> documentTypeRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId,
                List.of(DocumentType.SSZ));

        for (FileUploadRecord file : documentTypeRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                contents.add(content);
                log.info("根据DocumentType提取上诉状内容成功，文件: {}, 内容长度: {}", file.getFileName(), content.length());
            }
        }

        if (contents.isEmpty()) {
            log.warn("未找到上诉状文件，caseImportId: {}", caseImportId);
            return "";
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("上诉状内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }

    /**
     * 获取二审答辩状内容
     */
    private String getSecondInstanceDefenseContent(Long caseImportId) {
        List<String> contents = new ArrayList<>();

        // 根据DocumentType获取二审答辩状
        List<FileUploadRecord> documentTypeRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId,
                List.of(DocumentType.DBZ));

        for (FileUploadRecord file : documentTypeRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                contents.add(content);
                log.info("根据DocumentType提取二审答辩状内容成功，文件: {}, 内容长度: {}", file.getFileName(), content.length());
            }
        }

        if (contents.isEmpty()) {
            log.warn("未找到二审答辩状文件，caseImportId: {}", caseImportId);
            return "";
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("二审答辩状内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }

    /**
     * 使用规则提取事实认定内容（支持多段落提取）
     */
    private String extractFactsUsingRule(String fullContent, ExtractionRule rule) {
        try {
            Pattern startPattern = Pattern.compile(rule.getStartPattern(), Pattern.CASE_INSENSITIVE);
            Pattern endPattern = Pattern.compile(rule.getEndPattern(), Pattern.CASE_INSENSITIVE);

            List<String> extractedSegments = new ArrayList<>();
            int searchStartIndex = 0;

            // 循环查找所有满足条件的段落
            while (searchStartIndex < fullContent.length()) {
                Matcher startMatcher = startPattern.matcher(fullContent);
                if (!startMatcher.find(searchStartIndex)) {
                    // 没有找到更多的开始标记，退出循环
                    break;
                }

                int startIndex = startMatcher.end();

                Matcher endMatcher = endPattern.matcher(fullContent);
                if (!endMatcher.find(startIndex)) {
                    // 找到开始标记但没有找到对应的结束标记，记录警告并退出
                    log.warn("找到开始标记但未找到对应的结束标记，位置: {}", startMatcher.start());
                    break;
                }

                int endIndex = endMatcher.start();

                // 提取这一段内容
                String segment = fullContent.substring(startIndex, endIndex).trim();
                if (StringUtils.isNotBlank(segment)) {
                    // 去除开头的中文或英文冒号
                    segment = removeLeadingColons(segment);
                    extractedSegments.add(segment);
                    log.debug("提取到事实认定段落，长度: {}", segment.length());
                }

                // 更新搜索起始位置，从当前结束标记之后开始
                searchStartIndex = endMatcher.end();
            }

            if (extractedSegments.isEmpty()) {
                log.warn("未找到任何满足条件的事实认定内容，开始标记: {}, 结束标记: {}",
                        rule.getStartPattern(), rule.getEndPattern());
                return null;
            }

            // 将所有段落组合在一起
            String combinedContent = String.join("\n\n", extractedSegments);
            log.info("成功提取事实认定内容，规则: {}, 段落数: {}, 总长度: {}",
                    rule.getDescription(), extractedSegments.size(), combinedContent.length());

            return combinedContent;
        } catch (Exception e) {
            log.error("使用规则提取内容失败: {}", rule.getDescription(), e);
            return null;
        }
    }


    /**
     * 构建二审证据内容
     */
    private String buildSecondInstanceEvidenceContent(Long caseImportId) {
        try {
            log.info("开始构建二审证据内容，caseImportId: {}", caseImportId);

            // 获取证据概况信息
            List<EvidenceOverview> evidenceList = evidenceOverviewMapper.listByCaseImportId(caseImportId);
            if (evidenceList.isEmpty()) {
                log.warn("未找到证据概况信息，caseImportId: {}", caseImportId);
                return "";
            }

            StringBuilder evidenceBuilder = new StringBuilder();

            for (EvidenceOverview evidence : evidenceList) {
                if (StringUtils.isNotBlank(evidence.getEvidenceName())) {
                    evidenceBuilder.append("证据名称：").append(evidence.getEvidenceName()).append("\n");

                    // 优先使用证据摘要，如果没有则使用证据目的
                    String evidenceContent = "";
                    if (StringUtils.isNotBlank(evidence.getEvidenceSummary())) {
                        evidenceContent = evidence.getEvidenceSummary();
                    } else if (StringUtils.isNotBlank(evidence.getEvidencePurpose())) {
                        evidenceContent = evidence.getEvidencePurpose();
                    }

                    evidenceBuilder.append("证据内容：").append(evidenceContent).append("\n\n");
                }
            }

            String result = evidenceBuilder.toString().trim();
            log.info("二审证据内容构建完成，caseImportId: {}, 证据数量: {}, 内容长度: {}",
                    caseImportId, evidenceList.size(), result.length());

            return result;

        } catch (Exception e) {
            log.error("构建二审证据内容失败，caseImportId: {}", caseImportId, e);
            return "";
        }
    }


    /**
     * 生成无争议事实（异步方法，使用@Async）
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.UNDISPUTED_FACTS, description = "无争议事实生成")
    public void generateUndisputedFacts(Long caseImportId) {
        boolean isSecondInstance = caseImportRecordService.isSecondInstanceCase(caseImportId);
        executeUndisputedFactsByAgent(caseImportId,isSecondInstance);
    }

    /**
     * 使用Agent执行无争议事实分析任务
     * @param caseImportId 案件导入ID
     */
    @Transactional(rollbackFor = Exception.class)
    private void executeUndisputedFactsByAgent(Long caseImportId,boolean isSecondInstance) {
        log.info("开始执行无争议事实分析任务，caseImportId: {}", caseImportId);
        long startTime = System.currentTimeMillis();

        String bootYmlName = isSecondInstance ? "second_undisputed_facts_task.yml" : "undisputed_facts_task.yml";
        String agentName = isSecondInstance ? "星洲-二审无争议事实Agent" : "星洲-一审无争议事实Agent";

        try {
            // 1. 获取文件记录（起诉状/答辩状）
            List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, List.of(DocumentType.QSZ, DocumentType.DBZ));

            log.info("获取文件记录数量: {}", fileRecords.size());
            if (fileRecords.isEmpty()) {
                throw new IllegalStateException("未找到起诉状或答辩状文件记录");
            }

            // 2. 构建文本内容
            String fileContents = buildUndisputedFactsContents(fileRecords);
            if (fileContents.isEmpty()) {
                throw new IllegalStateException("未找到提取的起诉状或答辩状文本内容");
            }

            // 3. 使用Agent生成无争议事实
            String res = agentTaskUtils.executeTask(agentName, "main", bootYmlName, fileContents);
            log.info("Agent返回结果: {}", res);

            long duration = System.currentTimeMillis() - startTime;

            if (StringUtils.isNotBlank(res)) {
                saveOrUpdateUndisputedFacts(caseImportId, res);
                log.info("无争议事实生成完成，耗时: {}ms，共生成事实:{}", duration, res);
            } else {
                saveOrUpdateUndisputedFacts(caseImportId, "未能提取到无争议事实内容");
                log.warn("生成的无争议事实内容为空");
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("无争议事实分析任务执行失败，caseImportId: {}, 耗时: {}ms, 错误: {}",
                    caseImportId, duration, e.getMessage(), e);
            throw new RuntimeException("无争议事实分析任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存或更新无争议事实到evidence_facts_details表
     */
    @Transactional(rollbackFor = Exception.class)
    public EvidenceFactsDetails saveOrUpdateUndisputedFacts(Long caseImportId, String undisputedFacts) {
        try {
            // 查询是否已存在记录
            EvidenceFactsDetails existingDetails = getByCaseImportId(caseImportId);

            if (existingDetails != null) {
                // 更新现有记录的无争议事实字段
                existingDetails.setUndisputedFacts(undisputedFacts);
                existingDetails.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetails);
                log.info("更新证据详情记录的无争议事实成功，案件ID: {}, 内容长度: {}", caseImportId, undisputedFacts.length());
                return existingDetails;
            } else {
                // 新增记录，只设置无争议事实字段
                EvidenceFactsDetails newDetails = EvidenceFactsDetails.builder()
                        .caseImportId(caseImportId)
                        .undisputedFacts(undisputedFacts)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                this.save(newDetails);
                log.info("新增证据详情记录的无争议事实成功，案件ID: {}, 内容长度: {}", caseImportId, undisputedFacts.length());
                return newDetails;
            }
        } catch (Exception e) {
            log.error("保存无争议事实记录失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("保存无争议事实记录失败", e);
        }
    }

    /**
     * 构建无争议事实相关的文件内容（起诉意见、答辩意见）
     */
    private String buildUndisputedFactsContents(List<FileUploadRecord> fileRecords) {
        List<String> formattedContents = new ArrayList<>();

        for (FileUploadRecord file : fileRecords) {
            // 只处理文件名包含"起诉状"或"答辩状"的文件
            if (file.getFileName() != null &&
                    (file.getFileName().contains("起诉状") || file.getFileName().contains("起诉意见") || file.getFileName().contains("答辩状") || file.getFileName().contains("答辩意见"))) {

                log.info("找到起诉状或答辩状文件: {}", file.getFileName());

                // 优先从OCR结果提取文本
                String extractedText = extractTextFromOcrResult(file);

                // 如果OCR结果为空，尝试使用POI读取Word文档
                if (StringUtils.isBlank(extractedText)) {
                    extractedText = extractTextFromWordDocument(file);
                }

                // 如果还是为空，使用原有的extractedText字段
                if (StringUtils.isBlank(extractedText) &&
                        file.getExtractedText() != null && !file.getExtractedText().trim().isEmpty()) {
                    extractedText = file.getExtractedText();
                }

                if (StringUtils.isNotBlank(extractedText)) {
                    // 为起诉状和答辩状添加文件类型标识
                    String fileType = file.getFileName().contains("起诉状") ? "起诉状" : "答辩状";
                    String labeledContent = String.format("=== %s：%s ===\n%s", fileType, file.getFileName(), extractedText);
                    formattedContents.add(labeledContent);
                    log.info("成功提取文件内容，文件: {}, 类型: {}, 内容长度: {}", file.getFileName(), fileType, extractedText.length());
                } else {
                    log.warn("无法提取文件内容，文件: {}", file.getFileName());
                }
            }
        }

        if (formattedContents.isEmpty()) {
            log.warn("未找到任何起诉状或答辩状文件内容");
            return "";
        }

        return String.join("\n\n=== 文件分隔符 ===\n\n", formattedContents);
    }

    /**
     * 验证生成认定事实的前置条件
     * @param caseImportId 案件导入ID
     * @throws IllegalStateException 当前置条件不满足时抛出异常
     */
    public void validateDetermineFactsPrerequisites(Long caseImportId) {
        // 目前不需要特殊的前置条件验证
        // 可以在这里添加其他必要的验证逻辑
        log.info("认定事实生成前置条件验证通过，caseImportId: {}", caseImportId);
    }

    /**
     * 生成认定事实
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.DETERMINE_FACTS, description = "生成认定事实")
    public void generateDetermineFacts(Long caseImportId) {
        executeDetermineFactsAnalysisTask(caseImportId);
    }

    @Async
    @AnalysisTask(taskType = TaskType.DETERMINE_FACTS, description = "生成认定事实")
    public void generateDetermineFactsAsync(Long caseImportId) {
        executeDetermineFactsAnalysisTask(caseImportId);
    }

    /**
     * 执行认定事实分析任务
     * @param caseImportId 案件导入ID
     */
    private void executeDetermineFactsAnalysisTask(Long caseImportId) {
        // 1. 判断案件类型
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        String ajlxdm = caseRecord != null ? caseRecord.getAjlxdm() : "";

        // 2. 调用Agent生成认定事实
        String determineFacts = generateDetermineFactsWithAgent(caseImportId, ajlxdm);

        // 3. 保存到evidence_facts_details表的determineFacts字段
        if (StringUtils.isNotBlank(determineFacts)) {
            saveOrUpdateDetermineFactsAI(caseImportId, determineFacts);
            log.info("认定事实生成并保存成功，内容长度: {}", determineFacts.length());
        } else {
            throw new IllegalStateException("Agent未能生成有效的认定事实内容");
        }
    }

    /**
     * 构建证据名称及内容
     */
    private String buildEvidenceContent(Long caseImportId) {
        try {
            StringBuilder evidenceBuilder = new StringBuilder();

            // 获取证据概览数据
            List<EvidenceOverview> evidenceList = evidenceOverviewMapper.listByCaseImportId(caseImportId)
                    .stream()
                    .filter(EvidenceOverview::getAdopted)
                    .map(response -> {
                        EvidenceOverview evidence = new EvidenceOverview();
                        evidence.setEvidenceName(response.getEvidenceName());
                        evidence.setEvidenceSummary(response.getEvidenceSummary());
                        return evidence;
                    })
                    .toList();

            // 按照要求的格式拼接证据信息
            for (EvidenceOverview evidence : evidenceList) {
                String evidenceName = StringUtils.isNotBlank(evidence.getEvidenceName()) ? evidence.getEvidenceName() : "无";
                String evidenceSummary = StringUtils.isNotBlank(evidence.getEvidenceSummary()) ? evidence.getEvidenceSummary() : "无";

                evidenceBuilder.append("证据名称：").append(evidenceName).append("\n");
                evidenceBuilder.append("证据内容：").append(evidenceSummary).append("\n");
            }

            String result = evidenceBuilder.toString();
            log.info("构建证据内容完成，证据数量: {}, 内容长度: {}", evidenceList.size(), result.length());
            return result;

        } catch (Exception e) {
            log.error("构建证据内容失败，caseImportId: {}", caseImportId, e);
            return ""; // 返回空字符串而不是抛出异常，避免影响整个流程
        }
    }

    /**
     * 构建认定事实的用户消息（保留用于兼容）
     */
    private String buildDetermineFactsUserMessage(Long caseImportId) {
        try {
            StringBuilder userMessage = new StringBuilder();

            // 1. 获取起诉状内容（包括补充起诉状）
            String plaintiffOpinion = getPlaintiffOpinionByDocumentType(caseImportId);
            userMessage.append("起诉意见：").append(StringUtils.isNotBlank(plaintiffOpinion) ? plaintiffOpinion : "无").append("\n");

            // 2. 获取答辩状内容（包括补充答辩状）
            String defendantOpinion = getDefendantOpinionByDocumentType(caseImportId);
            userMessage.append("答辩意见：").append(StringUtils.isNotBlank(defendantOpinion) ? defendantOpinion : "无").append("\n");

            // 3. 获取开庭笔录内容
            String courtRecord = getCourtRecordByDocumentType(caseImportId);
            userMessage.append("庭审笔录：").append(StringUtils.isNotBlank(courtRecord) ? courtRecord : "无").append("\n");

            // 4. 获取无争议事实
            EvidenceFactsDetails evidenceDetails = getByCaseImportId(caseImportId);
            String undisputedFacts = evidenceDetails != null ? evidenceDetails.getUndisputedFacts() : null;
            userMessage.append(undisputedFacts).append("\n");

            // 5. 获取法官意见
            String dictum = evidenceDetails != null ? evidenceDetails.getDictum() : null;
            userMessage.append("法官意见：").append(StringUtils.isNotBlank(dictum) ? dictum : "无");

            String result = userMessage.toString();
            log.info("构建认定事实用户消息完成，消息长度: {}", result.length());
            return result;

        } catch (Exception e) {
            log.error("构建认定事实用户消息失败，caseImportId: {}", caseImportId, e);
            throw new RuntimeException("构建认定事实用户消息失败", e);
        }
    }

    /**
     * 从文件记录列表中获取起诉状内容
     */
    private String getPlaintiffOpinionFromRecords(List<FileUploadRecord> fileRecords) {
        // 这个方法保留用于兼容，但建议使用基于DocumentType的方法
        for (FileUploadRecord file : fileRecords) {
            if (file.getFileName() != null && file.getFileName().contains("起诉状")) {
                return extractFileContent(file);
            }
        }
        return null;
    }

    /**
     * 从文件记录列表中获取答辩状内容
     */
    private String getDefendantOpinionFromRecords(List<FileUploadRecord> fileRecords) {
        // 这个方法保留用于兼容，但建议使用基于DocumentType的方法
        for (FileUploadRecord file : fileRecords) {
            if (file.getFileName() != null && file.getFileName().contains("答辩状")) {
                return extractFileContent(file);
            }
        }
        return null;
    }

    /**
     * 从文件记录列表中获取开庭笔录内容
     */
    private String getCourtRecordFromRecords(List<FileUploadRecord> fileRecords) {
        // 这个方法保留用于兼容，但建议使用基于DocumentType的方法
        for (FileUploadRecord file : fileRecords) {
            if (file.getFileName() != null && (file.getFileName().contains("开庭笔录") || file.getFileName().contains("庭审笔录"))) {
                return extractFileContent(file);
            }
        }
        return null;
    }

    /**
     * 获取起诉状内容（优先根据DocumentType获取）
     */
    private String getPlaintiffOpinion(Long caseImportId) {
        List<FileUploadRecord> fileRecords = getFileRecords(caseImportId);
        return getPlaintiffOpinionFromRecords(fileRecords);
    }

    /**
     * 获取答辩状内容（优先根据DocumentType获取）
     */
    private String getDefendantOpinion(Long caseImportId) {
        List<FileUploadRecord> fileRecords = getFileRecords(caseImportId);
        return getDefendantOpinionFromRecords(fileRecords);
    }

    /**
     * 获取开庭笔录内容（优先根据DocumentType获取）
     */
    private String getCourtRecord(Long caseImportId) {
        List<FileUploadRecord> fileRecords = getFileRecords(caseImportId);
        return getCourtRecordFromRecords(fileRecords);
    }

    /**
     * 提取文件内容的通用方法
     */
    private String extractFileContent(FileUploadRecord file) {
        // 优先从OCR结果提取文本
        String extractedText = extractTextFromOcrResult(file);

        // 如果OCR结果为空，尝试使用POI读取Word文档
        if (StringUtils.isBlank(extractedText)) {
            extractedText = extractTextFromWordDocument(file);
        }

        // 如果还是为空，使用原有的extractedText字段
        if (StringUtils.isBlank(extractedText) &&
            file.getExtractedText() != null && !file.getExtractedText().trim().isEmpty()) {
            extractedText = file.getExtractedText();
        }

        return extractedText;
    }

    /**
     * 根据DocumentType获取起诉状内容（包括补充起诉状）
     * 优先根据DocumentType获取，如果DocumentType都没有找到文件则根据文件名获取
     */
    private String getPlaintiffOpinionByDocumentType(Long caseImportId) {
        List<String> contents = new ArrayList<>();

        // 1. 先根据DocumentType一次性获取起诉状和补充起诉状
        List<FileUploadRecord> documentTypeRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId,
                List.of(DocumentType.QSZ, DocumentType.BCQSYJ));

        for (FileUploadRecord file : documentTypeRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                String fileType = DocumentType.QSZ.equals(file.getDocumentType()) ? "起诉状" : "补充起诉状";
                contents.add(String.format("=== %s：%s ===\n%s", fileType, file.getFileName(), content));
                log.info("根据DocumentType提取{}内容成功，文件: {}, 内容长度: {}", fileType, file.getFileName(), content.length());
            }
        }

        // 2. 如果DocumentType都没有找到文件，则根据文件名查找
        if (contents.isEmpty()) {
            log.warn("根据DocumentType未找到起诉状文件，尝试根据文件名查找，caseImportId: {}", caseImportId);

            List<FileUploadRecord> fileNameRecords = getFileRecordsByFileNames(caseImportId,
                    List.of("起诉状", "补充起诉"));

            for (FileUploadRecord file : fileNameRecords) {
                String content = extractFileContent(file);
                if (StringUtils.isNotBlank(content)) {
                    String fileType = file.getFileName().contains("补充") ? "补充起诉状" : "起诉状";
                    contents.add(String.format("=== %s：%s ===\n%s", fileType, file.getFileName(), content));
                    log.info("根据文件名提取{}内容成功，文件: {}, 内容长度: {}", fileType, file.getFileName(), content.length());
                }
            }
        }

        if (contents.isEmpty()) {
            log.warn("未找到任何起诉状或补充起诉状文件，caseImportId: {}", caseImportId);
            return null;
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("起诉状内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }

    /**
     * 根据DocumentType获取答辩状内容（包括补充答辩状）
     * 优先根据DocumentType获取，如果DocumentType都没有找到文件则根据文件名获取
     */
    private String getDefendantOpinionByDocumentType(Long caseImportId) {
        List<String> contents = new ArrayList<>();

        // 1. 先根据DocumentType一次性获取答辩状和补充答辩状
        List<FileUploadRecord> documentTypeRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId,
                List.of(DocumentType.DBZ, DocumentType.BCDBYJ));

        for (FileUploadRecord file : documentTypeRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                String fileType = DocumentType.DBZ.equals(file.getDocumentType()) ? "答辩状" : "补充答辩状";
                contents.add(String.format("=== %s：%s ===\n%s", fileType, file.getFileName(), content));
                log.info("根据DocumentType提取{}内容成功，文件: {}, 内容长度: {}", fileType, file.getFileName(), content.length());
            }
        }

        // 2. 如果DocumentType都没有找到文件，则根据文件名查找
        if (contents.isEmpty()) {
            log.warn("根据DocumentType未找到答辩状文件，尝试根据文件名查找，caseImportId: {}", caseImportId);

            List<FileUploadRecord> fileNameRecords = getFileRecordsByFileNames(caseImportId,
                    List.of("答辩状", "补充答辩"));

            for (FileUploadRecord file : fileNameRecords) {
                String content = extractFileContent(file);
                if (StringUtils.isNotBlank(content)) {
                    String fileType = file.getFileName().contains("补充") ? "补充答辩状" : "答辩状";
                    contents.add(String.format("=== %s：%s ===\n%s", fileType, file.getFileName(), content));
                    log.info("根据文件名提取{}内容成功，文件: {}, 内容长度: {}", fileType, file.getFileName(), content.length());
                }
            }
        }

        if (contents.isEmpty()) {
            log.warn("未找到任何答辩状或补充答辩状文件，caseImportId: {}", caseImportId);
            return null;
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("答辩状内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }

    /**
     * 根据DocumentType获取庭审笔录内容
     * 优先根据DocumentType获取，如果DocumentType为空则根据文件名获取
     */
    private String getCourtRecordByDocumentType(Long caseImportId) {
        // 使用FileRecordUtils获取庭审笔录
        List<FileUploadRecord> courtRecords = fileRecordUtils.getCourtRecords(caseImportId);

        if (courtRecords.isEmpty()) {
            log.warn("未找到任何庭审笔录文件，caseImportId: {}", caseImportId);
            return null;
        }

        List<String> contents = new ArrayList<>();
        for (FileUploadRecord file : courtRecords) {
            String content = extractFileContent(file);
            if (StringUtils.isNotBlank(content)) {
                contents.add(String.format("=== 庭审笔录：%s ===\n%s", file.getFileName(), content));
                log.info("提取庭审笔录内容成功，文件: {}, 内容长度: {}", file.getFileName(), content.length());
            }
        }

        if (contents.isEmpty()) {
            log.warn("庭审笔录文件内容为空，caseImportId: {}", caseImportId);
            return null;
        }

        String result = String.join("\n\n=== 文件分隔符 ===\n\n", contents);
        log.info("庭审笔录内容拼接完成，caseImportId: {}, 文件数量: {}, 总长度: {}", caseImportId, contents.size(), result.length());
        return result;
    }


    /**
     * 根据多个文件名关键字获取文件记录
     */
    private List<FileUploadRecord> getFileRecordsByFileNames(Long caseImportId, List<String> fileNames) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .eq(FileUploadRecord::getDeleted, 0); // 过滤逻辑删除的记录

        // 构建OR条件：文件名包含任一关键字
        queryWrapper.and(wrapper -> {
            for (int i = 0; i < fileNames.size(); i++) {
                if (i == 0) {
                    wrapper.like(FileUploadRecord::getFileName, fileNames.get(i));
                } else {
                    wrapper.or().like(FileUploadRecord::getFileName, fileNames.get(i));
                }
            }
        });

        queryWrapper.orderByDesc(FileUploadRecord::getUploadTime);

        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.info("根据文件名列表获取文件记录，caseImportId: {}, fileNames: {}, 文件数量: {}",
                caseImportId, fileNames, records.size());
        return records;
    }

    /**
     * 使用Agent任务生成认定事实（统一方法）
     * @param caseImportId 案件导入ID
     * @param ajlxdm 案件类型代码
     * @return 生成的认定事实
     */
    private String generateDetermineFactsWithAgent(Long caseImportId, String ajlxdm) {
        try {
            log.info("开始使用Agent任务生成认定事实，案件类型: {}", ajlxdm);
            long startTime = System.currentTimeMillis();

            Map<String, String> propertyMap = new HashMap<>();
            String evidenceContent = buildEvidenceContent(caseImportId);
            String courtRecord = getCourtRecordByDocumentType(caseImportId);
            String defendantOpinion = getDefendantOpinionByDocumentType(caseImportId);
            propertyMap.put("答辩意见", StringUtils.defaultIfBlank(defendantOpinion,StringUtils.EMPTY));
            propertyMap.put("庭审笔录", StringUtils.defaultIfBlank(courtRecord,StringUtils.EMPTY));
            propertyMap.put("证据名称及内容", StringUtils.defaultIfBlank(evidenceContent,StringUtils.EMPTY));
            String yamlName;
            if (CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)) {
                yamlName = "mses-determine_facts_task.yml";
                String firstInstanceJudgment = getFirstInstanceJudgmentContent(caseImportId);
                String appealContent = getAppealContent(caseImportId);
                propertyMap.put("一审判决书", StringUtils.isNotBlank(firstInstanceJudgment) ? firstInstanceJudgment : "无");
                propertyMap.put("上诉状", StringUtils.isNotBlank(appealContent) ? appealContent : "无");

            } else {
                yamlName = "determine_facts_task.yml";
                String plaintiffOpinion = getPlaintiffOpinionByDocumentType(caseImportId);
                propertyMap.put("起诉意见", StringUtils.isNotBlank(plaintiffOpinion) ? plaintiffOpinion : "无");
            }

           String response = agentTaskUtils.executeTask("星洲-认定事实Agent", "main", yamlName, propertyMap);

            // 清理响应内容，移除markdown标记和think标签
            response = aiModelService.cleanResponse(response);

            long duration = System.currentTimeMillis() - startTime;
            log.info("Agent任务生成认定事实完成，耗时: {}ms，响应长度: {}",
                    duration, response != null ? response.length() : 0);

            return response;

        } catch (Exception e) {
            log.error("使用Agent任务生成认定事实失败", e);
            throw new RuntimeException("使用Agent任务生成认定事实失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Agent任务生成认定事实（保留用于兼容）
     */
    private String generateDetermineFactsWithAI(String userMessage) {
        try {
            log.info("开始使用Agent任务生成认定事实");
            long startTime = System.currentTimeMillis();

            String response = agentTaskUtils.executeTask("星洲-认定事实Agent", "main", "determine_facts_task.yml", userMessage);

            long duration = System.currentTimeMillis() - startTime;
            log.info("Agent任务生成认定事实完成，耗时: {}ms，响应长度: {}",
                    duration, response != null ? response.length() : 0);

            return response;

        } catch (Exception e) {
            log.error("使用Agent任务生成认定事实失败", e);
            throw new RuntimeException("使用Agent任务生成认定事实失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存或更新AI生成的认定事实到evidence_facts_details表
     */
    @Transactional(rollbackFor = Exception.class)
    public EvidenceFactsDetails saveOrUpdateDetermineFactsAI(Long caseImportId, String determineFacts) {
        try {
            // 查询是否已存在记录
            EvidenceFactsDetails existingDetails = getByCaseImportId(caseImportId);

            if (existingDetails != null) {
                // 更新现有记录的认定事实字段
                existingDetails.setDetermineFacts(determineFacts);
                existingDetails.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetails);
                log.info("更新证据详情记录的认定事实成功，案件ID: {}, 内容长度: {}", caseImportId, determineFacts.length());
                return existingDetails;
            } else {
                // 新增记录，只设置认定事实字段
                EvidenceFactsDetails newDetails = EvidenceFactsDetails.builder()
                        .caseImportId(caseImportId)
                        .determineFacts(determineFacts)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                this.save(newDetails);
                log.info("新增证据详情记录的认定事实成功，案件ID: {}, 内容长度: {}", caseImportId, determineFacts.length());
                return newDetails;
            }
        } catch (Exception e) {
            log.error("保存AI生成的认定事实记录失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("保存AI生成的认定事实记录失败", e);
        }
    }

}