package com.smxz.yjzs.service.impl;

import com.smxz.yjzs.common.utils.ArchiveUtils;
import com.smxz.yjzs.common.utils.UUIDHelper;
import com.smxz.yjzs.dto.request.FileDownloadRequest;
import com.smxz.yjzs.dto.request.FileUploadRequest;
import com.smxz.yjzs.dto.response.BatchFileUploadResponse;
import com.smxz.yjzs.dto.response.FileDownloadResponse;
import com.smxz.yjzs.dto.response.FileUploadResponse;
import com.smxz.yjzs.service.FileHandlerService;
import io.minio.*;
import io.minio.errors.*;
import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR> 诚
 * @since 2025-05-13 19:48
 **/
@Slf4j
@Service
public class MinioHanderServiceImpl implements FileHandlerService {

    @Autowired
    private MinioClient minioClient;


    @Value("${minio.bucketName:uploads}")
    private String minioBucketName;

    @Value("${file.upload.temp-dir:${java.io.tmpdir}}")
    private String tempDir;

    @PostConstruct
    public void init() {
        ensureBucketExists();
    }
    private void ensureBucketExists() {
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioBucketName).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(minioBucketName).build());
            }
        } catch (Exception e) {
            log.error("MinIO 存储桶初始化失败", e);
            throw new RuntimeException("MinIO 存储桶初始化失败", e);
        }
    }
    @Override
    public BatchFileUploadResponse uploadFiles(FileUploadRequest request) {
        if (ObjectUtils.isEmpty(request.getFile())){
            throw new RuntimeException("文件为空");
        }
        MultipartFile file = request.getFile();
        List<FileUploadResponse> fileInfos = new ArrayList<>();
            if (ArchiveUtils.isArchiveFile(file)&& request.isUnzip()) {
                fileInfos.addAll(handleArchiveFile(file));
            } else {
                FileUploadResponse fileInfo = doUploadFile(file);
                fileInfos.add(fileInfo);
            }
        long successCount = fileInfos.stream().filter(FileUploadResponse::isSuccess).count();
        long failCount = fileInfos.size() - successCount;
        return BatchFileUploadResponse.builder().
                totalCount(fileInfos.size()).
                successCount(successCount).
                failedCount(failCount).
                successFiles(fileInfos).build();
    }

    public List<FileUploadResponse> handleArchiveFile(MultipartFile archiveFile) {
        List<FileUploadResponse> records = new ArrayList<>();
        String archiveFileName = StringUtils.cleanPath(archiveFile.getOriginalFilename());
        String extractPath = tempDir + File.separator + "extract_" + UUID.randomUUID().toString().replaceAll("-", "");

        log.info("开始处理压缩文件: {}, 解压路径: {}", archiveFileName, extractPath);

        try {
            // 验证输入
            if (archiveFile == null || archiveFile.isEmpty()) {
                log.error("压缩文件为空: {}", archiveFileName);
                return records;
            }

            // 验证临时目录
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                boolean created = tempDirFile.mkdirs();
                log.info("创建临时目录: {}, 结果: {}", tempDir, created);
            }

            // 解压文件
            List<File> extractedFiles = ArchiveUtils.extractArchive(archiveFile, extractPath);
            log.info("解压成功，共 {} 个文件", extractedFiles.size());

            // 上传解压后的文件
            for (File file : extractedFiles) {
                try {
                    FileUploadResponse uploadResponse = doUploadFile(file);
                    uploadResponse.setSuccess(true);
                    records.add(uploadResponse);
                    log.info("上传文件成功: {}", file.getName());
                } catch (Exception e) {
                    log.error("上传文件失败: {}", file.getName(), e);
                    // 创建失败记录
                    FileUploadResponse failedResponse = FileUploadResponse.builder()
                            .fileName(file.getName())
                            .success(false)
                            .uploadTime(LocalDateTime.now())
                            .build();
                    records.add(failedResponse);
                }
            }

        } catch (Exception e) {
            log.error("处理压缩文件失败: {}, 错误: {}", archiveFileName, e.getMessage(), e);
            // 创建失败记录
            FileUploadResponse failedResponse = FileUploadResponse.builder()
                    .fileName(archiveFileName)
                    .success(false)
                    .uploadTime(LocalDateTime.now())
                    .build();
            records.add(failedResponse);
        } finally {
            // 清理解压目录
            try {
                File extractDir = new File(extractPath);
                if (extractDir.exists()) {
                    FileUtils.deleteDirectory(extractDir);
                    log.info("清理解压目录成功: {}", extractPath);
                }
            } catch (IOException e) {
                log.error("删除临时目录失败: {}", extractPath, e);
            }
        }

        log.info("压缩文件处理完成: {}, 成功: {}, 总数: {}",
                archiveFileName,
                records.stream().filter(FileUploadResponse::isSuccess).count(),
                records.size());

        return records;
    }

    public FileUploadResponse doUploadFile(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            String sourceFilename = file.getName();
            String s3filename = UUIDHelper.generateUUID()+"." +FilenameUtils.getExtension(sourceFilename);
            ObjectWriteResponse response = minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioBucketName)
                            .object(s3filename)
                            .stream(fis, file.length(), -1)
                            .build());
            FileUploadResponse record = FileUploadResponse.builder().
                    fileId(response.object()).fileName(sourceFilename)
                    .fileType(Files.probeContentType(file.toPath())).
                    fileSize(file.length()).uploadTime(LocalDateTime.now()).build();
            return record;
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败: " + file.getName(), e);
        }
    }

    public FileUploadResponse doUploadFile(MultipartFile file) {
        try (InputStream fis = file.getInputStream()) {
            String sourceFilename = file.getOriginalFilename();
            String s3filename = UUIDHelper.generateUUID()+"." +FilenameUtils.getExtension(sourceFilename);

            ObjectWriteResponse response = minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioBucketName)
                            .object(s3filename)
                            .stream(fis, file.getSize(), -1)
                            .contentType(Files.probeContentType(file.getResource().getFile().toPath()))
                            .build());
            FileUploadResponse record = FileUploadResponse.builder().fileId(response.object()).
                    fileName(sourceFilename)
                    .fileType(Files.probeContentType(file.getResource().getFile().toPath())).fileSize(file.getSize()).uploadTime(LocalDateTime.now()).build();
            return record;
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败: " + file.getName(), e);
        }
    }


    @Override
    public FileDownloadResponse downloadFile(FileDownloadRequest request) {
        try {
            GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder().bucket(minioBucketName).object(request.getFileId()).build());
            FileDownloadResponse response = FileDownloadResponse.builder().
                    fileId(request.getFileId()).
                    fileName(request.getFileName()).
                    inputStream(object).build();
            return response;
        } catch (ErrorResponseException | ServerException | XmlParserException | NoSuchAlgorithmException |
                 IOException | InvalidResponseException | InvalidKeyException | InternalException |
                 InsufficientDataException e) {
            throw new RuntimeException(e);
        }
    }
}
