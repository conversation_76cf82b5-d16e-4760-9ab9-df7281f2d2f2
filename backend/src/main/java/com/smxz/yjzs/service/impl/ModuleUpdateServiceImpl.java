package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.ModuleUpdateRecord;
import com.smxz.yjzs.enums.ModuleType;
import com.smxz.yjzs.mapper.ModuleUpdateMapper;
import com.smxz.yjzs.service.ModuleUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模块更新服务实现类
 */
@Slf4j
@Service
public class ModuleUpdateServiceImpl extends ServiceImpl<ModuleUpdateMapper, ModuleUpdateRecord> implements ModuleUpdateService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markModulesNeedUpdate(Long caseImportId, List<String> moduleCodes) {
        if (caseImportId == null || moduleCodes == null || moduleCodes.isEmpty()) {
            log.warn("案件ID或模块代码列表为空，跳过更新标记");
            return;
        }

        log.info("标记模块需要更新，案件ID: {}, 模块: {}", caseImportId, moduleCodes);

        for (String moduleCode : moduleCodes) {
            markModuleNeedUpdate(caseImportId, moduleCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markModuleNeedUpdate(Long caseImportId, String moduleCode) {
        if (caseImportId == null || moduleCode == null || moduleCode.trim().isEmpty()) {
            log.warn("案件ID或模块代码为空，跳过更新标记");
            return;
        }

        // 验证模块代码是否有效
        try {
            ModuleType.fromCode(moduleCode);
        } catch (IllegalArgumentException e) {
            log.error("无效的模块代码: {}", moduleCode);
            return;
        }

        ModuleUpdateRecord existingRecord = baseMapper.selectByCaseAndModule(caseImportId, moduleCode);
        
        if (existingRecord != null) {
            // 更新现有记录：清空operate_module字段
            baseMapper.clearOperateModule(caseImportId, moduleCode);
            log.info("清空模块操作记录，案件ID: {}, 模块: {}", caseImportId, moduleCode);
        } else {
            // 创建新记录
            ModuleUpdateRecord newRecord = ModuleUpdateRecord.builder()
                    .caseImportId(caseImportId)
                    .moduleCode(moduleCode)
                    .hasUpdate(0) // 初始化为0，后续只通过operate_module字段判断
                    .operateModule(null)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            save(newRecord);
            log.info("创建新的模块更新记录，案件ID: {}, 模块: {}", caseImportId, moduleCode);
        }
    }

    @Override
    public boolean checkModuleNeedUpdate(Long caseImportId, String moduleCode) {
        if (caseImportId == null || moduleCode == null || moduleCode.trim().isEmpty()) {
            return false;
        }

        ModuleUpdateRecord record = baseMapper.selectByCaseAndModule(caseImportId, moduleCode);
        // 如果记录不存在，或者operate_module为空，则认为需要更新
        return record == null || record.getOperateModule() == null || record.getOperateModule().trim().isEmpty();
    }

    @Override
    public boolean checkModuleNeedUpdateNew(Long caseImportId, String sourceModule, String operatedModule) {
        if (caseImportId == null || sourceModule == null || operatedModule == null) {
            return false;
        }

        int count = baseMapper.checkModuleNeedUpdate(caseImportId, sourceModule, operatedModule);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markModuleAsOperated(Long caseImportId, String sourceModule, String operatedModule) {
        if (caseImportId == null || sourceModule == null || operatedModule == null) {
            log.warn("参数不能为空，跳过模块操作标记");
            return;
        }

        int result = baseMapper.addOperateModule(caseImportId, sourceModule, operatedModule);
        if (result > 0) {
            log.info("标记模块已操作，案件ID: {}, 源模块: {}, 操作模块: {}", caseImportId, sourceModule, operatedModule);
        } else {
            log.warn("标记模块操作失败，可能记录不存在，案件ID: {}, 源模块: {}", caseImportId, sourceModule);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetModuleStatus(Long caseImportId, String moduleCode) {
        if (caseImportId == null || moduleCode == null || moduleCode.trim().isEmpty()) {
            log.warn("案件ID或模块代码为空，跳过状态重置");
            return;
        }

        LambdaUpdateWrapper<ModuleUpdateRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ModuleUpdateRecord::getCaseImportId, caseImportId)
                .eq(ModuleUpdateRecord::getModuleCode, moduleCode)
                .set(ModuleUpdateRecord::getOperateModule, null)
                .set(ModuleUpdateRecord::getUpdateTime, LocalDateTime.now());
        
        update(updateWrapper);
        log.info("重置模块状态，清空操作记录，案件ID: {}, 模块: {}", caseImportId, moduleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchResetModuleStatus(Long caseImportId, List<String> moduleCodes) {
        if (caseImportId == null) {
            log.warn("案件ID为空，跳过批量状态重置");
            return;
        }

        int affectedRows = baseMapper.batchResetUpdateStatus(caseImportId, moduleCodes);
        log.info("批量重置模块状态，案件ID: {}, 模块: {}, 影响行数: {}", 
                caseImportId, moduleCodes, affectedRows);
    }

    @Override
    public Map<String, Boolean> getAllModuleStatus(Long caseImportId) {
        if (caseImportId == null) {
            return new HashMap<>();
        }

        List<ModuleUpdateRecord> records = baseMapper.selectByCaseImportId(caseImportId);
        
        // 初始化所有模块状态为true（需要更新）
        Map<String, Boolean> statusMap = new HashMap<>();
        for (ModuleType moduleType : ModuleType.values()) {
            statusMap.put(moduleType.getCode(), true);
        }
        
        // 更新实际状态：如果有operate_module记录，则不需要更新
        for (ModuleUpdateRecord record : records) {
            boolean needUpdate = record.getOperateModule() == null || record.getOperateModule().trim().isEmpty();
            statusMap.put(record.getModuleCode(), needUpdate);
        }
        
        return statusMap;
    }

    @Override
    public Map<String, Boolean> getAllModuleStatusNew(Long caseImportId, String currentModule) {
        if (caseImportId == null) {
            return new HashMap<>();
        }

        // 初始化所有模块状态为true（需要更新）
        Map<String, Boolean> statusMap = new HashMap<>();
        for (ModuleType moduleType : ModuleType.values()) {
            statusMap.put(moduleType.getCode(), true);
        }

        // 如果当前模块为空，使用原逻辑
        if (currentModule == null || currentModule.trim().isEmpty()) {
            return getAllModuleStatus(caseImportId);
        }

        // 直接返回所有模块状态，新的检查逻辑在具体的checkModuleNeedUpdateNew方法中处理
        return getAllModuleStatus(caseImportId);
    }

    @Override
    public List<String> getNeedUpdateModules(Long caseImportId) {
        if (caseImportId == null) {
            return List.of();
        }

        return baseMapper.selectAllModules(caseImportId);
    }

    @Override
    public List<ModuleUpdateRecord> getModuleUpdateRecords(Long caseImportId) {
        if (caseImportId == null) {
            return List.of();
        }

        return baseMapper.selectByCaseImportId(caseImportId);
    }
}
