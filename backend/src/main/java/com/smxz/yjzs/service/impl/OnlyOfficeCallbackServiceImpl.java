package com.smxz.yjzs.service.impl;

import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.constant.OfficeStatus;
import com.smxz.yjzs.entity.DocumentGenerationInfo;
import com.smxz.yjzs.service.OnlyOfficeCallbackService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Map;

/**
 * OnlyOffice回调服务实现类
 */
@Slf4j
@Service
public class OnlyOfficeCallbackServiceImpl implements OnlyOfficeCallbackService {

    @Autowired
    private MinioClient minioClient;
    
    @Autowired
    private MinioConfig minioConfig;
    
    @Autowired
    private DocumentGenerationInfoService documentGenerationInfoService;

    @Override
    public Map<String, Object> handleCallback(Long documentId, Map<String, Object> callbackData) {
        try {
            log.info("OnlyOffice回调，documentId: {}, callbackData: {}", documentId, callbackData);
            Integer status = (Integer) callbackData.get("status");

            if (isDocumentReadyToSave(status)) {
                String fileUrl = (String) callbackData.get("url");
                String docKey = (String) callbackData.get("key");

                if (fileUrl != null && !fileUrl.isEmpty()) {
                    DocumentGenerationInfo info = documentGenerationInfoService.getById(documentId);
                    // 下载并上传文档到MinIO
                    String objectName = uploadDocumentToMinio(fileUrl, info.getDocumentKey());
                    
                    // 解析文档内容
//                    String docxText = extractDocumentContent(fileUrl);
                    
                    // 更新数据库
//                    updateDocumentInfo(documentId, objectName, docxText);
                }
            }
            
            // OnlyOffice 需要 {error:0} 结构
            return Collections.singletonMap("error", 0);
        } catch (Exception e) {
            log.error("OnlyOffice回调异常", e);
            return Collections.singletonMap("error", 1);
        }
    }

    /**
     * 检查文档是否准备保存
     * @param status 状态码
     * @return 是否准备保存
     */
    private boolean isDocumentReadyToSave(Integer status) {
        return status != null && (status == OfficeStatus.CallbackStatus.READY_TO_SAVE || 
                                 status == OfficeStatus.CallbackStatus.SAVED_CURRENT_STATE);
    }

    /**
     * 上传文档到MinIO
     * @param fileUrl 文件URL
     * @param docKey 文档Key
     * @return MinIO中的对象名称
     * @throws Exception 上传异常
     */
    private String uploadDocumentToMinio(String fileUrl, String docKey) throws Exception {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String objectName = "documents/" + docKey + ".docx";
        
        try (InputStream in = new URL(fileUrl).openStream()) {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(docKey)
                            .stream(in, -1, 10485760)
                            .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                            .build()
            );
        }
        
        log.info("文档上传到MinIO成功，objectName: {}", docKey);
        return docKey;
    }

    /**
     * 提取文档内容
     * @param fileUrl 文件URL
     * @return 文档文本内容
     */
    private String extractDocumentContent(String fileUrl) {
        try (InputStream in = new URL(fileUrl).openStream();
             XWPFDocument doc = new XWPFDocument(in)) {
            StringBuilder sb = new StringBuilder();
            doc.getParagraphs().forEach(p -> sb.append(p.getText()).append("\n"));
            String content = sb.toString();
            log.info("文档内容提取成功，内容长度: {}", content.length());
            return content;
        } catch (Exception e) {
            log.error("解析docx内容异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 更新文档信息
     * @param documentId 文档ID
     * @param objectName MinIO对象名称
     * @param docxText 文档内容
     */
    private void updateDocumentInfo(Long documentId, String objectName, String docxText) {
        String minioUrl = minioConfig.getEndpoint() + "/" + minioConfig.getBucketName() + "/" + objectName;
        DocumentGenerationInfo info = documentGenerationInfoService.getById(documentId);
        
        if (info != null) {
            info.setDocumentUrl(minioUrl);
            info.setUpdateTime(LocalDateTime.now());
            boolean result = documentGenerationInfoService.saveOrUpdate(info);
            
            if (result) {
                log.info("文档信息更新成功，documentId: {}, url: {}", documentId, minioUrl);
            } else {
                log.error("文档信息更新失败，documentId: {}", documentId);
            }
        } else {
            log.warn("未找到文档信息，documentId: {}", documentId);
        }
    }
} 