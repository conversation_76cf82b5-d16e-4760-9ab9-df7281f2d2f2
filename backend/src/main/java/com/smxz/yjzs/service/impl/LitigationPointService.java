package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.agent.client.AgentClient;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.DisputeConstants;
import com.smxz.yjzs.dto.UpdateLitigationPointPairRequest;
import com.smxz.yjzs.dto.request.SaveLitigationPointsRequest;
import com.smxz.yjzs.dto.response.LitigationPointsResponse;
import com.smxz.yjzs.entity.*;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.mapper.LitigationPointMapper;
import com.smxz.yjzs.mapper.LitigationRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * 诉辩观点服务实现类
 */
@Slf4j
@Service
public class LitigationPointService extends ServiceImpl<LitigationPointMapper, LitigationPoint> {

    @Autowired
    private LitigationRelationMapper litigationRelationMapper;

    @Autowired
    private LitigationRelationService litigationRelationService;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private SystemPromptConfigService systemPromptConfigService;

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    @Lazy
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    @Autowired
    private JudgmentSituationService judgmentSituationService;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    /**
     * 获取诉辩观点数据
     */
    public LitigationPointsResponse getLitigationPointsData(Long caseImportId) {
        log.info("获取诉辩观点数据，caseImportId: {}", caseImportId);

        LitigationPointsResponse response = new LitigationPointsResponse();

        // 获取诉辩观点，按照日期和sortOrder排序
        List<LitigationPoint> points = baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .orderBy(true, true, LitigationPoint::getPointDate, LitigationPoint::getSortOrder)
        );

        // 按诉辩方分组并按当事人和日期组织数据
        Map<String, List<LitigationPoint>> pointsBySide = points.stream()
                .collect(Collectors.groupingBy(LitigationPoint::getSide));

        // 处理诉方数据
        List<LitigationPointsResponse.PartyPoints> plaintiffs = processPartyPoints(
                pointsBySide.getOrDefault(DisputeConstants.PartyType.PLAINTIFF, new ArrayList<>()));
        response.setPlaintiffs(plaintiffs);

        // 处理辩方数据
        List<LitigationPointsResponse.PartyPoints> defendants = processPartyPoints(
                pointsBySide.getOrDefault(DisputeConstants.PartyType.DEFENDANT, new ArrayList<>()));
        response.setDefendants(defendants);

        // 获取诉辩关系
        List<LitigationRelation> relations = litigationRelationMapper.selectList(
                new LambdaQueryWrapper<LitigationRelation>()
                        .eq(LitigationRelation::getCaseImportId, caseImportId)
                        .orderBy(true, true, LitigationRelation::getCreateTime)
        );
        response.setRelations(relations);

        log.info("获取诉辩观点数据完成，诉方: {}, 辩方: {}, 关系: {}",
                plaintiffs.size(), defendants.size(), relations.size());

        return response;
    }

    /**
     * 根据案件导入ID获取所有诉方观点
     */
    public List<LitigationPoint> getPlaintiffPoints(Long caseImportId) {
        return baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .eq(LitigationPoint::getSide, DisputeConstants.PartyType.PLAINTIFF)
                        .orderBy(true, true, LitigationPoint::getPointDate, LitigationPoint::getSortOrder)
        );
    }

    /**
     * 根据案件导入ID获取所有辩方观点
     */
    public List<LitigationPoint> getDefendantPoints(Long caseImportId) {
        return baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .eq(LitigationPoint::getSide, DisputeConstants.PartyType.DEFENDANT)
                        .orderBy(true, true, LitigationPoint::getPointDate, LitigationPoint::getSortOrder)
        );
    }



    /**
     * 处理当事人观点数据，按当事人和日期分组
     */
    private List<LitigationPointsResponse.PartyPoints> processPartyPoints(List<LitigationPoint> points) {
        // 按当事人分组
        Map<String, List<LitigationPoint>> pointsByParty = points.stream()
                .collect(Collectors.groupingBy(LitigationPoint::getPartyName));

        return pointsByParty.entrySet().stream()
                .map(entry -> {
                    String partyName = entry.getKey();
                    List<LitigationPoint> partyPoints = entry.getValue();

                    // 按日期分组
                    Map<String, List<LitigationPointsResponse.PointInfo>> pointsByDate = partyPoints.stream()
                            .collect(Collectors.groupingBy(
                                    point -> point.getPointDate() != null ?
                                            point.getPointDate().toString() : "未知日期",
                                    TreeMap::new,
                                    Collectors.mapping(this::convertToPointInfo, Collectors.toList())
                            ));

                    return LitigationPointsResponse.PartyPoints.builder()
                            .name(partyName)
                            .pointsByDate(pointsByDate)
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换为PointInfo
     */
    private LitigationPointsResponse.PointInfo convertToPointInfo(LitigationPoint point) {
        return LitigationPointsResponse.PointInfo.builder()
                .id(point.getId())
                .content(point.getContent())
                .pointDate(point.getPointDate())
                .sortOrder(point.getSortOrder())  // 直接使用数据库中的sortOrder
                .partyName(point.getPartyName())
                .evidence(point.getEvidence())
                .side(point.getSide())
                .caseImportId(point.getCaseImportId())
                .pointId(point.getPointId())
                .build();
    }

    /**
     * 获取当前案件中各方的最大pointId
     */
    private Map<String, Integer> getMaxPointIds(Long caseImportId) {
        Map<String, Integer> maxPointIds = new HashMap<>();
        maxPointIds.put("plaintiff", 0);
        maxPointIds.put("defendant", 0);

        // 查询当前案件的所有观点
        List<LitigationPoint> existingPoints = baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .isNotNull(LitigationPoint::getPointId)
        );

        // 找出各方的最大pointId
        for (LitigationPoint point : existingPoints) {
            String pointId = point.getPointId();
            String side = point.getSide();

            if (pointId != null && pointId.matches("\\d+")) {
                try {
                    int id = Integer.parseInt(pointId);
                    String sideKey = DisputeConstants.PartyType.PLAINTIFF.equals(side) ?
                        DisputeConstants.PartyType.PLAINTIFF : DisputeConstants.PartyType.DEFENDANT;
                    maxPointIds.put(sideKey, Math.max(maxPointIds.get(sideKey), id));
                } catch (NumberFormatException e) {
                    log.warn("无法解析pointId为数字: {}", pointId);
                }
            }
        }

        log.info("当前案件 {} 的最大pointId - 诉方: {}, 辩方: {}",
                caseImportId, maxPointIds.get(DisputeConstants.PartyType.PLAINTIFF),
                maxPointIds.get(DisputeConstants.PartyType.DEFENDANT));

        return maxPointIds;
    }

    /**
     * 获取当前案件中各方的最大sortOrder
     */
    private Map<String, Integer> getMaxSortOrders(Long caseImportId) {
        Map<String, Integer> maxSortOrders = new HashMap<>();
        maxSortOrders.put(DisputeConstants.PartyType.PLAINTIFF, 0);
        maxSortOrders.put(DisputeConstants.PartyType.DEFENDANT, 0);

        // 查询当前案件的所有观点
        List<LitigationPoint> existingPoints = baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .isNotNull(LitigationPoint::getSortOrder)
        );

        // 找出各方的最大sortOrder
        for (LitigationPoint point : existingPoints) {
            Integer sortOrder = point.getSortOrder();
            String side = point.getSide();

            if (sortOrder != null) {
                String sideKey = DisputeConstants.PartyType.PLAINTIFF.equals(side) ?
                    DisputeConstants.PartyType.PLAINTIFF : DisputeConstants.PartyType.DEFENDANT;
                maxSortOrders.put(sideKey, Math.max(maxSortOrders.get(sideKey), sortOrder));
            }
        }

        log.info("当前案件 {} 的最大sortOrder - 诉方: {}, 辩方: {}",
                caseImportId, maxSortOrders.get(DisputeConstants.PartyType.PLAINTIFF),
                maxSortOrders.get(DisputeConstants.PartyType.DEFENDANT));

        return maxSortOrders;
    }

    /**
     * 更新单个观点
     */
    private void updateSinglePoint(UpdateLitigationPointPairRequest.LitigationPointUpdateData pointData) {
        if (pointData.getId() == null) {
            throw new RuntimeException("观点ID不能为空");
        }

        LitigationPoint existingPoint = baseMapper.selectById(pointData.getId());
        if (existingPoint == null) {
            throw new RuntimeException("观点不存在，ID: " + pointData.getId());
        }

        // 更新观点数据
        if (pointData.getContent() != null) {
            existingPoint.setContent(pointData.getContent());
        }
        if (pointData.getEvidence() != null) {
            existingPoint.setEvidence(pointData.getEvidence());
        }
        if (pointData.getPointDate() != null) {
            try {
                existingPoint.setPointDate(LocalDate.parse(pointData.getPointDate()));
            } catch (Exception e) {
                log.warn("日期解析失败，保持原日期: {}", pointData.getPointDate());
            }
        }
        // sortOrder在更新时保持不变，除非明确需要修改

        existingPoint.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(existingPoint);
    }

    /**
     * 更新关系
     */
    private void updateRelation(UpdateLitigationPointPairRequest.RelationUpdateData relationData) {
        if (relationData.getId() == null) {
            throw new RuntimeException("关系ID不能为空");
        }

        LitigationRelation existingRelation = litigationRelationMapper.selectById(relationData.getId());
        if (existingRelation == null) {
            throw new RuntimeException("关系不存在，ID: " + relationData.getId());
        }

        // 更新关系数据
        if (relationData.getRelationType() != null) {
            existingRelation.setRelationType(relationData.getRelationType());
        }
        if (relationData.getPlaintiffPointId() != null) {
            existingRelation.setPlaintiffPointId(relationData.getPlaintiffPointId());
        }
        if (relationData.getDefendantPointId() != null) {
            existingRelation.setDefendantPointId(relationData.getDefendantPointId());
        }

        existingRelation.setUpdateTime(LocalDateTime.now());
        litigationRelationMapper.updateById(existingRelation);
    }

    /**
     * 批量更新诉辩观点对和关系
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateLitigationPointPair(UpdateLitigationPointPairRequest request) {
        log.info("开始批量更新诉辩观点对，案件ID: {}", request.getCaseImportId());

        try {
            Map<String, Object> result = new HashMap<>();
            List<String> updatedItems = new ArrayList<>();

            // 更新诉方观点
            if (request.getPlaintiffPoint() != null && request.getPlaintiffPoint().getNeedUpdate()) {
                updateSinglePoint(request.getPlaintiffPoint());
                updatedItems.add("诉方观点");
                log.info("更新诉方观点成功，ID: {}", request.getPlaintiffPoint().getId());
            }

            // 更新辩方观点
            if (request.getDefendantPoint() != null && request.getDefendantPoint().getNeedUpdate()) {
                updateSinglePoint(request.getDefendantPoint());
                updatedItems.add("辩方观点");
                log.info("更新辩方观点成功，ID: {}", request.getDefendantPoint().getId());
            }

            // 更新关系
            if (request.getRelation() != null && request.getRelation().getNeedUpdate()) {
                if (request.getRelation().getId() != null) {
                    updateRelation(request.getRelation());
                    updatedItems.add("观点关系");
                    log.info("更新观点关系成功，ID: {}", request.getRelation().getId());
                } else {
                    log.warn("关系ID为空，跳过关系更新");
                }
            }

            result.put("success", true);
            result.put("updatedItems", updatedItems);
            result.put("message", "更新完成: " + String.join("、", updatedItems));

            log.info("批量更新诉辩观点对完成，案件ID: {}, 更新项目: {}", request.getCaseImportId(), updatedItems);
            return result;

        } catch (Exception e) {
            log.error("批量更新诉辩观点对失败", e);
            throw new RuntimeException("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 保存诉辩观点
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveLitigationPoints(SaveLitigationPointsRequest request) {
        log.info("开始保存诉辩观点，caseImportId: {}, 观点数量: {}",
                request.getCaseImportId(), request.getPoints().size());

        List<Long> savedPointIds = new ArrayList<>();
        Long relationId = null;
        // 记录保存的观点，用于自动建立关系
        String savedPlaintiffPointId = null;
        String savedDefendantPointId = null;

        try {
            // 获取当前案件的最大pointId，用于生成新的pointId
            Map<String, Integer> maxPointIds = getMaxPointIds(request.getCaseImportId());
            // 获取当前案件的最大sortOrder，用于生成新的sortOrder
            Map<String, Integer> maxSortOrders = getMaxSortOrders(request.getCaseImportId());

            // 保存诉辩观点
            for (SaveLitigationPointsRequest.LitigationPointData pointData : request.getPoints()) {
                LitigationPoint point = new LitigationPoint();
                point.setCaseImportId(request.getCaseImportId());

                // 确保使用英文标识
                String side = pointData.getSide();
                if ("诉方".equals(side)) {
                    side = DisputeConstants.PartyType.PLAINTIFF;
                } else if ("辩方".equals(side)) {
                    side = DisputeConstants.PartyType.DEFENDANT;
                }
                point.setSide(side);

                point.setPartyName(pointData.getPartyName());
                point.setContent(pointData.getContent());
                point.setEvidence(pointData.getEvidence());

                // 生成新的pointId
                String sideKey = DisputeConstants.PartyType.PLAINTIFF.equals(side) ?
                    DisputeConstants.PartyType.PLAINTIFF : DisputeConstants.PartyType.DEFENDANT;
                int nextPointId = maxPointIds.get(sideKey) + 1;
                point.setPointId(String.valueOf(nextPointId));
                maxPointIds.put(sideKey, nextPointId); // 更新计数器

                // 生成新的sortOrder（基于当前最大sortOrder + 1）
                int nextSortOrder = maxSortOrders.get(sideKey) + 1;
                point.setSortOrder(nextSortOrder);
                maxSortOrders.put(sideKey, nextSortOrder); // 更新计数器

                // 处理日期
                if (pointData.getPointDate() != null) {
                    try {
                        point.setPointDate(LocalDate.parse(pointData.getPointDate()));
                    } catch (Exception e) {
                        log.warn("日期解析失败，使用当前日期: {}", pointData.getPointDate());
                        point.setPointDate(LocalDate.now());
                    }
                } else {
                    point.setPointDate(LocalDate.now());
                }

                point.setCreateTime(LocalDateTime.now());
                point.setUpdateTime(LocalDateTime.now());

                baseMapper.insert(point);
                savedPointIds.add(point.getId());

                // 记录保存的观点ID，用于自动建立关系
                if (DisputeConstants.PartyType.PLAINTIFF.equals(side)) {
                    savedPlaintiffPointId = point.getPointId();
                } else if (DisputeConstants.PartyType.DEFENDANT.equals(side)) {
                    savedDefendantPointId = point.getPointId();
                }

                log.info("保存诉辩观点成功，ID: {}, pointId: {}, sortOrder: {}, side: {}, 内容: {}",
                        point.getId(), point.getPointId(), point.getSortOrder(), side, point.getContent());
            }

            // 保存诉辩关系（如果提供）
            if (request.getRelation() != null) {
                SaveLitigationPointsRequest.RelationData relationData = request.getRelation();

                // 获取实际的pointId
                String actualPlaintiffPointId = null;
                String actualDefendantPointId = null;

                // 如果前端提供了具体的pointId，直接使用
                if (relationData.getPlaintiffPointId() != null && !relationData.getPlaintiffPointId().isEmpty()) {
                    actualPlaintiffPointId = relationData.getPlaintiffPointId();
                } else {
                    // 如果前端没有提供pointId，使用自动记录的pointId
                    actualPlaintiffPointId = savedPlaintiffPointId;
                }

                if (relationData.getDefendantPointId() != null && !relationData.getDefendantPointId().isEmpty()) {
                    actualDefendantPointId = relationData.getDefendantPointId();
                } else {
                    // 如果前端没有提供pointId，使用自动记录的pointId
                    actualDefendantPointId = savedDefendantPointId;
                }

                // 只有当两个pointId都不为空时才创建关系
                if (actualPlaintiffPointId != null && actualDefendantPointId != null) {
                    LitigationRelation relation = new LitigationRelation();
                    relation.setCaseImportId(request.getCaseImportId());
                    relation.setPlaintiffPointId(actualPlaintiffPointId);
                    relation.setDefendantPointId(actualDefendantPointId);
                    relation.setRelationType(relationData.getRelationType());
                    relation.setCreateTime(LocalDateTime.now());
                    relation.setUpdateTime(LocalDateTime.now());

                    litigationRelationMapper.insert(relation);
                    relationId = relation.getId();

                    log.info("保存诉辩关系成功，ID: {}, 类型: {}, 诉方pointId: {}, 辩方pointId: {}",
                            relation.getId(), relation.getRelationType(), actualPlaintiffPointId, actualDefendantPointId);
                } else {
                    log.warn("无法创建诉辩关系，缺少必要的pointId - 诉方: {}, 辩方: {}",
                            actualPlaintiffPointId, actualDefendantPointId);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("savedPointIds", savedPointIds);
            result.put("relationId", relationId);
            result.put("totalSaved", savedPointIds.size());

            log.info("诉辩观点保存完成，保存观点数: {}, 关系ID: {}", savedPointIds.size(), relationId);
            return result;

        } catch (Exception e) {
            log.error("保存诉辩观点失败", e);
            throw new RuntimeException("保存诉辩观点失败: " + e.getMessage());
        }
    }

    /**
     * 删除诉辩观点
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteLitigationPoint(Long pointId) {
        log.info("删除诉辩观点，pointId: {}", pointId);

        try {
            // 删除相关的诉辩关系
            litigationRelationMapper.delete(
                    new LambdaQueryWrapper<LitigationRelation>()
                            .eq(LitigationRelation::getPlaintiffPointId, pointId.toString())
                            .or()
                            .eq(LitigationRelation::getDefendantPointId, pointId.toString())
            );

            // 删除观点
            int deleted = baseMapper.deleteById(pointId);

            Map<String, Object> result = new HashMap<>();
            result.put("deleted", deleted > 0);
            result.put("pointId", pointId);

            log.info("删除诉辩观点完成，pointId: {}, 删除成功: {}", pointId, deleted > 0);
            return result;

        } catch (Exception e) {
            log.error("删除诉辩观点失败", e);
            throw new RuntimeException("删除诉辩观点失败: " + e.getMessage());
        }
    }

    /**
     * 更新诉辩观点
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateLitigationPoint(Long pointId, Map<String, Object> updateData) {
        log.info("更新诉辩观点，pointId: {}, 更新数据: {}", pointId, updateData);

        try {
            LitigationPoint point = baseMapper.selectById(pointId);
            if (point == null) {
                throw new RuntimeException("观点不存在，ID: " + pointId);
            }

            // 更新字段
            if (updateData.containsKey("content")) {
                point.setContent((String) updateData.get("content"));
            }
            if (updateData.containsKey("partyName")) {
                point.setPartyName((String) updateData.get("partyName"));
            }
            if (updateData.containsKey("evidence")) {
                point.setEvidence((List<Evidence>) updateData.get("evidence"));
            }
            if (updateData.containsKey("pointDate")) {
                String dateStr = (String) updateData.get("pointDate");
                try {
                    point.setPointDate(LocalDate.parse(dateStr));
                } catch (Exception e) {
                    log.warn("日期解析失败: {}", dateStr);
                }
            }
            // 如果更新数据中包含sortOrder，则更新它
            if (updateData.containsKey("sortOrder")) {
                point.setSortOrder((Integer) updateData.get("sortOrder"));
            }

            point.setUpdateTime(LocalDateTime.now());
            int updated = baseMapper.updateById(point);

            Map<String, Object> result = new HashMap<>();
            result.put("updated", updated > 0);
            result.put("pointId", pointId);

            log.info("更新诉辩观点完成，pointId: {}, 更新成功: {}", pointId, updated > 0);
            return result;

        } catch (Exception e) {
            log.error("更新诉辩观点失败", e);
            throw new RuntimeException("更新诉辩观点失败: " + e.getMessage());
        }
    }

    /**
     * 使用结构化数据更新诉辩观点
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateLitigationPointWithData(Long pointId, SaveLitigationPointsRequest.LitigationPointData pointData) {
        log.info("使用结构化数据更新诉辩观点，pointId: {}", pointId);

        try {
            // 查询现有观点
            LitigationPoint existingPoint = baseMapper.selectById(pointId);
            if (existingPoint == null) {
                throw new RuntimeException("观点不存在，ID: " + pointId);
            }

            // 更新观点数据
            if (pointData.getContent() != null) {
                existingPoint.setContent(pointData.getContent());
            }
            if (pointData.getEvidence() != null) {
                existingPoint.setEvidence(pointData.getEvidence());
            }
            if (pointData.getPointDate() != null) {
                try {
                    existingPoint.setPointDate(LocalDate.parse(pointData.getPointDate()));
                } catch (Exception e) {
                    log.warn("日期解析失败，保持原日期: {}", pointData.getPointDate());
                }
            }
            // 如果请求数据中包含sortOrder，则更新它
            if (pointData.getSortOrder() != null) {
                existingPoint.setSortOrder(pointData.getSortOrder());
            }

            existingPoint.setUpdateTime(LocalDateTime.now());
            int updated = baseMapper.updateById(existingPoint);

            Map<String, Object> result = new HashMap<>();
            result.put("updated", updated > 0);
            result.put("pointId", pointId);
            result.put("point", existingPoint);

            log.info("使用结构化数据更新诉辩观点完成，pointId: {}, 更新成功: {}", pointId, updated > 0);
            return result;

        } catch (Exception e) {
            log.error("使用结构化数据更新诉辩观点失败", e);
            throw new RuntimeException("更新诉辩观点失败: " + e.getMessage());
        }
    }

    /**
     * 添加诉辩关系
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addLitigationRelation(Map<String, Object> relationData) {
        log.info("添加诉辩关系，数据: {}", relationData);

        try {
            LitigationRelation relation = new LitigationRelation();
            relation.setCaseImportId(Long.valueOf(relationData.get("caseImportId").toString()));
            relation.setPlaintiffPointId((String) relationData.get("plaintiffPointId"));
            relation.setDefendantPointId((String) relationData.get("defendantPointId"));
            relation.setRelationType((String) relationData.get("relationType"));
            relation.setCreateTime(LocalDateTime.now());
            relation.setUpdateTime(LocalDateTime.now());

            litigationRelationMapper.insert(relation);

            Map<String, Object> result = new HashMap<>();
            result.put("relationId", relation.getId());
            result.put("success", true);

            log.info("添加诉辩关系成功，relationId: {}", relation.getId());
            return result;

        } catch (Exception e) {
            log.error("添加诉辩关系失败", e);
            throw new RuntimeException("添加诉辩关系失败: " + e.getMessage());
        }
    }

    /**
     * 更新诉辩关系
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateLitigationRelation(Long relationId, Map<String, Object> updateData) {
        log.info("更新诉辩关系，relationId: {}, 更新数据: {}", relationId, updateData);

        try {
            LitigationRelation relation = litigationRelationMapper.selectById(relationId);
            if (relation == null) {
                throw new RuntimeException("关系不存在，ID: " + relationId);
            }

            // 更新字段
            if (updateData.containsKey("relationType")) {
                relation.setRelationType((String) updateData.get("relationType"));
            }
            if (updateData.containsKey("plaintiffPointId")) {
                relation.setPlaintiffPointId((String) updateData.get("plaintiffPointId"));
            }
            if (updateData.containsKey("defendantPointId")) {
                relation.setDefendantPointId((String) updateData.get("defendantPointId"));
            }

            relation.setUpdateTime(LocalDateTime.now());
            int updated = litigationRelationMapper.updateById(relation);

            Map<String, Object> result = new HashMap<>();
            result.put("updated", updated > 0);
            result.put("relationId", relationId);

            log.info("更新诉辩关系完成，relationId: {}, 更新成功: {}", relationId, updated > 0);
            return result;

        } catch (Exception e) {
            log.error("更新诉辩关系失败", e);
            throw new RuntimeException("更新诉辩关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除诉辩关系
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteLitigationRelation(Long relationId) {
        log.info("删除诉辩关系，relationId: {}", relationId);

        try {
            int deleted = litigationRelationMapper.deleteById(relationId);

            Map<String, Object> result = new HashMap<>();
            result.put("deleted", deleted > 0);
            result.put("relationId", relationId);

            log.info("删除诉辩关系完成，relationId: {}, 删除成功: {}", relationId, deleted > 0);
            return result;

        } catch (Exception e) {
            log.error("删除诉辩关系失败", e);
            throw new RuntimeException("删除诉辩关系失败: " + e.getMessage());
        }
    }

    /**
     * 生成诉辩关系（异步方法，使用@Async）
     * @param caseImportId 案件导入ID
     */
    @Async
    @AnalysisTask(taskType = TaskType.LITIGATION_RELATION, description = "诉辩关系分析")
    public void generate(Long caseImportId) {
        log.info("开始执行诉辩关系分析任务，caseImportId: {}", caseImportId);

        // 执行分析任务核心逻辑
        executeAnalysisTaskInternal(caseImportId);

        log.info("诉辩关系分析任务异步执行完成，caseImportId: {}", caseImportId);
    }

    /**
     * 重新生成时删除相关数据（内部方法，依赖外层事务）
     * @param caseImportId 案件导入ID
     */
    public void removeDataForRegenerate(Long caseImportId) {
        log.info("开始清理案件 {} 的诉辩关系相关数据", caseImportId);

        // 删除诉辩观点
        this.remove(new LambdaQueryWrapper<LitigationPoint>()
                .eq(LitigationPoint::getCaseImportId, caseImportId));

        // 删除诉辩关系
        litigationRelationMapper.delete(new LambdaQueryWrapper<LitigationRelation>()
                .eq(LitigationRelation::getCaseImportId, caseImportId));

        log.info("案件 {} 的诉辩关系相关数据清理完成", caseImportId);
    }

    /**
     * 执行诉辩关系分析任务（内部实现，纯业务逻辑）
     * @param caseImportId 案件导入ID
     */
    @Transactional(rollbackFor = Exception.class)
    protected LitigationRelationData executeAnalysisTaskInternal(Long caseImportId) {
        log.info("开始执行诉辩关系分析任务核心逻辑，caseImportId: {}", caseImportId);

        boolean isSecondInstance = isSecondInstanceCase(caseImportId);
        LitigationRelationData relationData = generateAndSaveLitigationRelation(caseImportId, isSecondInstance);

        // 如果是民事一审，处理JudgmentSituation数据
        if (isFirstInstanceCase(caseImportId)) {
            processFirstInstanceJudgmentSituation(caseImportId);
        }

        log.info("诉辩关系分析任务核心逻辑执行完成，caseImportId: {}", caseImportId);
        return relationData;
    }

    /**
     * 判断是否为二审案件
     */
    private boolean isSecondInstanceCase(Long caseImportId) {
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        return CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
    }

    /**
     * 判断是否为民事一审案件
     */
    private boolean isFirstInstanceCase(Long caseImportId) {
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        return CaseTypeConstants.AjlxdmCode.MSYS.equals(caseRecord.getAjlxdm());
    }

    /**
     * 生成并保存诉辩关系
     */
    private LitigationRelationData generateAndSaveLitigationRelation(Long caseImportId, boolean isSecondInstance) {
        log.info("开始生成诉辩关系，审级：{}", isSecondInstance ? "二审" : "一审");

        LitigationRelationData relationData = generateLitigationRelationByAgent(caseImportId, isSecondInstance);
        setLitigationRelationCaseId(relationData, caseImportId);
        saveLitigationRelationData(relationData, caseImportId);

        return relationData;
    }

    /**
     * 使用Agent生成诉辩关系
     */
    private LitigationRelationData generateLitigationRelationByAgent(Long caseImportId, boolean isSecondInstance) {
        log.info("开始使用Agent生成诉辩关系，caseImportId: {}, 审级: {}", caseImportId, isSecondInstance ? "二审" : "一审");

        try {
            Map<String, String> propertyMap = buildPropertyMap(caseImportId, isSecondInstance);
            String bootYmlName = isSecondInstance ? "second-instance-relation.yml" : "litigation-relation.yml";
            String agentName = isSecondInstance ? "星洲-二审诉辩关系Agent" : "星洲-一审诉辩关系Agent";

            String res = agentTaskUtils.executeTask(agentName, "main", bootYmlName, propertyMap);
            LitigationRelationData relationData = JSON.parseObject(res, LitigationRelationData.class);

            log.info("Agent诉辩关系分析完成，caseImportId: {}, 审级: {}, 观点数量: {}, 关系数量: {}",
                    caseImportId, isSecondInstance ? "二审" : "一审",
                    relationData.getPoints() != null ? relationData.getPoints().size() : 0,
                    relationData.getRelations() != null ? relationData.getRelations().size() : 0);

            return relationData;
        } catch (Exception e) {
            log.error("Agent诉辩关系分析失败，caseImportId: {}, 错误: {}", caseImportId, e.getMessage(), e);
            throw new RuntimeException("诉辩关系分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建Agent属性映射
     */
    private Map<String, String> buildPropertyMap(Long caseImportId, boolean isSecondInstance) {
        Map<String, String> propertyMap = new HashMap<>();

        if (isSecondInstance) {
            String yspjsContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.YSPJS);
            String sszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.SSZ);
            propertyMap.put("material:YSPJS", yspjsContent);
            propertyMap.put("material:SSZ", sszContent);
        } else {
            String qszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.QSZ);
            String bcQszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.BCQSYJ);
            String fszContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.FSZ);
            String dbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ);
            String bcDbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.BCDBYJ);
            propertyMap.put("material:QSZ", qszContent + "\n" + bcQszContent);
            propertyMap.put("material:FSZ", fszContent);
            propertyMap.put("material:DBZ", dbzContent + "\n" + bcDbzContent);
        }

        return propertyMap;
    }

    /**
     * 获取默认提示词
     */
    private String getDefaultPrompt() {
        return systemPromptConfigService.getMessageByKey("LITIGATION_RELATION_PROMPT",
            "请分析案件材料，提取诉辩双方的观点和关系。");
    }

    /**
     * 设置诉辩关系数据的案件ID
     */
    private void setLitigationRelationCaseId(LitigationRelationData relationData, Long caseImportId) {
        if (relationData.getPoints() != null) {
            relationData.getPoints().forEach(point -> {
                point.setCaseImportId(caseImportId);
            });
        }

        if (relationData.getRelations() != null) {
            relationData.getRelations().forEach(relation -> {
                relation.setCaseImportId(caseImportId);
            });
        }
    }

    /**
     * 保存诉辩关系数据（内部方法，依赖外层事务）
     */
    private void saveLitigationRelationData(LitigationRelationData relationData, Long caseImportId) {
        log.info("开始保存诉辩关系数据，caseImportId: {}", caseImportId);

        saveLitigationPoints(relationData.getPoints());
        saveLitigationRelations(relationData.getRelations());

        log.info("诉辩关系数据保存完成");
    }

    /**
     * 保存诉辩观点
     */
    private void saveLitigationPoints(List<LitigationPointAiData> aiDataPoints) {
        if (aiDataPoints == null || aiDataPoints.isEmpty()) return;

        List<LitigationPoint> points = aiDataPoints.stream()
                .map(LitigationPointAiData::toLitigationPoint)
                .collect(Collectors.toList());

        this.saveBatch(points);
        log.info("保存诉辩观点 {} 条", points.size());
    }

    /**
     * 保存诉辩关系
     */
    private void saveLitigationRelations(List<LitigationRelation> relations) {
        if (relations == null || relations.isEmpty()) return;

        relations.forEach(relation -> relation.setId(null));
        litigationRelationService.saveBatch(relations);
        log.info("保存诉辩关系 {} 条", relations.size());
    }

    /**
     * 处理民事一审JudgmentSituation数据
     * @param caseImportId 案件导入ID
     */
    private void processFirstInstanceJudgmentSituation(Long caseImportId) {
        log.info("开始处理民事一审JudgmentSituation数据，caseImportId: {}", caseImportId);

        // 1. 过滤side=plaintiff的LitigationPoint数据
        List<LitigationPoint> plaintiffPoints = baseMapper.selectList(
                new LambdaQueryWrapper<LitigationPoint>()
                        .eq(LitigationPoint::getCaseImportId, caseImportId)
                        .eq(LitigationPoint::getSide, DisputeConstants.PartyType.PLAINTIFF)
                        .orderBy(true, true, LitigationPoint::getPointDate, LitigationPoint::getSortOrder)
        );

        if (plaintiffPoints.isEmpty()) {
            log.info("未找到原告方观点数据，跳过JudgmentSituation处理，caseImportId: {}", caseImportId);
            return;
        }

        // 2. 先删除JudgmentSituation中documentCaseType=CIVIL_JUDGEMENT_1的数据
        judgmentSituationService.deleteByCaseImportIdAndDocumentCaseType(
                caseImportId, CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1);
        log.info("删除现有CIVIL_JUDGEMENT_1类型的JudgmentSituation数据，caseImportId: {}", caseImportId);

        List<FileUploadRecord> counterClaimFiles = fileUploadRecordMapper.getFileRecordsByDocumentTypes(
                caseImportId, List.of(DocumentType.FSZ));

        boolean hasCounterClaim = !counterClaimFiles.isEmpty();
        log.info("反诉状文件检查结果，caseImportId: {}, 是否有反诉状: {}", caseImportId, hasCounterClaim);

        // 4. 获取辩方当事人名单（用于判断反诉原告）
        Set<String> defendantPartyNames = new HashSet<>();
        if (hasCounterClaim) {
            List<LitigationPoint> defendantPoints = baseMapper.selectList(
                    new LambdaQueryWrapper<LitigationPoint>()
                            .eq(LitigationPoint::getCaseImportId, caseImportId)
                            .eq(LitigationPoint::getSide, DisputeConstants.PartyType.DEFENDANT)
            );
            defendantPartyNames = defendantPoints.stream()
                    .map(LitigationPoint::getPartyName)
                    .collect(Collectors.toSet());
        }

        // 5. 将数据插入JudgmentSituation中
        int xh = 1;
        for (LitigationPoint point : plaintiffPoints) {
            JudgmentSituation judgmentSituation = JudgmentSituation.builder()
                    .caseImportRecordId(caseImportId)
                    .judgment(point.getContent())
                    .opinion(2) // 支持
                    .xh(xh++)
                    .dataType(CaseTypeConstants.DataType.TQ) // 提取
                    .documentCaseType(CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1)
                    .build();

            // 6. 根据是否有反诉状设置dsrxm和ssdw
            if (hasCounterClaim) {
                judgmentSituation.setDsrxm(point.getPartyName());
                if (defendantPartyNames.contains(point.getPartyName())) {
                    // 在辩方名单中，设置为本诉被告（反诉原告）
                    judgmentSituation.setSsdw("本诉被告");
                } else {
                    // 不在辩方名单中，设置为本诉原告
                    judgmentSituation.setSsdw("本诉原告");
                }
            }
            // 如果没有反诉状，则不设置dsrxm和ssdw（保持为null）

            judgmentSituationService.save(judgmentSituation);
        }

        log.info("民事一审JudgmentSituation数据处理完成，caseImportId: {}, 插入记录数: {}",
                caseImportId, plaintiffPoints.size());
    }

    /**
     * 诉辩关系数据传输对象
     */
    public static class LitigationRelationData {
        private List<LitigationPointAiData> points;
        private List<LitigationRelation> relations;

        // Getters and Setters
        public List<LitigationPointAiData> getPoints() { return points; }
        public void setPoints(List<LitigationPointAiData> points) { this.points = points; }
        public List<LitigationRelation> getRelations() { return relations; }
        public void setRelations(List<LitigationRelation> relations) { this.relations = relations; }
    }
}