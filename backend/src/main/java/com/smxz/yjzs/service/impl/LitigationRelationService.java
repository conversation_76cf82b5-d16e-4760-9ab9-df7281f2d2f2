package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.LitigationRelation;
import com.smxz.yjzs.mapper.LitigationRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 诉辩关联关系服务实现类
 */
@Service
public class LitigationRelationService extends ServiceImpl<LitigationRelationMapper, LitigationRelation> {

    /**
     * 根据案件导入ID获取关联关系列表
     * @param caseImportId
     * @return
     */
    public List<LitigationRelation> getByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<LitigationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LitigationRelation::getCaseImportId, caseImportId);
        return list(wrapper);
    }

    /**
     * 根据原告点ID获取关联关系列表
     * @param plaintiffPointId
     * @return
     */
    public List<LitigationRelation> getByPlaintiffPointId(Long plaintiffPointId) {
        LambdaQueryWrapper<LitigationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LitigationRelation::getPlaintiffPointId, plaintiffPointId);
        return list(wrapper);
    }

    /**
     * 根据被告点ID获取关联关系列表
     * @param defendantPointId
     * @return
     */
    public List<LitigationRelation> getByDefendantPointId(Long defendantPointId) {
        LambdaQueryWrapper<LitigationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LitigationRelation::getDefendantPointId, defendantPointId);
        return list(wrapper);
    }

    /**
     * 根据原告点ID删除关联关系
     * @param plaintiffPointId
     * @return
     */
    public boolean deleteByPlaintiffPointId(Long plaintiffPointId) {
        LambdaQueryWrapper<LitigationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LitigationRelation::getPlaintiffPointId, plaintiffPointId);
        return remove(wrapper);
    }

    /**
     * 根据被告点ID删除关联关系
     * @param defendantPointId
     * @return
     */
    public boolean deleteByDefendantPointId(Long defendantPointId) {
        LambdaQueryWrapper<LitigationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LitigationRelation::getDefendantPointId, defendantPointId);
        return remove(wrapper);
    }
} 