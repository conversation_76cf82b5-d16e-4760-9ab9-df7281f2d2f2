package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.extractor.service.MaterialRecognitionService;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.dto.CasePartyTabDTO;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.CaseParty;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.exception.BusinessException;
import com.smxz.yjzs.mapper.CaseImportRecordMapper;
import com.smxz.yjzs.mapper.CasePartyMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CasePartyService {

    @Autowired
    private CasePartyMapper casePartyMapper;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private MaterialRecognitionService materialRecognitionService;

    @Autowired
    private CaseImportRecordMapper caseImportRecordMapper;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    @Autowired
    private DocumentExtractorService documentExtractorService;

    private final RetryTemplate retryTemplate = RetryTemplate.builder()
            .maxAttempts(6)
            .exponentialBackoff(1000, 2, 30000)
            .build();

    public List<CaseParty> listByCaseImportId(Long caseImportId) {
        return this.casePartyMapper.listByCaseImportId(caseImportId);
    }

    public List<CasePartyTabDTO> listTabByCaseImportId(Long caseImportId) {
        List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);

        Map<String, List<CaseParty>> groupedCaseParties = caseParties.stream()
                .collect(Collectors.groupingBy(party -> {
                    if (StringUtils.isBlank(party.getPartyLabel())) {
                        return "";
                    } else {
                        return party.getPartyLabel() + "-" + party.getPartyType();
                    }
                }, Collectors.toList()));

        List<CasePartyTabDTO> result = new ArrayList<>();
        groupedCaseParties.forEach((key, value) -> {
            if (Objects.equals(key, "")) {
                for (CaseParty caseParty : value) {
                    CasePartyTabDTO casePartyTabDTO = new CasePartyTabDTO();
                    casePartyTabDTO.setPartyNameString(caseParty.getPartyName());
                    casePartyTabDTO.setPartyType(caseParty.getPartyType());
                    casePartyTabDTO.setPartyNameList(List.of(caseParty.getPartyName()));
                    result.add(casePartyTabDTO);
                }
            } else {
                CasePartyTabDTO casePartyTabDTO = new CasePartyTabDTO();
                StringBuilder sb = new StringBuilder();
                List<String> partyNameList = new ArrayList<>();
                for (CaseParty caseParty : value) {
                    if (!sb.isEmpty()) {
                        sb.append("、");
                    }
                    casePartyTabDTO.setPartyType(caseParty.getPartyType());
                    partyNameList.add(caseParty.getPartyName());
                    sb.append(caseParty.getPartyName());
                }
                casePartyTabDTO.setPartyNameString(sb.toString());
                casePartyTabDTO.setPartyNameList(partyNameList);
                result.add(casePartyTabDTO);
            }
        });

        return result;
    }


    /**
     * 获取案件当事人-诉讼费用专用，含合并项及拆解
     * @param caseImportId
     * @return
     */
    public List<CasePartyTabDTO> listForLegalFeesByCaseImportId(Long caseImportId) {
        List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);


        Map<String, List<CaseParty>> groupedCaseParties = caseParties.stream()
                .collect(Collectors.groupingBy(party -> {
                    if (StringUtils.isBlank(party.getPartyLabel())) {
                        return "";
                    } else {
                        return party.getPartyLabel() + "|" + party.getPartyType();
                    }
                }, Collectors.toList()));

        List<CasePartyTabDTO> result = new ArrayList<>();
        groupedCaseParties.forEach((key, value) -> {
            if (Objects.equals(key, "")) {
                // partyLabel为空的情况：每个当事人单独一项
                for (CaseParty caseParty : value) {
                    CasePartyTabDTO casePartyTabDTO = new CasePartyTabDTO();
                    casePartyTabDTO.setPartyNameString(caseParty.getPartyName());
                    casePartyTabDTO.setPartyType(caseParty.getPartyType());
                    casePartyTabDTO.setPartyNameList(List.of(caseParty.getPartyName()));
                    result.add(casePartyTabDTO);
                }
            } else {
                String[] keyParts = key.split("\\|");
                String partyLabel = keyParts[0];
                String partyType = keyParts[1];

                // partyLabel不为空的情况：既要合并项，也要单独项
                // 1. 先添加合并项
                CasePartyTabDTO casePartyTabDTO = new CasePartyTabDTO();
                StringBuilder sb = new StringBuilder();
                List<String> partyNameList = new ArrayList<>();
                for (CaseParty caseParty : value) {
                    if (!sb.isEmpty()) {
                        sb.append("、");
                    }
                    partyNameList.add(caseParty.getPartyName());
                    sb.append(caseParty.getPartyName());
                }
                // 合并项的partyType后面加上"-共同"
                casePartyTabDTO.setPartyType(partyType + "-共同");
                casePartyTabDTO.setPartyNameString(sb.toString());
                casePartyTabDTO.setPartyNameList(partyNameList);
                result.add(casePartyTabDTO);

                // 2. 再添加每个当事人的单独项
                for (CaseParty caseParty : value) {
                    CasePartyTabDTO individualPartyDTO = new CasePartyTabDTO();
                    individualPartyDTO.setPartyNameString(caseParty.getPartyName());
                    individualPartyDTO.setPartyType(caseParty.getPartyType());
                    individualPartyDTO.setPartyNameList(List.of(caseParty.getPartyName()));
                    result.add(individualPartyDTO);
                }
            }
        });

        // 按照指定的诉讼地位顺序排序
        result.sort((a, b) -> {
            int priorityA = getPartyTypePriority(a.getPartyType());
            int priorityB = getPartyTypePriority(b.getPartyType());

            // 先按优先级排序
            if (priorityA != priorityB) {
                return Integer.compare(priorityA, priorityB);
            }

            // 同一优先级内按partyType字符串排序
            return a.getPartyType().compareTo(b.getPartyType());
        });

        return result;
    }

    /**
     * 获取诉讼地位的排序优先级
     * @param partyType 诉讼地位类型
     * @return 优先级数字，数字越小优先级越高
     */
    private int getPartyTypePriority(String partyType) {
        if (partyType == null) {
            return 999; // 空值排在最后
        }

        // 按照指定顺序定义优先级
        if (partyType.contains("原告")) {
            return 1;
        } else if (partyType.contains("被告")) {
            return 2;
        } else if (partyType.contains("第三方证人")) {
            return 3;
        } else if (partyType.contains("第三人")) {
            return 4;
        } else if (partyType.contains("代理人")) {
            return 5;
        } else {
            return 999; // 其他类型排在最后
        }
    }

    /**
     * 分析案件当事人（异步方法，使用@Async）
     *
     * @param caseImportId 案件导入ID
     * @return CompletableFuture<Void>
     */
    @Async
    @AnalysisTask(taskType = TaskType.CASE_PARTY, description = "当事人分析")
    public void analyseCaseParty(Long caseImportId) {
        analyseCasePartySync(caseImportId);
    }

    /**
     * 分析案件当事人（同步实现，纯业务逻辑）
     *
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.CASE_PARTY, description = "当事人分析")
    public void analyseCasePartySync(Long caseImportId) {
        boolean isSecondInstance = isSecondInstanceCase(caseImportId);
        List<CaseParty> parties = getCasePartiesByAi(caseImportId, isSecondInstance);

        transactionTemplate.execute(action -> {
            casePartyMapper.deleteByCaseImportId(caseImportId);
            casePartyMapper.insert(parties);
            return true;
        });
        log.info("当事人分析任务执行完成，caseImportId: {}, 当事人数量: {}", caseImportId, parties.size());
    }

    @NotNull
    private List<CaseParty> getCasePartiesByAi(Long caseImportId, boolean isSecondInstance) {

        String taskName;
        Map<String, String> properties = new HashMap<>();
        List<DocumentType> documentTypes;
        if (isSecondInstance) {
            documentTypes = List.of(DocumentType.SSZ, DocumentType.YSPJS);
            List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, documentTypes);

            String ssz = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.SSZ)
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String yspjs = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.YSPJS)
                    .findFirst()
                    .map(FileUploadRecord::getExtractedText)
                    .orElse("");

            String dbzs = fileRecords.stream()
                    .filter(e -> DocumentType.DBZ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String bcdbyjs = fileRecords.stream()
                    .filter(e -> DocumentType.BCDBYJ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String tsbls = fileRecords.stream()
                    .filter(e -> DocumentType.TSBL == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            properties.put("一审判决书", yspjs);
            properties.put("上诉状", ssz);
            properties.put("二审答辩状", dbzs + bcdbyjs);
            properties.put("庭审笔录", tsbls);

            taskName = "task2";
        } else {
            documentTypes = List.of(DocumentType.QSZ, DocumentType.BCQSYJ, DocumentType.DBZ, DocumentType.BCDBYJ, DocumentType.TSBL);
            List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, documentTypes);
            String qszs = fileRecords.stream()
                    .filter(e -> DocumentType.QSZ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());
            String bcqszs = fileRecords.stream()
                    .filter(e -> DocumentType.BCQSYJ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String dbzs = fileRecords.stream()
                    .filter(e -> DocumentType.DBZ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String bcdbyjs = fileRecords.stream()
                    .filter(e -> DocumentType.BCDBYJ == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            String tsbls = fileRecords.stream()
                    .filter(e -> DocumentType.TSBL == e.getDocumentType())
                    .map(FileUploadRecord::getExtractedText)
                    .collect(Collectors.joining());

            properties.put("起诉状", qszs + bcqszs);
            properties.put("答辩状", dbzs + bcdbyjs);
            properties.put("庭审笔录", tsbls);

            taskName = "task1";
        }

        List<CaseParty> parties = retryTemplate.execute(context -> {
            log.info("开始执行当事人分析模型任务启动, 尝试第{}次", context.getRetryCount());
            return runAgentTask(taskName, properties);
        });

        parties.forEach(p -> p.setCaseImportId(caseImportId));
        return parties;
    }

    /**
     * 一审案件
     * 1、首先看起诉书是一份还是多份？如果一份，该起诉状中所有原告显示在一个页签上。多份时，每份起诉状的原告分别对应一个页签，但如果多份起诉书的原告都一样时，要合并处理只显示一个页签。
     * 2、再看证据清单有几份？每份的提交人都是谁？如果有多次，则要分开页签显示，每个页签上显示对应证据清单上的原告信息。如果证据清单上提取不到，则按第1条规则。
     * 3、答辩状是一样的逻辑。
     * 二审案件
     * 1、首先看上诉状有几份？如果是一份，该上诉状中的上诉人显示在一个页签上。多份时，每份上诉状的上诉人分别对应一个页签，但如果多份上诉状的上诉人是一致时，也要合并。
     * 2、再看证据清单有几份？每份的提交人都是谁？如果有多次，则要分开页签显示，每个页签上显示对应证据清单上的上诉人信息。如果证据清单上提取不到，则按第1条规则。
     * 3、答辩状是一样的逻辑。
     */
    @AnalysisTask(taskType = TaskType.CASE_PARTY, description = "当事人分析")
    public void analyseCasePartySync2(Long caseImportId) {
        boolean isSecondInstance = isSecondInstanceCase(caseImportId);

        List<CaseParty> caseParties = getCasePartiesByAi(caseImportId, isSecondInstance);
        Map<String, CaseParty> casePartyMap = caseParties.stream()
                .collect(Collectors.toMap(CaseParty::getPartyName, Function.identity()));

        List<List<Party>> displayParties = new ArrayList<>();
        List<FileUploadRecord> evidenceIndexes;

        if (isSecondInstance) {
            // 二审案件处理逻辑
            List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, List.of(DocumentType.ZJML, DocumentType.SSZ, DocumentType.DBZ, DocumentType.YSPJS));

            evidenceIndexes = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.ZJML).toList();
            List<FileUploadRecord> sszs = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.SSZ).toList();
            List<FileUploadRecord> dbzs = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.DBZ).toList();

            // 1. 处理上诉状
            processSSZs(sszs, displayParties, casePartyMap);

            // 2. 处理答辩状
            processSSDBZs(dbzs, displayParties, casePartyMap);
        } else {
            // 一审案件处理逻辑（保持原有逻辑）
            List<FileUploadRecord> fileRecords = fileUploadRecordMapper.getFileRecordsByDocumentTypes(caseImportId, List.of(DocumentType.ZJML, DocumentType.QSZ, DocumentType.DBZ));

            evidenceIndexes = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.ZJML).toList();
            List<FileUploadRecord> qszs = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.QSZ).toList();
            List<FileUploadRecord> dbzs = fileRecords.stream().filter(e -> e.getDocumentType() == DocumentType.DBZ).toList();

            // 1. 处理起诉书
            processQSZs(qszs, displayParties, casePartyMap);

            // 2. 处理答辩状
            processDBZs(dbzs, displayParties, casePartyMap);
        }
        // 3. 处理证据清单
        processEvidenceIndexes(evidenceIndexes, displayParties, casePartyMap);

        savePartiesToDatabase(caseImportId, displayParties);
    }

    private void processQSZs(List<FileUploadRecord> qszs, List<List<Party>> displayParties, Map<String, CaseParty> casePartyMap) {
        // 如果qszs为空，直接返回
        if (qszs.isEmpty()) {
            return;
        }

        for (FileUploadRecord qsz : qszs) {
            List<Party> extractedParties = extractPartiesFromDocument(qsz, DocumentType.QSZ, casePartyMap);

            if (!extractedParties.isEmpty()) {
                displayParties.add(extractedParties);
            }
        }
    }


    private void processDBZs(List<FileUploadRecord> dbzs, List<List<Party>> displayParties, Map<String, CaseParty> casePartyMap) {
        if (dbzs.isEmpty()) {
            return;
        }

        for (FileUploadRecord dbz : dbzs) {
            List<Party> extractedParties = extractPartiesFromDocument(dbz, DocumentType.DBZ, casePartyMap);

            if (!extractedParties.isEmpty()) {
                displayParties.add(extractedParties);
            }
        }
    }

    private void processSSZs(List<FileUploadRecord> sszs, List<List<Party>> displayParties, Map<String, CaseParty> casePartyMap) {
        if (sszs.isEmpty()) {
            return;
        }

        for (FileUploadRecord ssz : sszs) {
            List<Party> extractedParties = extractPartiesFromDocument(ssz, DocumentType.SSZ, casePartyMap);

            if (!extractedParties.isEmpty()) {
                displayParties.add(extractedParties);
            }
        }
    }

    private void processSSDBZs(List<FileUploadRecord> ssdbzs, List<List<Party>> displayParties, Map<String, CaseParty> casePartyMap) {
        if (ssdbzs.isEmpty()) {
            return;
        }

        for (FileUploadRecord dbz : ssdbzs) {
            List<Party> extractedParties = extractPartiesFromDocument(dbz, DocumentType.DBZ, casePartyMap);

            if (!extractedParties.isEmpty()) {
                displayParties.add(extractedParties);
            }
        }
    }


    private List<Party> extractPartiesFromDocument(FileUploadRecord record, DocumentType docType, Map<String, CaseParty> casePartyMap) {
        DocumentExtractorRequest request = DocumentExtractorRequest.builder()
                .fileName(record.getFileName())
                .content(record.getExtractedText())
                .extractMethod(ExtractMethod.REGEX)
                .build();

        BaseResponse<DocumentExtractorResult> response = documentExtractorService
                .extractElements(docType, request);

        Map<String, Object> elements = response.getData().getElements();
        List<String> casePartyStrings = (List<String>) elements.get("当事人信息");

        List<Party> parties = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(casePartyStrings)) {
            for (String casePartyString : casePartyStrings) {
                String[] casePartyArray = casePartyString.split("[:：]");

                String partyName = casePartyArray[1];
                String partyType;
                if (casePartyMap.containsKey(partyName)) {
                    partyType = casePartyMap.get(partyName).getPartyType();
                } else {
                    partyType = casePartyArray[0];
                }

                parties.add(new Party(partyName, partyType));
            }
        }

        return parties;
    }

    private void processEvidenceIndexes(List<FileUploadRecord> evidenceIndexes, List<List<Party>> displayParties, Map<String, CaseParty> casePartyMap) {
        if (evidenceIndexes.isEmpty()) {
            return;
        }

        for (FileUploadRecord evidenceIndex : evidenceIndexes) {
            DocumentExtractorRequest request = DocumentExtractorRequest.builder()
                    .fileName(evidenceIndex.getFileName())
                    .content(evidenceIndex.getExtractedText())
                    .extractMethod(ExtractMethod.REGEX)
                    .build();

            BaseResponse<DocumentExtractorResult> response = documentExtractorService
                    .extractElements(DocumentType.ZJML, request);

            Map<String, Object> elements = response.getData().getElements();
            String submitter = (String) elements.get("证据持有人姓名");

            if (StringUtils.isNotBlank(submitter)) {
                List<Party> partis = new ArrayList<>();
                CaseParty caseParty = casePartyMap.get(submitter);
                if (caseParty == null) {
                    partis.add(new Party(submitter, "第三人"));
                } else {
                    partis.add(new Party(submitter, caseParty.getPartyType()));
                }


                displayParties.add(partis);
            }
        }
    }

    private void savePartiesToDatabase(Long caseImportId, List<List<Party>> displayParties) {
        displayParties.sort(Comparator.comparing(List::size));

        for (int i = 0; i < displayParties.size(); i++) {
            List<Party> parties = displayParties.get(i);
            for (int j = i + 1; j < displayParties.size(); j++) {
                List<Party> temp = displayParties.get(j);
                Collection<Party> newParties = CollectionUtils.subtract(parties, temp);
                displayParties.set(i, new ArrayList<>(newParties));
            }
        }

        List<CaseParty> allParties = new ArrayList<>();

        for (List<Party> parties : displayParties) {
            for (Party party : parties) {
                // 创建新的CaseParty对象，避免修改原始数据
                CaseParty newParty = new CaseParty();
                newParty.setCaseImportId(caseImportId);
                newParty.setPartyName(party.partyName());
                newParty.setPartyType(party.partyType()); // 保持原有的partyType（原告/被告）
                allParties.add(newParty);
            }
        }

        // 合并同名且当事人类型相同的数据
        List<CaseParty> mergedParties = mergeDuplicateParties(allParties);

        transactionTemplate.execute(action -> {
            casePartyMapper.deleteByCaseImportId(caseImportId);
            casePartyMapper.insert(mergedParties);
            return true;
        });

        log.info("当事人分析任务执行完成，caseImportId: {}, 页签数量: {}, 当事人数量: {}, 合并后数量: {}",
                caseImportId, displayParties.size(), allParties.size(), mergedParties.size());
    }

    private List<CaseParty> mergeDuplicateParties(List<CaseParty> parties) {
        Map<String, CaseParty> mergedMap = new HashMap<>();

        for (CaseParty party : parties) {
            // 使用当事人名称和类型作为合并键
            String mergeKey = party.getPartyName() + "|" + party.getPartyType();

            // 如果不存在重复，直接添加到map中
            mergedMap.putIfAbsent(mergeKey, party);
        }

        return new ArrayList<>(mergedMap.values());
    }

    private boolean isSecondInstanceCase(Long caseImportId) {
        CaseImportRecord caseRecord = caseImportRecordMapper.selectById(caseImportId);
        return CaseTypeConstants.AjlxdmCode.MSES.equals(caseRecord.getAjlxdm());
    }

    private List<CaseParty> runAgentTask(String taskName, Map<String, String> properties) {
        String res = agentTaskUtils.executeTask("星洲Agent", taskName, "当事人分析.yml", properties);

        if (!JSON.isValid(res)) {
            throw new RuntimeException("当事人分析，Agent返回的JSON格式不正确: " + res);
        }

        List<CaseParty> result = JSON.parseArray(res, CaseParty.class);

        if (result == null) {
            log.warn("当事人分析，Agent返回结果为空, {}", res);
            throw new BusinessException("当事人分析，Agent返回结果为空");
        }

        for (CaseParty party : result) {
            if (StringUtils.isBlank(party.getPartyName()) || StringUtils.isBlank(party.getPartyType())) {
                log.warn("当事人分析，Agent返回结果为空, {}", res);
                throw new BusinessException("当事人分析，Agent返回结果中存在当事人姓名或类型为空的情况");
            }
        }

        return result;
    }


    public record Party(
            String partyName,
            String partyType
    ) {
    }

    /**
     * Mock方法，通过AI获取案件当事人信息
     *
     * @return 案件当事人列表
     */
    public List<CaseParty> getCasePartiesByAiMock() {
        List<CaseParty> caseParties = new ArrayList<>();

        // 创建原告"李思"
        CaseParty plaintiff = new CaseParty();
        plaintiff.setPartyName("李思");
        plaintiff.setPartyType("原告");
        caseParties.add(plaintiff);

        // 创建被告"张山"
        CaseParty defendant = new CaseParty();
        defendant.setPartyName("张山");
        defendant.setPartyType("被告");
        caseParties.add(defendant);

        return caseParties;
    }
}
