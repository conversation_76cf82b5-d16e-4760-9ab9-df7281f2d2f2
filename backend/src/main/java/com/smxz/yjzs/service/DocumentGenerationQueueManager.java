package com.smxz.yjzs.service;

import com.smxz.yjzs.config.DocumentGenerationConfig;
import com.smxz.yjzs.entity.DocumentGenerationTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 文书生成队列管理器
 * 负责管理文书生成的并发控制和队列管理
 */
@Slf4j
@Service
public class DocumentGenerationQueueManager {
    
    @Autowired
    private DocumentGenerationConfig config;
    
    /**
     * 运行中的任务映射 (documentId -> DocumentGenerationTask)
     */
    private final ConcurrentHashMap<String, DocumentGenerationTask> runningTasks = new ConcurrentHashMap<>();
    
    /**
     * 等待队列
     */
    private final BlockingQueue<String> waitingQueue = new LinkedBlockingQueue<>();
    
    /**
     * 所有任务映射 (documentId -> DocumentGenerationTask)
     */
    private final ConcurrentHashMap<String, DocumentGenerationTask> allTasks = new ConcurrentHashMap<>();
    
    public void init() {
        log.info("文书生成队列管理器初始化完成，最大并发数: {}", config.getMaxConcurrentCount());
    }
    
    /**
     * 尝试获取执行权限
     * @param documentId 文书ID
     * @return 是否立即获得执行权限
     * @throws InterruptedException 等待被中断
     */
    public QueueResult tryAcquire(String documentId) {
        log.info("文书生成请求: documentId={}, 当前运行数: {}/{}", 
                documentId, runningTasks.size(), config.getMaxConcurrentCount());
        
        // 检查是否已经在处理中
        if (allTasks.containsKey(documentId)) {
            // 决策理由：如果任务已在运行中，返回可执行状态；如果在队列中，返回队列位置
            if (runningTasks.containsKey(documentId)) {
                log.info("文书 {} 已在运行中，返回可执行状态", documentId);
                return QueueResult.canExecute();
            } else {
                // 计算队列位置
                int position = getQueuePosition(documentId);
                log.info("文书 {} 已在等待队列中，位置: {}", documentId, position);
                return QueueResult.inQueue(position);
            }
        }
        
        // 创建任务
        DocumentGenerationTask task = new DocumentGenerationTask(documentId);
        allTasks.put(documentId, task);
        
        // 使用同步块确保检查和添加的原子性
        synchronized (this) {
            // 检查是否可以立即执行
            if (runningTasks.size() < config.getMaxConcurrentCount()) {
                // 可以立即执行
                task.markAsRunning();
                runningTasks.put(documentId, task);
                log.info("文书 {} 立即开始执行，当前运行数: {}/{}", 
                        documentId, runningTasks.size(), config.getMaxConcurrentCount());
                return QueueResult.canExecute();
            }
        }
        
        // 需要等待，加入队列但不阻塞
        try {
            waitingQueue.put(documentId);
            int position = waitingQueue.size();
            log.info("文书 {} 加入等待队列，位置: {}", documentId, position);
            return QueueResult.inQueue(position);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            allTasks.remove(documentId);
            log.error("文书 {} 加入队列失败", documentId, e);
            return QueueResult.error("加入队列失败: " + e.getMessage());
        }
    }
    

    
    /**
     * 释放执行权限
     * @param documentId 文书ID
     */
    public void release(String documentId) {
        log.info("释放文书生成权限: documentId={}", documentId);
        
        DocumentGenerationTask task = allTasks.get(documentId);
        if (task != null) {
            task.markAsCompleted();
        }
        
        // 使用同步块确保移除操作的原子性
        synchronized (this) {
            // 先处理队列头部任务，再移除当前任务
            processNextInQueue();
            
            // 从运行中任务移除
            runningTasks.remove(documentId);
            
            // 从所有任务中移除
            allTasks.remove(documentId);
            
            log.info("文书 {} 执行完成，当前运行数: {}/{}, 等待队列长度: {}", 
                    documentId, runningTasks.size(), config.getMaxConcurrentCount(), waitingQueue.size());
            
            // 通知等待中的任务
            this.notifyAll();
        }
    }
    
    /**
     * 处理等待队列中的下一个任务
     * 决策理由：在任务完成时自动激活队列中的下一个任务，避免前端轮询延迟
     */
    private void processNextInQueue() {
        
        // 从队列头部获取下一个任务
        String nextDocumentId = waitingQueue.poll();
        if (nextDocumentId == null) {
            return; // 队列为空
        }
        
        // 获取任务对象并验证
        DocumentGenerationTask nextTask = allTasks.get(nextDocumentId);
        if (nextTask == null) {
            log.warn("队列中的任务 {} 不存在，跳过", nextDocumentId);
            return;
        }
        
        // 标记任务为运行状态
        nextTask.markAsRunning();
        runningTasks.put(nextDocumentId, nextTask);
        
        log.info("队列任务 {} 自动开始执行，当前运行数: {}/{}", 
                nextDocumentId, runningTasks.size(), config.getMaxConcurrentCount());
    }
    
    /**
     * 强制完成任务（管理员操作）
     * @param documentId 文书ID
     * @return 是否成功
     */
    public boolean forceComplete(String documentId) {
        log.info("强制完成文书生成: documentId={}", documentId);
        
        DocumentGenerationTask task = allTasks.get(documentId);
        if (task == null) {
            log.warn("文书 {} 不在队列中", documentId);
            return false;
        }
        
        // 从等待队列中移除
        waitingQueue.remove(documentId);
        
        // 释放权限
        release(documentId);
        
        return true;
    }
    
    /**
     * 检查队列状态（供前端轮询使用）
     * @param documentId 文书ID
     * @return 队列结果
     */
    public QueueResult checkQueueStatus(String documentId) {
        // 检查任务是否存在
        if (!allTasks.containsKey(documentId)) {
            return QueueResult.notFound();
        }
        
        // 检查是否在运行中
        if (runningTasks.containsKey(documentId)) {
            return QueueResult.canExecute();
        }
        
        // 计算队列位置
        int position = getQueuePosition(documentId);
        if (position > 0) {
            return QueueResult.inQueue(position);
        } else {
            return QueueResult.error("任务状态异常");
        }
    }
    
    /**
     * 获取任务在队列中的位置
     * @param documentId 文书ID
     * @return 队列位置（1开始），如果不在队列中返回0
     */
    private int getQueuePosition(String documentId) {
        List<String> queueList = new ArrayList<>(waitingQueue);
        int index = queueList.indexOf(documentId);
        return index >= 0 ? index + 1 : 0;
    }
    
    /**
     * 获取队列状态
     * @return 队列状态信息
     */
    public Map<String, Object> getQueueStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("maxConcurrentCount", config.getMaxConcurrentCount());
        status.put("runningCount", runningTasks.size());
        status.put("waitingCount", waitingQueue.size());
        status.put("runningTasks", new ArrayList<>(runningTasks.keySet()));
        status.put("waitingTasks", new ArrayList<>(waitingQueue));
        
        return status;
    }
    
    /**
     * 定时清理超时任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRateString = "#{@documentGenerationConfig.cleanupIntervalMinutes * 60 * 1000}")
    public void cleanupTimeoutTasks() {
        log.debug("开始清理超时任务...");
        
        List<String> timeoutTasks = new ArrayList<>();
        
        // 检查运行中的任务
        for (Map.Entry<String, DocumentGenerationTask> entry : runningTasks.entrySet()) {
            DocumentGenerationTask task = entry.getValue();
            if (task.isTimeout(config.getTimeoutHours())) {
                timeoutTasks.add(entry.getKey());
            }
        }
        
        // 清理超时任务
        for (String documentId : timeoutTasks) {
            log.warn("清理超时任务: documentId={}, 开始时间: {}", 
                    documentId, runningTasks.get(documentId).getStartTime());
            
            DocumentGenerationTask task = allTasks.get(documentId);
            if (task != null) {
                task.markAsTimeout();
            }
            
            release(documentId);
        }
        
        if (!timeoutTasks.isEmpty()) {
            log.info("清理了 {} 个超时任务", timeoutTasks.size());
        }
    }
    
    /**
     * 获取任务信息
     * @param documentId 文书ID
     * @return 任务信息
     */
    public DocumentGenerationTask getTask(String documentId) {
        return allTasks.get(documentId);
    }
    
    /**
     * 检查任务是否存在
     * @param documentId 文书ID
     * @return 是否存在
     */
    public boolean hasTask(String documentId) {
        return allTasks.containsKey(documentId);
    }
}