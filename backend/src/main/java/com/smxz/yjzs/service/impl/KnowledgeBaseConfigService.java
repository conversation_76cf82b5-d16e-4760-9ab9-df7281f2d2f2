package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.KnowledgeBaseConfig;
import com.smxz.yjzs.mapper.KnowledgeBaseConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 知识库配置服务类
 */
@Service
@Slf4j
public class KnowledgeBaseConfigService extends ServiceImpl<KnowledgeBaseConfigMapper, KnowledgeBaseConfig> {

    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 知识库配置
     */
    public KnowledgeBaseConfig getEnabledConfig(String configKey) {
        try {
            LambdaQueryWrapper<KnowledgeBaseConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeBaseConfig::getConfigKey, configKey)
                       .last("LIMIT 1");
            KnowledgeBaseConfig config = this.getOne(queryWrapper);

            if (config == null) {
                log.warn("未找到知识库配置，configKey: {}，使用默认配置", configKey);
                return getDefaultConfig(configKey);
            }
            log.info("获取知识库配置成功，configKey: {}, configName: {}, isEnabled: {}",
                    configKey, config.getConfigName(), config.getIsEnabled());
            return config;
        } catch (Exception e) {
            log.error("获取知识库配置失败，configKey: {}", configKey, e);
            return getDefaultConfig(configKey);
        }
    }

    /**
     * 获取默认配置
     * 当数据库中没有配置时使用
     *
     * @param configKey 配置键
     * @return 默认知识库配置
     */
    private KnowledgeBaseConfig getDefaultConfig(String configKey) {
        log.info("使用默认知识库配置，configKey: {}", configKey);
        
        return KnowledgeBaseConfig.builder()
                .configKey(configKey)
                .configName("默认法条知识库配置")
                .datasetIds(Arrays.asList("1590c8f4683811f0a96fe6b44c2d4c16"))
                .isEnabled(true) // 默认启用知识库查询
                .llmConfig(KnowledgeBaseConfig.LlmConfig.builder()
                        .modelName(null) // 使用默认模型
                        .temperature(0.1f)
                        .topP(0.3f)
                        .presencePenalty(0.4f)
                        .frequencyPenalty(0.7f)
                        .build())
                .promptConfig(KnowledgeBaseConfig.PromptConfig.builder()
                        .similarityThreshold(0.2f)
                        .keywordsSimilarityWeight(0.7f)
                        .topN(6)
                        .topK(1024)
                        .emptyResponse("")
                        .opener("Hi! I am your assistant, can I help you?")
                        .showQuote(true)
                        .prompt("案件材料：\n{case_material}\n\n知识库：{knowledge}\n\n输出格式：\n法律名称，法条编号，法条原文。\n例如：\n《中华人民共和国民法典》，第一零七十九条第一款，夫妻一方要求离婚的，可以由有关组织进行调解或者直接向人民法院提起离婚诉讼。")
                        .variables(java.util.List.of(
                            java.util.Map.of("key", "knowledge", "optional", true)
                        ))
                        .build())
                .isEnabled(true)
                .build();
    }

    /**
     * 初始化默认配置
     * 用于系统启动时创建默认配置
     */
    public void initDefaultConfig() {
        String defaultConfigKey = "LAW_INFO_QUERY";

        // 检查是否已存在配置
        LambdaQueryWrapper<KnowledgeBaseConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseConfig::getConfigKey, defaultConfigKey)
                   .last("LIMIT 1");
        KnowledgeBaseConfig existingConfig = this.getOne(queryWrapper);

        if (existingConfig != null) {
            log.info("知识库配置已存在，跳过初始化，configKey: {}, isEnabled: {}",
                    defaultConfigKey, existingConfig.getIsEnabled());
            return;
        }

        // 创建默认配置
        KnowledgeBaseConfig defaultConfig = getDefaultConfig(defaultConfigKey);
        defaultConfig.setConfigName("法条信息查询配置");

        try {
            this.save(defaultConfig);
            log.info("默认知识库配置初始化成功");
        } catch (Exception e) {
            log.error("默认知识库配置初始化失败", e);
        }
    }
}
