package com.smxz.yjzs.service;

/**
 * 队列操作结果封装类
 * 决策理由：封装队列状态信息，支持前端轮询机制
 */
public class QueueResult {
    private boolean canExecute;
    private boolean inQueue;
    private boolean error;
    private int queuePosition;
    private String message;
    
    private QueueResult(boolean canExecute, boolean inQueue, boolean error, int queuePosition, String message) {
        this.canExecute = canExecute;
        this.inQueue = inQueue;
        this.error = error;
        this.queuePosition = queuePosition;
        this.message = message;
    }
    
    /**
     * 创建可执行状态的结果
     */
    public static QueueResult canExecute() {
        return new QueueResult(true, false, false, 0, "可以立即执行");
    }
    
    /**
     * 创建排队状态的结果
     */
    public static QueueResult inQueue(int position) {
        return new QueueResult(false, true, false, position, "任务已加入队列，位置: " + position);
    }
    
    /**
     * 创建错误状态的结果
     */
    public static QueueResult error(String message) {
        return new QueueResult(false, false, true, 0, message);
    }
    
    /**
     * 创建任务不存在的结果
     */
    public static QueueResult notFound() {
        return new QueueResult(false, false, true, 0, "任务不存在");
    }
    
    // Getters
    public boolean isCanExecute() {
        return canExecute;
    }
    
    public boolean isInQueue() {
        return inQueue;
    }
    
    public boolean isError() {
        return error;
    }
    
    public int getQueuePosition() {
        return queuePosition;
    }
    
    public String getMessage() {
        return message;
    }
}