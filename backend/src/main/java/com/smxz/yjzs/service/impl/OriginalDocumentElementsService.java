package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.entity.OriginalDocumentElements;
import com.smxz.yjzs.mapper.OriginalDocumentElementsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 原审文书生成要素服务实现类
 * 1对多关系：一个案件可以对应多个原审文书生成要素记录
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class OriginalDocumentElementsService extends ServiceImpl<OriginalDocumentElementsMapper, OriginalDocumentElements> {

    /**
     * 根据案件ID查询原审文书生成要素列表（1对多关系）
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 原审文书生成要素列表
     */
    public List<OriginalDocumentElements> listByCaseImportRecordId(Long caseImportRecordId) {
        log.info("查询案件原审文书生成要素信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 根据案件ID删除原审文书生成要素记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportRecordId(Long caseImportRecordId) {
        log.info("删除案件原审文书生成要素信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportRecordId(caseImportRecordId);
    }

    /**
     * 批量保存原审文书生成要素记录
     *
     * @param originalDocumentElementsList 原审文书生成要素列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<OriginalDocumentElements> originalDocumentElementsList) {
        log.info("批量保存原审文书生成要素信息，数量: {}", originalDocumentElementsList.size());
        return super.saveBatch(originalDocumentElementsList);
    }

    /**
     * 先删除后批量保存原审文书生成要素记录
     *
     * @param caseImportRecordId 案件导入记录ID
     * @param originalDocumentElementsList 原审文书生成要素列表
     * @return 是否保存成功
     */
    public boolean replaceAllByCaseImportRecordId(Long caseImportRecordId, 
                                                  List<OriginalDocumentElements> originalDocumentElementsList) {
        log.info("替换案件原审文书生成要素信息，caseImportRecordId: {}, 数量: {}", 
                caseImportRecordId, originalDocumentElementsList.size());
        
        // 先删除现有记录
        deleteByCaseImportRecordId(caseImportRecordId);
        
        // 批量保存新记录
        if (!originalDocumentElementsList.isEmpty()) {
            return saveBatch(originalDocumentElementsList);
        }
        
        return true;
    }

    /**
     * 保存或更新原审文书生成要素信息
     * 如果已存在则更新，否则新增
     *
     * @param originalDocumentElements 原审文书生成要素信息
     * @return 是否保存成功
     */
    public boolean saveOrUpdate(OriginalDocumentElements originalDocumentElements) {
        log.info("保存或更新原审文书生成要素信息，caseImportRecordId: {}",
                originalDocumentElements.getCaseImportRecordId());

        if (originalDocumentElements.getId() != null) {
            // 如果有ID，直接更新
            return updateById(originalDocumentElements);
        } else {
            // 如果没有ID，新增记录
            return save(originalDocumentElements);
        }
    }
}
