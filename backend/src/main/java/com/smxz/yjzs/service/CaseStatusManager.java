package com.smxz.yjzs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.smxz.yjzs.entity.AnalysisTaskRecord;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.service.impl.AnalysisTaskRecordService;
import com.smxz.yjzs.service.impl.CaseImportRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 案件状态管理服务
 * 负责管理案件的整体状态（分析中/已完成）
 */
@Slf4j
@Service
public class CaseStatusManager {
    
    @Autowired
    private CaseImportRecordService caseImportRecordService;
    
    @Autowired
    private AnalysisTaskRecordService analysisTaskRecordService;
    
    /**
     * 设置案件为分析中状态
     * @param caseId 案件ID
     */
    public void setCaseAnalyzing(Long caseId) {
        try {
            LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CaseImportRecord::getId, caseId)
                        .set(CaseImportRecord::getImportStatus, 0); // 0-分析中
            
            boolean updated = caseImportRecordService.update(updateWrapper);
            if (updated) {
                log.debug("案件状态设置为分析中，caseId: {}", caseId);
            } else {
                log.warn("设置案件状态为分析中失败，caseId: {}", caseId);
            }
        } catch (Exception e) {
            log.error("设置案件状态为分析中失败，caseId: {}", caseId, e);
        }
    }
    
    /**
     * 检查并更新案件状态
     * 如果没有执行中的任务，则设置为已完成
     * @param caseId 案件ID
     */
    public void checkAndUpdateCaseStatus(Long caseId) {
        try {
            log.debug("开始检查案件状态，caseId: {}", caseId);
            
            // 检查是否有执行中的任务
            boolean hasRunningTasks = hasRunningTasks(caseId);
            
            if (!hasRunningTasks) {
                // 没有执行中的任务，设置为已完成
                setCaseCompleted(caseId);
            } else {
                log.debug("案件仍有执行中的任务，保持分析中状态，caseId: {}", caseId);
            }
        } catch (Exception e) {
            log.error("检查并更新案件状态失败，caseId: {}", caseId, e);
        }
    }
    
    /**
     * 设置案件为已完成状态
     * @param caseId 案件ID
     */
    private void setCaseCompleted(Long caseId) {
        try {
            LambdaUpdateWrapper<CaseImportRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CaseImportRecord::getId, caseId)
                        .set(CaseImportRecord::getImportStatus, 1); // 1-已完成
            
            boolean updated = caseImportRecordService.update(updateWrapper);
            if (updated) {
                log.info("案件状态设置为已完成，caseId: {}", caseId);
            } else {
                log.warn("设置案件状态为已完成失败，caseId: {}", caseId);
            }
        } catch (Exception e) {
            log.error("设置案件状态为已完成失败，caseId: {}", caseId, e);
        }
    }
    
    /**
     * 检查是否有执行中的任务
     * @param caseId 案件ID
     * @return true-有执行中的任务, false-没有执行中的任务
     */
    private boolean hasRunningTasks(Long caseId) {
        try {
            LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseId)
                       .eq(AnalysisTaskRecord::getStatus, 1); // 1-执行中
            
            long count = analysisTaskRecordService.count(queryWrapper);
            log.debug("案件 {} 有 {} 个执行中的任务", caseId, count);
            return count > 0;
        } catch (Exception e) {
            log.error("检查执行中任务失败，caseId: {}", caseId, e);
            return true; // 出错时保守处理，认为有执行中任务
        }
    }

}
