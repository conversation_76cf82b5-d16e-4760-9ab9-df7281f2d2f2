package com.smxz.yjzs.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * AI模型统一调用服务
 */
@Slf4j
@Service
public class AiModelService {

    @Autowired
    private ChatModel chatModel;

    @Autowired
    private SystemPromptConfigService systemPromptConfigService;

    /**
     * 调用AI模型
     * @param messages 消息列表
     * @return AI响应内容
     */
    public String callModel(List<Message> messages) {
        try {
            log.debug("准备调用AI模型，消息数量: {}", messages.size());
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            String result = chatModel.call(new Prompt(messages)).getResult().getOutput().getText();

            stopWatch.stop();
            log.debug("AI模型调用完成，耗时: {}ms，响应长度: {}", 
                    stopWatch.getTotalTimeMillis(), result != null ? result.length() : 0);

            return result;
        } catch (Exception e) {
            log.error("AI模型调用失败", e);
            throw new RuntimeException("AI模型调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用AI模型（使用系统提示词和用户消息）
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @return AI响应内容
     */
    public String callModel(String systemPrompt, String userMessage) {
        List<Message> messages = List.of(
                new SystemMessage(systemPrompt),
                new UserMessage(userMessage)
        );
        return callModel(messages);
    }

    /**
     * 调用AI模型（使用配置的系统提示词）
     * @param promptKey 提示词配置键
     * @param userMessage 用户消息
     * @param defaultPrompt 默认提示词（当配置获取失败时使用）
     * @return AI响应内容
     */
    public String callModelWithConfig(String promptKey, String userMessage, String defaultPrompt) {
        String systemPrompt = getSystemPrompt(promptKey, defaultPrompt);
        return callModel(systemPrompt, userMessage);
    }

    /**
     * 调用AI模型（支持自定义提示词）
     * @param promptKey 提示词配置键
     * @param userMessage 用户消息
     * @param customPrompt 自定义提示词（优先使用）
     * @param defaultPrompt 默认提示词
     * @return AI响应内容
     */
    public String callModelWithCustomPrompt(String promptKey, String userMessage, 
                                          String customPrompt, String defaultPrompt) {
        String systemPrompt;
        if (customPrompt != null && !customPrompt.trim().isEmpty()) {
            systemPrompt = customPrompt;
            log.debug("使用自定义提示词");
        } else {
            systemPrompt = getSystemPrompt(promptKey, defaultPrompt);
            log.debug("使用配置的系统提示词，键: {}", promptKey);
        }
        return callModel(systemPrompt, userMessage);
    }

    /**
     * 获取系统提示词
     * @param promptKey 提示词配置键
     * @param defaultPrompt 默认提示词
     * @return 系统提示词
     */
    private String getSystemPrompt(String promptKey, String defaultPrompt) {
        try {
            return systemPromptConfigService.getMessageByKey(promptKey, defaultPrompt);
        } catch (Exception e) {
            log.warn("获取系统提示词失败，键: {}，使用默认提示词", promptKey, e);
            return defaultPrompt;
        }
    }

    /**
     * 清理AI响应内容（移除markdown标记和think标签）
     * @param response AI原始响应
     * @return 清理后的响应
     */
    public String cleanResponse(String response) {
        if (response == null) {
            return null;
        }
        // 移除markdown标记
        String cleaned = response.replaceAll("```json\\n?|\\n?```", "");
        // 移除<think>xxx</think>标签及其内容（使用DOTALL模式匹配换行符）
        cleaned = cleaned.replaceAll("(?s)<think>.*?</think>", "");
        // 清理多余的空行
        cleaned = cleaned.replaceAll("\\n\\s*\\n", "\n");
        return cleaned.trim();
    }

    /**
     * 验证JSON格式
     * @param jsonString JSON字符串
     * @return 是否为有效JSON
     */
    public boolean isValidJson(String jsonString) {
        try {
            com.alibaba.fastjson2.JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            log.warn("JSON格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 带重试的AI调用
     * @param promptKey 提示词配置键
     * @param userMessage 用户消息
     * @param customPrompt 自定义提示词
     * @param defaultPrompt 默认提示词
     * @param maxRetryCount 最大重试次数
     * @param validateJson 是否验证JSON格式
     * @return AI响应内容
     */
    public String callModelWithRetry(String promptKey, String userMessage, String customPrompt, 
                                   String defaultPrompt, int maxRetryCount, boolean validateJson) {
        int retryCount = 0;
        
        while (retryCount < maxRetryCount) {
            try {
                log.info("开始请求AI生成内容，第{}次尝试", retryCount + 1);
                
                String aiResponse = callModelWithCustomPrompt(promptKey, userMessage, customPrompt, defaultPrompt);
                log.info("获取到AI响应，长度: {}", aiResponse != null ? aiResponse.length() : 0);

                // 清理响应内容
                String cleanResponse = cleanResponse(aiResponse);

                // 如果需要验证JSON格式
                if (validateJson) {
                    if (isValidJson(cleanResponse)) {
                        log.info("JSON格式验证通过");
                        return cleanResponse;
                    } else {
                        log.warn("JSON格式验证失败，准备重试");
                        retryCount++;
                        continue;
                    }
                }
                
                return cleanResponse;
                
            } catch (Exception e) {
                log.error("AI调用失败", e);
                retryCount++;
                if (retryCount >= maxRetryCount) {
                    throw new RuntimeException("AI调用失败，已达到最大重试次数: " + e.getMessage(), e);
                }
            }
        }
        
        throw new RuntimeException("AI调用失败: 多次尝试后仍无法生成有效响应");
    }

    /**
     * 针对关系图谱分析的AI调用
     */
    public String callForRelationGraph(String fileContents, String customPrompt) {
        return callModelWithRetry(
                "RelationGraphHandler",
                fileContents,
                customPrompt,
                "请分析以下文档内容，提取其中的人物关系，生成关系图谱的JSON格式数据。",
                3,
                true
        );
    }

    /**
     * 针对案件时序链分析的AI调用
     */
    public String callForCaseTimeline(String fileContents, String customPrompt, String defaultPrompt) {
        return callModelWithRetry(
                "CaseTimelineHandler",
                fileContents,
                customPrompt,
                defaultPrompt,
                3,
                true
        );
    }

    /**
     * 针对诉辩关系分析的AI调用
     */
    public String callForLitigationRelation(String fileContents, String customPrompt) {
        return callModelWithRetry(
                "LitigationRelationHandler",
                fileContents,
                customPrompt,
                "请分析以下案件材料，提取诉辩观点和观点之间的关系，返回JSON格式数据。",
                3,
                true
        );
    }

    /**
     * 针对争议焦点分析的AI调用
     */
    public String callForDisputePoints(String fileContents, String defaultPrompt) {
        return callModelWithRetry(
                "DisputePointsHandler",
                fileContents,
                null,
                defaultPrompt,
                3,
                true
        );
    }

    /**
     * 针对矛盾识别分析的AI调用
     */
    public String callForContradiction(String fileContents, String customPrompt, String defaultPrompt) {
        return callModelWithRetry(
                "ContradictionHandler",
                fileContents,
                customPrompt,
                defaultPrompt,
                3,
                true
        );
    }

    /**
     * 针对争议焦点说理分析的AI调用
     * @param messageContent 消息内容（包含认定事实和争议焦点）
     * @param customPrompt 自定义提示词
     * @param defaultPrompt 默认提示词
     * @return AI生成的争议焦点说理
     */
    public String callForDisputePointReasoning(String messageContent, String customPrompt, String defaultPrompt) {
        return callModelWithRetry(
                "DisputePointReasoning",
                messageContent,
                customPrompt,
                defaultPrompt,
                3,
                false
        );
    }

}
