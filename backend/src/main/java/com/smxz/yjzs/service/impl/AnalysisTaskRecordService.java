package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.yjzs.dto.TaskStatusDTO;

import java.util.List;
import com.smxz.agent.dto.AgentSubTaskResult;
import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.yjzs.entity.AnalysisTaskRecord;
import com.smxz.yjzs.enums.TaskStatus;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.AnalysisTaskRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分析任务记录服务实现类
 */
@Slf4j
@Service
public class AnalysisTaskRecordService extends ServiceImpl<AnalysisTaskRecordMapper, AnalysisTaskRecord> {

    public void updateTaskSuccess(Long taskId) {
        LocalDateTime endTime = LocalDateTime.now();
        AnalysisTaskRecord existingTask = getById(taskId);

        long durationMs = 0;
        if (existingTask != null && existingTask.getStartTime() != null) {
            // 使用 Math.abs() 确保时间差为时间同步问题导致的负数
            durationMs = Math.abs(Duration.between(existingTask.getStartTime(), endTime).toMillis());

            // 记录调试信息，帮助排查时间问题
            log.debug("任务时间计算 - 开始时间: {}, 结束时间: {}, 耗时: {} ms",
                     existingTask.getStartTime(), endTime, durationMs);
        }

        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .status(2) // 成功
                .endTime(endTime)
                .durationMs(durationMs)
                .build();
        updateById(task);
        log.info("任务 {} 执行成功，耗时 {} ms", taskId, durationMs);
    }

    /**
     * 更新任务状态为成功（包含子任务结果）
     * @param taskId 任务ID
     * @param agentTaskInfoList 任务信息列表
     */
    public void updateTaskSuccess(Long taskId, List<AgentTaskInfo> agentTaskInfoList) {
        LocalDateTime endTime = LocalDateTime.now();
        AnalysisTaskRecord existingTask = getById(taskId);

        long durationMs = 0;
        if (existingTask != null && existingTask.getStartTime() != null) {
            // 使用 Math.abs() 确保时间差为正数，避免因时区或时间同步问题导致的负数
            durationMs = Math.abs(Duration.between(existingTask.getStartTime(), endTime).toMillis());

            // 记录调试信息，帮助排查时间问题
            log.debug("任务时间计算 - 开始时间: {}, 结束时间: {}, 耗时: {} ms",
                     existingTask.getStartTime(), endTime, durationMs);
        }

        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .agentTaskInfo(agentTaskInfoList)
                .status(TaskStatus.SUCCESS.getCode())
                .endTime(endTime)
                .durationMs(durationMs)
                .build();
        updateById(task);
        log.info("任务 {} 执行成功，耗时 {} ms", taskId, durationMs);
    }

    public void updateTaskFailure(Long taskId, String errorMessage) {
        LocalDateTime endTime = LocalDateTime.now();
        AnalysisTaskRecord existingTask = getById(taskId);

        long durationMs = 0;
        if (existingTask != null && existingTask.getStartTime() != null) {
            // 使用 Math.abs() 确保时间差为正数，避免因时区或时间同步问题导致的负数
            durationMs = Math.abs(Duration.between(existingTask.getStartTime(), endTime).toMillis());

            // 记录调试信息，帮助排查时间问题
            log.debug("任务时间计算 - 开始时间: {}, 结束时间: {}, 耗时: {} ms",
                     existingTask.getStartTime(), endTime, durationMs);
        }

        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .status(3) // 失败
                .endTime(endTime)
                .durationMs(durationMs)
                .errorMessage(errorMessage)
                .build();
        updateById(task);
        log.error("任务 {} 执行失败，耗时 {} ms，错误信息: {}", taskId, durationMs, errorMessage);
    }

    /**
     * 更新任务状态为失败（包含子任务结果）
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param agentTaskInfoList 任务信息列表
     */
    public void updateTaskFailure(Long taskId, String errorMessage, List<AgentTaskInfo> agentTaskInfoList) {
        LocalDateTime endTime = LocalDateTime.now();
        AnalysisTaskRecord existingTask = getById(taskId);

        long durationMs = 0;
        if (existingTask != null && existingTask.getStartTime() != null) {
            durationMs = Math.abs(Duration.between(existingTask.getStartTime(), endTime).toMillis());
            log.debug("任务时间计算 - 开始时间: {}, 结束时间: {}, 耗时: {} ms",
                     existingTask.getStartTime(), endTime, durationMs);
        }

        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .status(3) // 失败
                .endTime(endTime)
                .durationMs(durationMs)
                .errorMessage(errorMessage)
                .agentTaskInfo(agentTaskInfoList)
                .build();
        updateById(task);
        log.error("任务 {} 执行失败，耗时 {} ms，错误信息: {}，任务信息列表: {}",
                taskId, durationMs, errorMessage, agentTaskInfoList);
    }

    /**
     * 创建正在执行的分析任务记录
     * @param caseImportId 案件导入ID
     * @param taskType 任务类型
     * @param taskName 任务名称
     * @return 任务记录
     */
    public AnalysisTaskRecord createRunningAnalysisTask(Long caseImportId, String taskType, String taskName) {
        LocalDateTime now = LocalDateTime.now();
        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .caseImportId(caseImportId)
                .taskType(taskType)
                .taskName(taskName)
                .status(1) // 执行中
                .createTime(now)
                .startTime(now) // 设置开始时间
                .build();
        save(task);

        log.info("为案件 {} 创建了分析任务: {} ({})，状态: 执行中", caseImportId, taskName, taskType);
        return task;
    }

    /**
     * 仅更新任务的agentTaskInfo字段
     * @param taskId 任务ID
     * @param agentTaskInfoList 任务信息列表
     */
    public void updateTaskInfo(Long taskId, List<AgentTaskInfo> agentTaskInfoList) {
        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .agentTaskInfo(agentTaskInfoList)
                .build();
        updateById(task);
        log.info("任务信息更新成功，taskId: {}, 任务信息列表: {}", taskId, agentTaskInfoList);
    }

    /**
     * 仅更新任务结果，不更新完成时间（用于流式任务的finalProcess）
     * @param taskId 任务ID
     * @param agentTaskInfoList 任务信息列表
     */
    public void updateTaskResultOnly(Long taskId, List<AgentTaskInfo> agentTaskInfoList) {
        AnalysisTaskRecord task = AnalysisTaskRecord.builder()
                .id(taskId)
                .agentTaskInfo(agentTaskInfoList)
                .build();
        updateById(task);
        log.info("任务结果更新成功（不更新完成时间），taskId: {}, 任务信息列表: {}", taskId, agentTaskInfoList);
    }

    /**
     * 终止指定案件和任务类型的所有执行中任务
     * @param caseImportId 案件导入ID
     * @param taskType 任务类型
     * @param errorMessage 错误信息
     */
    public void terminateRunningTasksByType(Long caseImportId, String taskType, String errorMessage) {
        LocalDateTime endTime = LocalDateTime.now();

        // 查找所有执行中的指定类型任务
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId)
                   .eq(AnalysisTaskRecord::getTaskType, taskType)
                   .eq(AnalysisTaskRecord::getStatus, TaskStatus.RUNNING.getCode());

        List<AnalysisTaskRecord> runningTasks = list(queryWrapper);

        if (runningTasks.isEmpty()) {
            log.debug("没有找到执行中的任务需要终止，caseImportId: {}, taskType: {}", caseImportId, taskType);
            return;
        }

        // 批量更新任务状态为失败
        for (AnalysisTaskRecord task : runningTasks) {
            long durationMs = 0;
            if (task.getStartTime() != null) {
                durationMs = Math.abs(Duration.between(task.getStartTime(), endTime).toMillis());
            }

            AnalysisTaskRecord updateTask = AnalysisTaskRecord.builder()
                    .id(task.getId())
                    .status(TaskStatus.FAILED.getCode())
                    .endTime(endTime)
                    .durationMs(durationMs)
                    .errorMessage(errorMessage)
                    .build();
            updateById(updateTask);

            log.info("终止执行中任务，taskId: {}, caseImportId: {}, taskType: {}, 错误信息: {}",
                    task.getId(), caseImportId, taskType, errorMessage);
        }

        log.info("成功终止 {} 个执行中的任务，caseImportId: {}, taskType: {}",
                runningTasks.size(), caseImportId, taskType);
    }

    /**
     * 获取案件的所有任务状态
     * @param caseImportId 案件导入ID
     * @return 任务状态列表
     */
    public List<TaskStatusDTO> getCaseTaskStatus(Long caseImportId) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId)
                   .orderByDesc(AnalysisTaskRecord::getCreateTime);

        List<AnalysisTaskRecord> taskRecords = list(queryWrapper);

        return taskRecords.stream()
                .map(this::convertToTaskStatusDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取特定任务类型的最新状态
     * @param caseImportId 案件导入ID
     * @param taskType 任务类型
     * @return 任务状态DTO，如果不存在则返回null
     */
    public TaskStatusDTO getTaskStatus(Long caseImportId, String taskType) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId)
                   .eq(AnalysisTaskRecord::getTaskType, taskType)
                   .orderByDesc(AnalysisTaskRecord::getCreateTime)
                   .last("LIMIT 1");

        AnalysisTaskRecord taskRecord = getOne(queryWrapper);

        return taskRecord != null ? convertToTaskStatusDTO(taskRecord) : null;
    }

    /**
     * 获取特定任务类型的完整任务记录（包含agentTaskInfo）
     * @param caseImportId 案件导入ID
     * @param taskType 任务类型
     * @return 完整任务记录，如果不存在则返回null
     */
    public AnalysisTaskRecord getFullTaskRecord(Long caseImportId, String taskType) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId)
                   .eq(AnalysisTaskRecord::getTaskType, taskType)
                   .orderByDesc(AnalysisTaskRecord::getCreateTime)
                   .last("LIMIT 1");

        AnalysisTaskRecord taskRecord = getOne(queryWrapper);
        log.info("获取完整任务记录，caseImportId: {}, taskType: {}, 找到记录: {}",
                caseImportId, taskType, taskRecord != null);

        return taskRecord;
    }

    /**
     * 检查任务是否正在执行
     * @param caseImportId 案件导入ID
     * @param taskType 任务类型
     * @return true-正在执行，false-未执行或已完成
     */
    public boolean isTaskRunning(Long caseImportId, String taskType) {
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId)
                   .eq(AnalysisTaskRecord::getTaskType, taskType)
                   .eq(AnalysisTaskRecord::getStatus, TaskStatus.RUNNING.getCode())
                   .orderByDesc(AnalysisTaskRecord::getCreateTime)
                   .last("LIMIT 1");

        AnalysisTaskRecord runningTask = getOne(queryWrapper);
        boolean isRunning = runningTask != null;

        log.debug("检查任务执行状态，caseImportId: {}, taskType: {}, isRunning: {}",
                 caseImportId, taskType, isRunning);

        return isRunning;
    }

    /**
     * 将AnalysisTaskRecord转换为TaskStatusDTO
     * @param taskRecord 任务记录
     * @return 任务状态DTO
     */
    private TaskStatusDTO convertToTaskStatusDTO(AnalysisTaskRecord taskRecord) {
        TaskStatus taskStatus = TaskStatus.fromCode(taskRecord.getStatus());


        // 截取错误信息前150个字符，不然前端展示有问题
        String errorMessage  = taskRecord.getErrorMessage();
        if (StringUtils.isNoneEmpty(errorMessage)) {
            errorMessage = errorMessage.length() > 150 ? errorMessage.substring(0, 150) : errorMessage;
        }
        return TaskStatusDTO.builder()
                .taskType(taskRecord.getTaskType())
                .taskName(taskRecord.getTaskName())
                .status(taskRecord.getStatus())
                .statusText(taskStatus != null ? taskStatus.getName() : "未知状态")
                .startTime(taskRecord.getStartTime())
                .endTime(taskRecord.getEndTime())
                .durationMs(taskRecord.getDurationMs())
                .errorMessage(errorMessage)
                .createTime(taskRecord.getCreateTime())
                .build();
    }
}
