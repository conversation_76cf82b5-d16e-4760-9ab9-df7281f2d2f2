package com.smxz.yjzs.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smxz.document.convert.DocumentConvertService;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.common.model.recognition.MaterialRecognitionResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.extractor.service.MaterialRecognitionService;
import com.smxz.ocr.model.OcrPreview;
import com.smxz.ocr.model.OcrResponseData;
import com.smxz.ocr.model.enums.FileType;
import com.smxz.ocr.service.OcrService;
import com.smxz.yjzs.common.utils.ArchiveUtils;
import com.smxz.yjzs.common.utils.ArchiveUtils.ExtractedFile;
import com.smxz.yjzs.common.utils.CaseNumberExtractUtil;
import com.smxz.yjzs.common.utils.FileExtractUtil;
import com.smxz.yjzs.common.utils.ImageCompressionUtil;
import com.smxz.yjzs.common.utils.ThreadPoolUtils;

import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.exception.FilePreprocessException;
import com.smxz.yjzs.mapper.CaseImportRecordMapper;
import com.smxz.yjzs.service.impl.CaseImportRecordService;
import com.smxz.yjzs.service.impl.FileUploadRecordService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.smxz.yjzs.config.OcrRetryConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 文件预处理服务
 * 负责文件的转换、文本提取和数据入库
 */
@Slf4j
@Service
public class FilePreprocessService {

    @Autowired
    private FileUploadRecordService fileUploadRecordService;
    @Autowired
    private MaterialRecognitionService materialRecognitionService;
    @Autowired
    private DocumentExtractorService documentExtractorService;
    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Autowired
    private OcrService ocrService;

    @Autowired
    private DocumentConvertService documentConvertService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CaseImportRecordMapper caseImportRecordMapper;

    /**
     * OCR重试配置
     */
    @Autowired
    private OcrRetryConfig ocrRetryConfig;

    /**
     * 要素提取并发线程数配置
     */
    @Value("${thread-pool.core-pool-size:10}")
    private int corePoolSize;

    @Value("${thread-pool.max-pool-size:20}")
    private int maxPoolSize;

    @Value("${thread-pool.queue-capacity:200}")
    private int queueCapacity;

    @Value("${thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;


    /**
     * 要素提取处理线程池
     */
    private ExecutorService elementExtractService;

    /**
     * OCR重试模板配置
     * 使用指数退避策略，配置从application.yml读取
     */
    private RetryTemplate ocrRetryTemplate;

    /**
     * 初始化OCR重试模板
     */
    @PostConstruct
    public void initOcrRetryTemplate() {
        // 初始化OCR重试模板，使用指数退避策略
        this.ocrRetryTemplate = RetryTemplate.builder()
                .maxAttempts(ocrRetryConfig.getMaxAttempts())
                .exponentialBackoff(
                    ocrRetryConfig.getInitialDelay(),
                    ocrRetryConfig.getMultiplier(),
                    ocrRetryConfig.getMaxDelay()
                )
                .build();
        log.info("OCR重试模板初始化完成，最大重试次数: {}, 初始延迟: {}ms, 延迟倍数: {}, 最大延迟: {}ms",
                ocrRetryConfig.getMaxAttempts(),
                ocrRetryConfig.getInitialDelay(),
                ocrRetryConfig.getMultiplier(),
                ocrRetryConfig.getMaxDelay());
    }

    /**
     * 初始化要素提取线程池
     */
    @PostConstruct
    public void initelementExtractService() {
        ThreadFactory elementExtractThreadFactory = new ThreadFactory() {
            private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = defaultFactory.newThread(r);
                t.setName(String.format("element-extract-async-%d", threadNumber.getAndIncrement()));
                return t;
            }
        };
        // 使用 ThreadPoolExecutor 的构造函数，提供有界队列
        this.elementExtractService = new ThreadPoolExecutor(
                corePoolSize,       // corePoolSize: 核心线程数
                maxPoolSize,       // maximumPoolSize: 最大线程数
                keepAliveSeconds,               // keepAliveTime: 空闲线程存活时间
                TimeUnit.SECONDS,               // keepAliveTime 的单位
                new ArrayBlockingQueue<>(queueCapacity), // workQueue: 使用有界队列，例如 ArrayBlockingQueue
                elementExtractThreadFactory,    // threadFactory: 自定义的线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // handler:
        );

        log.info("要素提取线程池初始化完成，核心线程数: {}, 队列容量: {}", corePoolSize, queueCapacity);
    }

    /**
     * 销毁要素提取线程池
     */
    @PreDestroy
    public void destroyelementExtractService() {
        if (elementExtractService != null && !elementExtractService.isShutdown()) {
            elementExtractService.shutdown();
            log.info("要素提取线程池已关闭");
        }
    }

    /**
     * 根据案件类型获取对应的文档类型数组
     *
     * @param ajlxdm 案件类型代码
     * @return 对应的DocumentType数组
     */
    public DocumentType[] getDocumentTypesByCase(String ajlxdm) {
        if (CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)) {
            // 二审案件：没有起诉状，有上诉状和一审判决书
            return new DocumentType[]{
                    DocumentType.LSZ,
                    DocumentType.SFZ,
                    DocumentType.SSCYRZTZGCL, // 诉讼参与人主体资格材料
                    DocumentType.ZJML,      // 证据目录
                    DocumentType.ZJ,         // 证据
                    DocumentType.DBZ,       // 答辩状
                    DocumentType.SSZ,       // 上诉状
                    DocumentType.PJS,     // 一审判决书
                    DocumentType.BCDBYJ,    // 补充答辩意见
                    DocumentType.TSBL,      // 庭审笔录
                    DocumentType.LADJB,
                    DocumentType.JAXXB,
                    DocumentType.SPLCGLXXB,
                    DocumentType.AJPDTZS, //案件排定表
                    DocumentType.HYTZCRYTZS  //合议庭组成人员通知书
            };
        } else {
            // 一审案件（默认）：没有上诉状和一审判决书
            return new DocumentType[]{
                    DocumentType.LSZ,
                    DocumentType.SFZ,
                    DocumentType.SSCYRZTZGCL, // 诉讼参与人主体资格材料
                    DocumentType.ZJML,      // 证据目录
                    DocumentType.ZJ,         // 证据
                    DocumentType.DBZ,       // 答辩状
                    DocumentType.FSZ,       // 反诉状
                    DocumentType.SSZ,       // 上诉状
                    DocumentType.QSZ,       // 起诉状
                    DocumentType.BCQSYJ,    // 补充起诉意见
                    DocumentType.BCDBYJ,    // 补充答辩意见
                    DocumentType.TSBL ,     // 庭审笔录
                    DocumentType.PJS,    // 一审判决书
                    DocumentType.LADJB,
                    DocumentType.JAXXB,
                    DocumentType.SPLCGLXXB,
                    DocumentType.AJPDTZS, //案件排定表
                    DocumentType.HYTZCRYTZS  //合议庭组成人员通知书
            };
        }
    }

    /**
     * 分析文件并设置基础文档类型（在OCR之前执行）
     * 不包含需要文本内容的判决书案号识别
     *
     * @param fileRecords 文件记录列表
     * @param ajlxdm 案件类型代码
     */
    public void analyzeBasicDocumentType(List<FileUploadRecord> fileRecords, String ajlxdm) {
        if (fileRecords == null || fileRecords.isEmpty()) {
            log.warn("文件记录列表为空，跳过基础文档类型分析");
            return;
        }

        // 根据案件类型获取对应的文档类型数组
        DocumentType[] caseDocumentTypes = getDocumentTypesByCase(ajlxdm);

        // 进行文件分析，判断文件类型
        List<String> fileNames = fileRecords.stream()
                .map(FileUploadRecord::getFileName)
                .collect(Collectors.toList());

        try {
            BaseResponse<MaterialRecognitionResult> response = materialRecognitionService.batchRecognizeMaterials(fileNames, caseDocumentTypes);

            if (response != null && response.getData() != null && response.getData().getDetails() != null) {
                response.getData().getDetails().forEach(detail ->
                    fileRecords.stream()
                            .filter(record -> record.getFileName().equals(detail.getFileName()))
                            .forEach(record -> record.setDocumentType(detail.getMatchedType()))
                );
                log.info("基础文档类型分析完成，案件类型: {}, 处理文件数: {}", ajlxdm, fileRecords.size());
            } else {
                log.warn("文档类型识别服务返回空结果");
            }
        } catch (Exception e) {
            log.error("基础文档类型分析失败，案件类型: {}", ajlxdm, e);
        }
    }

    /**
     * 分析判决书文档并设置精确类型（在文本提取后执行）
     * 根据案号判断是一审判决书还是二审判决书
     *
     * @param fileRecords 文件记录列表
     */
    public void analyzeJudgmentDocumentType(List<FileUploadRecord> fileRecords) {
        if (fileRecords == null || fileRecords.isEmpty()) {
            log.warn("文件记录列表为空，跳过判决书文档类型分析");
            return;
        }

        try {
            // 筛选出判决书文件并进行案号识别
            List<FileUploadRecord> judgmentRecords = fileRecords.stream()
                    .filter(record -> DocumentType.PJS.equals(record.getDocumentType()))
                    .filter(record -> record.getExtractedText() != null && !record.getExtractedText().trim().isEmpty())
                    .collect(Collectors.toList());

            if (judgmentRecords.isEmpty()) {
                log.debug("未找到需要进行案号识别的判决书文件");
                return;
            }

            log.info("开始进行判决书案号识别，文件数量: {}", judgmentRecords.size());

            judgmentRecords.parallelStream().forEach(record -> {
                try {
                    BaseResponse<DocumentExtractorResult> extracted = documentExtractorService.extractElements(
                            DocumentType.PJS,
                            DocumentExtractorRequest.builder()
                                    .extractMethod(ExtractMethod.REGEX)
                                    .content(record.getExtractedText())
                                    .build()
                    );

                    if (extracted.getData() != null && extracted.getData().getElements() != null) {
                        String caseNumber = (String) extracted.getData().getElements().get("案号");
                        DocumentType documentType = CaseTypeConstants.determinePjsByCaseName(caseNumber);
                        record.setDocumentType(documentType);
                        log.debug("判决书案号识别完成，文件: {}, 案号: {}, 类型: {}",
                                record.getFileName(), caseNumber, documentType);
                    } else {
                        log.warn("判决书案号提取失败，文件: {}", record.getFileName());
                    }
                } catch (Exception e) {
                    log.error("判决书案号识别异常，文件: {}", record.getFileName(), e);
                }
            });

            log.info("判决书案号识别完成，处理文件数: {}", judgmentRecords.size());
        } catch (Exception e) {
            log.error("判决书文档类型分析失败", e);
        }
    }
    /**
     * 处理案件压缩包
     * 包括：解压压缩文件、上传文件、文档转换、文本提取
     *
     * 支持的压缩格式：ZIP、RAR、7Z
     * 支持的内部文件：PDF、DOC、DOCX、PNG、JPG、JPEG
     *
     * @param caseImportId 案件导入ID
     * @param archive 压缩包文件（支持ZIP/RAR/7Z格式）
     * @throws FilePreprocessException 当文件格式不支持或处理失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void processArchive(Long caseImportId, MultipartFile archive) {
        log.info("开始处理案件压缩包，caseImportId: {}, 文件: {}", caseImportId, archive.getOriginalFilename());

        Path tempDir = null;
        try {
            // 第一步：解压压缩文件（返回临时目录和文件列表）
            ArchiveExtractionResult extractionResult = extractArchiveFilesWithTempDir(archive);
            tempDir = extractionResult.getTempDir();
            List<File> extractedFiles = extractionResult.getExtractedFiles();
            log.info("解压完成，文件数量: {}", extractedFiles.size());

            // 第二步：转换为PDF
            Map<File, File> pdfFiles = convertToPdfFiles(extractedFiles);
            log.info("PDF转换完成，转换数量: {}", pdfFiles.size());

            // 第三步：上传原文件和PDF到MinIO
            List<FileUploadRecord> uploadRecords = uploadFilesToMinio(pdfFiles);
            log.info("文件上传完成，上传数量: {}", uploadRecords.size());

            // 第四步：保存到数据库
            saveFilesToDatabase(uploadRecords, caseImportId);

            log.info("案件压缩包处理完成，caseImportId: {}", caseImportId);

        } catch (Exception e) {
            log.error("处理案件压缩包失败，caseImportId: {}", caseImportId, e);
            throw FilePreprocessException.processingFailed("压缩包处理", e);
        } finally {
            // 清理临时目录
            if (tempDir != null) {
                try {
                    deleteDirectory(tempDir);
                    log.info("清理临时目录: {}", tempDir);
                } catch (IOException e) {
                    log.warn("清理临时目录失败: {}", tempDir, e);
                }
            }
        }
    }



    /**
     * 处理案件的所有文件
     * 包括：文档转PDF、文本提取、数据入库
     *
     * @param caseImportId 案件导入ID
     * @throws RuntimeException 处理失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void processFiles(Long caseImportId) {
        log.info("开始处理案件文件，caseImportId: {}", caseImportId);

        // 查询案件的所有文件
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId);
        List<FileUploadRecord> fileRecords = fileUploadRecordService.list(queryWrapper);

        if (fileRecords.isEmpty()) {
            throw FilePreprocessException.fileNotFound("案件中未找到需要处理的文件");
        }

        processFiles(caseImportId, fileRecords);
    }

    /**
     * 处理指定的文件列表
     *
     * @param caseImportId 案件导入ID
     * @param fileRecords 文件记录列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void processFiles(Long caseImportId, List<FileUploadRecord> fileRecords) {
        log.info("开始处理文件列表，caseImportId: {}, 文件数量: {}", caseImportId, fileRecords.size());

        int totalFiles = fileRecords.size();
        int successFiles = 0;
        int failedFiles = 0;

        for (FileUploadRecord fileRecord : fileRecords) {
            try {
                boolean success = processSingleFile(fileRecord);
                if (success) {
                    successFiles++;
                } else {
                    failedFiles++;
                }
            } catch (Exception e) {
                log.error("处理文件失败: {}", fileRecord.getFileName(), e);
                failedFiles++;
                // 更新文件状态为失败
                updateFileStatus(fileRecord.getId(), 3);
            }
        }

        log.info("文件处理完成，总数: {}, 成功: {}, 失败: {}", totalFiles, successFiles, failedFiles);
    }

    /**
     * 重新处理案件的所有文件
     * 只验证文本内容是否存在，不重新提取文本
     * 分析任务由案件服务调用
     *
     * @param caseImportId 案件导入ID
     * @throws RuntimeException 处理失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void reprocessFiles(Long caseImportId) {
        log.info("开始验证案件文件，caseImportId: {}", caseImportId);

        // 查询案件的所有文件
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId);
        List<FileUploadRecord> fileRecords = fileUploadRecordService.list(queryWrapper);

        if (fileRecords.isEmpty()) {
            throw FilePreprocessException.fileNotFound("案件中未找到需要处理的文件");
        }

        // 检查是否有提取的文本内容
        boolean hasExtractedText = fileRecords.stream()
                .anyMatch(file -> file.getExtractedText() != null && !file.getExtractedText().trim().isEmpty());

        if (!hasExtractedText) {
            log.warn("未找到已提取的文本内容，caseImportId: {}", caseImportId);
            throw FilePreprocessException.fileNotFound("未找到已提取的文本内容");
        }

        log.info("案件文件验证完成，caseImportId: {}", caseImportId);
    }

    /**
     * 处理单个上传的文件
     * 包括：上传到MinIO、创建数据库记录、转换PDF、提取文本
     *
     * @param caseImportId 案件导入ID
     * @param file 上传的文件
     * @throws RuntimeException 处理失败时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void processSingleFile(Long caseImportId, MultipartFile file) {
        log.info("开始处理单个上传文件: {}, 案件ID: {}", file.getOriginalFilename(), caseImportId);

        try {
            // 1. 上传文件到MinIO
            String fileName = file.getOriginalFilename();
            String fileExtension = FilenameUtils.getExtension(fileName);
            String storedFileName = UUID.randomUUID().toString() + "." + fileExtension;

            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(storedFileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());

            // 2. 创建文件记录
            FileUploadRecord fileRecord = FileUploadRecord.builder()
                    .caseImportId(caseImportId)
                    .fileName(fileName)
                    .filePath(response.object())
                    .fileSize(file.getSize())
                    .fileType(fileExtension)
                    .uploadTime(new Date())
                    .status(0) // 待处理
                    .build();

            fileUploadRecordService.save(fileRecord);

            // 3. 处理文件（转换PDF、提取文本）
            boolean success = processSingleFile(fileRecord);

            if (!success) {
                log.error("单个文件处理失败: {}", fileName);
                throw FilePreprocessException.processingFailed(fileName, null);
            }

            log.info("单个文件处理成功: {}", fileName);

        } catch (Exception e) {
            log.error("处理单个文件失败: {}", file.getOriginalFilename(), e);
            throw FilePreprocessException.processingFailed(file.getOriginalFilename(), e);
        }
    }

    /**
     * 处理单个文件
     */
    private boolean processSingleFile(FileUploadRecord fileRecord) {
        log.info("开始处理文件: {}", fileRecord.getFileName());

        try {
            // 更新状态为处理中
            updateFileStatus(fileRecord.getId(), 1);

            // 1. 文档转PDF（如果需要）
            boolean convertSuccess = convertToPdfIfNeeded(fileRecord);
            if (!convertSuccess) {
                updateFileStatus(fileRecord.getId(), 3);
                return false;
            }

            // 2. 提取文本内容
            String extractedText = extractTextFromPdf(fileRecord);
            if (extractedText == null || extractedText.trim().isEmpty()) {
                updateFileStatus(fileRecord.getId(), 3);
                return false;
            }

            // 3. OCR识别
            OcrResponseData ocrResult = performOcrRecognition(fileRecord);

            // 4. 保存文本内容和OCR结果到数据库
            fileRecord.setExtractedText(extractedText);
            if (ocrResult != null) {
                fileRecord.setOcrResult(ocrResult.getResults());
                fileRecord.setOcrPreviews(ocrResult.getPreviews());
            }

            // 6. 裁判书案号识别（如果是判决书文件）
            if (DocumentType.PJS.equals(fileRecord.getDocumentType()) &&
                extractedText != null && !extractedText.trim().isEmpty()) {
                try {
                    analyzeJudgmentDocumentType(Arrays.asList(fileRecord));
                    log.info("裁判书案号识别完成: {}", fileRecord.getFileName());
                } catch (Exception e) {
                    log.error("裁判书案号识别失败: {}", fileRecord.getFileName(), e);
                }
            }

            updateFileStatus(fileRecord.getId(), 2);
            fileUploadRecordService.updateById(fileRecord);

            log.info("文件处理成功: {}, 提取文本长度: {}", fileRecord.getFileName(), extractedText.length());
            return true;

        } catch (Exception e) {
            log.error("处理文件失败: {}", fileRecord.getFileName(), e);
            updateFileStatus(fileRecord.getId(), 3);
            return false;
        }
    }

    /**
     * 如果需要，将文档转换为PDF
     */
    private boolean convertToPdfIfNeeded(FileUploadRecord fileRecord) {
        try {
            // 如果已经有PDF路径，跳过转换
            if (fileRecord.getPdfPath() != null && !fileRecord.getPdfPath().isEmpty()) {
                log.info("文件已有PDF路径，跳过转换: {}", fileRecord.getFileName());
                return true;
            }

            String fileName = fileRecord.getFilePath();

            // 检查文件格式是否支持转换
            if (!documentConvertService.isSupportedWordFormat(fileName)) {
                log.info("文件格式不支持转换，直接使用原文件: {}", fileRecord.getFileName());
                fileRecord.setPdfPath(fileRecord.getFilePath());
                fileUploadRecordService.updateById(fileRecord);
                return true;
            }

            // 从MinIO获取原文件流
            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(fileRecord.getFilePath())
                            .build())) {

                // 使用DocumentUtil转换文档为PDF字节数组
                byte[] pdfBytes;
                if (documentConvertService.isSupportedWordFormat(fileName)) {
                    pdfBytes = documentConvertService.wordToPdfBytes(inputStream);
                    // 上传PDF文件到MinIO
                    String pdfFileName = FilenameUtils.removeExtension(fileName) + ".pdf";
                    ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(pdfFileName)
                            .stream(new ByteArrayInputStream(pdfBytes), pdfBytes.length, -1)
                            .contentType("application/pdf")
                            .build());

                    // 更新PDF路径
                    fileRecord.setPdfPath(response.object());
                    fileUploadRecordService.updateById(fileRecord);

                    log.info("PDF转换成功: {}", pdfFileName);
                    return true;
                }

              return true;
            }

        } catch (Exception e) {
            log.error("文档转PDF失败: {}", fileRecord.getFileName(), e);
            return false;
        }
    }

    /**
     * 从文件中提取文本内容
     * 根据文件类型选择不同的提取方式：
     * - Word/PDF 文件：使用 FileExtractUtil.extractString
     * - 图片文件：从 OCR 结果中提取文本
     * - QT（其他类型）文件：跳过文本提取
     */
    private String extractTextFromPdf(FileUploadRecord fileRecord) {
        try {
            // 检查文件类型，如果是QT（其他类型），跳过文本提取
            if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                log.debug("文件类型为QT（其他类型），跳过文本提取: {}", fileRecord.getFileName());
                return null;
            }

            String fileType = fileRecord.getFileType().toLowerCase();

            // 判断是否为图片文件
//            if (isImageFile(fileType)) {
             return extractTextFromOcrResult(fileRecord);
//            }

            // 从MinIO获取原始文件
//            try (InputStream fileStream = minioClient.getObject(
//                    GetObjectArgs.builder()
//                            .bucket(minioConfig.getBucketName())
//                            .object(fileRecord.getFilePath())
//                            .build())) {
//
//                // 使用 FileExtractUtil 提取文本
//                String extractedText = FileExtractUtil.extractString(fileStream, fileRecord.getFileName());
//
//                if (StringUtils.isBlank(extractedText)) {
//                    log.warn("文件文本提取为空: {}", fileRecord.getFileName());
//                    return null;
//                }
//
//                // 格式化输出，保持与原有格式一致
//                String fullText = String.format("=== 文件ID：%d | 文件：%s ===\n%s",
//                        fileRecord.getId(),
//                        fileRecord.getFileName(),
//                        extractedText);
//
//                log.info("文本提取成功: {}, 文本长度: {}",
//                        fileRecord.getFileName(), fullText.length());
//
//                return fullText;
//            }

        } catch (Exception e) {
            log.error("文本提取失败: {}", fileRecord.getFileName(), e);
            return null;
        }
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String fileType) {
        return "png".equalsIgnoreCase(fileType) ||
               "jpg".equalsIgnoreCase(fileType) ||
               "jpeg".equalsIgnoreCase(fileType);
    }

    /**
     * 执行OCR识别，失败时自动重试
     *
     * @param base64Content 文件的base64内容
     * @param fileType 文件类型
     * @param needPreview 是否需要预览
     * @param fileName 文件名（用于日志）
     * @return OCR识别结果，如果重试后仍失败则返回null
     */
    private OcrResponseData executeOcrWithRetry(String base64Content,
                                               FileType fileType,
                                               boolean needPreview,
                                               String fileName) {

        return ocrRetryTemplate.execute(context -> {
            int attemptCount = context.getRetryCount() + 1;
            log.info("开始OCR识别，文件: {}, 第{}次尝试", fileName, attemptCount);

            try {
                // 调用OCR服务
                OcrResponseData result = ocrService.getLayoutData(base64Content, fileType, needPreview);

                // 检查结果是否有效
                if (isValidOcrResult(result)) {
                    if (attemptCount > 1) {
                        log.info("OCR识别重试成功，文件: {}, 第{}次尝试成功", fileName, attemptCount);
                    } else {
                        log.info("OCR识别成功，文件: {}", fileName);
                    }
                    return result;
                } else {
                    log.warn("OCR识别结果无效，文件: {}, 第{}次尝试失败", fileName, attemptCount);
                    // 抛出异常触发重试
                    throw new RuntimeException("OCR识别结果无效");
                }

            } catch (Exception e) {
                log.error("OCR识别异常，文件: {}, 第{}次尝试", fileName, attemptCount, e);
                throw e; // 重新抛出异常触发重试
            }
        }, context -> {
            // 重试耗尽后的回调
            log.error("OCR识别最终失败，文件: {}, 已重试{}次", fileName, context.getRetryCount() + 1);
            return null;
        });
    }

    /**
     * 检查OCR结果是否有效
     *
     * @param result OCR识别结果
     * @return true表示结果有效，false表示结果无效需要重试
     */
    private boolean isValidOcrResult(OcrResponseData result) {
        if (result == null) {
            return false;
        }

        // 检查预览数据
        if (result.getPreviews() == null || result.getPreviews().isEmpty()) {
            return false;
        }

        // 检查识别结果
        if (result.getResults() == null || result.getResults().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 从OCR结果中提取文本内容
     */
    private String extractTextFromOcrResult(FileUploadRecord fileRecord) {
        try {
            if (fileRecord.getOcrResult() == null || fileRecord.getOcrResult().isEmpty()) {
                log.debug("文件 {} 的OCR结果为空", fileRecord.getFileName());
                return null;
            }

            // 使用 stream 提取每页的 recTexts 并合并
            String result = fileRecord.getOcrResult().stream()
                    .map(ocrResult -> ocrResult.getRecTexts())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("\n"));

            if (StringUtils.isNotBlank(result)) {
                // 格式化输出，保持与原有格式一致
                String fullText = String.format("=== 文件ID：%d | 文件：%s ===\n%s",
                        fileRecord.getId(),
                        fileRecord.getFileName(),
                        result);

                log.info("从OCR结果提取文本成功，文件: {}, 内容长度: {}", fileRecord.getFileName(), fullText.length());
                return fullText;
            } else {
                log.debug("OCR结果中未找到有效的文本内容，文件: {}", fileRecord.getFileName());
                return null;
            }

        } catch (Exception e) {
            log.error("解析OCR结果失败，文件: {}", fileRecord.getFileName(), e);
            return null;
        }
    }

    /**
     * 更新文件状态
     */
    private void updateFileStatus(Long fileId, Integer status) {
        FileUploadRecord updateRecord = new FileUploadRecord();
        updateRecord.setId(fileId);
        updateRecord.setStatus(status);
        fileUploadRecordService.updateById(updateRecord);
    }

    /**
     * 解压结果类
     */
    private static class ArchiveExtractionResult {
        private final Path tempDir;
        private final List<File> extractedFiles;

        public ArchiveExtractionResult(Path tempDir, List<File> extractedFiles) {
            this.tempDir = tempDir;
            this.extractedFiles = extractedFiles;
        }

        public Path getTempDir() {
            return tempDir;
        }

        public List<File> getExtractedFiles() {
            return extractedFiles;
        }
    }

    /**
     * 解压压缩文件并返回临时目录信息
     * 支持格式：ZIP（自动检测编码，支持中文文件名）
     *
     * @param archive 压缩包
     * @return 解压结果（包含临时目录和文件列表）
     */
    private ArchiveExtractionResult extractArchiveFilesWithTempDir(MultipartFile archive) {
        log.info("开始解压压缩文件: {}", archive.getOriginalFilename());

        String fileName = archive.getOriginalFilename();
        if (fileName == null) {
            throw FilePreprocessException.fileNotFound("压缩包文件名不能为空");
        }

        // 检查是否为支持的压缩包格式
        if (!ArchiveUtils.isArchiveFile(archive)) {
            String fileExtension = FilenameUtils.getExtension(fileName).toLowerCase();
            throw FilePreprocessException.unsupportedArchiveFormat(fileExtension);
        }

        try {
            // 创建临时目录
            Path tempDir = Files.createTempDirectory("extract_" + System.currentTimeMillis() + "_");

            // 使用 ArchiveUtils 解压文件
            List<File> extractedFiles = ArchiveUtils.extractArchive(archive, tempDir.toString());

            log.info("压缩文件解压完成，提取文件数量: {}", extractedFiles.size());
            return new ArchiveExtractionResult(tempDir, extractedFiles);

        } catch (IOException e) {
            log.error("解压压缩文件失败: {}", fileName, e);
            throw FilePreprocessException.processingFailed("压缩文件解压", e);
        }
    }

    /**
     * 第二步：转换为PDF
     *
     * @param extractedFiles 解压后的文件列表
     * @return 原文件到PDF文件的映射
     */
    public Map<File, File> convertToPdfFiles(List<File> extractedFiles) {
        log.info("开始转换文件为PDF，文件数量: {}", extractedFiles.size());

        Map<File, File> pdfFiles = new HashMap<>();

        for (File originalFile : extractedFiles) {
            try {
                String fileName = originalFile.getName();
                String fileExtension = FilenameUtils.getExtension(fileName).toLowerCase();

                File pdfFile = originalFile;
                if ("pdf".equals(fileExtension)) {
                    // 已经是PDF文件，直接使用
                    pdfFile = originalFile;
                    log.debug("文件已是PDF格式: {}", fileName);
                } else if ("wps".equals(fileExtension) || "doc".equals(fileExtension) || "docx".equals(fileExtension)) {
                    // Word文档转PDF
                    try (FileInputStream fis = new FileInputStream(originalFile)) {
                        byte[] pdfBytes = documentConvertService.wordToPdfBytes(fis);
                        pdfFile = createPdfFile(originalFile, pdfBytes, fileName);
                        log.debug("Word文档转换成功: {}", fileName);
                    }
                } else if ("jpg".equals(fileExtension) || "jpeg".equals(fileExtension) || "png".equals(fileExtension)) {
                    // 图片文件进行等比尺寸压缩处理
                    File resizedFile = ImageCompressionUtil.compressImageFile(
                            originalFile,
                            960,  // 最大宽度
                            960   // 最大高度
                    );
                    pdfFile = resizedFile;
                    log.debug("图片文件等比压缩处理完成: {}", fileName);
                }

                pdfFiles.put(originalFile, pdfFile);

            } catch (Exception e) {
                log.error("文件转换失败: {}", originalFile.getName(), e);
                // 转换失败时，使用原文件
                pdfFiles.put(originalFile, originalFile);
            }
        }

        log.info("PDF转换完成，处理文件数量: {}", pdfFiles.size());
        return pdfFiles;
    }

    /**
     * 第三步：上传原文件和PDF到MinIO
     *
     * @param pdfFiles 原文件到PDF文件的映射
     * @return 文件上传记录列表
     */
    public List<FileUploadRecord> uploadFilesToMinio(Map<File, File> pdfFiles) {
        log.info("开始上传文件到MinIO，文件数量: {}", pdfFiles.size());

        List<FileUploadRecord> uploadRecords = new ArrayList<>();

        for (Map.Entry<File, File> entry : pdfFiles.entrySet()) {
            File originalFile = entry.getKey();
            File pdfFile = entry.getValue();

            try {
                // 获取原始文件名（如果是ExtractedFile则使用原始名称，否则使用文件名）
                String originalFileName;
                if (originalFile instanceof ExtractedFile) {
                    originalFileName = ((ExtractedFile) originalFile).getOriginalFileName();
                } else {
                    originalFileName = originalFile.getName();
                }

                String fileExtension = FilenameUtils.getExtension(originalFileName);

                // 上传原文件
                String originalStoredName = UUID.randomUUID().toString() + "." + fileExtension;
                String originalPath = uploadFileToMinio(originalFile, originalStoredName, getContentType(fileExtension));

                // 上传PDF文件（如果不同于原文件）
                String pdfPath;
                if (pdfFile.equals(originalFile)) {
                    // 原文件就是PDF或无需转换的文件
                    pdfPath = originalPath;
                } else {
                    // 上传转换后的PDF文件
                    String pdfStoredName = UUID.randomUUID().toString() + ".pdf";
                    pdfPath = uploadFileToMinio(pdfFile, pdfStoredName, "application/pdf");
                }

                // 创建文件上传记录
                FileUploadRecord uploadRecord = FileUploadRecord.builder()
                        .fileName(originalFileName)  // 使用原始文件名
                        .filePath(originalPath)
                        .pdfPath(pdfPath)
                        .fileSize(originalFile.length())
                        .fileType(fileExtension)
                        .uploadTime(new Date())
                        .status(0) // 待处理
                        .build();

                uploadRecords.add(uploadRecord);
                log.debug("文件上传成功: {}", originalFileName);

            } catch (Exception e) {
                log.error("文件上传失败: {}", originalFile.getName(), e);
                // 上传失败的文件不加入结果集
            }
        }

        log.info("文件上传完成，成功上传数量: {}", uploadRecords.size());
        return uploadRecords;
    }

    /**
     * 第四步：OCR识别、文本提取并保存到数据库
     *
     * @param uploadRecords 文件上传记录列表
     * @param caseImportId 案件ID
     */
    public void saveFilesToDatabase(List<FileUploadRecord> uploadRecords, Long caseImportId) {
        log.info("开始OCR识别、文本提取并保存到数据库，文件数量: {}", uploadRecords.size());

        // 第一步：先批量保存基本信息到数据库，获取文件ID
        List<FileUploadRecord> recordsToSave = uploadRecords.stream()
                .peek(fileRecord -> fileRecord.setCaseImportId(caseImportId))
                .collect(Collectors.toList());

        // 获取案件信息
        CaseImportRecord caseImportRecord = caseImportRecordMapper.selectById(caseImportId);
        if (caseImportRecord == null) {
            throw new RuntimeException("案件信息不存在，案件ID: " + caseImportId);
        }

        // 第一步：基础文件类型识别（在OCR之前）
        this.analyzeBasicDocumentType(recordsToSave, caseImportRecord.getAjlxdm());

        // 批量保存到数据库获取ID
        fileUploadRecordService.saveBatch(recordsToSave);
        log.info("文件基本信息批量保存成功，数量: {}", recordsToSave.size());

        // 验证所有记录都已获得ID
        List<FileUploadRecord> savedRecords = recordsToSave.stream()
                .peek(fileRecord -> {
                    if (fileRecord.getId() == null) {
                        throw new RuntimeException("文件保存失败，未获得ID: " + fileRecord.getFileName());
                    }
                    log.debug("文件基本信息保存成功，ID: {}, 文件名: {}", fileRecord.getId(), fileRecord.getFileName());
                })
                .collect(Collectors.toList());

        // 第二步：OCR识别（并发处理）
        List<FileUploadRecord> ocrProcessedRecords = performOcrRecognitionConcurrently(savedRecords);

        // 第三步：文本提取（串行处理，依赖OCR结果）
        List<FileUploadRecord> processedRecords = performTextExtraction(ocrProcessedRecords);

        // 第四步：判决书案号识别（在文本提取后执行）
        this.analyzeJudgmentDocumentType(processedRecords);

        // 第五步：批量更新数据库（更新OCR结果、文本内容和状态）
        fileUploadRecordService.updateBatchById(processedRecords);

        // 更新案件文件处理状态
        caseImportRecord.setFileStatus(1);
        caseImportRecordMapper.updateById(caseImportRecord);

        log.info("文件批量处理完成，成功处理: {} 个文件",
                processedRecords.stream().mapToInt(record -> record.getStatus() == 2 ? 1 : 0).sum());
    }

    /**
     * 执行OCR识别
     */
    private OcrResponseData performOcrRecognition(FileUploadRecord fileRecord) {
        try {
            // 检查文件类型，如果是QT（其他类型），跳过OCR识别
            if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                log.debug("文件类型为QT（其他类型），跳过OCR识别: {}", fileRecord.getFileName());
                return null;
            }

            log.debug("开始OCR识别: {}", fileRecord.getFileName());

            // 记录OCR开始时间
            LocalDateTime startTime = LocalDateTime.now();
            fileRecord.setOcrStartTime(startTime);

            try {
                String fileType = fileRecord.getFileType().toLowerCase();

                // 根据文件类型选择不同的处理方式
                boolean isImage = isImageFile(fileType);
                String filePath = isImage ? fileRecord.getFilePath() : fileRecord.getPdfPath();
                FileType ocrFileType = isImage ? FileType.IMAGE : FileType.PDF;

                log.info("{}，进行OCR识别: {}", isImage ? "图片文件" : "非图片文件", fileRecord.getFileName());

                OcrResponseData result;
                try (InputStream fileStream = minioClient.getObject(
                        GetObjectArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .object(filePath)
                                .build())) {

                    byte[] fileBytes = IOUtils.toByteArray(fileStream);
                    String base64Content = Base64.getEncoder().encodeToString(fileBytes);

                    result = ocrService.getLayoutData(base64Content, ocrFileType, true);
                }

                // 记录OCR结束时间和耗时
                LocalDateTime endTime = LocalDateTime.now();
                fileRecord.setOcrEndTime(endTime);
                long duration = java.time.Duration.between(startTime, endTime).toMillis();
                fileRecord.setOcrDuration(duration);

                if (result != null) {
                    log.info("OCR识别成功: {}, 耗时: {}ms", fileRecord.getFileName(), duration);
                } else {
                    log.warn("OCR识别失败: {}, 耗时: {}ms", fileRecord.getFileName(), duration);
                }

                return result;

            } catch (Exception e) {
                // 即使异常也记录结束时间
                LocalDateTime endTime = LocalDateTime.now();
                fileRecord.setOcrEndTime(endTime);
                long duration = java.time.Duration.between(startTime, endTime).toMillis();
                fileRecord.setOcrDuration(duration);
                log.error("OCR识别失败: {}, 耗时: {}ms", fileRecord.getFileName(), duration, e);
                throw e;
            }

        } catch (Exception e) {
            log.error("OCR识别失败: {}", fileRecord.getFileName(), e);
            return null;
        }
    }

    /**
     * 上传单个文件到MinIO
     */
    private String uploadFileToMinio(File file, String storedName, String contentType) throws Exception {
        try (FileInputStream fis = new FileInputStream(file)) {
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(storedName)
                    .stream(fis, file.length(), -1)
                    .contentType(contentType)
                    .build());
            return response.object();
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     * 只支持：PDF、Word文档、图片格式
     */
    private String getContentType(String fileExtension) {
        switch (fileExtension.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 创建PDF文件
     *
     * @param originalFile 原文件
     * @param pdfBytes PDF字节数组
     * @param originalFileName 原文件名
     * @return 创建的PDF文件
     */
    private File createPdfFile(File originalFile, byte[] pdfBytes, String originalFileName) throws IOException {
        // 创建PDF文件名
        String pdfFileName = FilenameUtils.removeExtension(originalFileName) + ".pdf";
        Path pdfPath = originalFile.getParentFile().toPath().resolve("pdf_" + pdfFileName);

        // 写入PDF文件
        try (FileOutputStream fos = new FileOutputStream(pdfPath.toFile())) {
            fos.write(pdfBytes);
        }

        return pdfPath.toFile();
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
        }
    }













    /**
     * 并发执行OCR识别
     *
     * @param fileRecords 文件记录列表
     * @return OCR处理后的文件记录列表
     */
    private List<FileUploadRecord> performOcrRecognitionConcurrently(List<FileUploadRecord> fileRecords) {
        log.info("开始并发OCR识别，文件数量: {}", fileRecords.size());

        // 创建 OCR 任务列表，使用OCR专用线程池
        List<CompletableFuture<FileUploadRecord>> ocrFutures = fileRecords.stream()
                .map(fileRecord -> threadPoolUtils.supplyAsyncOcr(() -> {
                    try {
                        // 检查文件类型，如果是QT（其他类型），进行PDF转图片处理
                        if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                            log.debug("文件类型为QT（其他类型），进行PDF转图片处理: {}", fileRecord.getFileName());

                            try {
                                // 记录OCR开始时间
                                LocalDateTime startTime = LocalDateTime.now();
                                fileRecord.setOcrStartTime(startTime);

                                // 处理QT类型文件：PDF转图片并生成OCR预览格式
                                List<OcrPreview> ocrPreviews = processQtFileToOcrPreviews(fileRecord);
                                
                                // 记录OCR结束时间和耗时
                                LocalDateTime endTime = LocalDateTime.now();
                                fileRecord.setOcrEndTime(endTime);
                                long duration = java.time.Duration.between(startTime, endTime).toMillis();
                                fileRecord.setOcrDuration(duration);

                                if (ocrPreviews != null && !ocrPreviews.isEmpty()) {
                                    // 直接设置OcrPreview对象列表
                                    fileRecord.setOcrPreviews(ocrPreviews);
                                    log.info("QT文件PDF转图片处理成功: {}, 生成图片数量: {}, 耗时: {}ms",
                                            fileRecord.getFileName(), ocrPreviews.size(), duration);
                                } else {
                                    log.warn("QT文件PDF转图片处理失败: {}, 耗时: {}ms", fileRecord.getFileName(), duration);
                                }
                            } catch (Exception e) {
                                log.error("QT文件PDF转图片处理异常: {}", fileRecord.getFileName(), e);
                                // 即使异常也记录结束时间
                                if (fileRecord.getOcrStartTime() != null) {
                                    LocalDateTime endTime = LocalDateTime.now();
                                    fileRecord.setOcrEndTime(endTime);
                                    long duration = java.time.Duration.between(fileRecord.getOcrStartTime(), endTime).toMillis();
                                    fileRecord.setOcrDuration(duration);
                                }
                            }

                            return fileRecord;
                        }

                        log.debug("开始OCR识别: {}", fileRecord.getFileName());

                        // 记录OCR开始时间
                        LocalDateTime startTime = LocalDateTime.now();
                        fileRecord.setOcrStartTime(startTime);

                        try {
                            // 执行OCR识别（使用重试机制）
                            OcrResponseData ocrResult = executeOcrWithRetry(
                                getBase64ContentFromFile(fileRecord),
                                getOcrFileType(fileRecord),
                                true,
                                fileRecord.getFileName()
                            );

                            // 记录OCR结束时间和耗时
                            LocalDateTime endTime = LocalDateTime.now();
                            fileRecord.setOcrEndTime(endTime);
                            long duration = java.time.Duration.between(startTime, endTime).toMillis();
                            fileRecord.setOcrDuration(duration);

                            if (ocrResult != null) {
                                fileRecord.setOcrResult(ocrResult.getResults());
                                fileRecord.setOcrPreviews(ocrResult.getPreviews());
                                log.debug("OCR识别成功: {}, 耗时: {}ms", fileRecord.getFileName(), duration);
                            } else {
                                log.warn("OCR识别失败: {}, 耗时: {}ms", fileRecord.getFileName(), duration);
                            }
                        } catch (Exception e) {
                            // 即使异常也记录结束时间
                            LocalDateTime endTime = LocalDateTime.now();
                            fileRecord.setOcrEndTime(endTime);
                            long duration = java.time.Duration.between(startTime, endTime).toMillis();
                            fileRecord.setOcrDuration(duration);
                            log.error("OCR识别异常: {}, 耗时: {}ms", fileRecord.getFileName(), duration, e);
                            throw e;
                        }

                        return fileRecord;
                    } catch (Exception e) {
                        log.error("OCR识别异常: {}", fileRecord.getFileName(), e);
                        return fileRecord;
                    }
                }))
                .collect(Collectors.toList());

        // 基于 OCR 结果，独立创建要素提取任务列表（不阻塞 OCR 收集）
        List<CompletableFuture<FileUploadRecord>> elementFutures = ocrFutures.stream()
                .map(ocrFuture -> ocrFuture.thenApplyAsync(record -> {
                    try {
                        // 仅当存在可用的OCR文本、文档类型非空且非QT时才进行要素提取
                        if (record != null && record.getDocumentType() != null && (!DocumentType.QT.equals(record.getDocumentType()))) {
                            log.info("开始要素提取: {}", record.getFileName());
                            String contentFromOcr = extractTextFromOcrResult(record); // QT 类型OCR文本为null
                            if (StringUtils.isNotBlank(contentFromOcr)) {
                                DocumentExtractorRequest request = DocumentExtractorRequest.builder()
                                        .fileName(record.getFileName())
                                        .content(contentFromOcr)
                                        .extractMethod(ExtractMethod.REGEX)
                                        .build();

                                BaseResponse<DocumentExtractorResult> response = documentExtractorService.extractElements(record.getDocumentType(), request);

                                if (response != null && response.getData() != null) {
                                    record.setExtract_element_result(response.getData());
                                    log.info("要素提取成功: {}", record.getFileName());
                                } else {
                                    log.info("要素提取无结果: {}", record != null ? record.getFileName() : "null");
                                }
                            } else {
                                log.info("OCR文本为空，跳过要素提取: {}", record.getFileName());
                            }
                        }else {
                            log.info("要素提取-名称:{},类型:{}",record.getFileName(), record.getDocumentType());
                        }
                    } catch (Exception e) {
                        log.error("要素提取-文件名称: {},异常信息:{}", record != null ? record.getFileName() : "null", e.getMessage());
                    }
                    return record;
                }, elementExtractService))
                .collect(Collectors.toList());

        // 等待 OCR 任务完成并收集结果
        List<FileUploadRecord> results = ocrFutures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        log.info("并发OCR识别完成，成功处理文件数量: {}", results.size());

        // 批量更新数据库
        try {
            fileUploadRecordService.updateBatchById(results);
            log.debug("OCR结果批量更新数据库成功");
        } catch (Exception e) {
            log.error("OCR结果批量更新数据库失败", e);
        }


        // 要素提取批量更新改为在 elementExtractService 线程池中异步执行，不阻塞返回
        CompletableFuture
                .allOf(elementFutures.toArray(new CompletableFuture[0]))
                .thenRunAsync(() -> {
                    try {
                        List<FileUploadRecord> elementUpdatedRecords = elementFutures.stream()
                                .map(CompletableFuture::join)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        // 可选：过滤仅包含已提取要素的记录，减少无效更新
                        List<FileUploadRecord> hasElementRecords = elementUpdatedRecords.stream()
                                .filter(r -> r.getExtract_element_result() != null)
                                .collect(Collectors.toList());

                        if (!hasElementRecords.isEmpty()) {
                            fileUploadRecordService.updateBatchById(hasElementRecords);
                            log.debug("要素提取结果批量更新数据库成功，数量: {}", hasElementRecords.size());
                        } else {
                            log.debug("无要素提取结果需要批量更新");
                        }
                    } catch (Exception e) {
                        log.error("要素提取结果批量更新数据库失败", e);
                    }
                }, elementExtractService);

        return results;
    }

    /**
     * 执行文本提取
     *
     * @param fileRecords OCR处理后的文件记录列表
     * @return 文本提取后的文件记录列表
     */
    private List<FileUploadRecord> performTextExtraction(List<FileUploadRecord> fileRecords) {
        log.info("开始文本提取，文件数量: {}", fileRecords.size());

        List<FileUploadRecord> processedRecords = fileRecords.stream()
                .peek(fileRecord -> {
                    try {
                        // 检查文件类型，如果是QT（其他类型），跳过文本提取
                        if (DocumentType.QT.equals(fileRecord.getDocumentType())) {
                            log.debug("文件类型为QT（其他类型），跳过文本提取: {}", fileRecord.getFileName());
                            fileRecord.setStatus(2); // 直接设置为处理成功
                            return;
                        }

                        log.debug("开始文本提取: {}", fileRecord.getFileName());

                        // 提取文本内容（此时fileRecord已有ID和OCR结果）
                        String extractedText = extractTextFromPdf(fileRecord);
                        if (extractedText != null && !extractedText.trim().isEmpty()) {
                            fileRecord.setExtractedText(extractedText);
                            fileRecord.setStatus(2); // 处理成功
                            log.debug("文本提取成功: {}", fileRecord.getFileName());
                        } else {
                            fileRecord.setStatus(3); // 处理失败
                            log.warn("文本提取失败: {}", fileRecord.getFileName());
                        }

                    } catch (Exception e) {
                        fileRecord.setStatus(3); // 处理失败
                        log.error("文本提取异常: {}", fileRecord.getFileName(), e);
                    }
                })
                .collect(Collectors.toList());

        // 批量更新数据库
        try {
            fileUploadRecordService.updateBatchById(processedRecords);
            log.debug("文本提取结果批量更新数据库成功");
        } catch (Exception e) {
            log.error("文本提取结果批量更新数据库失败", e);
        }

        log.info("文本提取完成，成功处理文件数量: {}",
                processedRecords.stream().mapToInt(r -> r.getStatus() == 2 ? 1 : 0).sum());

        return processedRecords;
    }


    /**
     * 从文件记录获取base64内容
     */
    private String getBase64ContentFromFile(FileUploadRecord fileRecord) throws Exception {
        String fileType = fileRecord.getFileType().toLowerCase();
        boolean isImage = isImageFile(fileType);
        String filePath = isImage ? fileRecord.getFilePath() : fileRecord.getPdfPath();

        try (InputStream fileStream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(filePath)
                        .build())) {

            byte[] fileBytes = IOUtils.toByteArray(fileStream);
            return Base64.getEncoder().encodeToString(fileBytes);
        }
    }

    /**
     * 根据文件记录获取OCR文件类型
     */
    private FileType getOcrFileType(FileUploadRecord fileRecord) {
        String fileType = fileRecord.getFileType().toLowerCase();
        boolean isImage = isImageFile(fileType);
        return isImage ? FileType.IMAGE : FileType.PDF;
    }

    /**
     * 处理QT类型文件：将PDF转换为图片并生成OCR预览格式
     *
     * @param fileRecord 文件记录
     * @return OCR预览对象列表
     */
    private List<OcrPreview> processQtFileToOcrPreviews(FileUploadRecord fileRecord) {
        try {
            log.info("开始处理QT文件PDF转图片: {}", fileRecord.getFileName());

            // 从MinIO获取PDF文件
            String pdfPath = fileRecord.getPdfPath() != null ? fileRecord.getPdfPath() : fileRecord.getFilePath();

            try (InputStream pdfStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(pdfPath)
                            .build())) {

                // 使用DocumentConvertService将PDF转换为图片字节数组
                // 直接使用InputStream调用pdfToImageBytes方法
                List<byte[]> imageBytesList = documentConvertService.pdfToImageBytes(pdfStream);

                if (imageBytesList == null || imageBytesList.isEmpty()) {
                    log.warn("PDF转图片失败，未生成图片: {}", fileRecord.getFileName());
                    return null;
                }

                log.info("PDF转图片成功，生成图片数量: {}, 文件: {}", imageBytesList.size(), fileRecord.getFileName());

                // 上传图片到MinIO并生成OCR预览格式
                List<OcrPreview> ocrPreviews = new ArrayList<>();

                for (int i = 0; i < imageBytesList.size(); i++) {
                    byte[] imageBytes = imageBytesList.get(i);

                    // 生成图片文件名
                    String imageFileName = UUID.randomUUID().toString() + ".jpeg";

                    // 上传图片到MinIO
                    try (ByteArrayInputStream imageStream = new ByteArrayInputStream(imageBytes)) {
                        ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .object("ocr/" + imageFileName)
                                .stream(imageStream, imageBytes.length, -1)
                                .contentType("image/jpeg")
                                .build());

                        // 生成图片访问URL
                        String imageUrl = String.format("%s/%s/ocr/%s",
                                minioConfig.getEndpoint(), minioConfig.getBucketName(), imageFileName);

                        // 创建OcrPreview对象
                        OcrPreview ocrPreview = new OcrPreview();
                        ocrPreview.setInputImage(imageUrl);
                        ocrPreview.setInputImageWidth(1191);  // 默认宽度
                        ocrPreview.setInputImageHeight(1684); // 默认高度
                        ocrPreview.setTotalPages(imageBytesList.size());
                        ocrPreview.setCurrentPage(i + 1);
                        ocrPreview.setImageType("jpeg");

                        ocrPreviews.add(ocrPreview);

                        log.debug("图片上传成功: {}, 页码: {}/{}", imageFileName, i + 1, imageBytesList.size());
                    }
                }

                log.info("QT文件PDF转图片处理完成: {}, 总页数: {}", fileRecord.getFileName(), ocrPreviews.size());
                return ocrPreviews;

            }

        } catch (Exception e) {
            log.error("处理QT文件PDF转图片失败: {}", fileRecord.getFileName(), e);
            return null;
        }
    }

    /**
     * 从文档中提取立案信息
     * 参考DocumentGenerationInfoService#extractYsRdssAndByrw方法
     * @return 提取到的立案日期，如果提取失败返回null
     */
    public String extractLaxxFromDocument(FileUploadRecord fileRecord, String caseNameAh) {
        try {
            if (fileRecord.getExtract_element_result() != null && fileRecord.getExtract_element_result().getElements() != null && fileRecord.getExtract_element_result().getElements().size() > 0) {

                // 从提取的要素中获取立案日期
                String larq = extractLarqFromElements(fileRecord.getExtract_element_result().getElements(),caseNameAh);
                if (StringUtils.isNotBlank(larq)) {
                    // 更新案件导入记录的立案日期
                    updateCaseImportRecordLarq(fileRecord.getCaseImportId(), larq);
                    return larq; // 返回提取到的立案日期
                }
            }
        } catch (Exception e) {
            log.error("从文档中提取立案信息失败，文件ID: {}, 错误: {}", fileRecord.getId(), e.getMessage(), e);
        }
        return null; // 提取失败返回null
    }

    /**
     * 从提取的要素中获取立案日期
     * 只有当caseName提取出来的案号和elements提取出来的案号一致时才返回larq
     */
    private String extractLarqFromElements(Map<String, Object> elements, String caseNameAh) {
        // 从立案日期要素中获取立案日期
        String larq = StringUtils.EMPTY;
        Object larqElement = elements.get("立案日期");
        if (larqElement != null && StringUtils.isNotBlank(larqElement.toString())) {
            larq = larqElement.toString().trim();
        }

        // 从案号要素中获取案号
        String elementsAh = StringUtils.EMPTY;
        Object ahElement = elements.get("案号");
        if (ahElement != null && StringUtils.isNotBlank(ahElement.toString())) {
            elementsAh = ahElement.toString().trim().replaceAll("\\s+", "");
        }

        log.info("案号比较 - 案件名称提取: {}, elements提取: {}, elements原始: {}",
                caseNameAh, elementsAh, elementsAh);

        // 只有当两个案号一致时才返回立案日期
        if (CaseNumberExtractUtil.isCaseNumberEqual(caseNameAh, elementsAh)) {
            log.info("案号匹配成功，返回立案日期: {}, 匹配案号: {}", larq, caseNameAh);
            return StringUtils.isNotBlank(larq) ? larq : null;
        } else {
            log.warn("案号不匹配，不返回立案日期 - 案件名称案号: {}, elements案号: {}", 
                    caseNameAh, elementsAh);
            return null;
        }
    }

    /**
     * 更新案件导入记录的立案日期
     */
    private void updateCaseImportRecordLarq(Long caseImportId, String larq) {
        try {
            CaseImportRecord record = new CaseImportRecord();
            record.setId(caseImportId);
            record.setLarq(larq);
            
            int updated = caseImportRecordMapper.updateById(record);
            if (updated > 0) {
                log.info("成功更新案件导入记录的立案日期，案件ID: {}, 立案日期: {}", caseImportId, larq);
            } else {
                log.warn("更新案件导入记录的立案日期失败，案件ID: {}", caseImportId);
            }
        } catch (Exception e) {
            log.error("更新案件导入记录的立案日期时发生异常，案件ID: {}, 立案日期: {}, 错误: {}", 
                    caseImportId, larq, e.getMessage(), e);
        }
    }

}
