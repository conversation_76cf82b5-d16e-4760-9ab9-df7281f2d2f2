package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.common.ExceptionCode;
import com.smxz.yjzs.exception.BusinessException;
import com.smxz.yjzs.entity.ContradictionRecognition;
import com.smxz.yjzs.entity.Evidence;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.ContradictionRecognitionMapper;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.service.ContradictionRecognitionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.smxz.yjzs.common.utils.AiOutputUtils.removeCodeBlock;

/**
 * 矛盾识别服务实现类
 */
@Slf4j
@Service
public class ContradictionRecognitionServiceImpl extends ServiceImpl<ContradictionRecognitionMapper, ContradictionRecognition> 
        implements ContradictionRecognitionService {

    @Autowired
    private ContradictionRecognitionMapper contradictionRecognitionMapper;
    
    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;
    
    @Autowired
    private AgentTaskUtils agentTaskUtils;
    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Override
    public List<ContradictionRecognition> listByCaseImportId(Long caseImportId) {
        return list(new LambdaQueryWrapper<ContradictionRecognition>()
                .eq(ContradictionRecognition::getCaseImportId, caseImportId)
                .orderByDesc(ContradictionRecognition::getCreateTime));
    }

    @Override
    public List<ContradictionRecognition> listByCaseImportIdAndType(Long caseImportId, String type) {
        return list(new LambdaQueryWrapper<ContradictionRecognition>()
                .eq(ContradictionRecognition::getCaseImportId, caseImportId)
                .eq(ContradictionRecognition::getType, type)
                .orderByDesc(ContradictionRecognition::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAgentResult(Long caseImportId, List<ContradictionRecognition> agentResult) {
        log.info("开始保存案件{}的矛盾识别分析结果", caseImportId);

        // 设置案件ID
        agentResult.forEach(result -> {
            result.setCaseImportId(caseImportId);
        });

        // 批量保存新的分析结果
        saveBatch(agentResult);
        
        log.info("案件{}的矛盾识别分析结果保存完成", caseImportId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCaseImportId(Long caseImportId) {
        remove(new LambdaQueryWrapper<ContradictionRecognition>()
                .eq(ContradictionRecognition::getCaseImportId, caseImportId));
        log.info("已删除案件{}的旧矛盾识别记录", caseImportId);
    }

    @Override
    public void reanalyze(Long caseImportId) {
        log.info("开始重新分析案件{}的矛盾识别", caseImportId);
        // TODO: 这里需要触发Agent重新分析任务
        // 先删除旧的分析结果
        deleteByCaseImportId(caseImportId);
        // 可以通过事件发布或直接调用分析任务服务
        generate(caseImportId);
    }

    /**
     * 生成矛盾识别分析
     * @param caseImportId 案件导入ID
     */
    @AnalysisTask(taskType = TaskType.CONTRADICTION_RECOGNITION, description = "矛盾识别分析")
    public void generate(Long caseImportId) {
        log.info("开始生成案件{}的矛盾识别分析", caseImportId);
        
        try {
            // 调用Agent进行矛盾识别分析
            String agentResult = agentTaskUtils.executeTask("星洲-矛盾识别Agent", "main", "mdsb.yml", buildAnalysisInput(caseImportId));
            
            // 解析Agent返回结果并保存
            processAgentResult(caseImportId, agentResult);
            log.info("案件{}矛盾识别分析完成", caseImportId);

        } catch (Exception e) {
            log.error("案件{}矛盾识别分析失败", caseImportId, e);
            throw new BusinessException(ExceptionCode.CONTRADICTION_ANALYSIS_ERROR, e);
        }
    }
    
    /**
     * 构建Agent分析输入参数
     */
    private Map<String, String> buildAnalysisInput(Long caseImportId) {
        Map<String, String> inputMap = new HashMap<>();
        // 获取案件相关文件内容
        String extractedTextByDocumentTypes = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ, DocumentType.QSZ, DocumentType.TSBL);
        inputMap.put("CASE_MATERIALS", extractedTextByDocumentTypes);
        return inputMap;
    }
    
    /**
     * 处理Agent返回结果
     */
    private void processAgentResult(Long caseImportId, String agentResult) {
        try {
            // 解析JSON结果
            List<ContradictionRecognition> results = JSON.parseArray(agentResult, ContradictionRecognition.class);

            // 保存矛盾识别分析结果
            saveAgentResult(caseImportId, results);

        } catch (Exception e) {
            log.error("处理Agent矛盾识别结果失败", e);
            throw new BusinessException(ExceptionCode.CONTRADICTION_RESULT_PARSING_ERROR, e);
        }
    }
}