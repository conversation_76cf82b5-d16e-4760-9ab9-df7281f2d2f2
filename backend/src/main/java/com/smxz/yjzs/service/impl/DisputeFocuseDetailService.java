package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.agent.client.AgentClient;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.DisputeConstants;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.DisputeFocus;
import com.smxz.yjzs.entity.DisputeFocuseDetail;
import com.smxz.yjzs.entity.EvidenceFactsDetails;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.mapper.DisputeFocuseDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 争议焦点详情表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
@Slf4j
public class DisputeFocuseDetailService extends ServiceImpl<DisputeFocuseDetailMapper, DisputeFocuseDetail> {



    @Autowired
    @Lazy
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    @Autowired
    @Lazy
    private DisputeFocusService disputeFocusService;

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private SystemPromptConfigService systemPromptConfigService;

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;
    @Autowired
    private AgentTaskUtils agentTaskUtils;

    /**
     * 根据案件ID查询争议焦点详情
     *
     * @param caseImportId 案件导入ID
     * @return 争议焦点详情
     */
    public DisputeFocuseDetail getByCaseImportId(Long caseImportId) {
        log.info("查询争议焦点详情，caseImportId: {}", caseImportId);
        LambdaQueryWrapper<DisputeFocuseDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisputeFocuseDetail::getCaseImportId, caseImportId)
                   .orderByDesc(DisputeFocuseDetail::getCreateTime)
                   .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 生成争议焦点说理
     * @param caseImportId 案件导入ID
     */
    @Async
    @AnalysisTask(taskType = TaskType.DISPUTE_FOCUS_REASONING, description = "生成争议焦点说理")
    public void generateReasoning(Long caseImportId) {
        executeReasoningGeneration(caseImportId);
    }

    /**
     * 执行争议焦点说理生成任务
     * @param caseImportId 案件导入ID
     */
    private void executeReasoningGeneration(Long caseImportId) {
        log.info("开始生成争议焦点说理，caseImportId: {}", caseImportId);

        // 清空现有的争议焦点说理内容
        clearExistingReasoning(caseImportId);

        // 判断案件类型
        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        String ajlxdm = caseRecord != null ? caseRecord.getAjlxdm() : "";

        // 获取争议焦点列表（Controller已验证存在）
        List<DisputeFocus> disputeFocusList = disputeFocusService.getDisputeFocusByCaseId(caseImportId);
        String messageContent = buildZyjdAndJlContent(disputeFocusList);
        log.info("组装的消息内容长度: {}", messageContent.length());

        // 统一使用Agent任务生成争议焦点说理
        String reasoning = generateReasoningWithAgent(caseImportId, messageContent, ajlxdm);

        if (StringUtils.isNotBlank(reasoning)) {
            // 保存到数据库
            saveOrUpdateReasoning(caseImportId, reasoning);
            log.info("争议焦点说理生成并保存成功，caseImportId: {}, 内容长度: {}", caseImportId, reasoning.length());
        } else {
            log.warn("AI生成的争议焦点说理为空，caseImportId: {}", caseImportId);
            throw new RuntimeException("AI生成的争议焦点说理为空");
        }
    }

    /**
     * 将阿拉伯数字转换为中文数字
     */
    private String convertToChineseNumber(int number) {
        if (number <= 0) {
            return String.valueOf(number);
        }

        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

        if (number <= 10) {
            if (number == 10) {
                return "十";
            }
            return chineseNumbers[number];
        } else if (number < 20) {
            return "十" + chineseNumbers[number - 10];
        } else if (number < 100) {
            int tens = number / 10;
            int units = number % 10;
            if (units == 0) {
                return chineseNumbers[tens] + "十";
            } else {
                return chineseNumbers[tens] + "十" + chineseNumbers[units];
            }
        } else if (number < 1000) {
            // 处理100-999的数字
            int hundreds = number / 100;
            int remainder = number % 100;

            StringBuilder result = new StringBuilder();
            result.append(chineseNumbers[hundreds]).append("百");

            if (remainder == 0) {
                // 整百数，如：一百、二百
                return result.toString();
            } else if (remainder < 10) {
                // 百位和个位之间有零，如：一百零一、二百零五
                result.append("零").append(chineseNumbers[remainder]);
            } else {
                // 百位后面是十位数，如：一百一十、二百三十五
                result.append(convertToChineseNumber(remainder));
            }

            return result.toString();
        } else if (number < 10000) {
            // 处理1000-9999的数字
            int thousands = number / 1000;
            int remainder = number % 1000;

            StringBuilder result = new StringBuilder();
            result.append(chineseNumbers[thousands]).append("千");

            if (remainder == 0) {
                // 整千数，如：一千、二千
                return result.toString();
            } else if (remainder < 100) {
                // 千位和百位之间有零，如：一千零一、二千零五十
                result.append("零").append(convertToChineseNumber(remainder));
            } else {
                // 千位后面是百位数，如：一千一百、二千三百五十
                result.append(convertToChineseNumber(remainder));
            }

            return result.toString();
        } else {
            // 对于大于等于10000的数字，返回原数字（实际使用中应该不会遇到）
            return String.valueOf(number);
        }
    }

    /**
     * 组装消息内容
     */
    private String buildZyjdAndJlContent( List<DisputeFocus> disputeFocusList) {
        StringBuilder messageBuilder = new StringBuilder();


        // 添加争议焦点标题
        messageBuilder.append("争议焦点：\n");

        for (int i = 0; i < disputeFocusList.size(); i++) {
            DisputeFocus focus = disputeFocusList.get(i);
            String chineseNumber = convertToChineseNumber(i + 1);

            // 争议焦点
            messageBuilder.append("争议焦点").append(chineseNumber).append("：")
                         .append(focus.getDescription()).append("\n");

            // 结论
            String conclusion = StringUtils.isNotBlank(focus.getConclusion()) ? focus.getConclusion() : "";
            messageBuilder.append("争议焦点").append(chineseNumber).append("的结论：")
                         .append(convertConclusion(conclusion)).append("\n");

            // 法条
            StringBuilder lawContent = new StringBuilder();
            if (focus.getLaws() != null && !focus.getLaws().isEmpty()) {
                List<String> laws = focus.getLaws();
                List<String> lawSummaries = focus.getLawSummaries();

                for (int j = 0; j < laws.size(); j++) {
                    String law = laws.get(j);
                    String summary = (lawSummaries != null && j < lawSummaries.size()) ?
                                   lawSummaries.get(j) : "";

                    if (j > 0) {
                        lawContent.append("；");
                    }
                    lawContent.append(law);
                    if (StringUtils.isNotBlank(summary)) {
                        lawContent.append("：").append(summary);
                    }
                }
            }
            messageBuilder.append("争议焦点").append(chineseNumber).append("的法条：")
                         .append(lawContent.toString()).append("\n");
        }

        return messageBuilder.toString();
    }

    private String convertConclusion(String  conclusion){
        if(StringUtils.equals(DisputeConstants.FocusConclusion.YES,conclusion)){
            return "是";
        }else if(StringUtils.equals(DisputeConstants.FocusConclusion.NO,conclusion)){
            return "否";
        }else{
            return conclusion;
        }

    }

    /**
     * 使用Agent任务生成争议焦点说理（统一方法）
     * @param caseImportId 案件导入ID
     * @param messageContent 争议焦点和结论内容
     * @param ajlxdm 案件类型代码
     * @return 生成的争议焦点说理
     */
    private String generateReasoningWithAgent(Long caseImportId, String messageContent, String ajlxdm) {
        try {
            log.info("开始使用Agent任务生成争议焦点说理，案件类型: {}", ajlxdm);

            Map<String, String> properties = new HashMap<>();

            EvidenceFactsDetails evidenceFactsDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
            String determineFacts = evidenceFactsDetails != null ?
                    StringUtils.defaultIfBlank(evidenceFactsDetails.getDetermineFactsExtract(), evidenceFactsDetails.getDetermineFacts()) : "";

            properties.put("争议焦点和结论", messageContent);
            properties.put("认定事实", determineFacts);
            // 根据案件类型设置不同的任务名称和参数
            String ymlName;
            if (CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm)) {
                ymlName = "mses-dispute_focus_reasoning_task.yml";
                // 设置二审参数
                String firstInstanceJudgment = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.YSPJS);
                String appealContent  = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.SSZ);
                // 获取答辩状内容
                String dbzContent = fileUploadRecordService.getExtractedTextByDocumentTypes(caseImportId, DocumentType.DBZ, DocumentType.BCDBYJ);
                properties.put("一审判决书", firstInstanceJudgment);
                properties.put("上诉状", appealContent);
                properties.put("二审答辩状", dbzContent);
            } else {
                ymlName = "dispute_focus_reasoning_task.yml";
            }
            String reasoning = agentTaskUtils.executeTask("星洲-争议焦点说理Agent", "main", ymlName, properties);

            log.info("Agent任务生成争议焦点说理完成，内容长度: {}", reasoning != null ? reasoning.length() : 0);
            return reasoning;

        } catch (Exception e) {
            log.error("使用Agent任务生成争议焦点说理失败", e);
            throw new RuntimeException("使用Agent任务生成争议焦点说理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新争议焦点说理（用户手动保存）
     */
    @Transactional(rollbackFor = Exception.class)
    public DisputeFocuseDetail updateReasoning(Long caseImportId, String reasoning) {
        try {
            log.info("手动更新争议焦点说理，案件ID: {}, 内容长度: {}", caseImportId, reasoning != null ? reasoning.length() : 0);

            // 查询是否已存在记录
            DisputeFocuseDetail existingDetail = getByCaseImportId(caseImportId);

            if (existingDetail != null) {
                // 更新现有记录
                existingDetail.setDisputeFocuseReasoning(reasoning);
                existingDetail.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetail);
                log.info("手动更新争议焦点说理成功，案件ID: {}, 内容长度: {}", caseImportId, reasoning != null ? reasoning.length() : 0);
                return existingDetail;
            } else {
                // 新增记录
                DisputeFocuseDetail newDetail = DisputeFocuseDetail.builder()
                        .caseImportId(caseImportId)
                        .disputeFocuseReasoning(reasoning)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                this.save(newDetail);
                log.info("手动新增争议焦点说理成功，案件ID: {}, 内容长度: {}", caseImportId, reasoning != null ? reasoning.length() : 0);
                return newDetail;
            }
        } catch (Exception e) {
            log.error("手动更新争议焦点说理失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("手动更新争议焦点说理失败", e);
        }
    }

    /**
     * 保存或更新争议焦点说理（AI生成时使用）
     */
    @Transactional(rollbackFor = Exception.class)
    public DisputeFocuseDetail saveOrUpdateReasoning(Long caseImportId, String reasoning) {
        try {
            // 查询是否已存在记录
            DisputeFocuseDetail existingDetail = getByCaseImportId(caseImportId);

            if (existingDetail != null) {
                // 更新现有记录
                existingDetail.setDisputeFocuseReasoning(reasoning);
                existingDetail.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetail);
                log.info("更新争议焦点说理成功，案件ID: {}, 内容长度: {}", caseImportId, reasoning.length());
                return existingDetail;
            } else {
                // 新增记录
                DisputeFocuseDetail newDetail = DisputeFocuseDetail.builder()
                        .caseImportId(caseImportId)
                        .disputeFocuseReasoning(reasoning)
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                this.save(newDetail);
                log.info("新增争议焦点说理成功，案件ID: {}, 内容长度: {}", caseImportId, reasoning.length());
                return newDetail;
            }
        } catch (Exception e) {
            log.error("保存争议焦点说理失败，案件ID: {}", caseImportId, e);
            throw new RuntimeException("保存争议焦点说理失败", e);
        }
    }

    /**
     * 清空现有的争议焦点说理内容
     */

    private void clearExistingReasoning(Long caseImportId) {
        try {
            DisputeFocuseDetail existingDetail = getByCaseImportId(caseImportId);
            if (existingDetail != null) {
                existingDetail.setDisputeFocuseReasoning("");
                existingDetail.setUpdateTime(LocalDateTime.now());
                this.updateById(existingDetail);
                log.info("清空现有争议焦点说理内容成功，案件ID: {}", caseImportId);
            }
        } catch (Exception e) {
            log.error("清空现有争议焦点说理内容失败，案件ID: {}", caseImportId, e);
        }
    }
}
