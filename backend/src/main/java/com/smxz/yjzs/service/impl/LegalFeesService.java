package com.smxz.yjzs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.yjzs.dto.LegalFeesWithPartyDTO;
import com.smxz.yjzs.entity.LegalFees;
import com.smxz.yjzs.mapper.LegalFeesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 诉讼费服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class LegalFeesService extends ServiceImpl<LegalFeesMapper, LegalFees> {

    /**
     * 根据案件ID查询诉讼费列表
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 诉讼费列表
     */
    public List<LegalFees> listByCaseImportId(Long caseImportRecordId) {
        log.info("查询案件诉讼费信息，caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listByCaseImportId(caseImportRecordId);
    }

    /**
     * 根据案件ID查询诉讼费列表，包含当事人信息
     *
     * @param caseImportRecordId 案件导入记录ID
     * @return 包含当事人信息的诉讼费列表
     */
    public List<LegalFeesWithPartyDTO> listWithPartyInfoByCaseImportId(Long caseImportRecordId) {
        log.info("查询案件诉讼费信息（包含当事人信息），caseImportRecordId: {}", caseImportRecordId);
        return baseMapper.listWithPartyInfoByCaseImportId(caseImportRecordId);
    }

    /**
     * 根据案件ID删除诉讼费记录
     *
     * @param caseImportRecordId 案件导入记录ID
     */
    public void deleteByCaseImportId(Long caseImportRecordId) {
        log.info("删除案件诉讼费信息，caseImportRecordId: {}", caseImportRecordId);
        baseMapper.deleteByCaseImportId(caseImportRecordId);
    }

    /**
     * 批量保存诉讼费记录
     *
     * @param legalFeesList 诉讼费列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<LegalFees> legalFeesList) {
        log.info("批量保存诉讼费信息，数量: {}", legalFeesList.size());
        return super.saveBatch(legalFeesList);
    }
}
