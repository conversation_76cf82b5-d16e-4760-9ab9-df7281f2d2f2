package com.smxz.yjzs.service;

import com.smxz.yjzs.dto.request.FileUploadRequest;
import com.smxz.yjzs.dto.request.FileDownloadRequest;
import com.smxz.yjzs.dto.response.FileUploadResponse;
import com.smxz.yjzs.dto.response.FileDownloadResponse;
import com.smxz.yjzs.dto.response.BatchFileUploadResponse;

public interface FileHandlerService {
    /**
     * 上传多个文件
     * @param request 文件上传请求
     * @return 批量文件上传响应
     */
    BatchFileUploadResponse uploadFiles(FileUploadRequest request);


    /**
     * 下载文件
     * @param request 文件下载请求
     * @return 文件下载响应
     */
    FileDownloadResponse downloadFile(FileDownloadRequest request);
} 