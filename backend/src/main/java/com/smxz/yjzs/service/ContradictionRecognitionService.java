package com.smxz.yjzs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.smxz.yjzs.entity.ContradictionRecognition;

import java.util.List;

/**
 * 矛盾识别服务接口
 */
public interface ContradictionRecognitionService extends IService<ContradictionRecognition> {

    /**
     * 根据案件导入ID查询矛盾识别列表
     * @param caseImportId 案件导入ID
     * @return 矛盾识别列表
     */
    List<ContradictionRecognition> listByCaseImportId(Long caseImportId);

    /**
     * 根据案件导入ID和当事人类型查询矛盾识别列表
     * @param caseImportId 案件导入ID
     * @param type 当事人类型
     * @return 矛盾识别列表
     */
    List<ContradictionRecognition> listByCaseImportIdAndType(Long caseImportId, String type);

    /**
     * 保存Agent分析结果
     * @param caseImportId 案件导入ID
     * @param agentResult Agent返回的JSON结果
     */
    void saveAgentResult(Long caseImportId, List<ContradictionRecognition> agentResult);

    /**
     * 根据案件导入ID删除矛盾识别记录
     * @param caseImportId 案件导入ID
     */
    void deleteByCaseImportId(Long caseImportId);

    /**
     * 重新分析矛盾识别
     * @param caseImportId 案件导入ID
     */
    void reanalyze(Long caseImportId);

    /**
     * 生成矛盾识别分析
     * @param caseImportId 案件导入ID
     */
    void generate(Long caseImportId);

}