package com.smxz.yjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.ocr.model.OcrResult;
import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.annotation.ModuleUpdate;
import com.smxz.yjzs.common.utils.AgentTaskUtils;
import com.smxz.yjzs.common.utils.SymbolUtils;
import com.smxz.yjzs.dto.EvidenceInfoDTO;
import com.smxz.yjzs.dto.EvidenceSummaryDTO;
import com.smxz.yjzs.dto.OcrPageBlockDTO;
import com.smxz.yjzs.dto.request.EvidenceOverviewRequest;
import com.smxz.yjzs.dto.response.EvidenceOverviewResponse;
import com.smxz.yjzs.entity.CaseParty;
import com.smxz.yjzs.entity.EvidenceOverrideParty;
import com.smxz.yjzs.entity.EvidenceOverview;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.enums.ModuleType;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.exception.BusinessException;
import com.smxz.yjzs.mapper.*;
import com.smxz.yjzs.service.ModuleUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class EvidenceOverviewService {

    private static final int BLOCK_SIZE = 15000;

    private static final String GENERATING = "生成中...";

    private static final String NONE = "无";

    @Autowired
    private EvidenceOverviewMapper evidenceOverviewMapper;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private CasePartyMapper casePartyMapper;

    @Autowired
    private CaseImportRecordMapper caseImportRecordMapper;

    @Autowired
    private ModuleUpdateService moduleUpdateService;

    @Autowired
    private AgentTaskUtils agentTaskUtils;

    @Autowired
    private EvidenceOverridePartyMapper evidenceOverridePartyMapper;

    @Async
    @AnalysisTask(taskType = TaskType.EVIDENCE_OVERVIEW, description = "证据情况分析")
    public void analyzeEvidence(Long caseImportId) {
        syncAnalyzeEvidence(caseImportId);
    }

    /**
     * 分析证据情况，生成证据内容等
     * 如果不存在证据目录，则使用分块拆分的逻辑，如果存在则使用原来对齐的逻辑
     *
     * @param caseImportId 案件导入id
     */
    @AnalysisTask(taskType = TaskType.EVIDENCE_OVERVIEW, description = "证据情况分析")
    public void syncAnalyzeEvidence(Long caseImportId) {
        List<FileUploadRecord> fileUploadRecords = fileUploadRecordMapper.listByCaseImportId(caseImportId);

        boolean existIndex = fileUploadRecords.stream().anyMatch(e -> DocumentType.ZJML == e.getDocumentType());
        boolean existSscyrjzcl = fileUploadRecords.stream().anyMatch(record -> StringUtils.contains(record.getFileName(), "诉讼参与人举证材料"));

        if (!existSscyrjzcl && existIndex) {
            analyzeEvidenceOld(caseImportId, fileUploadRecords);
        } else {
            analyzeEvidenceNew(caseImportId, fileUploadRecords, existSscyrjzcl);
        }
    }

    private void analyzeEvidenceOld(Long caseImportId, List<FileUploadRecord> fileUploadRecords) {
        evidenceOverviewMapper.deleteByCaseImportId(caseImportId);
        evidenceOverridePartyMapper.deleteByCaseImportId(caseImportId);

        List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);
        String partiesString = caseParties.stream().map(CaseParty::getPartyName).collect(Collectors.joining("、"));

        // 获取证据
        List<OcrPageBlockDTO> zjList = fileUploadRecords.stream()
                .filter(e -> Objects.equals(e.getDocumentType(), DocumentType.ZJ))
                .flatMap(e -> splitStringByLength(e, BLOCK_SIZE).stream())
                .toList();

        // 获取证据清单
        List<OcrPageBlockDTO> zjmlList = fileUploadRecords.stream()
                .filter(e -> Objects.equals(e.getDocumentType(), DocumentType.ZJML))
                .flatMap(e -> splitStringByLength(e, BLOCK_SIZE).stream())
                .toList();

        // 获取庭审笔录
        List<OcrPageBlockDTO> tsblList = fileUploadRecords.stream()
                .filter(e -> Objects.equals(e.getDocumentType(), DocumentType.TSBL))
                .flatMap(e -> splitStringByLength(e, BLOCK_SIZE).stream())
                .toList();

        // 庭审笔录和证据目录都传进去
        List<OcrPageBlockDTO> task1ProcessStringBlocks = new ArrayList<>(zjmlList);
        task1ProcessStringBlocks.addAll(tsblList);


        StringBuilder allTask1Result = new StringBuilder();
        for (OcrPageBlockDTO zjBlock : task1ProcessStringBlocks) {
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("分块证据", zjBlock.getTextContent());
            propertyMap.put("待选提交人", partiesString);
            String task1Result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task1", "证据情况.yaml", propertyMap);
            allTask1Result.append(task1Result);
            allTask1Result.append("\n");
        }

        Map<String, String> task2PropertyMap = new HashMap<>();
        task2PropertyMap.put("证据信息列表", allTask1Result.toString());
        // 这个任务主要是用于去重
        String task2Result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task2", "证据情况.yaml", task2PropertyMap);
        List<EvidenceInfoDTO> allEvidenceInfo = JSON.parseArray(task2Result, EvidenceInfoDTO.class);

        List<EvidenceOverview> evidenceOverviews = insertEvidenceOverviews(caseImportId, allEvidenceInfo);

        // 因为走到这边的逻辑都会不存在诉讼参与人举证材料文件，所以直接用另外一个模型任务进行尝试匹配
        matchEvidenceAndFileBlock(caseParties, allEvidenceInfo, zjList, evidenceOverviews);

        Map<String, EvidenceOverview> evidenceOverviewMap = evidenceOverviews.stream()
                .collect(Collectors.toMap(EvidenceOverview::getEvidenceName, Function.identity(), (e1, e2) -> e2));

        updateEvidenceSummary(zjList, allEvidenceInfo, evidenceOverviewMap);
    }

    private void updateEvidenceSummary(List<OcrPageBlockDTO> zjList, List<EvidenceInfoDTO> allEvidenceInfo, Map<String, EvidenceOverview> evidenceOverviewMap) {
        for (OcrPageBlockDTO zj : zjList) {
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("分块证据", zj.getTextContent());
            propertyMap.put("证据目录", JSON.toJSONString(allEvidenceInfo));
            String result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task3", "证据情况.yaml", propertyMap);
            List<EvidenceSummaryDTO> evidenceSummaries = JSON.parseArray(result, EvidenceSummaryDTO.class);
            if (!CollectionUtils.isEmpty(evidenceSummaries)) {
                List<EvidenceOverview> updateEvidenceOverrides = new ArrayList<>();
                for (EvidenceSummaryDTO evidenceSummaryDTO : evidenceSummaries) {
                    EvidenceOverview evidenceOverview = evidenceOverviewMap.get(evidenceSummaryDTO.getEvidenceName());
                    if (evidenceOverview != null) {
                        evidenceOverview.setEvidenceSummary(evidenceSummaryDTO.getEvidenceSummary());
                        // 这里FileId为null的话，才去设置溯源信息，否则可能是Task2-1进行了溯源，那就不用再溯源覆盖了
                        if (evidenceOverview.getOriginalFileId() == null) {
                            evidenceOverview.setOriginalFileId(zj.getFileId());
                            evidenceOverview.setOriginalFileName(zj.getFileName());
                            evidenceOverview.setOriginalFileStartPage(zj.getStartPageNumber());
                        }
                        updateEvidenceOverrides.add(evidenceOverview);
                    } else {
                        log.warn("未找到对应的证据概述: {}", evidenceSummaryDTO.getEvidenceName());
                    }
                }
                evidenceOverviewMapper.updateById(updateEvidenceOverrides);
            }
        }

        List<EvidenceOverview> unmatchedEvidenceOverride = new ArrayList<>();
        for (EvidenceOverview evidenceOverview : evidenceOverviewMap.values()) {
            if (GENERATING.equals(evidenceOverview.getEvidenceSummary())) {
                evidenceOverview.setEvidenceSummary(NONE);
                unmatchedEvidenceOverride.add(evidenceOverview);
            }
        }

        evidenceOverviewMapper.updateById(unmatchedEvidenceOverride);
    }


    public void analyzeEvidenceNew(Long caseImportId, List<FileUploadRecord> fileUploadRecords, boolean existSscyrjzcl) {
        evidenceOverviewMapper.deleteByCaseImportId(caseImportId);
        evidenceOverridePartyMapper.deleteByCaseImportId(caseImportId);

        List<CaseParty> caseParties = casePartyMapper.listByCaseImportId(caseImportId);
        String partiesString = caseParties.stream().map(CaseParty::getPartyName).collect(Collectors.joining("、"));

        // 获取庭审笔录
        List<OcrPageBlockDTO> tsblList = fileUploadRecords.stream()
                .filter(e -> Objects.equals(e.getDocumentType(), DocumentType.TSBL))
                .flatMap(e -> splitStringByLength(e, BLOCK_SIZE).stream())
                .toList();

        // 获取证据
        List<OcrPageBlockDTO> zjList = fileUploadRecords.stream()
                .filter(e -> Objects.equals(e.getDocumentType(), DocumentType.ZJ))
                .flatMap(e -> splitStringByLength(e, BLOCK_SIZE).stream())
                .toList();

        List<OcrPageBlockDTO> task1StringBlocks = new ArrayList<>(tsblList);

        task1StringBlocks.addAll(zjList);

        StringBuilder allTask1Result = new StringBuilder();
        // 总的分块获取完成后每个分块都调用第一步

        for (OcrPageBlockDTO stringBlock : task1StringBlocks) {
            Map<String, String> propertyMap = new HashMap<>();
            propertyMap.put("分块证据", stringBlock.getTextContent());
            propertyMap.put("待选提交人", partiesString);
            String task1Result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task1", "证据情况.yaml", propertyMap);
            allTask1Result.append(task1Result);
            allTask1Result.append("\n");
        }

        Map<String, String> task2PropertyMap = new HashMap<>();
        task2PropertyMap.put("证据信息列表", allTask1Result.toString());
        // 这个任务主要是用于去重, 拿到结果了，其实就是拿到了证据目录
        String task2Result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task2", "证据情况.yaml", task2PropertyMap);
        List<EvidenceInfoDTO> allEvidenceInfo = JSON.parseArray(task2Result, EvidenceInfoDTO.class);

        List<EvidenceOverview> evidenceOverviews = insertEvidenceOverviews(caseImportId, allEvidenceInfo);

        // 不存在诉讼参与人举证材料文件，用另外一个模型任务进行尝试匹配
        if (!existSscyrjzcl) {
            matchEvidenceAndFileBlock(caseParties, allEvidenceInfo, zjList, evidenceOverviews);
        }

        Map<String, EvidenceOverview> evidenceOverviewMap = evidenceOverviews.stream()
                .collect(Collectors.toMap(EvidenceOverview::getEvidenceName, Function.identity(), (e1, e2) -> e2));

        updateEvidenceSummary(zjList, allEvidenceInfo, evidenceOverviewMap);
    }

    private void matchEvidenceAndFileBlock(List<CaseParty> caseParties, List<EvidenceInfoDTO> allEvidenceInfo, List<OcrPageBlockDTO> zjList, List<EvidenceOverview> evidenceOverviews) {
        String casePartiesString = caseParties.stream()
                .map(e -> e.getPartyType() + ": " + e.getPartyName())
                .collect(Collectors.joining("\n"));

        String evidenceInfoString = allEvidenceInfo.stream()
                .map(e -> "证据提交人：" + e.getSubmitter() + "\n" + "证据名称：" + e.getEvidenceName())
                .collect(Collectors.joining("\n"));

        String fileListString = zjList.stream().map(OcrPageBlockDTO::getFileName).collect(Collectors.joining("\n"));

        Map<String, String> propertyMap = new HashMap<>();
        propertyMap.put("当事人信息", casePartiesString);
        propertyMap.put("证据清单", evidenceInfoString);
        propertyMap.put("文件列表", fileListString);
        String result = agentTaskUtils.executeTask("星洲-证据情况Agent", "task2_1", "证据情况.yaml", propertyMap);
        List<EvidenceAndFileDTO> evidenceAndFileDTOs = JSON.parseArray(result, EvidenceAndFileDTO.class);
        // 由大模型输出的对应关系
        Map<String, String> evidenceFileMap = evidenceAndFileDTOs.stream()
                .collect(Collectors.toMap(EvidenceAndFileDTO::getEvidenceName, EvidenceAndFileDTO::getFileName, (e1, e2) -> e2));

        for (EvidenceOverview evidenceOverview : evidenceOverviews) {
            String fileName = evidenceFileMap.get(evidenceOverview.getEvidenceName());
            List<OcrPageBlockDTO> matchResult = zjList.stream()
                    .filter(zj -> SymbolUtils.containsIgnoreSymbol(zj.getFileName(), fileName))
                    .toList();
            // 匹配结果只有一个时才真正匹配
            if (matchResult.size() == 1) {
                OcrPageBlockDTO zj = matchResult.get(0);
                evidenceOverview.setOriginalFileId(zj.getFileId());
                evidenceOverview.setOriginalFileName(zj.getFileName());
                evidenceOverview.setOriginalFileStartPage(zj.getStartPageNumber());
            }
        }
    }

    @NotNull
    private List<EvidenceOverview> insertEvidenceOverviews(Long caseImportId, List<EvidenceInfoDTO> allEvidenceInfo) {
        List<EvidenceOverview> evidenceOverviews = new ArrayList<>();
        for (EvidenceInfoDTO evidenceInfoDTO : allEvidenceInfo) {
            EvidenceOverview evidenceOverview = new EvidenceOverview();
            evidenceOverview.setCaseImportId(caseImportId);
            evidenceOverview.setPartyName(evidenceInfoDTO.getSubmitter());
            evidenceOverview.setEvidenceName(evidenceInfoDTO.getEvidenceName());
            evidenceOverview.setEvidencePurpose(evidenceInfoDTO.getEvidencePurpose());
            evidenceOverview.setEvidenceSummary(GENERATING);
            evidenceOverview.setAdopted(true);
            evidenceOverview.setCreateTime(LocalDateTime.now());
            evidenceOverview.setUpdateTime(LocalDateTime.now());

            evidenceOverviews.add(evidenceOverview);
        }

        evidenceOverviewMapper.insert(evidenceOverviews);

        List<EvidenceOverrideParty> allEvidenceOverrideParties = new ArrayList<>();
        for (EvidenceOverview evidenceOverview : evidenceOverviews) {
            if (StringUtils.isNotBlank(evidenceOverview.getPartyName())) {
                String[] submitters = evidenceOverview.getPartyName().split("[,，、]");
                List<EvidenceOverrideParty> evidenceOverrideParties = Stream.of(submitters)
                        .distinct()
                        .map(e -> {
                            EvidenceOverrideParty evidenceOverrideParty = new EvidenceOverrideParty();
                            evidenceOverrideParty.setCaseImportId(caseImportId);
                            evidenceOverrideParty.setEvidenceOverviewId(evidenceOverview.getId());
                            evidenceOverrideParty.setPartyName(e);

                            return evidenceOverrideParty;
                        }).toList();
                allEvidenceOverrideParties.addAll(evidenceOverrideParties);
            }
        }

        evidenceOverridePartyMapper.insert(allEvidenceOverrideParties);
        return evidenceOverviews;
    }

    /**
     * 根据案件导入ID查询证据情况列表
     *
     * @param caseImportId 案件导入ID
     * @return 证据情况响应列表
     */
    public List<EvidenceOverviewResponse> list(Long caseImportId, List<String> partyNames) {
        log.info("查询案件证据情况，caseImportId: {}", caseImportId);

        if (caseImportId == null) {
            throw new BusinessException("案件导入ID不能为空");
        }

        List<EvidenceOverview> evidenceList = evidenceOverviewMapper.listByCaseImportIdAndPartyName(caseImportId, partyNames);

        return evidenceList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询证据情况详情
     *
     * @param id 证据情况ID
     * @return 证据情况响应
     */
    public EvidenceOverviewResponse getById(Long id) {
        log.info("查询证据情况详情，id: {}", id);

        if (id == null) {
            throw new BusinessException("证据情况ID不能为空");
        }

        EvidenceOverview evidenceOverview = evidenceOverviewMapper.selectById(id);
        if (evidenceOverview == null) {
            throw new BusinessException("证据情况不存在");
        }

        return convertToResponse(evidenceOverview);
    }

    /**
     * 创建证据情况
     *
     * @param request 证据情况请求
     * @return 创建的证据情况响应
     */
    @ModuleUpdate({ModuleType.EVIDENCE_OVERVIEW})
    public EvidenceOverviewResponse create(EvidenceOverviewRequest request) {
        log.info("创建证据情况，案件ID: {}, 证据名称: {}", request.getCaseImportId(), request.getEvidenceName());

        EvidenceOverview evidenceOverview = new EvidenceOverview();
        BeanUtils.copyProperties(request, evidenceOverview);
        evidenceOverview.setCreateTime(LocalDateTime.now());
        evidenceOverview.setUpdateTime(LocalDateTime.now());

        int result = evidenceOverviewMapper.insert(evidenceOverview);
        if (result <= 0) {
            throw new BusinessException("证据情况创建失败");
        }

        log.info("证据情况创建成功，ID: {}", evidenceOverview.getId());
        return convertToResponse(evidenceOverview);
    }

    /**
     * 更新证据情况
     *
     * @param request 证据情况请求
     * @return 更新的证据情况响应
     */
    @ModuleUpdate({ModuleType.EVIDENCE_OVERVIEW})
    public EvidenceOverviewResponse update(EvidenceOverviewRequest request) {
        log.info("更新证据情况，ID: {}", request.getId());

        if (request.getId() == null) {
            throw new BusinessException("证据情况ID不能为空");
        }

        // 检查记录是否存在
        EvidenceOverview existingEvidence = evidenceOverviewMapper.selectById(request.getId());
        if (existingEvidence == null) {
            throw new BusinessException("证据情况不存在");
        }

        EvidenceOverview evidenceOverview = new EvidenceOverview();
        BeanUtils.copyProperties(request, evidenceOverview);
        evidenceOverview.setUpdateTime(LocalDateTime.now());

        int result = evidenceOverviewMapper.updateById(evidenceOverview);
        if (result <= 0) {
            throw new BusinessException("证据情况更新失败");
        }

        log.info("证据情况更新成功，ID: {}", evidenceOverview.getId());
        return getById(evidenceOverview.getId());
    }

    /**
     * 删除证据情况
     *
     * @param id 证据情况ID
     * @return 删除结果
     */
    public boolean delete(Long id) {
        log.info("删除证据情况，ID: {}", id);

        if (id == null) {
            throw new BusinessException("证据情况ID不能为空");
        }

        // 检查记录是否存在
        EvidenceOverview existingEvidence = evidenceOverviewMapper.selectById(id);
        if (existingEvidence == null) {
            throw new BusinessException("证据情况不存在");
        }
        if (existingEvidence.getAdopted()) {
            moduleUpdateService.markModulesNeedUpdate(existingEvidence.getCaseImportId(), List.of(ModuleType.EVIDENCE_OVERVIEW.getCode()));
        }
        int result = evidenceOverviewMapper.deleteById(id);
        boolean success = result > 0;

        if (success) {
            log.info("证据情况删除成功，ID: {}", id);
        } else {
            log.error("证据情况删除失败，ID: {}", id);
            throw new BusinessException("证据情况删除失败");
        }

        return success;
    }

    /**
     * 保存或更新证据情况
     * 根据ID判断是新增还是更新
     *
     * @param request 证据情况请求
     * @return 证据情况响应
     */
    @ModuleUpdate({ModuleType.EVIDENCE_OVERVIEW})
    public EvidenceOverviewResponse save(EvidenceOverviewRequest request) {
        if (request.getId() == null || request.getId() == 0) {
            return create(request);
        } else {
            return update(request);
        }
    }

    /**
     * 实体转换为响应DTO
     *
     * @param evidenceOverview 证据情况实体
     * @return 证据情况响应DTO
     */
    private EvidenceOverviewResponse convertToResponse(EvidenceOverview evidenceOverview) {
        EvidenceOverviewResponse response = new EvidenceOverviewResponse();
        BeanUtils.copyProperties(evidenceOverview, response);
        return response;
    }

    /**
     * 将字符串切分成指定长度的字符串列表
     *
     * @param fileUploadRecord 文件记录
     * @param length           每个子字符串的最大长度
     * @return 切分后的字符串列表
     */
    private List<OcrPageBlockDTO> splitStringByLength(FileUploadRecord fileUploadRecord, int length) {
        List<OcrResult> ocrResults = fileUploadRecord.getOcrResult();
        List<OcrPageBlockDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(ocrResults)) {
            return result;
        }
        ocrResults.sort(Comparator.comparing(OcrResult::getPageNumber));

        StringBuilder sb = new StringBuilder();
        int startPageNumber = 1;
        int endPageNumber = 1;
        for (OcrResult ocrResult : ocrResults) {
            endPageNumber = ocrResult.getPageNumber();
            String orcText = ocrResult.getRecTexts();
            if (sb.length() + orcText.length() > length) {
                OcrPageBlockDTO ocrPageBlockDTO = new OcrPageBlockDTO();
                ocrPageBlockDTO.setFileId(fileUploadRecord.getId());
                ocrPageBlockDTO.setFileName(fileUploadRecord.getFileName());
                ocrPageBlockDTO.setStartPageNumber(startPageNumber);
                ocrPageBlockDTO.setTextContent(sb.toString());
                ocrPageBlockDTO.setEndPageNumber(endPageNumber);
                result.add(ocrPageBlockDTO);
                sb = new StringBuilder();
                startPageNumber = ocrResult.getPageNumber() + 1;
            } else {
                sb.append(orcText);
            }
        }

        if (!sb.isEmpty()) {
            OcrPageBlockDTO ocrPageBlockDTO = new OcrPageBlockDTO();
            ocrPageBlockDTO.setFileId(fileUploadRecord.getId());
            ocrPageBlockDTO.setFileName(fileUploadRecord.getFileName());
            ocrPageBlockDTO.setStartPageNumber(startPageNumber);
            ocrPageBlockDTO.setTextContent(sb.toString());
            ocrPageBlockDTO.setEndPageNumber(endPageNumber);
            result.add(ocrPageBlockDTO);
        }

        return result;
    }
}
