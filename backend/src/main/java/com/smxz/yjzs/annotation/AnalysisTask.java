package com.smxz.yjzs.annotation;

import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.enums.TaskCompletionStrategy;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分析任务注解
 * 用于标记需要进行状态管理的分析任务方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AnalysisTask {
    
    /**
     * 任务类型（可选，如果方法参数中有TaskType则优先使用参数中的值）
     */
    TaskType taskType() default TaskType.CASE_PARTY;
    
    /**
     * 任务描述（可选）
     */
    String description() default "";
    
    /**
     * 任务完成策略（可选，默认为所有子任务成功才完成）
     */
    TaskCompletionStrategy completionStrategy() default TaskCompletionStrategy.ALL_SUCCESS;
}
