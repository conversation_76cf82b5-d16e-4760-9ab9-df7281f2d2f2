package com.smxz.yjzs.annotation;

import com.smxz.yjzs.enums.ModuleType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 模块更新注解
 * 用于标记会影响其他模块状态的方法
 * 当被标记的方法执行成功后，会自动标记指定的模块需要更新
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModuleUpdate {
    
    /**
     * 受影响的模块类型数组
     * 当前方法执行成功后，这些模块将被标记为需要更新
     */
    ModuleType[] value();
    
    /**
     * 描述信息（可选）
     */
    String description() default "";
}
