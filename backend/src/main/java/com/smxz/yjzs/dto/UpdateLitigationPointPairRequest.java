package com.smxz.yjzs.dto;

import com.smxz.yjzs.entity.Evidence;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量更新诉辩观点对请求DTO
 */
@Data
@Schema(description = "批量更新诉辩观点对请求")
public class UpdateLitigationPointPairRequest {

    @Schema(description = "案件导入ID")
    private Long caseImportId;

    @Schema(description = "诉方观点数据")
    private LitigationPointUpdateData plaintiffPoint;

    @Schema(description = "辩方观点数据")
    private LitigationPointUpdateData defendantPoint;

    @Schema(description = "关系数据")
    private RelationUpdateData relation;

    @Data
    @Schema(description = "观点更新数据")
    public static class LitigationPointUpdateData {
        @Schema(description = "观点ID")
        private Long id;

        @Schema(description = "当事人姓名")
        private String partyName;

        @Schema(description = "观点内容")
        private String content;

        @Schema(description = "证据列表")
        private List<Evidence> evidence;

        @Schema(description = "观点日期")
        private String pointDate;

        @Schema(description = "排序")
        private Integer sortOrder;

        @Schema(description = "是否需要更新")
        private Boolean needUpdate = false;
    }

    @Data
    @Schema(description = "关系更新数据")
    public static class RelationUpdateData {
        @Schema(description = "关系ID")
        private Long id;

        @Schema(description = "关系类型")
        private String relationType;

        @Schema(description = "诉方观点ID")
        private String plaintiffPointId;

        @Schema(description = "辩方观点ID")
        private String defendantPointId;

        @Schema(description = "是否需要更新")
        private Boolean needUpdate = false;
    }
}
