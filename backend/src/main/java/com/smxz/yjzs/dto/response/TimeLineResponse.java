package com.smxz.yjzs.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.smxz.yjzs.entity.Evidence;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TimeLineResponse {

    private Long id;

    private Long caseImportId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    private String eventResume;

    private String eventDescription;

    private List<Evidence> evidence;

    private Integer version;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
