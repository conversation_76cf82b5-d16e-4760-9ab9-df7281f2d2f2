package com.smxz.yjzs.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent任务执行结果
 * 包含任务结果和子任务信息
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentTaskResult {
    
    /**
     * 任务执行结果
     */
    private String result;
    
    /**
     * 子任务结果（JSON格式）
     */
    private String subTaskResults;
}
