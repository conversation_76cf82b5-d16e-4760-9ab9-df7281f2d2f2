package com.smxz.yjzs.dto.request;

import com.smxz.yjzs.entity.Evidence;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 保存诉辩观点请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "保存诉辩观点请求")
public class SaveLitigationPointsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 案件导入ID
     */
    @Schema(description = "案件导入ID", example = "1")
    private Long caseImportId;

    /**
     * 诉辩观点列表
     */
    @Valid
    @Schema(description = "诉辩观点列表")
    private List<LitigationPointData> points;

    /**
     * 诉辩关系（可选）
     */
    @Valid
    @Schema(description = "诉辩关系")
    private RelationData relation;

    /**
     * 诉辩观点数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "诉辩观点数据")
    public static class LitigationPointData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 诉辩方（plaintiff/defendant）
         */
        @Schema(description = "诉辩方", example = "plaintiff", allowableValues = {"plaintiff", "defendant"})
        private String side;

        /**
         * 当事人姓名
         */
        @Schema(description = "当事人姓名", example = "张三")
        private String partyName;

        /**
         * 观点内容
         */
        @Schema(description = "观点内容", example = "请求确认双方签订的合同有效")
        private String content;

        /**
         * 支持证据
         */
        @Valid
        @Schema(description = "支持证据列表")
        private List<Evidence> evidence;

        /**
         * 观点日期
         */
        @Schema(description = "观点日期", example = "2024-01-15")
        private String pointDate;

        /**
         * 排序序号
         */
        @Schema(description = "排序序号", example = "1")
        private Integer sortOrder;
    }

    /**
     * 诉辩关系数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "诉辩关系数据")
    public static class RelationData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 关系类型
         */
        @Schema(description = "关系类型", example = "对立", allowableValues = {"对立", "支持", "补充", "质疑"})
        private String relationType;

        /**
         * 诉方观点ID
         */
        @Schema(description = "诉方观点ID")
        private String plaintiffPointId;

        /**
         * 辩方观点ID
         */
        @Schema(description = "辩方观点ID")
        private String defendantPointId;
    }
}
