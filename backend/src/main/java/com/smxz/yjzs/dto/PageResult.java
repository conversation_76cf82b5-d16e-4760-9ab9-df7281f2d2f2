package com.smxz.yjzs.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 通用分页结果类
 */
@Data
@Accessors(chain = true)
public class PageResult<T> {
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页数据
     */
    private List<T> records;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> allRecords, int page, int pageSize) {
        // 参数校验
        if (allRecords == null) {
            allRecords = List.of();
        }
        if (page < 1) {
            page = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        PageResult<T> result = new PageResult<>();
        long total = allRecords.size();
        long pages = total == 0 ? 1 : (long) Math.ceil((double) total / pageSize);

        result.setCurrent((long) page)
              .setSize((long) pageSize)
              .setTotal(total)
              .setPages(pages);

        // 计算当前页数据
        List<T> pageRecords;
        if (total == 0 || page > pages) {
            // 没有数据或页码超出范围时返回空列表
            pageRecords = List.of();
        } else {
            int fromIndex = (page - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, allRecords.size());
            pageRecords = allRecords.subList(fromIndex, toIndex);
        }
        result.setRecords(pageRecords);

        // 计算是否有上一页和下一页
        result.setHasPrevious(page > 1)
              .setHasNext(page < pages && total > 0);

        return result;
    }
} 