package com.smxz.yjzs.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 诉讼费与当事人信息DTO
 * 用于返回包含当事人姓名和诉讼地位的诉讼费信息
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LegalFeesWithPartyDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 案件导入记录ID
     */
    private Long caseImportRecordId;

    /**
     * 当事人主键ID
     */
    private Long casePartyId;

    /**
     * 费用类型
     */
    private String feeType;

    /**
     * 已缴金额
     */
    private BigDecimal amountPaid;

    /**
     * 承担金额
     */
    private BigDecimal amountToBear;

    /**
     * 当事人姓名
     */
    private String partyName;

    /**
     * 诉讼地位
     */
    private String partyType;
}
