package com.smxz.yjzs.dto.response;

import lombok.Data;
import lombok.Builder;

import java.util.List;

@Data
@Builder
public class BatchFileUploadResponse {
    /**
     * 上传成功的文件列表
     */
    private List<FileUploadResponse> successFiles;
    
    /**
     * 上传失败的文件列表
     */
    private List<FailedFileInfo> failedFiles;
    
    /**
     * 总文件数
     */
    private long totalCount;
    
    /**
     * 成功数量
     */
    private long successCount;
    
    /**
     * 失败数量
     */
    private long failedCount;
    
    @Data
    @Builder
    public static class FailedFileInfo {
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 失败原因
         */
        private String errorMessage;
    }
} 