package com.smxz.yjzs.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TaskInfoExcelDTO {

    @ExcelProperty(value = "模型名称")
    private String modelName;

    @ExcelProperty("案卷名称")
    private String caseName;

    // 关系图谱
    @ExcelProperty(value = "关系图谱模型输入")
    private String relationGraphMessage;

    @ExcelProperty(value = "关系图谱模型输出")
    private String relationGraphOutput;

    @ExcelProperty(value = "关系图谱模型输出耗时")
    private Long relationGraphCostTime;

    // 案件时序链
    @ExcelProperty(value = "案件时序链模型输入")
    private String caseTimelineMessage;

    @ExcelProperty(value = "案件时序链模型输出")
    private String caseTimelineOutput;

    @ExcelProperty(value = "案件时序链模型输出耗时")
    private Long caseTimelineCostTime;

    // 矛盾分析
    @ExcelProperty(value = "矛盾分析模型输入")
    private String contradictionMessage;

    @ExcelProperty(value = "矛盾分析模型输出")
    private String contradictionOutput;

    @ExcelProperty(value = "矛盾分析模型输出耗时")
    private Long contradictionCostTime;

    // 争议焦点
    @ExcelProperty(value = "争议焦点模型输入")
    private String disputePointsMessage;

    @ExcelProperty(value = "争议焦点模型输出")
    private String disputePointsOutput;

    @ExcelProperty(value = "争议焦点模型输出耗时")
    private Long disputePointsCostTime;


}
