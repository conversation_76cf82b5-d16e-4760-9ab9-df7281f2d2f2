package com.smxz.yjzs.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 证据情况请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "证据情况请求")
public class EvidenceOverviewRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时需要）
     */
    @Schema(description = "主键ID，更新时必填", example = "1")
    private Long id;

    /**
     * 案件导入ID
     */
    @NotNull(message = "案件导入ID不能为空")
    @Schema(description = "案件导入ID", example = "1", required = true)
    private Long caseImportId;

    /**
     * 当事人类型
     */
    @Schema(description = "当事人类型", example = "plaintiff")
    private String partyType;

    /**
     * 当事人姓名
     */
    @Schema(description = "当事人姓名", example = "张三")
    private String partyName;

    /**
     * 证据名称
     */
    @NotBlank(message = "证据名称不能为空")
    @Schema(description = "证据名称", example = "合同原件", required = true)
    private String evidenceName;

    /**
     * 证明目的
     */
    @Schema(description = "证明目的", example = "证明双方存在合同关系")
    private String evidencePurpose;

    /**
     * 证明综述
     */
    @Schema(description = "证明综述", example = "该合同能够证明双方当事人之间的权利义务关系")
    private String evidenceSummary;

    /**
     * 是否采纳
     */
    @Schema(description = "是否采纳", example = "true")
    private Boolean adopted;
}
