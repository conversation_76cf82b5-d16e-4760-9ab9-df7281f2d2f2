package com.smxz.yjzs.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 法条树形结构节点
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LegalProvisionTreeNode {

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点标题（如：第一条、第一款、第一项）
     */
    private String title;

    /**
     * 节点内容
     */
    private String content;

    /**
     * 层级：1=条,2=款,3=项
     */
    private Integer level;

    /**
     * 条号
     */
    private Integer articleNo;

    /**
     * 款号
     */
    private Integer paragraphNo;

    /**
     * 项号
     */
    private Integer subparagraphNo;

    /**
     * 子节点列表
     */
    private List<LegalProvisionTreeNode> children;
}
