package com.smxz.yjzs.dto.response;

import com.smxz.yjzs.entity.Evidence;
import com.smxz.yjzs.entity.LitigationRelation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 诉辩观点响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "诉辩观点响应数据")
public class LitigationPointsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 诉方观点数据
     */
    @Schema(description = "诉方观点数据")
    private List<PartyPoints> plaintiffs;

    /**
     * 辩方观点数据
     */
    @Schema(description = "辩方观点数据")
    private List<PartyPoints> defendants;

    /**
     * 诉辩关系数据
     */
    @Schema(description = "诉辩关系数据")
    private List<LitigationRelation> relations;

    /**
     * 当事人观点数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "当事人观点数据")
    public static class PartyPoints implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 当事人姓名
         */
        @Schema(description = "当事人姓名", example = "张三")
        private String name;

        /**
         * 按日期分组的观点
         */
        @Schema(description = "按日期分组的观点")
        private Map<String, List<PointInfo>> pointsByDate;
    }

    /**
     * 诉辩观点信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "诉辩观点信息")
    public static class PointInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 观点ID
         */
        @Schema(description = "观点ID", example = "1")
        private Long id;

        /**
         * 观点内容
         */
        @Schema(description = "观点内容", example = "请求确认双方签订的合同有效")
        private String content;

        /**
         * 观点日期
         */
        @Schema(description = "观点日期", example = "2024-01-15")
        private LocalDate pointDate;

        /**
         * 排序序号
         */
        @Schema(description = "排序序号", example = "1")
        private Integer sortOrder;

        /**
         * 当事人姓名
         */
        @Schema(description = "当事人姓名", example = "张三")
        private String partyName;

        /**
         * 支持证据
         */
        @Schema(description = "支持证据列表")
        private List<Evidence> evidence;

        /**
         * 诉辩方
         */
        @Schema(description = "诉辩方", example = "plaintiff")
        private String side;

        /**
         * 案件导入ID
         */
        @Schema(description = "案件导入ID", example = "1")
        private Long caseImportId;

        /**
         * 观点ID（字符串）
         */
        @Schema(description = "观点ID字符串")
        private String pointId;
    }
}
