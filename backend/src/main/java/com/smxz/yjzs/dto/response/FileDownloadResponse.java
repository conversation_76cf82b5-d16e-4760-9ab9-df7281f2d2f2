package com.smxz.yjzs.dto.response;

import lombok.Data;
import lombok.Builder;

import java.io.InputStream;

@Data
@Builder
public class FileDownloadResponse {
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件输入流
     */
    private InputStream inputStream;
} 