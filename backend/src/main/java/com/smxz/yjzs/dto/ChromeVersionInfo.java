package com.smxz.yjzs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Chrome版本信息")
public class ChromeVersionInfo {
    @Schema(description = "推荐版本号")
    private String version;

    @Schema(description = "下载链接")
    private String downloadUrl;

    @Schema(description = "操作系统")
    private String os;
}