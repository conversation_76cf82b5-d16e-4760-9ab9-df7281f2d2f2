package com.smxz.yjzs.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文书生成前置检查响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentGenerationPreCheckVO {

    /**
     * 是否正在加载/处理中
     */
    private Boolean loading;

    /**
     * 任务类型，如果loading为true时返回具体的任务类型
     */
    private String taskType;

    /**
     * 需要异步调用的方法名，如果需要在Controller层调用异步方法时使用
     */
    private String needsAsyncCall;
}
