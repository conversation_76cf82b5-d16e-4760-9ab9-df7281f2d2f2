package com.smxz.yjzs.dto.response;

import com.smxz.extractor.common.enums.DocumentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 文件上传记录响应DTO
 * 用于返回文件上传记录信息，不包含OCR相关字段
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "文件上传记录响应")
public class FileUploadRecordDTO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "1")
    private Long id;

    /**
     * 案件导入ID
     * 关联到案件导入记录表
     */
    @Schema(description = "案件导入ID", example = "1")
    private Long caseImportId;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "合同.pdf")
    private String fileName;

    /**
     * 文件存储路径
     * 可以是本地路径或MinIO对象存储路径
     */
    @Schema(description = "文件存储路径")
    private String filePath;

    /**
     * PDF文件存储路径
     * 用于存储转换后的PDF文件路径
     * 当文件类型为PDF时，该字段与filePath相同
     */
    @Schema(description = "PDF文件存储路径")
    private String pdfPath;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    /**
     * 文件类型
     * 例如：pdf, doc, docx, zip, rar等
     */
    @Schema(description = "文件类型", example = "pdf")
    private String fileType;

    /**
     * 上传者ID
     */
    @Schema(description = "上传者ID", example = "1")
    private Integer uploaderId;

    /**
     * 上传者姓名
     */
    @Schema(description = "上传者姓名", example = "张三")
    private String uploaderName;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private Date uploadTime;

    /**
     * 文件状态
     * 0: 待处理
     * 1: 处理中
     * 2: 处理完成
     * 3: 处理失败
     */
    @Schema(description = "文件状态", example = "2")
    private Integer status;

    /**
     * 文件MD5哈希值
     * 用于文件完整性校验和去重
     */
    @Schema(description = "文件MD5哈希值")
    private String md5Hash;

    /**
     * 文件描述
     */
    @Schema(description = "文件描述")
    private String description;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数", example = "0")
    private Integer downloadCount;

    /**
     * 提取的文本内容
     * 存储从PDF文件中提取的完整文本内容
     */
    @Schema(description = "提取的文本内容")
    private String extractedText;

    /**
     * 逻辑删除标识
     * 0: 未删除
     * 1: 已删除
     */
    @Schema(description = "逻辑删除标识", example = "0")
    private Integer deleted;

    /**
     * 删除时间
     */
    @Schema(description = "删除时间")
    private LocalDateTime deletedTime;

    /**
     * 删除人
     */
    @Schema(description = "删除人")
    private String deletedBy;

    /**
     * 文档类型
     */
    @Schema(description = "文档类型")
    private DocumentType documentType;

    /**
     * OCR开始时间
     */
    @Schema(description = "OCR开始时间")
    private LocalDateTime ocrStartTime;

    /**
     * OCR结束时间
     */
    @Schema(description = "OCR结束时间")
    private LocalDateTime ocrEndTime;

    /**
     * OCR耗时（毫秒）
     */
    @Schema(description = "OCR耗时（毫秒）", example = "5000")
    private Long ocrDuration;
}
