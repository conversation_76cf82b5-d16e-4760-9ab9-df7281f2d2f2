package com.smxz.yjzs.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.smxz.yjzs.enums.EvidenceOverviewAnalyseStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 证据情况响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "证据情况响应")
public class EvidenceOverviewResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "1")
    private Long id;

    /**
     * 案件导入ID
     */
    @Schema(description = "案件导入ID", example = "1")
    private Long caseImportId;

    /**
     * 当事人类型
     */
    @Schema(description = "当事人类型", example = "plaintiff")
    private String partyType;

    /**
     * 当事人姓名
     */
    @Schema(description = "当事人姓名", example = "张三")
    private String partyName;

    /**
     * 证据名称
     */
    @Schema(description = "证据名称", example = "合同原件")
    private String evidenceName;

    /**
     * 证明目的
     */
    @Schema(description = "证明目的", example = "证明双方存在合同关系")
    private String evidencePurpose;

    /**
     * 证明综述
     */
    @Schema(description = "证明综述", example = "该合同能够证明双方当事人之间的权利义务关系")
    private String evidenceSummary;

    /**
     * 源文件id
     */
    @Schema(description = "指向的源文件id", example = "1")
    private Long originalFileId;

    /**
     * 源文件名
     */
    @Schema(description = "指向的源文件名", example = "证明双方存在合同关系")
    private String originalFileName;

    @Schema(title = "引用的起始页")
    private Integer originalFileStartPage;

    /**
     * 是否采纳
     */
    @Schema(description = "是否采纳", example = "true")
    private Boolean adopted;

    @Schema(description = "分析状态")
    private EvidenceOverviewAnalyseStatus analyseStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
