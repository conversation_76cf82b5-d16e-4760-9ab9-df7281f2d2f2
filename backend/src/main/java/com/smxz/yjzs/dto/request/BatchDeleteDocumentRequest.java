package com.smxz.yjzs.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批量删除文书请求DTO
 */
@Data
@Schema(description = "批量删除文书请求")
public class BatchDeleteDocumentRequest {

    @NotEmpty(message = "文书ID列表不能为空")
    @Size(min = 1, max = 100, message = "文书ID列表长度必须在1-100之间")
    @Schema(description = "要删除的文书ID列表", required = true, example = "[1, 2, 3]")
    private List<Long> documentIds;
} 