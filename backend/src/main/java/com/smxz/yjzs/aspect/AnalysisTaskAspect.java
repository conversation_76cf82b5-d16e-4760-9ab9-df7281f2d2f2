package com.smxz.yjzs.aspect;

import com.smxz.yjzs.annotation.AnalysisTask;
import com.smxz.yjzs.common.utils.AgentTaskContext;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.service.CaseStatusManager;
import com.smxz.yjzs.service.TaskStatusManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 分析任务AOP切面
 * 自动处理任务状态管理
 */
@Slf4j
@Aspect
@Component
public class AnalysisTaskAspect {
    
    @Autowired
    private TaskStatusManager taskStatusManager;

    @Autowired
    private CaseStatusManager caseStatusManager;
    
    /**
     * 环绕通知：处理分析任务的状态管理
     */
    @Around("@annotation(analysisTask)")
    public Object handleAnalysisTask(ProceedingJoinPoint joinPoint, AnalysisTask analysisTask) throws Throwable {
        log.info("=== AOP切面被触发 ===");
        log.info("方法: {}", joinPoint.getSignature().getName());
        log.info("注解任务类型: {}", analysisTask.taskType().getCode());

        // 优先从方法参数中提取TaskType，如果没有则使用注解中的TaskType
        TaskType taskType = extractTaskType(joinPoint.getArgs());
        if (taskType == null) {
            taskType = analysisTask.taskType();
            log.info("使用注解中的任务类型: {}", taskType.getCode());
        } else {
            log.info("使用参数中的任务类型: {}", taskType.getCode());
        }

        Long caseId = extractCaseId(joinPoint.getArgs());

        if (caseId == null) {
            log.error("无法从方法参数中提取案件ID，方法: {}", joinPoint.getSignature().getName());
            throw new IllegalArgumentException("方法参数中必须包含案件ID");
        }

        long startTime = System.currentTimeMillis();
        log.info("=== AOP开始执行分析任务 ===，caseId: {}, taskType: {}", caseId, taskType.getCode());
        
        Long taskId = null;
        try {
            // 1. 终止同类型的执行中任务
            taskStatusManager.terminateRunningTask(caseId, taskType);

            // 2. 开始任务（创建新的任务记录）
            taskId = taskStatusManager.startTask(caseId, taskType);

            // 3. 设置任务ID到ThreadLocal，供executeTask使用
            AgentTaskContext.setCurrentTaskId(taskId);

            // 4. 设置任务完成策略到ThreadLocal
            AgentTaskContext.setCompletionStrategy(analysisTask.completionStrategy());

            // 5. 设置案件为分析中状态
            caseStatusManager.setCaseAnalyzing(caseId);

            // 6. 执行业务逻辑
            Object result = joinPoint.proceed();

            taskStatusManager.markTaskSuccess(taskId);

            // 7. 检查并更新案件状态（任务结果已在finalProcess中更新）
            caseStatusManager.checkAndUpdateCaseStatus(caseId);

            long duration = System.currentTimeMillis() - startTime;
            log.info("=== AOP分析任务执行成功 ===，caseId: {}, taskType: {}, taskId: {}, 耗时: {}ms",
                    caseId, taskType.getCode(), taskId, duration);

            return result;

        } catch (Throwable e) {
            // 8. 任务失败（使用任务ID直接更新）
            if (taskId != null) {
                String message = ExceptionUtils.getMessage(e);
                String rootCauseMessage = ExceptionUtils.getRootCauseMessage(e);
                taskStatusManager.markTaskFailed(taskId, e.getMessage());
            }

            // 9. 检查并更新案件状态
            caseStatusManager.checkAndUpdateCaseStatus(caseId);

            long duration = System.currentTimeMillis() - startTime;
            log.error("=== AOP分析任务执行失败 ===，caseId: {}, taskType: {}, taskId: {}, 耗时: {}ms, 错误: {}",
                     caseId, taskType.getCode(), taskId, duration, ExceptionUtils.getStackTrace(e));

            throw new RuntimeException("分析任务执行失败: " + e.getMessage(), e);
        } finally {
            // 10. 清理ThreadLocal
            AgentTaskContext.clearAll();
        }
    }
    
    /**
     * 从方法参数中提取案件ID
     * 约定：第一个Long类型参数为案件ID
     */
    private Long extractCaseId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        // 查找第一个Long类型的参数作为案件ID
        for (Object arg : args) {
            if (arg instanceof Long) {
                return (Long) arg;
            }
        }

        return null;
    }

    /**
     * 从方法参数中提取任务类型
     * 约定：第二个参数如果是TaskType类型，则使用该参数
     */
    private TaskType extractTaskType(Object[] args) {
        if (args == null || args.length < 2) {
            return null;
        }

        // 查找TaskType类型的参数
        for (Object arg : args) {
            if (arg instanceof TaskType) {
                return (TaskType) arg;
            }
        }

        return null;
    }
}
