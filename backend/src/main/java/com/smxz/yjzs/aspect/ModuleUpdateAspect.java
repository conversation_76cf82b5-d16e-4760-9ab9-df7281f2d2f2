package com.smxz.yjzs.aspect;

import com.smxz.yjzs.annotation.ModuleUpdate;
import com.smxz.yjzs.enums.ModuleType;
import com.smxz.yjzs.service.ModuleUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模块更新AOP切面
 * 自动处理模块更新状态管理
 */
@Slf4j
@Aspect
@Component
public class ModuleUpdateAspect {
    
    @Autowired
    private ModuleUpdateService moduleUpdateService;
    
    /**
     * 环绕通知：处理模块更新状态管理
     */
    @Around("@annotation(moduleUpdate)")
    public Object handleModuleUpdate(ProceedingJoinPoint joinPoint, ModuleUpdate moduleUpdate) throws Throwable {
        log.info("=== 模块更新AOP切面被触发 ===");
        log.info("方法: {}", joinPoint.getSignature().getName());
        log.info("受影响的模块: {}", Arrays.toString(moduleUpdate.value()));

        Long caseId = extractCaseId(joinPoint.getArgs());
        
        if (caseId == null) {
            log.error("无法从方法参数中提取案件ID，方法: {}", joinPoint.getSignature().getName());
            throw new IllegalArgumentException("方法参数中必须包含案件ID");
        }

        long startTime = System.currentTimeMillis();
        log.info("=== 开始执行业务方法 ===，caseId: {}", caseId);
        
        try {
            // 执行业务逻辑
            Object result = joinPoint.proceed();
            
            // 业务方法执行成功后，标记相关模块需要更新
            List<String> affectedModuleCodes = Arrays.stream(moduleUpdate.value())
                    .map(ModuleType::getCode)
                    .collect(Collectors.toList());
            
            moduleUpdateService.markModulesNeedUpdate(caseId, affectedModuleCodes);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("=== 模块更新处理成功 ===，caseId: {}, 受影响模块: {}, 耗时: {}ms",
                    caseId, affectedModuleCodes, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("=== 业务方法执行失败，跳过模块更新 ===，caseId: {}, 耗时: {}ms, 错误: {}",
                     caseId, duration, e.getMessage());
            
            // 业务方法失败时不更新模块状态，直接抛出异常
            throw e;
        }
    }
    
    /**
     * 从方法参数中提取案件ID
     * 约定：
     * 1. 优先查找第一个Long类型参数作为案件ID
     * 2. 如果没有Long类型参数，则从第一个对象中反射获取caseImportId字段
     */
    private Long extractCaseId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        // 1. 优先查找第一个Long类型的参数作为案件ID
        for (Object arg : args) {
            if (arg instanceof Long) {
                return (Long) arg;
            }
        }

        // 2. 如果没有Long类型参数，从第一个对象中反射获取caseImportId字段
        Object firstArg = args[0];
        if (firstArg != null) {
            return extractCaseIdFromObject(firstArg);
        }

        return null;
    }

    /**
     * 从对象中反射获取caseImportId字段
     * @param obj 目标对象
     * @return 案件ID，如果获取失败返回null
     */
    private Long extractCaseIdFromObject(Object obj) {
        try {
            Class<?> clazz = obj.getClass();

            // 尝试获取caseImportId字段
            Field caseImportIdField = null;
            try {
                caseImportIdField = clazz.getDeclaredField("caseImportId");
            } catch (NoSuchFieldException e) {
                // 如果当前类没有，尝试从父类获取
                Class<?> superClass = clazz.getSuperclass();
                while (superClass != null && superClass != Object.class) {
                    try {
                        caseImportIdField = superClass.getDeclaredField("caseImportId");
                        break;
                    } catch (NoSuchFieldException ex) {
                        superClass = superClass.getSuperclass();
                    }
                }
            }

            if (caseImportIdField != null) {
                caseImportIdField.setAccessible(true);
                Object value = caseImportIdField.get(obj);
                if (value instanceof Long) {
                    log.debug("通过反射从对象 {} 中获取到案件ID: {}", obj.getClass().getSimpleName(), value);
                    return (Long) value;
                }
            }

            log.warn("无法从对象 {} 中获取caseImportId字段", obj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            log.error("反射获取案件ID失败，对象类型: {}, 错误: {}",
                     obj.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }
}
