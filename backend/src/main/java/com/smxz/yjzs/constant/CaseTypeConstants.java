package com.smxz.yjzs.constant;

import com.smxz.extractor.common.enums.DocumentType;

/**
 * 案件类型常量类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public class CaseTypeConstants {

    /**
     * 案件类型代码
     */
    public static class AjlxdmCode {

        /** 民事一审案件 */
        public static final String MSYS = "0301";

        /** 民事二审案件 */
        public static final String MSES = "0302";

    }

    /**
     * 案件名称关键词
     */
    public static class CaseNameKeywords {

        /** 民事一审关键词 */
        public static final String MSYS_KEYWORD = "民初";

        /** 民事二审关键词 */
        public static final String MSES_KEYWORD = "民终";

    }

    /**
     * 文书案件类型
     */
    public static class DocumentCaseType {

        /** 民事一审 */
        public static final String CIVIL_JUDGEMENT_1 = "civil_judgement_1";

        /** 民事二审驳回上诉 */
        public static final String CIVIL_JUDGEMENT_2_REJECT = "civil_judgement_2_reject";

        /** 民事二审改判 */
        public static final String CIVIL_JUDGEMENT_2_OVERRULE = "civil_judgement_2_overrule";

        /** 民事二审部分改判 */
        public static final String CIVIL_JUDGEMENT_2_PARTIAL_OVERRULE = "civil_judgement_2_partial_overrule";

    }


    public static class DataType {

        /** 提取 */
        public static final Integer TQ = 1;

        /** 新增 */
        public static final Integer NEW = 2;

    }

    /**
     * 根据案件名称判断案件类型代码
     *
     * @param caseName 案件名称
     * @return 案件类型代码
     */
    public static String determineAjlxdmByCaseName(String caseName) {
        if (caseName == null || caseName.trim().isEmpty()) {
            return AjlxdmCode.MSYS; // 默认为民事一审
        }

        String caseNameTrimmed = caseName.trim();

        // 按优先级判断
        if (caseNameTrimmed.contains(CaseNameKeywords.MSES_KEYWORD)) {
            return AjlxdmCode.MSES;
        } else if (caseNameTrimmed.contains(CaseNameKeywords.MSYS_KEYWORD)) {
            return AjlxdmCode.MSYS;
        } else {
            // 默认为民事一审
            return AjlxdmCode.MSYS;
        }
    }
        /**
         * 根据案件名称判断案件类型代码
         *
         * @param ah 案号
         * @return 案件类型代码
         */
        public static DocumentType determinePjsByCaseName(String ah) {
            if (ah == null || ah.trim().isEmpty()) {
                return DocumentType.YSPJS; // 默认为民事一审
            }

            String caseNameTrimmed = ah.trim();

            // 按优先级判断
            if (caseNameTrimmed.contains(CaseNameKeywords.MSES_KEYWORD)) {
                return DocumentType.ESPJS;
            } else if (caseNameTrimmed.contains(CaseNameKeywords.MSYS_KEYWORD)) {
                return DocumentType.YSPJS; // 默认为民事一审
            } else {
                // 默认为民事一审
                return DocumentType.YSPJS; // 默认为民事一审
            }
        }
}
