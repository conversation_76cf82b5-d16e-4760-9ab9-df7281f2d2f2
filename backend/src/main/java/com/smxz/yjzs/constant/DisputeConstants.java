package com.smxz.yjzs.constant;

/**
 * 争议焦点相关常量
 */
public class DisputeConstants {

    /**
     * 诉讼当事人类型
     */
    public static class PartyType {
        /** 诉方/原告 */
        public static final String PLAINTIFF = "plaintiff";

        /** 辩方/被告 */
        public static final String DEFENDANT = "defendant";
    }

    /**
     * 争议焦点结论类型
     */
    public static class FocusConclusion {
        /** 是 */
        public static final String YES = "yes";

        /** 否 */
        public static final String NO = "no";

        /** 其他 */
        public static final String OTHER = "other";
    }

    /**
     * 知识库配置键
     */
    public static class KnowledgeBaseConfigKey {
        /** 法条信息查询配置 */
        public static final String LAW_INFO_QUERY = "LAW_INFO_QUERY";
    }

    /**
     * 私有构造函数，防止实例化
     */
    private DisputeConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
