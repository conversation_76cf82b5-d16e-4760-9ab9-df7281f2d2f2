package com.smxz.yjzs.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文书生成队列配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "wssc.generation.queue")
public class DocumentGenerationConfig {
    
    /**
     * 最大并发生成数量
     * 默认值：3
     */
    private Integer maxConcurrentCount = 1;
    
    /**
     * 任务超时时间（小时）
     * 默认值：1小时
     */
    private Integer timeoutHours = 1;
    
    /**
     * 定时清理任务执行间隔（分钟）
     * 默认值：5分钟
     */
    private Integer cleanupIntervalMinutes = 5;
}