package com.smxz.yjzs.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OCR重试配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.preprocess.ocr.retry")
public class OcrRetryConfig {
    
    /**
     * 最大重试次数
     * 默认值：3
     */
    private Integer maxAttempts = 3;
    
    /**
     * 初始延迟时间（毫秒）
     * 默认值：1000ms
     */
    private Long initialDelay = 1000L;
    
    /**
     * 延迟倍数
     * 默认值：2（每次重试延迟时间翻倍）
     */
    private Double multiplier = 2.0;
    
    /**
     * 最大延迟时间（毫秒）
     * 默认值：10000ms
     */
    private Long maxDelay = 10000L;
}
