package com.smxz.yjzs.config;

import com.smxz.dynamic.ai.core.ChatModelKeyHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Value("${thread-pool.core-pool-size:10}")
    private int corePoolSize;

    @Value("${thread-pool.max-pool-size:20}")
    private int maxPoolSize;

    @Value("${thread-pool.queue-capacity:200}")
    private int queueCapacity;

    @Value("${thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${thread-pool.thread-name-prefix:yjzs-async-}")
    private String threadNamePrefix;

    @Value("${file.preprocess.ocr.concurrent-threads:4}")
    private int ocrConcurrentThreads;

    @Value("${file.preprocess.ocr.queue-capacity:200}")
    private int ocrQueueCapacity;


    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        // 队列容量
        executor.setQueueCapacity(queueCapacity);
        // 线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 线程名前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        // 设置装饰器，让其能够支持模型切换
        executor.setTaskDecorator(runnable -> {
            String key = ChatModelKeyHolder.get();
            return () -> {
                ChatModelKeyHolder.set(key);
                runnable.run();
                ChatModelKeyHolder.clear();
            };
        });

        // 初始化
        executor.initialize();
        
        log.info("线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }

    @Bean("ocrTaskExecutor")
    public Executor ocrTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // OCR专用线程池：固定线程数 + 有界队列
        executor.setCorePoolSize(ocrConcurrentThreads);
        executor.setMaxPoolSize(ocrConcurrentThreads);
        executor.setQueueCapacity(ocrQueueCapacity); // 可配置队列容量
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("ocr-async-");
        // 队列满时使用CallerRunsPolicy提供背压机制
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        // 设置装饰器，让其能够支持模型切换
        executor.setTaskDecorator(runnable -> {
            String key = ChatModelKeyHolder.get();
            return () -> {
                ChatModelKeyHolder.set(key);
                runnable.run();
                ChatModelKeyHolder.clear();
            };
        });

        executor.initialize();
        
        log.info("OCR线程池初始化完成，并发线程数：{}，队列容量：{}", ocrConcurrentThreads, ocrQueueCapacity);
        
        return executor;
    }
} 