package com.smxz.yjzs.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 缓存配置
 */
@Configuration
@EnableCaching
@ConfigurationProperties(prefix = "cache")
@Data
public class CacheConfig {

    /**
     * 是否启用异步预加载树形结构缓存
     */
    private boolean asyncPreloadEnabled = true;

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        // 设置缓存名称
        cacheManager.setCacheNames(Arrays.asList("legalProvisionTree","userCache"));
        return cacheManager;
    }
}
