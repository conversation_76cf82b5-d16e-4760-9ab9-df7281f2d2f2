package com.smxz.yjzs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {
    
    /**
     * 待执行
     */
    PENDING(0, "待执行"),
    
    /**
     * 执行中
     */
    RUNNING(1, "执行中"),
    
    /**
     * 执行成功
     */
    SUCCESS(2, "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED(3, "执行失败");
    
    /**
     * 状态代码
     */
    private final Integer code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 根据代码获取任务状态
     * @param code 状态代码
     * @return 任务状态枚举
     */
    public static TaskStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的任务状态代码: " + code);
    }
}
