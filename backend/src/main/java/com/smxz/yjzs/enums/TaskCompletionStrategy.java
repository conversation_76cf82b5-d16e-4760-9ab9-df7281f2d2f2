package com.smxz.yjzs.enums;

/**
 * 任务完成策略枚举
 */
public enum TaskCompletionStrategy {
    
    /**
     * 第一个子任务成功即完成
     */
    FIRST_SUCCESS("FIRST_SUCCESS", "第一个子任务成功即完成"),
    
    /**
     * 所有子任务成功才完成（默认策略）
     */
    ALL_SUCCESS("ALL_SUCCESS", "所有子任务成功才完成");
    
    private final String code;
    private final String name;
    
    TaskCompletionStrategy(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
}