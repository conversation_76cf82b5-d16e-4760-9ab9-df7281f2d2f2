package com.smxz.yjzs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分析任务类型枚举
 */
@Getter
@AllArgsConstructor
public enum TaskType {
    
    /**
     * 诉辩关系分析
     */
    LITIGATION_RELATION("LITIGATION_RELATION", "诉辩关系分析"),

    /**
     * 争议焦点分析
     */
    DISPUTE_FOCUS("DISPUTE_FOCUS", "争议焦点分析"),
    
    /**
     * 案件事实认定提取
     */
    CASE_FACT("CASE_FACT", "案件事实认定提取"),

    /**
     * 当事人分析
     */
    CASE_PARTY("CASE_PARTY", "当事人分析"),

    /**
     * 案由提取
     */
    CASE_CAUSE_EXTRACTION("CASE_CAUSE_EXTRACTION", "案由提取"),

    /**
     * 证据情况分析
     */
    EVIDENCE_OVERVIEW("EVIDENCE_OVERVIEW", "证据情况分析"),

    /**
     * 质证情况分析
     */
    VERIFICATION_EVIDENCE("VERIFICATION_EVIDENCE", "质证情况分析"),

    /**
     * 无争议事实生成
     */
    UNDISPUTED_FACTS("UNDISPUTED_FACTS", "无争议事实生成"),

    /**
     * 争议焦点说理分析
     */
    DISPUTE_FOCUS_REASONING("DISPUTE_FOCUS_REASONING", "争议焦点说理分析"),

    /**
     * 认定事实生成
     */
    DETERMINE_FACTS("DETERMINE_FACTS", "认定事实生成"),

    /**
     * 文书生成
     */
    DOCUMENT_GENERATION("DOCUMENT_GENERATION","文书生成"),
    /**
     * 提取原审信息
     */
    TQ_YSXX_FROM_YSPJS("TQ_YSXX_FROM_YSPJS", "提取原审信息"),
    /**
     * 提取原审判决结果信息
     */
    TQ_YSCPJG_FROM_YSPJS("TQ_YSCPJG_FROM_YSPJS", "提取原审判决结果信息"),

    /**
     * 文书生成-当事人信息
     */
    WSSC_DSRXX("WSSC-DSRXX", "文书生成-当事人信息"),

    /**
     * 文书生成-审理经过
     */
    WSSC_SLGC("WSSC-SLGC", "文书生成-审理经过"),

    /**
     * 文书生成-诉辩信息
     */
    WSSC_SBXX("WSSC-SBXX", "文书生成-诉辩信息"),

    /**
     * 文书生成-原审诉讼请求
     */
    WSSC_YSSQQ("WSSC-YSSQQ", "文书生成-原审诉讼请求"),

    /**
     * 文书生成-原审认定事实
     */
    WSSC_YSRDSJ("WSSC-YSRDSJ", "文书生成-原审认定事实"),

    /**
     * 文书生成-原审本院认为+原审裁判结果
     */
    WSSC_YSBYCP("WSSC-YSBYCP", "文书生成-原审本院认为+原审裁判结果"),

    /**
     * 文书生成-原审裁判结果
     */
    WSSC_YSCPJG("WSSC-YSCPJG", "文书生成-原审裁判结果"),

    /**
     * 文书生成-认定事实
     */
    WSSC_RDSJ("WSSC-RDSJ", "文书生成-认定事实"),

    /**
     * 文书生成-总结
     */
    WSSC_ZJ("WSSC-ZJ", "裁判理由、综上所述、裁判结果、诉讼费"),

    /**
     * 矛盾识别分析
     */
    CONTRADICTION_RECOGNITION("CONTRADICTION_RECOGNITION", "矛盾识别分析");

    /**
     * 任务类型代码
     */
    private final String code;
    
    /**
     * 任务类型名称
     */
    private final String name;
    
    /**
     * 根据代码获取任务类型
     * @param code 任务类型代码
     * @return 任务类型枚举
     */
    public static TaskType fromCode(String code) {
        for (TaskType taskType : values()) {
            if (taskType.getCode().equals(code)) {
                return taskType;
            }
        }
        throw new IllegalArgumentException("未知的任务类型代码: " + code);
    }
}
