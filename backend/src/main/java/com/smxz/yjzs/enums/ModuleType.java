package com.smxz.yjzs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模块类型枚举
 * 定义系统中所有可能需要更新状态管理的模块
 */
@Getter
@AllArgsConstructor
public enum ModuleType {
    
    /**
     * 争议焦点模块
     */
    DISPUTE_FOCUS("DISPUTE_FOCUS", "争议焦点"),
    
    /**
     * 争议焦点说理模块
     */
    DISPUTE_FOCUS_REASONING("DISPUTE_FOCUS_REASONING", "争议焦点说理"),
    
    /**
     * 认定事实模块
     */
    DETERMINE_FACTS("DETERMINE_FACTS", "认定事实"),
    
    /**
     * 无争议事实模块
     */
    UNDISPUTED_FACTS("UNDISPUTED_FACTS", "无争议事实"),
    
    /**
     * 当事人分析模块
     */
    CASE_PARTY("CASE_PARTY", "当事人分析"),
    
    /**
     * 诉辩关系模块
     */
    LITIGATION_RELATION("LITIGATION_RELATION", "诉辩关系"),
    
    /**
     * 证据情况模块
     */
    EVIDENCE_OVERVIEW("EVIDENCE_OVERVIEW", "证据情况"),
    
    /**
     * 质证情况模块
     */
    VERIFICATION_EVIDENCE("VERIFICATION_EVIDENCE", "质证情况"),
    
    /**
     * 案由提取模块
     */
    CASE_CAUSE_EXTRACTION("CASE_CAUSE_EXTRACTION", "案由提取"),
    
    /**
     * 文书生成模块
     */
    DOCUMENT_GENERATION("DOCUMENT_GENERATION", "文书生成");
    
    /**
     * 模块代码
     */
    private final String code;
    
    /**
     * 模块名称
     */
    private final String name;
    
    /**
     * 根据代码获取模块类型
     * @param code 模块代码
     * @return 模块类型枚举
     */
    public static ModuleType fromCode(String code) {
        for (ModuleType moduleType : values()) {
            if (moduleType.getCode().equals(code)) {
                return moduleType;
            }
        }
        throw new IllegalArgumentException("未知的模块类型代码: " + code);
    }
}
