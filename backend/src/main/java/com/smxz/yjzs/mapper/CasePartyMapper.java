package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.CaseParty;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CasePartyMapper extends BaseMapper<CaseParty> {

    default List<CaseParty> listByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<CaseParty> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CaseParty::getCaseImportId, caseImportId);
        return this.selectList(wrapper);
    }

    default void deleteByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<CaseParty> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CaseParty::getCaseImportId, caseImportId);
        this.delete(wrapper);
    }

}
