package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.dto.LegalFeesWithPartyDTO;
import com.smxz.yjzs.entity.LegalFees;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 诉讼费Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface LegalFeesMapper extends BaseMapper<LegalFees> {

    /**
     * 根据案件ID查询诉讼费列表
     */
    default List<LegalFees> listByCaseImportId(Long caseImportRecordId) {
        LambdaQueryWrapper<LegalFees> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LegalFees::getCaseImportRecordId, caseImportRecordId);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID查询诉讼费列表，包含当事人信息
     * 直接使用LegalFees表中的dsrxm和ssdw字段，无需联查
     */
    @Select("""
            SELECT 
                lf.id,
                lf.case_import_record_id as caseImportRecordId,
                lf.case_party_id as casePartyId,
                lf.fee_type as feeType,
                lf.amount_paid as amountPaid,
                lf.amount_to_bear as amountToBear,
                lf.dsrxm as partyName,
                lf.ssdw as partyType
            FROM legal_fees lf
            WHERE lf.case_import_record_id = #{caseImportRecordId}
            ORDER BY lf.id
            """)
    List<LegalFeesWithPartyDTO> listWithPartyInfoByCaseImportId(@Param("caseImportRecordId") Long caseImportRecordId);

    /**
     * 根据案件ID删除诉讼费记录
     */
    default void deleteByCaseImportId(Long caseImportRecordId) {
        LambdaQueryWrapper<LegalFees> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LegalFees::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }
}
