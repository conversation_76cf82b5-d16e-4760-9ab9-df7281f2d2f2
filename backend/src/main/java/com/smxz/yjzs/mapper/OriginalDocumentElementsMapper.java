package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.OriginalDocumentElements;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 原审文书生成要素Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface OriginalDocumentElementsMapper extends BaseMapper<OriginalDocumentElements> {

    /**
     * 根据案件ID查询原审文书生成要素列表（1对多关系）
     */
    default List<OriginalDocumentElements> listByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<OriginalDocumentElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OriginalDocumentElements::getCaseImportRecordId, caseImportRecordId)
               .orderByDesc(OriginalDocumentElements::getCreateTime);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID删除原审文书生成要素记录
     */
    default void deleteByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<OriginalDocumentElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OriginalDocumentElements::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }
}
