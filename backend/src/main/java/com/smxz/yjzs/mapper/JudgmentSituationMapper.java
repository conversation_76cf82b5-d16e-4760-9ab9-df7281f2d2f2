package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.JudgmentSituation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 判项情况Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface JudgmentSituationMapper extends BaseMapper<JudgmentSituation> {

    /**
     * 根据案件ID查询判项情况列表
     */
    default List<JudgmentSituation> listByCaseImportId(Long caseImportRecordId) {
        LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportRecordId)
               .orderByAsc(JudgmentSituation::getXh);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID删除判项情况记录
     */
    default void deleteByCaseImportId(Long caseImportRecordId) {
        LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }

    /**
     * 根据案件ID和文书类型查询判项情况列表
     */
    default List<JudgmentSituation> listByCaseImportIdAndDocumentCaseType(Long caseImportRecordId, String documentCaseType) {
        LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportRecordId)
               .eq(JudgmentSituation::getDocumentCaseType, documentCaseType)
               .orderByAsc(JudgmentSituation::getXh);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID和文书类型删除判项情况记录
     */
    default void deleteByCaseImportIdAndDocumentCaseType(Long caseImportRecordId, String documentCaseType) {
        LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(JudgmentSituation::getCaseImportRecordId, caseImportRecordId);
        wrapper.eq(JudgmentSituation::getDocumentCaseType, documentCaseType);
        this.delete(wrapper);
    }

    /**
     * 根据文书类型查询最大序号
     */
    default Integer getMaxXhByDocumentCaseType(String documentCaseType) {
        LambdaQueryWrapper<JudgmentSituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(JudgmentSituation::getDocumentCaseType, documentCaseType)
               .orderByDesc(JudgmentSituation::getXh)
               .last("LIMIT 1");
        JudgmentSituation maxRecord = this.selectOne(wrapper);
        return maxRecord != null && maxRecord.getXh() != null ? maxRecord.getXh() : 0;
    }
}
