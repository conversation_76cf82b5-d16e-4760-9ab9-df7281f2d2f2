package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.SystemPromptConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SystemPromptConfigMapper extends BaseMapper<SystemPromptConfig> {

    @Select("""
            SELECT id, prompt_key, prompt_message, prompt_version, create_time, update_time
            FROM system_prompt_config spc
            WHERE prompt_version = (SELECT max(prompt_version) FROM system_prompt_config spc2 WHERE spc2.prompt_key = #{key}) AND spc.prompt_key = #{key};
            """)
    SystemPromptConfig getNewestConfigByKey(@Param("key") String key);

    /**
     * 该查询会保证版本号为-1的在首位
     */
    @Select("""
            SELECT id, prompt_key, prompt_message, prompt_version, create_time, update_time
            FROM system_prompt_config spc
            WHERE spc.prompt_key = #{promptKey}
            ORDER BY (spc.prompt_version = -1) DESC, spc.prompt_version DESC;
            """)
    List<SystemPromptConfig> listOrderByVersion(String promptKey);

}
