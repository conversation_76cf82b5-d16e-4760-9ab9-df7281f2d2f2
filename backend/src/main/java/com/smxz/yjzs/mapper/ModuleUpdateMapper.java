package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.ModuleUpdateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 模块更新记录Mapper接口
 */
@Mapper
public interface ModuleUpdateMapper extends BaseMapper<ModuleUpdateRecord> {

    /**
     * 根据案件ID和模块代码查询更新记录
     * @param caseImportId 案件导入ID
     * @param moduleCode 模块代码
     * @return 更新记录
     */
    @Select("SELECT * FROM module_update_records WHERE case_import_id = #{caseImportId} AND module_code = #{moduleCode}")
    ModuleUpdateRecord selectByCaseAndModule(@Param("caseImportId") Long caseImportId, @Param("moduleCode") String moduleCode);

    /**
     * 根据案件ID查询所有模块更新状态
     * @param caseImportId 案件导入ID
     * @return 更新记录列表
     */
    @Select("SELECT * FROM module_update_records WHERE case_import_id = #{caseImportId}")
    List<ModuleUpdateRecord> selectByCaseImportId(@Param("caseImportId") Long caseImportId);

    /**
     * 检查特定模块是否需要更新（基于operate_module字段）
     * @param caseImportId 案件导入ID
     * @param sourceModule 源模块代码（要查询的记录）
     * @param operatedModule 目标操作模块代码（检查是否已在operate_module中）
     * @return 是否需要更新的记录数量
     */
    @Select("SELECT COUNT(*) FROM module_update_records " +
            "WHERE case_import_id = #{caseImportId} " +
            "AND module_code = #{sourceModule} " +
            "AND (operate_module IS NULL " +
            "     OR operate_module = '' " +
            "     OR FIND_IN_SET(#{operatedModule}, operate_module) = 0)")
    int checkModuleNeedUpdate(@Param("caseImportId") Long caseImportId, 
                             @Param("sourceModule") String sourceModule, 
                             @Param("operatedModule") String operatedModule);

    /**
     * 添加模块到operate_module字段
     * @param caseImportId 案件导入ID
     * @param moduleCode 源模块代码
     * @param operateModule 要添加的已操作模块
     * @return 影响行数
     */
    @Update("UPDATE module_update_records " +
            "SET operate_module = CASE " +
            "    WHEN operate_module IS NULL OR operate_module = '' THEN #{operateModule} " +
            "    ELSE CONCAT(operate_module, ',', #{operateModule}) " +
            "END, " +
            "update_time = NOW() " +
            "WHERE case_import_id = #{caseImportId} AND module_code = #{moduleCode}")
    int addOperateModule(@Param("caseImportId") Long caseImportId, 
                        @Param("moduleCode") String moduleCode, 
                        @Param("operateModule") String operateModule);

    /**
     * 批量重置模块更新状态
     * @param caseImportId 案件导入ID
     * @param moduleCodes 模块代码列表
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE module_update_records SET operate_module = NULL, update_time = NOW() " +
            "WHERE case_import_id = #{caseImportId} " +
            "<if test='moduleCodes != null and moduleCodes.size() > 0'>" +
            "AND module_code IN " +
            "<foreach collection='moduleCodes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    int batchResetUpdateStatus(@Param("caseImportId") Long caseImportId, @Param("moduleCodes") List<String> moduleCodes);

    /**
     * 清空operate_module字段
     * @param caseImportId 案件导入ID
     * @param moduleCode 模块代码
     * @return 影响行数
     */
    @Update("UPDATE module_update_records " +
            "SET operate_module = NULL, update_time = NOW() " +
            "WHERE case_import_id = #{caseImportId} AND module_code = #{moduleCode}")
    int clearOperateModule(@Param("caseImportId") Long caseImportId, @Param("moduleCode") String moduleCode);

    /**
     * 批量查询需要更新的模块
     * @param caseImportId 案件导入ID
     * @return 需要更新的模块代码列表
     */
    @Select("SELECT module_code FROM module_update_records WHERE case_import_id = #{caseImportId}")
    List<String> selectAllModules(@Param("caseImportId") Long caseImportId);
}
