package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.LegalProvision;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 法条Mapper接口
 */
@Mapper
public interface LegalProvisionMapper extends BaseMapper<LegalProvision> {

    /**
     * 查询所有法律记录（level=0）
     */
    @Select("SELECT * FROM legal_provisions WHERE level = 0 ORDER BY law_name")
    List<LegalProvision> selectAllLaws();

    /**
     * 根据法律ID查询所有条款项，按条、款、项排序
     * 先通过法律ID找到法律名称，然后查询该法律下的所有条款项
     */
    @Select("""
            SELECT lp.* FROM legal_provisions lp
            WHERE lp.law_name = (SELECT law_name FROM legal_provisions WHERE id = #{lawId} AND level = 0)
            ORDER BY level, article_no, paragraph_no, subparagraph_no
            """)
    List<LegalProvision> selectByLawIdOrderByHierarchy(Long lawId);

    /**
     * 根据法律名称查询法律记录
     */
    @Select("SELECT * FROM legal_provisions WHERE law_name = #{lawName} AND level = 0")
    LegalProvision selectLawByName(String lawName);


}
