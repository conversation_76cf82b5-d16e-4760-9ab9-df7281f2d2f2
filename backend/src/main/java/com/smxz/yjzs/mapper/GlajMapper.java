package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.Glaj;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 关联案件Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface GlajMapper extends BaseMapper<Glaj> {

    /**
     * 根据案件ID查询关联案件列表（1对多关系）
     */
    default List<Glaj> listByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<Glaj> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Glaj::getCaseImportRecordId, caseImportRecordId)
               .orderByDesc(Glaj::getCreateTime);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID和关联关系查询关联案件列表
     */
    default List<Glaj> listByCaseImportRecordIdAndGlgx(Long caseImportRecordId, Integer glgx) {
        LambdaQueryWrapper<Glaj> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Glaj::getCaseImportRecordId, caseImportRecordId)
               .eq(Glaj::getGlgx, glgx)
               .orderByDesc(Glaj::getCreateTime);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID删除关联案件记录
     */
    default void deleteByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<Glaj> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Glaj::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }
}
