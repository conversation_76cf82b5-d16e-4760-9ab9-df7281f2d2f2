package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.entity.FileUploadRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件上传记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper
public interface FileUploadRecordMapper extends BaseMapper<FileUploadRecord> {

    default List<FileUploadRecord> listByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId);

        return this.selectList(queryWrapper);
    }

    /**
     * 根据多个DocumentType获取文件记录
     */
    default List<FileUploadRecord> getFileRecordsByDocumentTypes(Long caseImportId, List<DocumentType> documentTypes) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                .in(FileUploadRecord::getDocumentType, documentTypes)
                .orderByDesc(FileUploadRecord::getUploadTime);

        List<FileUploadRecord> records = this.selectList(queryWrapper);
        return records;
    }

    default Object getFileRecordElementByDocumentTypes(Long caseImportId, List<DocumentType> documentTypes,String elementName) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                .in(FileUploadRecord::getDocumentType, documentTypes)
                .orderByDesc(FileUploadRecord::getUploadTime);

        List<FileUploadRecord> records = this.selectList(queryWrapper);
        for(FileUploadRecord record : records){
            if(record.getExtract_element_result() != null && record.getExtract_element_result().getElements() != null){
                if(record.getExtract_element_result().getElements().get(elementName) != null){
                    return record.getExtract_element_result().getElements().get(elementName);
                }
            }
        }
        return null;
    }

    /**
     * 获取回收站文件列表（绕过@TableLogic）
     */
    @Select("SELECT * FROM file_upload_records WHERE case_import_id = #{caseImportId} AND deleted = 1 ORDER BY deleted_time DESC")
    List<FileUploadRecord> selectRecycleBinFiles(@Param("caseImportId") Long caseImportId);

    /**
     * 逻辑删除文件（绕过@TableLogic，手动设置删除信息）
     */
    @Update("UPDATE file_upload_records SET deleted = 1, deleted_time = #{deletedTime}, deleted_by = #{deletedBy} WHERE id = #{id} AND deleted = 0")
    int logicalDeleteFile(@Param("id") Long id, @Param("deletedTime") LocalDateTime deletedTime, @Param("deletedBy") String deletedBy);

    /**
     * 恢复文件（绕过@TableLogic）
     */
    @Update("UPDATE file_upload_records SET deleted = 0, deleted_time = NULL, deleted_by = NULL WHERE id = #{id} AND deleted = 1")
    int restoreFile(@Param("id") Long id);

    /**
     * 查询回收站中的文件（deleted=1）
     */
    @Select("SELECT * FROM file_upload_records WHERE id = #{id} AND deleted = 1")
    FileUploadRecord selectDeletedFileById(@Param("id") Long id);

    /**
     * 物理删除文件（绕过@TableLogic）
     */
    @Update("DELETE FROM file_upload_records WHERE id = #{id}")
    int physicalDeleteById(@Param("id") Long id);

    /**
     * 批量物理删除回收站中的文件
     */
    @Update("DELETE FROM file_upload_records WHERE case_import_id = #{caseImportId} AND deleted = 1")
    int batchPhysicalDeleteByCaseId(@Param("caseImportId") Long caseImportId);


}