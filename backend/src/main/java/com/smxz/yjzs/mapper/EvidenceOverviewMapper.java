package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smxz.yjzs.entity.EvidenceOverview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface EvidenceOverviewMapper extends BaseMapper<EvidenceOverview> {

    default List<EvidenceOverview> listByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<EvidenceOverview> wrapper = Wrappers.<EvidenceOverview>lambdaQuery()
                .eq(EvidenceOverview::getCaseImportId, caseImportId);

        return this.selectList(wrapper);
    }

    @Select("""
            <script>
            SELECT eo.*
            FROM evidence_overview eo left join evidence_overview_party eop on eo.id = eop.evidence_overview_id
            <where>
                eo.case_import_id = #{caseImportId}
                <if test='partyNames != null and partyNames.size() > 0'>
                    and eop.party_name in
                    <foreach collection='partyNames' item='partyName' open='(' separator=',' close=')'>
                        #{partyName}
                    </foreach>
                </if>
            </where>
            </script>
            """)
    List<EvidenceOverview> listByCaseImportIdAndPartyName(Long caseImportId, List<String> partyNames);

    default void deleteByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<EvidenceOverview> wrapper = Wrappers.<EvidenceOverview>lambdaQuery()
                .eq(EvidenceOverview::getCaseImportId, caseImportId);

        this.delete(wrapper);
    }

}
