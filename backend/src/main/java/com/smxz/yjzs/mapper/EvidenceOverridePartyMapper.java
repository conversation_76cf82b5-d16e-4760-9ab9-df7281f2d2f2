package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smxz.yjzs.entity.EvidenceOverrideParty;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface EvidenceOverridePartyMapper extends BaseMapper<EvidenceOverrideParty> {

    default void deleteByCaseImportId(Long caseImportId) {
        LambdaQueryWrapper<EvidenceOverrideParty> wrapper = Wrappers.<EvidenceOverrideParty>lambdaQuery()
                .eq(EvidenceOverrideParty::getCaseImportId, caseImportId);

        this.delete(wrapper);
    }

}
