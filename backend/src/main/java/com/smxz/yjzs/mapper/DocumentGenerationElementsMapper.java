package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.DocumentGenerationElements;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文书生成要素Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface DocumentGenerationElementsMapper extends BaseMapper<DocumentGenerationElements> {

    /**
     * 根据案件ID查询文书生成要素（1对1关系）
     */
    default DocumentGenerationElements getByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<DocumentGenerationElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentGenerationElements::getCaseImportRecordId, caseImportRecordId)
               .orderByDesc(DocumentGenerationElements::getCreateTime)
               .last("LIMIT 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据案件ID删除文书生成要素记录
     */
    default void deleteByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<DocumentGenerationElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentGenerationElements::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }
}
