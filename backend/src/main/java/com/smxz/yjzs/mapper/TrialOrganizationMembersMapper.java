package com.smxz.yjzs.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.yjzs.entity.TrialOrganizationMembers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 审判组织成员Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface TrialOrganizationMembersMapper extends BaseMapper<TrialOrganizationMembers> {

    /**
     * 根据案件ID查询审判组织成员列表（1对多关系）
     * 默认按照xh升序排序
     */
    default List<TrialOrganizationMembers> listByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<TrialOrganizationMembers> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrialOrganizationMembers::getCaseImportRecordId, caseImportRecordId)
               .orderByAsc(TrialOrganizationMembers::getXh)
               .orderByDesc(TrialOrganizationMembers::getCreateTime);
        return this.selectList(wrapper);
    }

    /**
     * 根据案件ID删除审判组织成员记录
     */
    default void deleteByCaseImportRecordId(Long caseImportRecordId) {
        LambdaQueryWrapper<TrialOrganizationMembers> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrialOrganizationMembers::getCaseImportRecordId, caseImportRecordId);
        this.delete(wrapper);
    }
}
