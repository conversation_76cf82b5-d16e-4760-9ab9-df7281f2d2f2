package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.request.EvidenceOverrideListRequest;
import com.smxz.yjzs.dto.request.EvidenceOverviewRequest;
import com.smxz.yjzs.dto.response.EvidenceOverviewResponse;
import com.smxz.yjzs.service.impl.EvidenceOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/evidenceOverview")
@Tag(name = "证据情况管理", description = "证据情况相关接口")
@Validated
public class EvidenceOverviewController {

    @Autowired
    private EvidenceOverviewService evidenceOverviewService;

    @Operation(summary = "分析证据情况", description = "根据案件导入ID分析证据情况")
    @PostMapping("/analyze/{caseImportId}")
    public ApiResult<?> analyzeEvidence(@PathVariable("caseImportId") Long caseImportId) {
        evidenceOverviewService.analyzeEvidence(caseImportId);
        return ApiResult.success();
    }

    @Operation(summary = "重新分析证据情况", description = "根据案件导入ID分析证据情况")
    @PostMapping("/reanalyze/{caseImportId}")
    public ApiResult<?> reanalyzeEvidence(@PathVariable("caseImportId") Long caseImportId) {
        evidenceOverviewService.analyzeEvidence(caseImportId);
        return ApiResult.success();
    }

    /**
     * 根据案件导入ID查询证据情况列表
     */
    @Operation(summary = "查询证据情况列表", description = "根据案件导入ID查询该案件的所有证据情况")
    @PostMapping("/list")
    public ApiResult<List<EvidenceOverviewResponse>> listEvidenceOverview(@RequestBody EvidenceOverrideListRequest request) {
        List<EvidenceOverviewResponse> evidenceList = evidenceOverviewService.list(request.getCaseImportId(), request.getPartyNames());
        return ApiResult.success(evidenceList);
    }

    /**
     * 根据ID查询证据情况详情
     */
    @Operation(summary = "查询证据情况详情", description = "根据ID查询证据情况的详细信息")
    @GetMapping("/{id}")
    public ApiResult<EvidenceOverviewResponse> getEvidenceOverview(
            @Parameter(description = "证据情况ID", required = true)
            @PathVariable @NotNull(message = "证据情况ID不能为空") Long id) {
        EvidenceOverviewResponse evidence = evidenceOverviewService.getById(id);
        return ApiResult.success(evidence);
    }

    /**
     * 创建证据情况
     */
    @Operation(summary = "创建证据情况", description = "创建新的证据情况记录")
    @PostMapping("/create")
    public ApiResult<EvidenceOverviewResponse> createEvidenceOverview(
            @Parameter(description = "证据情况信息")
            @RequestBody @Valid EvidenceOverviewRequest request) {
        EvidenceOverviewResponse evidence = evidenceOverviewService.create(request);
        return ApiResult.success(evidence, "证据情况创建成功");
    }

    /**
     * 更新证据情况
     */
    @Operation(summary = "更新证据情况", description = "更新现有的证据情况记录")
    @PutMapping("/update")
    public ApiResult<EvidenceOverviewResponse> updateEvidenceOverview(
            @Parameter(description = "证据情况信息")
            @RequestBody @Valid EvidenceOverviewRequest request) {
        log.info("更新证据情况，ID: {}", request.getId());
        EvidenceOverviewResponse evidence = evidenceOverviewService.update(request);
        return ApiResult.success(evidence, "证据情况更新成功");
    }

    /**
     * 删除证据情况
     */
    @Operation(summary = "删除证据情况", description = "根据ID删除证据情况记录")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Boolean> deleteEvidenceOverview(
            @Parameter(description = "证据情况ID", required = true)
            @PathVariable @NotNull(message = "证据情况ID不能为空") Long id) {
        log.info("删除证据情况，ID: {}", id);
        boolean result = evidenceOverviewService.delete(id);
        return ApiResult.success(result, "证据情况删除成功");
    }

    /**
     * 保存或更新证据情况
     * 根据ID判断是新增还是更新
     */
    @Operation(summary = "保存证据情况", description = "保存证据情况，根据是否有ID自动判断新增或更新")
    @PostMapping("/save")
    public ApiResult<EvidenceOverviewResponse> saveEvidenceOverview(
            @Parameter(description = "证据情况信息")
            @RequestBody @Valid EvidenceOverviewRequest request) {
        boolean isCreate = request.getId() == null || request.getId() == 0;
        log.info("{}证据情况，案件ID: {}, 证据名称: {}",
                isCreate ? "创建" : "更新",
                request.getCaseImportId(),
                request.getEvidenceName());

        EvidenceOverviewResponse evidence = evidenceOverviewService.create(request);
        String message = isCreate ? "证据情况创建成功" : "证据情况更新成功";
        return ApiResult.success(evidence, message);
    }

}
