package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.request.SavePromptRequest;
import com.smxz.yjzs.entity.SystemPromptConfig;
import com.smxz.yjzs.service.impl.SystemPromptConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 诚
 * @since 2025-05-16 21:42
 **/
@Tag(name = "系统提示词配置")
@RequestMapping("api/prompt")
@RestController
@RequiredArgsConstructor
public class SystemPromptController {

    private final SystemPromptConfigService systemPromptService;

    @Operation(summary = "通过key获取提示词")
    @GetMapping("/getPromptByKey")
    public ApiResult<String> getPrompt(@RequestParam(value = "key") String key) {
        String prompt = systemPromptService.getMessageByKey(key, "");
        return ApiResult.success(prompt);
    }

    @Operation(summary = "通过key保存提示词")
    @PostMapping("/savePrompt")
    public ApiResult<Boolean> savePrompt(@RequestBody SavePromptRequest savePromptRequest) {
        systemPromptService.upsert(savePromptRequest.getKey(), savePromptRequest.getPrompt());
        return ApiResult.success(Boolean.TRUE, "保存成功");
    }

    @Operation(summary = "暂存历史提示词")
    @PostMapping("/staging")
    public ApiResult<Boolean> staging(@RequestBody SavePromptRequest savePromptRequest) {
        systemPromptService.staging(savePromptRequest.getKey(), savePromptRequest.getPrompt());
        return ApiResult.success(Boolean.TRUE, "暂存成功");
    }

    @Operation(summary = "获取历史提示词列表")
    @GetMapping("/history")
    public ApiResult<List<SystemPromptConfig>> history(String key) {
        List<SystemPromptConfig> configs = systemPromptService.listAllVersion(key);
        return ApiResult.success(configs);
    }
}
