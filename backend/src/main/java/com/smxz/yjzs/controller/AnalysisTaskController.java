package com.smxz.yjzs.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smxz.yjzs.common.ApiResult;
import org.apache.commons.lang3.StringUtils;
import com.smxz.yjzs.dto.TaskStatusDTO;
import com.smxz.yjzs.dto.TaskTypeDTO;
import com.smxz.yjzs.entity.AnalysisTaskRecord;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.service.impl.AnalysisTaskRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分析任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/analysis-task")
@Tag(name = "分析任务管理", description = "分析任务状态查询相关接口")
public class AnalysisTaskController {

    @Autowired
    private AnalysisTaskRecordService analysisTaskRecordService;

    /**
     * 获取案件的所有任务状态
     */
    @Operation(summary = "获取案件任务状态", description = "获取指定案件的所有分析任务状态")
    @GetMapping("/case/{caseImportId}")
    public ApiResult<List<TaskStatusDTO>> getCaseTaskStatus(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取案件任务状态，caseImportId: {}", caseImportId);
        
        List<TaskStatusDTO> taskStatusList = analysisTaskRecordService.getCaseTaskStatus(caseImportId);
        return ApiResult.success(taskStatusList);
    }

    /**
     * 获取特定任务类型的最新状态
     */
    @Operation(summary = "获取特定任务状态", description = "获取指定案件特定任务类型的最新状态")
    @GetMapping("/case/{caseImportId}/task/{taskType}")
    public ApiResult<TaskStatusDTO> getTaskStatus(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "任务类型") @PathVariable("taskType") String taskType) {
        log.info("获取特定任务状态，caseImportId: {}, taskType: {}", caseImportId, taskType);

        TaskStatusDTO taskStatus = analysisTaskRecordService.getTaskStatus(caseImportId, taskType);
        // 如果没有任务记录，返回 null 而不是错误，让前端知道任务还未开始
        return ApiResult.success(taskStatus);
    }

    /**
     * 获取特定任务类型的完整任务记录（包含agentTaskInfo）
     */
    @Operation(summary = "获取完整任务记录", description = "获取指定案件特定任务类型的完整任务记录，包含agentTaskInfo")
    @GetMapping("/case/{caseImportId}/task/{taskType}/full")
    public ApiResult<AnalysisTaskRecord> getFullTaskRecord(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "任务类型") @PathVariable("taskType") String taskType) {
        log.info("获取完整任务记录，caseImportId: {}, taskType: {}", caseImportId, taskType);

        AnalysisTaskRecord taskRecord = analysisTaskRecordService.getFullTaskRecord(caseImportId, taskType);
        return ApiResult.success(taskRecord);
    }

    /**
     * 检查任务是否正在执行
     */
    @Operation(summary = "检查任务执行状态", description = "检查指定任务是否正在执行中")
    @GetMapping("/case/{caseImportId}/task/{taskType}/running")
    public ApiResult<Boolean> isTaskRunning(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "任务类型") @PathVariable("taskType") String taskType) {
        log.debug("检查任务执行状态，caseImportId: {}, taskType: {}", caseImportId, taskType);
        
        boolean isRunning = analysisTaskRecordService.isTaskRunning(caseImportId, taskType);
        return ApiResult.success(isRunning);
    }

    /**
     * 获取任务预估执行时间（分钟）
     */
    @Operation(summary = "获取任务预估时间", description = "获取指定任务类型的预估执行时间")
    @GetMapping("/estimated-duration/{taskType}")
    public ApiResult<Integer> getEstimatedDuration(
            @Parameter(description = "任务类型") @PathVariable("taskType") String taskType) {
        log.info("获取任务预估时间，taskType: {}", taskType);
        
        // 根据任务类型返回预估时间（分钟）
        int estimatedMinutes = getTaskEstimatedDuration(taskType);
        return ApiResult.success(estimatedMinutes);
    }

    /**
     * 获取任务详情
     */
    @Operation(summary = "获取任务详情", description = "根据任务ID获取任务详细信息")
    @GetMapping("/{id}")
    public ApiResult<AnalysisTaskRecord> getTaskDetail(@PathVariable Long id) {
        AnalysisTaskRecord taskRecord = analysisTaskRecordService.getById(id);
        if (taskRecord == null) {
            return ApiResult.error("任务记录不存在");
        }
        return ApiResult.success(taskRecord);
    }

    /**
     * 获取任务列表（分页）
     */
    @Operation(summary = "获取任务列表", description = "分页获取任务列表，支持多条件筛选")
    @GetMapping("/list")
    public ApiResult<IPage<AnalysisTaskRecord>> getTaskList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long caseImportId,
            @RequestParam(required = false) String taskType,
            @RequestParam(required = false) Integer status) {

        Page<AnalysisTaskRecord> page = new Page<>(current, size);
        LambdaQueryWrapper<AnalysisTaskRecord> queryWrapper = new LambdaQueryWrapper<>();

        if (caseImportId != null) {
            queryWrapper.eq(AnalysisTaskRecord::getCaseImportId, caseImportId);
        }
        if (StringUtils.isNotBlank(taskType)) {
            queryWrapper.eq(AnalysisTaskRecord::getTaskType, taskType);
        }
        if (status != null) {
            queryWrapper.eq(AnalysisTaskRecord::getStatus, status);
        }

        queryWrapper.orderByDesc(AnalysisTaskRecord::getCreateTime);

        IPage<AnalysisTaskRecord> result = analysisTaskRecordService.page(page, queryWrapper);
        return ApiResult.success(result);
    }

    /**
     * 删除任务记录
     */
    @Operation(summary = "删除任务记录", description = "根据任务ID删除任务记录")
    @DeleteMapping("/{id}")
    public ApiResult<Void> deleteTask(@PathVariable Long id) {
        boolean success = analysisTaskRecordService.removeById(id);
        if (success) {
            return ApiResult.success();
        } else {
            return ApiResult.error("删除失败");
        }
    }

    /**
     * 获取所有任务类型
     */
    @Operation(summary = "获取任务类型列表", description = "获取系统中所有任务类型的代码和名称")
    @GetMapping("/task-types")
    public ApiResult<List<TaskTypeDTO>> getTaskTypes() {
        log.info("获取任务类型列表");

        List<TaskTypeDTO> taskTypes = Arrays.stream(TaskType.values())
                .map(taskType -> new TaskTypeDTO(taskType.getCode(), taskType.getName()))
                .collect(Collectors.toList());

        return ApiResult.success(taskTypes);
    }

    /**
     * 获取任务预估执行时间的内部方法
     */
    private int getTaskEstimatedDuration(String taskType) {
        switch (taskType) {
            case "LITIGATION_RELATION":
                return 2; // 诉辩关系分析：2分钟
            case "DISPUTE_FOCUS":
                return 5; // 争议焦点分析：5分钟
            case "CASE_FACT":
                return 4; // 案件事实认定提取：4分钟
            case "CASE_PARTY":
                return 1; // 当事人分析：1分钟
            case "CASE_CAUSE_EXTRACTION":
                return 1; // 案由提取：1分钟
            case "EVIDENCE_OVERVIEW":
                return 3; // 证据情况分析：3分钟
            case "VERIFICATION_EVIDENCE":
                return 2; // 质证情况分析：2分钟
            case "UNDISPUTED_FACTS":
                return 2; // 无争议事实生成：2分钟
            case "DISPUTE_FOCUS_REASONING":
                return 5; // 争议焦点说理分析：5分钟
            default:
                return 3; // 默认3分钟
        }
    }
}
