package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.TrialOrganizationMembers;
import com.smxz.yjzs.service.impl.TrialOrganizationMembersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审判组织成员控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/trial-organization-members")
@Tag(name = "审判组织成员管理", description = "审判组织成员相关接口")
public class TrialOrganizationMembersController {

    @Autowired
    private TrialOrganizationMembersService trialOrganizationMembersService;

    /**
     * 获取案件的审判组织成员列表
     */
    @Operation(summary = "获取审判组织成员列表", description = "获取指定案件的所有审判组织成员信息")
    @GetMapping("/list")
    public ApiResult<List<TrialOrganizationMembers>> list(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId) {
        log.info("获取审判组织成员列表，caseImportRecordId: {}", caseImportRecordId);
        List<TrialOrganizationMembers> trialOrganizationMembersList = trialOrganizationMembersService.listByCaseImportRecordId(caseImportRecordId);
        return ApiResult.success(trialOrganizationMembersList);
    }

    /**
     * 批量保存审判组织成员
     */
    @Operation(summary = "批量保存审判组织成员", description = "批量保存案件的审判组织成员信息")
    @PostMapping("/saveBatch")
    public ApiResult<Boolean> saveBatch(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId,
            @RequestBody List<TrialOrganizationMembers> trialOrganizationMembersList) {
        log.info("批量保存审判组织成员，caseImportRecordId: {}, 数量: {}", caseImportRecordId, trialOrganizationMembersList.size());

        // 设置案件ID
        trialOrganizationMembersList.forEach(member -> member.setCaseImportRecordId(caseImportRecordId));

        boolean result = trialOrganizationMembersService.replaceAllByCaseImportRecordId(caseImportRecordId, trialOrganizationMembersList);
        return ApiResult.success(result, "审判组织成员保存成功");
    }

    /**
     * 智能获取审判组织成员信息
     * 如果数据库中已有数据则直接返回，如果没有则从庭审笔录提取并保存
     */
    @Operation(summary = "智能获取审判组织成员", description = "智能获取审判组织成员信息，优先从数据库获取，无数据时从庭审笔录提取")
    @GetMapping("/getOrExtract")
    public ApiResult<List<TrialOrganizationMembers>> getOrExtract(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId) {
        log.info("智能获取审判组织成员信息，caseImportRecordId: {}", caseImportRecordId);
        List<TrialOrganizationMembers> trialOrganizationMembersList =
                trialOrganizationMembersService.getOrExtractTrialOrganizationMembers(caseImportRecordId);
        return ApiResult.success(trialOrganizationMembersList);
    }
}