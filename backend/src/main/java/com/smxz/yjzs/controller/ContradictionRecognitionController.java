package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.ContradictionRecognition;
import com.smxz.yjzs.service.ContradictionRecognitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 矛盾识别控制器
 */
@Tag(name = "矛盾识别", description = "矛盾识别相关接口")
@RestController
@RequestMapping("/api/contradiction")
@RequiredArgsConstructor
@Slf4j
public class ContradictionRecognitionController {

    private final ContradictionRecognitionService contradictionRecognitionService;

    @Operation(summary = "根据案件导入ID获取矛盾识别列表")
    @GetMapping("/{caseImportId}")
    public ApiResult<List<ContradictionRecognition>> getContradictionList(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        log.info("获取案件{}的矛盾识别列表", caseImportId);
        List<ContradictionRecognition> list = contradictionRecognitionService.listByCaseImportId(caseImportId);
        return ApiResult.success(list);
    }

    @Operation(summary = "根据案件导入ID和当事人类型获取矛盾识别列表")
    @GetMapping("/{caseImportId}/type/{type}")
    public ApiResult<List<ContradictionRecognition>> getContradictionListByType(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "当事人类型") @PathVariable String type) {
        log.info("获取案件{}当事人类型{}的矛盾识别列表", caseImportId, type);
        List<ContradictionRecognition> list = contradictionRecognitionService.listByCaseImportIdAndType(caseImportId, type);
        return ApiResult.success(list);
    }

    @Operation(summary = "编辑矛盾识别记录")
    @PutMapping("/{id}")
    public ApiResult<String> updateContradiction(
            @Parameter(description = "矛盾识别记录ID") @PathVariable Long id,
            @RequestBody ContradictionRecognition contradiction) {
        log.info("编辑矛盾识别记录: {}", id);
        contradiction.setId(id);
        boolean success = contradictionRecognitionService.updateById(contradiction);
        if (success) {
            return ApiResult.success("编辑成功");
        } else {
            return ApiResult.error("编辑失败");
        }
    }

    @Operation(summary = "删除矛盾识别记录")
    @DeleteMapping("/{id}")
    public ApiResult<String> deleteContradiction(
            @Parameter(description = "矛盾识别记录ID") @PathVariable Long id) {
        log.info("删除矛盾识别记录: {}", id);
        boolean success = contradictionRecognitionService.removeById(id);
        if (success) {
            return ApiResult.success("删除成功");
        } else {
            return ApiResult.error("删除失败");
        }
    }

    @Operation(summary = "重新分析矛盾识别")
    @PostMapping("/{caseImportId}/reanalyze")
    public ApiResult<String> reanalyze(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        log.info("重新分析案件{}的矛盾识别", caseImportId);
        try {
            contradictionRecognitionService.reanalyze(caseImportId);
            return ApiResult.success("重新分析任务已启动");
        } catch (Exception e) {
            log.error("重新分析失败", e);
            return ApiResult.error("重新分析失败: " + e.getMessage());
        }
    }
}