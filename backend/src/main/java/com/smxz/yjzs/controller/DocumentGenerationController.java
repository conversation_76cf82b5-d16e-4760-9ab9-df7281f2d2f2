package com.smxz.yjzs.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.PageResult;
import com.smxz.yjzs.dto.request.BatchDeleteDocumentRequest;
import com.smxz.yjzs.dto.vo.DocumentGenerationPreCheckVO;
import com.smxz.yjzs.entity.DocumentGenerationInfo;
import com.smxz.yjzs.entity.EvidenceFactsDetails;
import com.smxz.yjzs.service.impl.DocumentGenerationInfoService;
import com.smxz.yjzs.service.impl.EvidenceFactsDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import java.util.List;

/**
 * 文书生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/document-generation")
@Tag(name = "文书生成管理", description = "文书生成信息相关接口")
@Validated
public class DocumentGenerationController {

    @Autowired
    private DocumentGenerationInfoService documentGenerationInfoService;

    @Autowired
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    /**
     * 创建文书生成信息
     */
    @Operation(summary = "创建文书生成信息", description = "创建新的文书生成信息")
    @PostMapping("/create")
    public ApiResult<String> createDocumentGeneration(
            @Valid @RequestBody DocumentGenerationInfo request) {
        log.info("创建文书生成信息，案件ID: {}", request.getCaseImportId());

        if (request.getCaseImportId() != null && request.getCaseImportId() > 0L) {
            Long caseImportId = request.getCaseImportId();

            DocumentGenerationInfo documentGenerationInfo = DocumentGenerationInfo.builder()
                    .caseImportId(caseImportId)
                    .caseTrialType(request.getCaseTrialType())
                    .createBy(request.getCreateBy())
                    .build();

            documentGenerationInfoService.generateDocument(documentGenerationInfo, caseImportId);
            return ApiResult.success("文书信息生成任务已启动");
        } else {
            return ApiResult.error("生成文书失败没有案件ID!");
        }
    }

    /**
     * 根据ID获取文书生成信息
     */
    @Operation(summary = "获取文书生成信息", description = "根据ID获取文书生成信息详情")
    @GetMapping("/{id}")
    public ApiResult<DocumentGenerationInfo> getDocumentGeneration(
            @Parameter(description = "文书ID") @PathVariable("id") Long id) {
        log.info("获取文书生成信息，ID: {}", id);
        
        DocumentGenerationInfo documentGenerationInfo = documentGenerationInfoService.getById(id);
        if (documentGenerationInfo != null) {
            return ApiResult.success(documentGenerationInfo);
        } else {
            return ApiResult.error("文书生成信息不存在");
        }
    }

    /**
     * 根据案件ID获取文书生成信息列表
     */
    @Operation(summary = "获取案件文书列表", description = "根据案件ID获取所有文书生成信息")
    @GetMapping("/case/{caseImportId}")
    public ApiResult<List<DocumentGenerationInfo>> getDocumentGenerationsByCase(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取案件文书列表，案件ID: {}", caseImportId);
        
        List<DocumentGenerationInfo> documentGenerations = documentGenerationInfoService.getByCaseImportId(caseImportId);
        return ApiResult.success(documentGenerations);
    }

    /**
     * 根据案件ID获取文书生成信息列表（兼容旧接口）
     */
    @Operation(summary = "获取案件文书列表", description = "根据案件ID获取所有文书生成信息（兼容旧接口）")
    @GetMapping("/list/{caseImportId}")
    public ApiResult<List<DocumentGenerationInfo>> getDocumentGenerationsList(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取案件文书列表（兼容接口），案件ID: {}", caseImportId);
        
        List<DocumentGenerationInfo> documentGenerations = documentGenerationInfoService.getByCaseImportId(caseImportId);
        return ApiResult.success(documentGenerations);
    }

    /**
     * 根据案件ID和文书类型获取文书生成信息
     */
    @Operation(summary = "获取特定类型文书", description = "根据案件ID和文书类型获取文书生成信息")
    @GetMapping("/case/{caseImportId}/type/{documentType}")
    public ApiResult<List<DocumentGenerationInfo>> getDocumentGenerationsByCaseAndType(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "文书类型") @PathVariable("documentType") String documentType) {
        log.info("获取特定类型文书，案件ID: {}, 文书类型: {}", caseImportId, documentType);
        
        List<DocumentGenerationInfo> documentGenerations = 
                documentGenerationInfoService.getByCaseImportIdAndDocumentType(caseImportId, documentType);
        return ApiResult.success(documentGenerations);
    }

    /**
     * 分页查询文书生成信息
     */
    @Operation(summary = "分页查询文书生成信息", description = "分页查询文书生成信息列表")
    @GetMapping("/page")
    public ApiResult<PageResult<DocumentGenerationInfo>> getDocumentGenerationPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "案件导入ID") @RequestParam(required = false) Long caseImportId,
            @Parameter(description = "文书类型") @RequestParam(required = false) String documentType,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime) {
        log.info("分页查询文书生成信息，页码: {}, 每页大小: {}, 案件ID: {}, 文书类型: {}, 开始时间: {}, 结束时间: {}", 
                current, size, caseImportId, documentType, startTime, endTime);
        
        Page<DocumentGenerationInfo> page = new Page<>(current, size);
        Page<DocumentGenerationInfo> result = documentGenerationInfoService.getPage(page, caseImportId, documentType, startTime, endTime);
        
        PageResult<DocumentGenerationInfo> pageResult = new PageResult<>();
        pageResult.setRecords(result.getRecords());
        pageResult.setTotal(result.getTotal());
        pageResult.setCurrent(result.getCurrent());
        pageResult.setSize(result.getSize());
        
        return ApiResult.success(pageResult);
    }

    /**
     * 更新文书生成信息
     */
    @Operation(summary = "更新文书生成信息", description = "更新文书生成信息")
    @PutMapping("/update")
    public ApiResult<Boolean> updateDocumentGeneration(
            @Valid @RequestBody DocumentGenerationInfo documentGenerationInfo) {
        log.info("更新文书生成信息，ID: {}", documentGenerationInfo.getId());
        
        boolean success = documentGenerationInfoService.updateDocumentGenerationInfo(documentGenerationInfo);
        if (success) {
            return ApiResult.success(true);
        } else {
            return ApiResult.error("更新文书生成信息失败");
        }
    }

    /**
     * 根据ID删除文书生成信息
     */
    @Operation(summary = "删除文书生成信息", description = "根据ID删除文书生成信息")
    @DeleteMapping("delete/{id}")
    public ApiResult<Boolean> deleteDocumentGeneration(
            @Parameter(description = "文书ID") @PathVariable("id") Long id) {
        log.info("删除文书生成信息，ID: {}", id);
        
        boolean success = documentGenerationInfoService.deleteById(id);
        if (success) {
            return ApiResult.success(true);
        } else {
            return ApiResult.error("删除文书生成信息失败");
        }
    }

    /**
     * 根据案件ID删除所有相关文书生成信息
     */
    @Operation(summary = "删除案件相关文书", description = "根据案件ID删除所有相关文书生成信息")
    @DeleteMapping("/case/{caseImportId}")
    public ApiResult<Long> deleteDocumentGenerationsByCase(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("删除案件相关文书，案件ID: {}", caseImportId);
        
        long deletedCount = documentGenerationInfoService.deleteByCaseImportId(caseImportId);
        return ApiResult.success(deletedCount);
    }

    /**
     * 批量删除文书生成信息
     */
    @Operation(summary = "批量删除文书", description = "根据文书ID列表批量删除文书生成信息")
    @DeleteMapping("/batch-delete")
    public ApiResult<Long> batchDeleteDocuments(
            @Valid @RequestBody BatchDeleteDocumentRequest request) {
        log.info("批量删除文书，文书ID列表: {}", request.getDocumentIds());
        
        long deletedCount = documentGenerationInfoService.batchDeleteByIds(request.getDocumentIds());
        if (deletedCount > 0) {
            return ApiResult.success(deletedCount, "批量删除成功，共删除 " + deletedCount + " 个文书");
        } else {
            return ApiResult.error("批量删除失败，未找到要删除的文书");
        }
    }

    /**
     * 检查案件是否已有指定类型的文书
     */
    @Operation(summary = "检查文书是否存在", description = "检查案件是否已有指定类型的文书")
    @GetMapping("/exists/case/{caseImportId}/type/{documentType}")
    public ApiResult<Boolean> existsDocumentGeneration(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "文书类型") @PathVariable("documentType") String documentType) {
        log.info("检查文书是否存在，案件ID: {}, 文书类型: {}", caseImportId, documentType);

        boolean exists = documentGenerationInfoService.existsByCaseImportIdAndDocumentType(caseImportId, documentType);
        return ApiResult.success(exists);
    }

    /**
     * 文书生成前置检查
     */
    @Operation(summary = "文书生成前置检查", description = "检查文书生成的前置条件")
    @GetMapping("/pre-check")
    public ApiResult<DocumentGenerationPreCheckVO> preCheckDocumentGeneration(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportId,
            @Parameter(description = "文书类型") @RequestParam String documentType) {
        log.info("文书生成前置检查，案件ID: {}, 文书类型: {}", caseImportId, documentType);

        DocumentGenerationPreCheckVO result = documentGenerationInfoService.preCheckDocumentGeneration(caseImportId, documentType);

        // 如果需要异步调用，在Controller层调用相应的异步方法
        if (result.getNeedsAsyncCall() != null) {
            switch (result.getNeedsAsyncCall()) {
                case "ysxxExtractAndSave":
                    log.info("调用原审信息提取异步方法，案件ID: {}", caseImportId);
                    documentGenerationInfoService.ysxxExtractAndSave(caseImportId);
                    break;
                case "generateOriginalJudgmentResultForQd":
                    log.info("调用原审裁判结果生成异步方法，案件ID: {}", caseImportId);
                    documentGenerationInfoService.generateOriginalJudgmentResultForQd(caseImportId);
                    break;
                default:
                    log.warn("未知的异步调用方法: {}", result.getNeedsAsyncCall());
                    break;
            }
            // 清除needsAsyncCall字段，避免返回给前端
            result.setNeedsAsyncCall(null);
        }

        return ApiResult.success(result);
    }

    /**
     * 基于模板生成文书
     */
    @Operation(summary = "基于模板生成文书", description = "基于Word模板生成裁判书等文书")
    @PostMapping("/generateFromTemplate")
    public ApiResult<DocumentGenerationInfo> generateFromTemplate(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportId,
            @Parameter(description = "案件类型代码") @RequestParam String ajlxdm,
            @Parameter(description = "文书类型") @RequestParam String documentType) {
        log.info("基于模板生成文书，案件ID: {}, 案件类型: {}, 文书类型: {}", caseImportId, ajlxdm, documentType);

        try {
            DocumentGenerationInfo documentInfo = documentGenerationInfoService.generateDocumentFromTemplate(caseImportId, ajlxdm, documentType);
            return ApiResult.success(documentInfo, "文书生成成功");
        } catch (Exception e) {
            log.error("基于模板生成文书失败", e);
            return ApiResult.error("文书生成失败: " + e.getMessage());
        }
    }

    /**
     * 1. 流式生成当事人基本情况 (SSE版本)
     */
    @Operation(summary = "流式生成当事人基本情况 (SSE版本)")
    @GetMapping(value = "/stream-party-basic/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamPartyBasicInfo(@PathVariable Long documentId) {
        log.info("开始流式生成当事人基本情况，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamPartyBasicInfoSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 2. 流式生成审理经过 (SSE版本)
     */
    @Operation(summary = "流式生成审理经过 (SSE版本)")
    @GetMapping(value = "/stream-trial-process/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamTrialProcess(@PathVariable Long documentId) {
        log.info("开始流式生成审理经过，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamTrialProcessSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 3. 流式生成诉辩信息 (SSE版本)
     */
    @Operation(summary = "流式生成诉辩信息 (SSE版本)")
    @GetMapping(value = "/stream-litigation-defense/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamLitigationDefenseInfo(@PathVariable Long documentId) {
        log.info("开始流式生成诉辩信息，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamLitigationDefenseInfoSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 4. 流式生成原审诉讼请求 (SSE版本)
     */
    @Operation(summary = "流式生成原审诉讼请求 (SSE版本)")
    @GetMapping(value = "/stream-original-litigation/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamOriginalLitigationRequest(@PathVariable Long documentId) {
        log.info("开始流式生成原审诉讼请求，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamOriginalLitigationRequestSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 5. 流式生成原审认定事实 (SSE版本)
     */
    @Operation(summary = "流式生成原审认定事实 (SSE版本)")
    @GetMapping(value = "/stream-original-recognized-facts/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamOriginalRecognizedFacts(@PathVariable Long documentId) {
        log.info("开始流式生成原审认定事实，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamOriginalRecognizedFactsSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 6. 流式生成原审本院认为+原审裁判结果 (SSE版本)
     */
    @Operation(summary = "流式生成原审本院认为+原审裁判结果 (SSE版本)")
    @GetMapping(value = "/stream-original-court-opinion-judgment/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamOriginalCourtOpinionAndJudgment(@PathVariable Long documentId) {
        log.info("开始流式生成原审本院认为+原审裁判结果，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamOriginalCourtOpinionAndJudgmentSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 7. 流式生成原审裁判结果 (SSE版本) - 独立接口
     */
    @Operation(summary = "流式生成原审裁判结果 (SSE版本) - 独立接口")
    @GetMapping(value = "/stream-original-judgment/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamOriginalJudgmentResult(@PathVariable Long documentId) {
        log.info("开始流式生成原审裁判结果，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamOriginalJudgmentResultSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 8. 流式生成认定事实 (SSE版本)
     */
    @Operation(summary = "流式生成认定事实 (SSE版本)")
    @GetMapping(value = "/stream-recognized-facts/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamRecognizedFacts(@PathVariable Long documentId) {
        log.info("开始流式生成认定事实，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();

        EvidenceFactsDetails evidenceDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);

        if (evidenceDetails != null && StringUtils.isNotBlank(evidenceDetails.getDetermineFacts())) {
            // 如果有现有数据
            log.info("认定事实已存在，直接返回现有数据，案件ID: {}", caseImportId);

            // 直接使用查询到的数据，避免再次查询
            Flux<String> existingData = documentGenerationInfoService.addSeparatorToFrontFlux(documentGenerationInfoService.addSeparatorToFlux(documentGenerationInfoService.createFluxFromExisting(evidenceDetails.getDetermineFacts())));
            Flux<String> streamWithBookmark = Flux.just("<bookmark>证据和事实认定</bookmark>")
                    .concatWith(existingData);
            return documentGenerationInfoService.processStreamWithBookmarkAndIndent(streamWithBookmark, documentId);
        } else {
            // 如果没有现有数据，调用AI生成
            log.info("认定事实不存在，调用AI生成，案件ID: {}", caseImportId);
            return documentGenerationInfoService.streamRecognizedFactsSSE(caseImportId, documentId, documentInfo);
        }
    }

    /**
     * 10. 流式生成总结 (SSE版本)
     */
    @Operation(summary = "流式生成总结 (SSE版本)")
    @GetMapping(value = "/stream-summary/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamSummary(@PathVariable Long documentId) {
        log.info("开始流式生成总结，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamSummarySSE(caseImportId, documentId, documentInfo);
    }


    /**
     * 12. 保存书签信息（追加模式）
     */
    @Operation(summary = "保存书签信息")
    @PostMapping("/save-bookmark/{documentId}")
    public ApiResult<String> saveBookmark(@PathVariable Long documentId, @RequestBody String bookmarkContent) {
        log.info("保存书签信息，文书ID: {}, 书签内容: {}", documentId, bookmarkContent);

        try {
            // 根据documentId查询DocumentGenerationInfo
            DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
            if (documentInfo == null) {
                return ApiResult.error("文书生成信息不存在，文书ID: " + documentId);
            }

            // 获取现有书签内容
            String existingBookmark = documentInfo.getBookmark();
            String newBookmarkContent;

            if (StringUtils.isNotBlank(existingBookmark)) {
                // 如果已有书签内容，使用分号分隔追加
                newBookmarkContent = existingBookmark + "；" + bookmarkContent;
            } else {
                // 如果没有现有书签内容，直接使用新内容
                newBookmarkContent = bookmarkContent;
            }

            log.info("书签内容更新: 原有='{}', 新增='{}', 合并后='{}'", 
                    existingBookmark, bookmarkContent, newBookmarkContent);

            // 更新书签信息
            documentInfo.setBookmark(newBookmarkContent);
            boolean updated = documentGenerationInfoService.updateById(documentInfo);

            if (updated) {
                log.info("书签信息保存成功，文书ID: {}", documentId);
                return ApiResult.success("书签信息保存成功");
            } else {
                log.error("书签信息保存失败，文书ID: {}", documentId);
                return ApiResult.error("书签信息保存失败");
            }
        } catch (Exception e) {
            log.error("保存书签信息异常，文书ID: {}", documentId, e);
            return ApiResult.error("保存书签信息异常: " + e.getMessage());
        }
    }

    /**
     * 统一流式生成接口 - 并发执行多个生成任务，按顺序返回结果
     */
    @Operation(summary = "统一流式生成接口", description = "并发执行多个文书生成任务，按顺序返回结果，认定事实和总结有依赖关系")
    @GetMapping(value = "/stream-unified-generation/{documentId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamUnifiedGeneration(@PathVariable Long documentId) {
        log.info("开始统一流式生成，文书ID: {}", documentId);

        // 根据documentId查询DocumentGenerationInfo
        DocumentGenerationInfo documentInfo = documentGenerationInfoService.getById(documentId);
        if (documentInfo == null) {
            return Flux.error(new RuntimeException("文书生成信息不存在，文书ID: " + documentId));
        }

        Long caseImportId = documentInfo.getCaseImportId();
        return documentGenerationInfoService.streamUnifiedGenerationSSE(caseImportId, documentId, documentInfo);
    }

    /**
     * 测试SSE流式输出 - 一个字一个字返回
     */
    @Operation(summary = "测试SSE流式输出 - 一个字一个字返回")
    @GetMapping(value = "/test-stream-char-by-char", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> testStreamCharByChar() {
        log.info("开始测试SSE流式输出 - 一个字一个字返回");
        return documentGenerationInfoService.testStreamCharByChar();
    }
}