package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.JudgmentSituation;
import com.smxz.yjzs.service.impl.JudgmentSituationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 判项情况控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/judgmentSituation")
@Tag(name = "判项情况管理", description = "判项情况相关接口")
public class JudgmentSituationController {

    @Autowired
    private JudgmentSituationService judgmentSituationService;

    /**
     * 获取案件的判项情况列表
     */
    @Operation(summary = "获取判项情况列表", description = "获取指定案件的所有判项情况信息")
    @GetMapping("/list")
    public ApiResult<List<JudgmentSituation>> list(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId) {
        log.info("获取判项情况列表，caseImportRecordId: {}", caseImportRecordId);
        List<JudgmentSituation> judgmentSituationList = judgmentSituationService.listByCaseImportId(caseImportRecordId);
        return ApiResult.success(judgmentSituationList);
    }

    /**
     * 根据案件ID和文书类型获取判项情况列表
     */
    @Operation(summary = "根据案件ID和文书类型获取判项情况列表", description = "获取指定案件和文书类型的判项情况信息")
    @GetMapping("/listByCaseImportIdAndDocumentCaseType")
    public ApiResult<List<JudgmentSituation>> listByCaseImportIdAndDocumentCaseType(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId,
            @Parameter(description = "文书类型") @RequestParam String documentCaseType) {
        log.info("根据案件ID和文书类型获取判项情况列表，caseImportRecordId: {}, documentCaseType: {}", caseImportRecordId, documentCaseType);
        List<JudgmentSituation> judgmentSituationList = judgmentSituationService.listByCaseImportIdAndDocumentCaseType(caseImportRecordId, documentCaseType);
        return ApiResult.success(judgmentSituationList);
    }

    /**
     * 保存判项情况信息
     * 如果是新记录，会自动设置序号为相同documentCaseType下的最大序号+1
     */
    @Operation(summary = "保存判项情况信息", description = "保存单个判项情况记录")
    @PostMapping("/save")
    public ApiResult<Boolean> save(@RequestBody JudgmentSituation judgmentSituation) {
        log.info("保存判项情况信息: {}", judgmentSituation);
        // 使用重写的save方法，会自动处理序号
        boolean result = judgmentSituationService.save(judgmentSituation);
        return ApiResult.success(result);
    }

    /**
     * 批量保存判项情况信息
     */
    @Operation(summary = "批量保存判项情况信息", description = "批量保存判项情况记录")
    @PostMapping("/saveBatch")
    public ApiResult<Boolean> saveBatch(@RequestBody List<JudgmentSituation> judgmentSituationList) {
        log.info("批量保存判项情况信息，数量: {}", judgmentSituationList.size());
        boolean result = judgmentSituationService.saveBatch(judgmentSituationList);
        return ApiResult.success(result);
    }

    /**
     * 批量保存或更新判项情况信息
     */
    @Operation(summary = "批量保存或更新判项情况信息", description = "根据ID是否存在自动判断是新增还是更新判项情况记录")
    @PostMapping("/saveOrUpdateBatch")
    public ApiResult<Boolean> saveOrUpdateBatch(@RequestBody List<JudgmentSituation> judgmentSituationList) {
        log.info("批量保存或更新判项情况信息，数量: {}", judgmentSituationList.size());
        boolean result = judgmentSituationService.saveOrUpdateBatch(judgmentSituationList);
        return ApiResult.success(result);
    }

    /**
     * 更新判项情况信息
     */
    @Operation(summary = "更新判项情况信息", description = "更新判项情况记录")
    @PutMapping("/update")
    public ApiResult<Boolean> update(@RequestBody JudgmentSituation judgmentSituation) {
        log.info("更新判项情况信息: {}", judgmentSituation);
        boolean result = judgmentSituationService.updateById(judgmentSituation);
        return ApiResult.success(result);
    }

    /**
     * 删除判项情况信息
     */
    @Operation(summary = "删除判项情况信息", description = "根据ID删除判项情况记录")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Boolean> delete(@Parameter(description = "判项情况ID") @PathVariable Long id) {
        log.info("删除判项情况信息，id: {}", id);
        boolean result = judgmentSituationService.removeById(id);
        return ApiResult.success(result);
    }

    /**
     * 根据案件ID删除判项情况信息
     */
    @Operation(summary = "根据案件ID删除判项情况信息", description = "删除指定案件的所有判项情况记录")
    @DeleteMapping("/deleteByCaseId/{caseImportRecordId}")
    public ApiResult<Boolean> deleteByCaseId(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportRecordId) {
        log.info("根据案件ID删除判项情况信息，caseImportRecordId: {}", caseImportRecordId);
        judgmentSituationService.deleteByCaseImportId(caseImportRecordId);
        return ApiResult.success(true);
    }
}
