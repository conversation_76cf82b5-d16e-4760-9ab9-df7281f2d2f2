package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.common.utils.VersionHolder;
import com.smxz.yjzs.dto.ChromeVersionInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/version")
@Tag(name = "版本相关工具", description = "查看版本信息")
public class VersionController {

    @Value("${chrome.recommended-version}")
    private String recommendedVersion;
    
    @Value("${chrome.download.windows}")
    private String windowsDownloadUrl;
    
    @Value("${chrome.download.linux}")
    private String linuxDownloadUrl;
    
    @Value("${chrome.download.default}")
    private String defaultDownloadUrl;

    @GetMapping("/getBuildInfo")
    public ApiResult<String> getVersion() {
        String data = "构建时间: " + VersionHolder.getBuildTime() + "\n" +
                "分支: " + VersionHolder.getBranch() + "\n" +
                "最后提交Id: " + VersionHolder.getCommitId() + "\n" +
                "最后提交时间: " + VersionHolder.getCommitTime() + "\n" +
                "最后提交信息: " + VersionHolder.getCommitMessageFull() + "\n" +
                "最后提交用户: " + VersionHolder.getCommitUserName() + "\n" +
                "构建用户: " + VersionHolder.getBuildUserName() + "\n" +
                "总提交数: " + VersionHolder.getTotalCommitCount() + "\n";

        return ApiResult.success(data);
    }

    @GetMapping("/getChromeRecommendation")
    @Operation(summary = "获取Chrome推荐版本", description = "根据操作系统返回推荐的Chrome版本和下载链接")
    public ApiResult<ChromeVersionInfo> getChromeRecommendation(
            @Parameter(description = "操作系统 (Windows, Linux, MacOS)")
            @RequestParam(required = false, defaultValue = "Windows") String os) {
        
        ChromeVersionInfo chromeVersionInfo = new ChromeVersionInfo();
        chromeVersionInfo.setOs(os);
        
        // 设置推荐版本号（从配置文件中获取）
        chromeVersionInfo.setVersion(recommendedVersion);
        
        // 根据操作系统设置下载链接
        String downloadUrl = getChromeDownloadUrl(os);
        chromeVersionInfo.setDownloadUrl(downloadUrl);
        
        return ApiResult.success(chromeVersionInfo);
    }
    
    private String getChromeDownloadUrl(String os) {
        return switch (os.toLowerCase()) {
            case "windows" -> windowsDownloadUrl;
            case "linux" -> linuxDownloadUrl;
            default -> defaultDownloadUrl;
        };
    }
}
