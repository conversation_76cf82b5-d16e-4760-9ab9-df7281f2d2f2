package com.smxz.yjzs.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.smxz.document.convert.DocumentConvertService;
import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.constant.CaseTypeConstants;
import com.smxz.yjzs.constant.RightConstants;
import com.smxz.yjzs.dto.CaseImportSearchDTO;
import com.smxz.yjzs.dto.PageResult;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.service.impl.CaseImportRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/case")
public class CaseInfoController {

    @Autowired
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private ChatModel openAiChatModel;

    @Autowired
    private DocumentConvertService documentConvertService;

    @Parameter(name = "files", description = "案件ZIP压缩包（仅支持.zip格式）", in = ParameterIn.DEFAULT,
            schema = @Schema(name = "files", format = "binary"))
    @Parameter(name = "caseType", description = "案件类型（0-简单案件，1-普通案件）", in = ParameterIn.QUERY)
    @PostMapping("/upload/batch")
    public ApiResult<String> uploadArchives(@RequestParam("files") List<MultipartFile> files,
                                            @RequestParam(value = "caseType", required = false) String caseType) {
        log.info("开始批量上传案件ZIP压缩包，数量: {}, 案件类型: {}", files.size(), caseType);

        // 转换案件类型参数
        int caseTypeInt = 0; // 默认为简单案件
        if (caseType != null && !caseType.trim().isEmpty()) {
            try {
                caseTypeInt = Integer.parseInt(caseType);
                if (caseTypeInt != 0 && caseTypeInt != 1) {
                    return ApiResult.error("案件类型参数错误，只能为 0（简单案件）或 1（普通案件）");
                }
            } catch (NumberFormatException e) {
                return ApiResult.error("案件类型参数格式错误，必须为数字");
            }
        }
        caseImportRecordService.uploadAndProcessArchives(files,caseTypeInt);
        return ApiResult.success("案件ZIP压缩包上传成功");
    }

    @PostMapping("/import/list")
    public ApiResult<PageResult<CaseImportRecord>> listCaseImportRecord(
            @RequestBody CaseImportSearchDTO searchDTO) {
        return listCaseRecords(searchDTO);
    }

    @PostMapping("/file/upload/list")
    public ApiResult<PageResult<CaseImportRecord>> listFileUploadRecord(
            @RequestBody CaseImportSearchDTO searchDTO) {
        return listCaseRecords(searchDTO);
    }

    /**
     * 通用的案件记录查询方法
     */
    private ApiResult<PageResult<CaseImportRecord>> listCaseRecords(CaseImportSearchDTO searchDTO) {
        PageResult<CaseImportRecord> records = caseImportRecordService.listCaseImportRecords(searchDTO);
        return ApiResult.success(records);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取案件详情", description = "根据案件ID获取案件详情信息")
    public ApiResult<CaseImportRecord> getCaseImportRecord(@PathVariable("id") Long id) {
        CaseImportRecord record = caseImportRecordService.getById(id);
        if (record == null) {
            return ApiResult.error("案件不存在");
        }
        
        // 权限检查：如果用户没有查看所有数据权限，只能查看自己导入的案件
        if (!StpUtil.hasPermission(RightConstants.RIGHT_ALL_DATA)) {
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!currentUserId.equals(record.getImporterId())) {
                return ApiResult.error("无权限访问该案件");
            }
        }
        
        return ApiResult.success(record);
    }

    /**
     * 获取案件状态（轻量级查询）
     */
    @Operation(summary = "获取案件状态", description = "获取案件的文件处理状态和分析状态")
    @GetMapping("/status/{id}")
    public ApiResult<Map<String, Object>> getCaseStatus(@PathVariable("id") Long id) {
        CaseImportRecord record = caseImportRecordService.getById(id);
        if (record == null) {
            return ApiResult.error("案件不存在");
        }

        Map<String, Object> status = new HashMap<>();
        status.put("fileStatus", record.getFileStatus());
        status.put("importStatus", record.getImportStatus());

        return ApiResult.success(status);
    }

    /**
     * 获取案件类型信息
     */
    @Operation(summary = "获取案件类型", description = "根据案件ID获取案件类型（一审/二审）")
    @GetMapping("/type/{caseImportId}")
    public ApiResult<Map<String, Object>> getCaseType(@PathVariable("caseImportId") Long caseImportId) {
        log.info("获取案件类型，caseImportId: {}", caseImportId);

        CaseImportRecord caseRecord = caseImportRecordService.getById(caseImportId);
        if (caseRecord == null) {
            return ApiResult.error("案件不存在");
        }

        String ajlxdm = caseRecord.getAjlxdm();
        Map<String, Object> result = new HashMap<>();
        result.put("ajlxdm", ajlxdm);

        // 根据ajlxdm判断案件类型
        if (CaseTypeConstants.AjlxdmCode.MSES.equals(ajlxdm) ||
            (caseRecord.getCaseName() != null && caseRecord.getCaseName().contains(CaseTypeConstants.CaseNameKeywords.MSES_KEYWORD))) {
            result.put("caseType", "二审");
            result.put("caseTypeCode", "MSES");
            result.put("isSecondInstance", true);
        } else {
            result.put("caseType", "一审");
            result.put("caseTypeCode", "MSYS");
            result.put("isSecondInstance", false);
        }

        return ApiResult.success(result);
    }

    /**
     * 重新处理案件文件（文档转换和文本提取）
     */
    @PostMapping("/preprocess/{caseImportId}")
    public ApiResult<String> preprocessFiles(@PathVariable("caseImportId") Long caseImportId) {
        // 这个方法现在需要在 CaseImportRecordService 中实现
        // 或者可以移除这个接口，因为文件处理已经集成到上传流程中
        return ApiResult.error("此接口已废弃，请使用批量上传接口");
    }

    /**
     * 重新处理案件文件（清除已有文本后重新处理）
     */
    @PostMapping("/reprocess/{caseImportId}")
    public ApiResult<String> reprocessFiles(@PathVariable("caseImportId") Long caseImportId) {
        caseImportRecordService.reprocessFiles(caseImportId);
        return ApiResult.success("文件重新处理成功");
    }

    /**
     * 重新生成全部分析任务
     */
    @PostMapping("/regenerate-all/{caseImportId}")
    public ApiResult<String> regenerateAllAnalysis(@PathVariable("caseImportId") Long caseImportId) {
        caseImportRecordService.startAnalysisTasks(caseImportId);
        return ApiResult.success("重新生成全部分析任务已启动");
    }

    /**
     * 删除案件记录（逻辑删除）
     */
    @DeleteMapping("/{caseImportId}")
    public ApiResult<Boolean> deleteCaseRecord(@PathVariable("caseImportId") Long caseImportId) {
        boolean result = caseImportRecordService.deleteCaseRecord(caseImportId);
        return ApiResult.success(result);
    }

    @Operation(summary = "测试docx转pdf")
    @PostMapping(value = "/doc2pdf")
    public ResponseEntity<byte[]> doc2pdf(MultipartFile file) {
        try {
            byte[] pdfBytes = documentConvertService.wordToPdfBytes(file.getInputStream());

// URL编码文件名
            String originalFilename = file.getOriginalFilename();
            String filename = FilenameUtils.getBaseName(originalFilename) + ".pdf";
            String encodedFileName = URLEncoder.encode(filename, StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"")
                    .body(pdfBytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

