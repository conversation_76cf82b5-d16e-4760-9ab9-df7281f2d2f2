package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.TaskStatusDTO;
import com.smxz.yjzs.enums.TaskType;
import com.smxz.yjzs.service.TaskStatusManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务状态查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/task-status")
@Tag(name = "任务状态管理", description = "任务状态查询相关接口")
public class TaskStatusController {

    @Autowired
    private TaskStatusManager taskStatusManager;

    /**
     * 获取指定案件的所有任务状态
     */
    @Operation(summary = "获取案件所有任务状态", description = "获取指定案件的所有分析任务状态")
    @GetMapping("/{caseId}")
    public ApiResult<List<TaskStatusDTO>> getTaskStatuses(
            @Parameter(description = "案件ID") @PathVariable("caseId") Long caseId) {
        log.info("获取案件任务状态，caseId: {}", caseId);
        List<TaskStatusDTO> statuses = taskStatusManager.getTaskStatuses(caseId);
        return ApiResult.success(statuses);
    }

    /**
     * 获取指定案件和任务类型的最新任务状态
     */
    @Operation(summary = "获取特定任务状态", description = "获取指定案件和任务类型的最新任务状态")
    @GetMapping("/{caseId}/{taskType}")
    public ApiResult<TaskStatusDTO> getTaskStatus(
            @Parameter(description = "案件ID") @PathVariable("caseId") Long caseId,
            @Parameter(description = "任务类型") @PathVariable("taskType") String taskTypeCode) {
        log.info("获取特定任务状态，caseId: {}, taskType: {}", caseId, taskTypeCode);
        
        try {
            TaskType taskType = TaskType.fromCode(taskTypeCode);
            TaskStatusDTO status = taskStatusManager.getTaskStatus(caseId, taskType);
            return ApiResult.success(status);
        } catch (IllegalArgumentException e) {
            log.error("无效的任务类型代码: {}", taskTypeCode);
            return ApiResult.error("无效的任务类型代码: " + taskTypeCode);
        }
    }
}
