package com.smxz.yjzs.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.ocr.model.OcrPreview;
import com.smxz.ocr.model.OcrResult;
import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.config.MinioConfig;
import com.smxz.yjzs.dto.response.FileUploadRecordDTO;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.service.impl.CaseImportRecordService;
import com.smxz.yjzs.service.impl.FileUploadRecordService;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import io.minio.errors.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.util.ArrayList;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @since 2025-05-12 15:19
 **/
@Slf4j
@RestController
@RequestMapping("/api/file")
@RequiredArgsConstructor
public class FileHandlerController {

    private final FileUploadRecordService fileUploadRecordService;

    private final MinioClient  minioClient;

    private final MinioConfig minioConfig;

    private final CaseImportRecordService caseImportRecordService;


    /**
     * 单文件上传接口
     * 逻辑：先转为PDF → 原文件、PDF上传到MinIO → 保存基本信息到数据库 → 异步提取文本并更新数据库
     */
    @PostMapping("/upload/single")
    public ApiResult<String> uploadSingleFile(
            @RequestParam("caseImportId") Long caseImportId,
            @RequestParam("file") MultipartFile file) throws Exception {
        log.info("开始上传单个文件: {}, 案件ID: {}", file.getOriginalFilename(), caseImportId);

        // 验证文件格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.isEmpty()) {
            return ApiResult.error("文件名不能为空");
        }

        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (!isAllowedFileType(fileExtension)) {
            return ApiResult.error("不支持的文件格式，只支持 word、pdf、png、jpg 格式");
        }

        // 调用案件导入服务同步处理单个文件
        caseImportRecordService.processSingleFileSync(caseImportId, file);

        log.info("文件上传处理成功: {}", fileName);
        return ApiResult.success("文件上传处理成功");
    }

    /**
     * 验证文件类型是否允许
     */
    private boolean isAllowedFileType(String fileExtension) {
        return fileExtension.equals("doc") ||
               fileExtension.equals("docx") ||
               fileExtension.equals("pdf") ||
               fileExtension.equals("png") ||
               fileExtension.equals("jpg") ||
               fileExtension.equals("jpeg");
    }

    @PostMapping("/upload/list")
    public ApiResult<List<FileUploadRecordDTO>> listFileUploadRecord(@RequestParam("caseImportId") Integer caseImportId) {
        List<FileUploadRecordDTO> records = fileUploadRecordService.listFileUploadRecordsWithoutOcr(caseImportId);
        return ApiResult.success(records);
    }



    // 后端接口
    @GetMapping("/download/stream")
    public ResponseEntity<StreamingResponseBody> getFileStream(@RequestParam("id") Long id) {
        log.info("获取PDF文件流，fileId: {}", id);
        FileUploadRecord record = fileUploadRecordService.getById(id);
        String path = record.getPdfPath();
        StreamingResponseBody responseBody = outputStream -> {
            // 获取文件流并写入outputStream
            try (GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(path)
                    .build());) {
                object.transferTo(outputStream);
            } catch (ServerException | InsufficientDataException | ErrorResponseException | NoSuchAlgorithmException |
                     InvalidKeyException | InvalidResponseException | XmlParserException | InternalException e) {
                throw new RuntimeException(e);
            }
        };

        // URL编码文件名
        String encodedFileName = URLEncoder.encode(record.getFileName(), StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20"); // 将+替换为%20，保持空格的可读性

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header("Content-Disposition", "inline; filename=\"" + encodedFileName + "\"")
                .body(responseBody);
    }

    /**
     * 删除文件（移入回收站）
     */
    @PostMapping("/delete/{id}")
    public ApiResult<Boolean> deleteFile(@PathVariable("id") Long id, @RequestParam(value = "deletedBy", required = false) String deletedBy) {
        boolean result = fileUploadRecordService.deleteFileToRecycleBin(id, deletedBy);
        if (result) {
            return ApiResult.success(true, "文件已移入回收站");
        } else {
            return ApiResult.error("文件删除失败");
        }
    }

    /**
     * 获取回收站文件列表
     */
    @GetMapping("/recycle-bin/{caseImportId}")
    public ApiResult<List<FileUploadRecord>> getRecycleBinFiles(@PathVariable("caseImportId") Long caseImportId) {
        List<FileUploadRecord> files = fileUploadRecordService.getRecycleBinFiles(caseImportId);
        return ApiResult.success(files);
    }

    /**
     * 从回收站恢复文件
     */
    @PostMapping("/restore/{id}")
    public ApiResult<Boolean> restoreFile(@PathVariable("id") Long id) {
        boolean result = fileUploadRecordService.restoreFileFromRecycleBin(id);
        if (result) {
            return ApiResult.success(true, "文件恢复成功");
        } else {
            return ApiResult.error("文件恢复失败");
        }
    }

    /**
     * 获取文件OCR结果
     */
    @GetMapping("/ocr/{fileId}")
    public ApiResult<List<OcrResult>> getFileOcrResult(@PathVariable("fileId") Long fileId) {
        log.info("获取文件OCR结果，fileId: {}", fileId);

        try {
            FileUploadRecord fileRecord = fileUploadRecordService.getById(fileId);
            if (fileRecord == null) {
                return ApiResult.error("文件不存在");
            }

            if (fileRecord.getOcrResult() != null) {
                return ApiResult.success(fileRecord.getOcrResult());
            } else {
                // 返回空列表而不是错误，避免前端显示错误提示
                return ApiResult.success(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("获取文件OCR结果失败，fileId: {}", fileId, e);
            return ApiResult.error("获取OCR结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件OCR预览
     */
    @GetMapping("/ocr/previews/{fileId}")
    public ApiResult<List<OcrPreview>> getFileOcrPreviews(@PathVariable("fileId") Long fileId) {
        log.info("获取文件OCR预览，fileId: {}", fileId);

        FileUploadRecord fileRecord = fileUploadRecordService.getById(fileId);
        if (fileRecord == null) {
            return ApiResult.error("文件不存在");
        }

        if (fileRecord.getOcrPreviews() != null) {
            return ApiResult.success(fileRecord.getOcrPreviews());
        } else {
            // 返回空列表而不是错误，避免前端显示错误提示
            return ApiResult.success(new ArrayList<>());
        }
    }

    /**
     * 重新OCR识别文件
     */
    @PostMapping("/ocr/retry/{fileId}")
    public ApiResult<String> retryFileOcr(@PathVariable("fileId") Long fileId) {
        log.info("重新OCR识别文件，fileId: {}", fileId);

        try {
            FileUploadRecord fileRecord = fileUploadRecordService.getById(fileId);
            if (fileRecord == null) {
                return ApiResult.error("文件不存在");
            }

            // 异步重新处理OCR
            caseImportRecordService.retryFileOcr(fileId);

            return ApiResult.success("OCR重新识别任务已启动");
        } catch (Exception e) {
            log.error("重新OCR识别失败，fileId: {}", fileId, e);
            return ApiResult.error("重新OCR识别失败: " + e.getMessage());
        }
    }



    /**
     * 获取文件PDF内容（base64格式）
     * 用于PDF预览器显示
     */
    @GetMapping("/pdf/{fileId}")
    public ApiResult<String> getFilePdfContent(@PathVariable("fileId") Long fileId) {
        log.info("获取文件PDF内容，fileId: {}", fileId);

        try {
            FileUploadRecord fileRecord = fileUploadRecordService.getById(fileId);
            if (fileRecord == null) {
                return ApiResult.error("文件不存在");
            }

            // 获取PDF文件路径
            String pdfPath = fileRecord.getPdfPath();
            if (pdfPath == null || pdfPath.isEmpty()) {
                return ApiResult.error("PDF文件路径不存在");
            }

            // 从MinIO获取PDF文件内容
            try (GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(pdfPath)
                    .build())) {

                // 读取文件内容并转换为base64
                byte[] pdfBytes = object.readAllBytes();
                String base64Content = java.util.Base64.getEncoder().encodeToString(pdfBytes);

                log.info("成功获取PDF内容，fileId: {}, 文件大小: {} bytes", fileId, pdfBytes.length);
                return ApiResult.success(base64Content);

            } catch (Exception e) {
                log.error("从MinIO获取PDF文件失败，fileId: {}, pdfPath: {}", fileId, pdfPath, e);
                return ApiResult.error("获取PDF文件失败: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("获取文件PDF内容失败，fileId: {}", fileId, e);
            return ApiResult.error("获取PDF内容失败: " + e.getMessage());
        }
    }



    /**
     * 永久删除文件
     */
    @DeleteMapping("/permanent/{id}")
    public ApiResult<Boolean> permanentDeleteFile(@PathVariable("id") Long id) {
        boolean result = fileUploadRecordService.permanentDeleteFile(id);
        if (result) {
            return ApiResult.success(true, "文件已永久删除");
        } else {
            return ApiResult.error("文件永久删除失败");
        }
    }

    /**
     * 清空回收站（批量永久删除案件的所有回收站文件）
     */
    @DeleteMapping("/recycle-bin/clear/{caseImportId}")
    public ApiResult<Integer> clearRecycleBin(@PathVariable("caseImportId") Long caseImportId) {
        try {
            int deletedCount = fileUploadRecordService.clearRecycleBin(caseImportId);
            return ApiResult.success(deletedCount, String.format("回收站已清空，共删除 %d 个文件", deletedCount));
        } catch (Exception e) {
            log.error("清空回收站失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("清空回收站失败: " + e.getMessage());
        }
    }



}
