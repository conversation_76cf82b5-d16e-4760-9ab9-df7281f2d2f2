package com.smxz.yjzs.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.KnowledgeBaseConfig;
import com.smxz.yjzs.service.impl.KnowledgeBaseConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库配置管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/knowledge-base-config")
@Tag(name = "知识库配置管理", description = "知识库配置的增删改查接口")
public class KnowledgeBaseConfigController {

    @Autowired
    private KnowledgeBaseConfigService knowledgeBaseConfigService;

    /**
     * 获取所有知识库配置
     */
    @GetMapping("/list")
    @Operation(summary = "获取所有知识库配置")
    public ApiResult<List<KnowledgeBaseConfig>> list() {
        log.info("获取所有知识库配置");
        List<KnowledgeBaseConfig> configList = knowledgeBaseConfigService.list();
        return ApiResult.success(configList);
    }

    /**
     * 根据ID获取知识库配置
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取知识库配置")
    public ApiResult<KnowledgeBaseConfig> getById(@PathVariable Long id) {
        log.info("根据ID获取知识库配置，id: {}", id);
        KnowledgeBaseConfig config = knowledgeBaseConfigService.getById(id);
        return ApiResult.success(config);
    }

    /**
     * 根据配置键获取启用的配置
     */
    @GetMapping("/by-key/{configKey}")
    @Operation(summary = "根据配置键获取启用的配置")
    public ApiResult<KnowledgeBaseConfig> getByConfigKey(@PathVariable String configKey) {
        log.info("根据配置键获取启用的配置，configKey: {}", configKey);
        KnowledgeBaseConfig config = knowledgeBaseConfigService.getEnabledConfig(configKey);
        return ApiResult.success(config);
    }

    /**
     * 创建知识库配置
     */
    @PostMapping
    @Operation(summary = "创建知识库配置")
    public ApiResult<KnowledgeBaseConfig> create(@RequestBody KnowledgeBaseConfig config) {
        log.info("创建知识库配置，configKey: {}", config.getConfigKey());

        // 检查配置键是否已存在
        LambdaQueryWrapper<KnowledgeBaseConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseConfig::getConfigKey, config.getConfigKey());
        KnowledgeBaseConfig existingConfig = knowledgeBaseConfigService.getOne(queryWrapper);

        if (existingConfig != null) {
            return ApiResult.error("配置键已存在: " + config.getConfigKey());
        }

        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());

        boolean result = knowledgeBaseConfigService.save(config);
        if (result) {
            return ApiResult.success(config);
        } else {
            return ApiResult.error("创建配置失败");
        }
    }

    /**
     * 更新知识库配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新知识库配置")
    public ApiResult<Boolean> update(@PathVariable Long id, @RequestBody KnowledgeBaseConfig config) {
        log.info("更新知识库配置，id: {}", id);

        config.setId(id);
        config.setUpdateTime(LocalDateTime.now());

        boolean result = knowledgeBaseConfigService.updateById(config);
        return ApiResult.success(result);
    }

    /**
     * 删除知识库配置
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除知识库配置")
    public ApiResult<Boolean> delete(@PathVariable Long id) {
        log.info("删除知识库配置，id: {}", id);
        boolean result = knowledgeBaseConfigService.removeById(id);
        return ApiResult.success(result);
    }

    /**
     * 启用/禁用知识库配置
     */
    @PutMapping("/{id}/toggle")
    @Operation(summary = "启用/禁用知识库配置")
    public ApiResult<Boolean> toggle(@PathVariable Long id) {
        log.info("切换知识库配置状态，id: {}", id);

        KnowledgeBaseConfig config = knowledgeBaseConfigService.getById(id);
        if (config == null) {
            return ApiResult.error("配置不存在，id: " + id);
        }

        config.setIsEnabled(!config.getIsEnabled());
        config.setUpdateTime(LocalDateTime.now());

        boolean result = knowledgeBaseConfigService.updateById(config);
        return ApiResult.success(result);
    }

    /**
     * 重新初始化默认配置
     */
    @PostMapping("/init-default")
    @Operation(summary = "重新初始化默认配置")
    public ApiResult<String> initDefault() {
        log.info("重新初始化默认配置");
        knowledgeBaseConfigService.initDefaultConfig();
        return ApiResult.success("默认配置初始化完成");
    }
}
