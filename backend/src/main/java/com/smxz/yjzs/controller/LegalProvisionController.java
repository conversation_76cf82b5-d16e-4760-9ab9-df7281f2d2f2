package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.response.LawNameResponse;
import com.smxz.yjzs.dto.response.LegalProvisionTreeNode;
import com.smxz.yjzs.service.impl.LegalProvisionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 法条搜索控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/legal-provision")
@Tag(name = "法条搜索", description = "法条搜索相关接口")
public class LegalProvisionController {

    @Autowired
    private LegalProvisionService legalProvisionService;

    /**
     * 导入法条Excel文件（支持多文件）
     */
    @PostMapping("/import")
    @Operation(summary = "导入法条Excel文件", description = "上传Excel文件导入法条数据到数据库，支持多文件上传")
    public ApiResult<String> importFromExcel(
            @Parameter(description = "Excel文件列表", required = true)
            @RequestParam("files") List<MultipartFile> files) {

        try {
            if (files == null || files.isEmpty()) {
                return ApiResult.error("文件不能为空");
            }

            // 验证所有文件
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return ApiResult.error("存在空文件，请检查上传的文件");
                }

                String fileName = file.getOriginalFilename();
                if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                    return ApiResult.error("请上传Excel文件（.xlsx或.xls格式），文件名: " + fileName);
                }
            }

            log.info("开始导入 {} 个法条Excel文件", files.size());
            String result = legalProvisionService.importFromExcelFiles(files);

            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("导入法条Excel文件失败", e);
            return ApiResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有法律名称
     */
    @GetMapping("/law-names")
    @Operation(summary = "获取所有法律名称", description = "获取系统中所有法律名称列表，用于前端选择")
    public ApiResult<List<LawNameResponse>> getAllLawNames() {
        try {
            List<LawNameResponse> lawNames = legalProvisionService.getAllLawNames();
            return ApiResult.success(lawNames);
        } catch (Exception e) {
            log.error("获取法律名称列表失败", e);
            return ApiResult.error("获取法律名称列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据法律ID获取条款项树形结构
     */
    @GetMapping("/tree/{lawId}")
    @Operation(summary = "获取法条树形结构", description = "根据法律ID获取该法律下所有条款项的树形结构")
    public ApiResult<List<LegalProvisionTreeNode>> getProvisionTree(
            @Parameter(description = "法律ID", required = true)
            @PathVariable("lawId") Long lawId) {

        try {
            if (lawId == null) {
                return ApiResult.error("法律ID不能为空");
            }

            List<LegalProvisionTreeNode> tree = legalProvisionService.getProvisionTree(lawId);
            return ApiResult.success(tree);
        } catch (Exception e) {
            log.error("获取法条树形结构失败，法律ID: {}", lawId, e);
            return ApiResult.error("获取法条树形结构失败: " + e.getMessage());
        }
    }

    /**
     * 刷新所有缓存
     */
    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新缓存", description = "手动刷新法律名称缓存和法条树形结构缓存")
    public ApiResult<String> refreshCache() {
        try {
            legalProvisionService.refreshAllCache();
            return ApiResult.success("所有缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
            return ApiResult.error("刷新缓存失败: " + e.getMessage());
        }
    }


}
