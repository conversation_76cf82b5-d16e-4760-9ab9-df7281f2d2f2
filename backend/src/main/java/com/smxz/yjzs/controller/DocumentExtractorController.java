/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.yjzs.controller;

import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.enums.ExtractMethod;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorRequest;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.extractor.service.DocumentExtractorService;
import com.smxz.yjzs.entity.CaseImportRecord;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.service.impl.CaseImportRecordService;
import com.smxz.yjzs.service.impl.FileUploadRecordService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import java.security.IdentityScope;

/**
 * <AUTHOR> 诚
 * @description 要素提取测试
 * @since 2025-07-30 14:35
 **/
@RestController
@RequestMapping("/api/document-extractor")
@Tag(name = "要素提取测试", description = "要素提取相关接口")
@Slf4j
public class DocumentExtractorController {


    @Autowired
    private CaseImportRecordService caseImportRecordService;

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Autowired
    private DocumentExtractorService documentExtractorService;

    @GetMapping("/extractText/{fileId}")
    public BaseResponse<DocumentExtractorResult> extractText(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "文档类型", required = true) @RequestParam("documentType") DocumentType documentType) {
        FileUploadRecord record = fileUploadRecordService.getById(fileId);
        DocumentExtractorRequest documentExtractorRequest = DocumentExtractorRequest.builder()
                .fileName(record.getFileName())
                .content(record.getExtractedText())
                .extractMethod(ExtractMethod.REGEX)
                .build();
        log.info("调用内容提取服务，文件: {}", record.getFileName());
        BaseResponse<DocumentExtractorResult> response = documentExtractorService
                .extractElements(documentType, documentExtractorRequest);
        return response;
    }


}
