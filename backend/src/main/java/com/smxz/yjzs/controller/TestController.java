package com.smxz.yjzs.controller;

import com.smxz.ocr.model.OcrResponseData;
import com.smxz.ocr.model.enums.FileType;
import com.smxz.ocr.service.OcrService;
import com.smxz.yjzs.common.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Base64;

/**
 * 测试控制器
 * 用于测试各种功能的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Tag(name = "测试接口", description = "用于测试各种功能的接口")
public class TestController {

    private final OcrService ocrService;

    /**
     * OCR 测试接口
     * 支持上传文件进行 OCR 识别并返回 extracted_text
     */
    @Operation(summary = "OCR 文本识别测试", description = "上传文件进行 OCR 识别，支持 PDF、PNG、JPG、JPEG 格式，直接返回提取的文本内容")
    @PostMapping("/ocr")
    public ApiResult<String> testOcr(
            @Parameter(description = "要进行 OCR 识别的文件，支持 PDF、PNG、JPG、JPEG 格式")
            @RequestParam("file") MultipartFile file) {
        log.info("开始测试 OCR，文件名: {}", file.getOriginalFilename());

        try {
            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.isEmpty()) {
                return ApiResult.error("文件名不能为空");
            }

            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileType fileType = determineFileType(fileExtension);
            if (fileType == null) {
                return ApiResult.error("不支持的文件格式，支持 PDF、PNG、JPG、JPEG");
            }

            // 将文件转换为 Base64
            byte[] fileBytes = file.getBytes();
            String base64Content = Base64.getEncoder().encodeToString(fileBytes);

            // 调用 OCR 服务
            OcrResponseData ocrResult = ocrService.getLayoutData(base64Content, fileType, true);

            if (ocrResult == null) {
                return ApiResult.error("OCR 识别失败");
            }

            // 提取文本内容
            String extractedText = extractTextFromOcrResult(ocrResult, fileName);

            log.info("OCR 测试完成，文件: {}, 提取文本长度: {}", fileName,
                    extractedText != null ? extractedText.length() : 0);

            return ApiResult.success(extractedText);

        } catch (Exception e) {
            log.error("OCR 测试失败，文件: {}", file.getOriginalFilename(), e);
            return ApiResult.error("OCR 测试失败: " + e.getMessage());
        }
    }


    /**
     * 根据文件扩展名确定文件类型
     */
    private FileType determineFileType(String fileExtension) {
        switch (fileExtension.toLowerCase()) {
            case "pdf":
                return FileType.PDF;
            case "png":
            case "jpg":
            case "jpeg":
                return FileType.IMAGE;
            default:
                return null;
        }
    }

    /**
     * 从 OCR 结果中提取文本内容
     * 复用 FilePreprocessService 中的逻辑
     */
    private String extractTextFromOcrResult(OcrResponseData ocrResult, String fileName) {
        try {
            if (ocrResult == null || ocrResult.getResults() == null || ocrResult.getResults().isEmpty()) {
                log.debug("OCR 结果为空，文件: {}", fileName);
                return null;
            }

            // 提取每页的 recTexts 并合并
            StringBuilder textBuilder = new StringBuilder();
            for (int i = 0; i < ocrResult.getResults().size(); i++) {
                String recText = ocrResult.getResults().get(i).getRecTexts();
                if (recText != null && !recText.trim().isEmpty()) {
                    if (textBuilder.length() > 0) {
                        textBuilder.append("\n");
                    }
                    textBuilder.append(recText);
                }
            }

            String result = textBuilder.toString();
            if (!result.trim().isEmpty()) {
                // 格式化输出，保持与 FilePreprocessService 一致的格式
                String fullText = String.format("=== 测试文件：%s ===\n%s", fileName, result);
                log.info("从 OCR 结果提取文本成功，文件: {}, 内容长度: {}", fileName, fullText.length());
                return fullText;
            } else {
                log.debug("OCR 结果中未找到有效的文本内容，文件: {}", fileName);
                return null;
            }

        } catch (Exception e) {
            log.error("解析 OCR 结果失败，文件: {}", fileName, e);
            return null;
        }
    }


}