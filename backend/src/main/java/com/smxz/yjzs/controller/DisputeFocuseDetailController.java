package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.DisputeFocus;
import com.smxz.yjzs.entity.DisputeFocuseDetail;
import com.smxz.yjzs.entity.EvidenceFactsDetails;
import com.smxz.yjzs.service.impl.DisputeFocuseDetailService;
import com.smxz.yjzs.service.impl.DisputeFocusService;
import com.smxz.yjzs.service.impl.EvidenceFactsDetailsService;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 争议焦点详情控制器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@RestController
@RequestMapping("/api/dispute-focuse-detail")
@Tag(name = "争议焦点详情管理", description = "争议焦点说理相关接口")
@Slf4j
public class DisputeFocuseDetailController {

    @Autowired
    private DisputeFocuseDetailService disputeFocuseDetailService;

    @Autowired
    private EvidenceFactsDetailsService evidenceFactsDetailsService;

    @Autowired
    private DisputeFocusService disputeFocusService;

    /**
     * 生成争议焦点说理
     *
     * @param caseImportId 案件导入ID
     * @return 操作结果
     */
    @PostMapping("/generate/{caseImportId}")
    @Operation(summary = "生成争议焦点说理", description = "根据案件的认定事实和争议焦点生成说理内容")
    public ApiResult<String> generateReasoning(
            @Parameter(description = "案件导入ID", required = true)
            @PathVariable Long caseImportId) {
        
        try {
            log.info("收到生成争议焦点说理请求，caseImportId: {}", caseImportId);

            // 前置检查：验证是否有认定事实和争议焦点
            String validationResult = validatePrerequisites(caseImportId);
            if (validationResult != null) {
                return ApiResult.error(validationResult);
            }

            // 异步生成争议焦点说理
            disputeFocuseDetailService.generateReasoning(caseImportId);

            return ApiResult.success("争议焦点说理生成任务已启动");

        } catch (Exception e) {
            log.error("生成争议焦点说理失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("生成争议焦点说理失败: " + e.getMessage());
        }
    }

    /**
     * 获取争议焦点说理
     *
     * @param caseImportId 案件导入ID
     * @return 争议焦点说理内容
     */
    @GetMapping("/{caseImportId}")
    @Operation(summary = "获取争议焦点说理", description = "根据案件ID获取争议焦点说理内容")
    public ApiResult<DisputeFocuseDetail> getReasoning(
            @Parameter(description = "案件导入ID", required = true)
            @PathVariable Long caseImportId) {
        
        try {
            log.info("收到获取争议焦点说理请求，caseImportId: {}", caseImportId);
            
            DisputeFocuseDetail detail = disputeFocuseDetailService.getByCaseImportId(caseImportId);
            
            if (detail != null) {
                return ApiResult.success(detail);
            } else {
                return ApiResult.success(null, "暂无争议焦点说理数据");
            }
            
        } catch (Exception e) {
            log.error("获取争议焦点说理失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("获取争议焦点说理失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成争议焦点说理
     *
     * @param caseImportId 案件导入ID
     * @return 操作结果
     */
    @PostMapping("/regenerate/{caseImportId}")
    @Operation(summary = "重新生成争议焦点说理", description = "重新生成争议焦点说理内容，会覆盖原有内容")
    public ApiResult<String> regenerateReasoning(
            @Parameter(description = "案件导入ID", required = true)
            @PathVariable Long caseImportId) {
        
        try {
            log.info("收到重新生成争议焦点说理请求，caseImportId: {}", caseImportId);

            // 前置检查：验证是否有认定事实和争议焦点
            String validationResult = validatePrerequisites(caseImportId);
            if (validationResult != null) {
                return ApiResult.error(validationResult);
            }

            // 直接执行生成任务（会覆盖原有内容）
            disputeFocuseDetailService.generateReasoning(caseImportId);

            return ApiResult.success("争议焦点说理重新生成任务已启动");

        } catch (Exception e) {
            log.error("重新生成争议焦点说理失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("重新生成争议焦点说理失败: " + e.getMessage());
        }
    }

    /**
     * 更新争议焦点说理
     *
     * @param caseImportId 案件导入ID
     * @param reasoning 争议焦点说理内容
     * @return 操作结果
     */
    @PutMapping("/{caseImportId}")
    @Operation(summary = "更新争议焦点说理", description = "更新指定案件的争议焦点说理内容")
    public ApiResult<DisputeFocuseDetail> updateReasoning(
            @Parameter(description = "案件导入ID", required = true)
            @PathVariable Long caseImportId,
            @Parameter(description = "争议焦点说理内容", required = false)
            @RequestBody(required = false) String reasoning) {

        try {
            // 处理null值，转换为空字符串
            String processedReasoning = reasoning != null ? reasoning : "";
            log.info("收到更新争议焦点说理请求，caseImportId: {}, 内容长度: {}", caseImportId, processedReasoning.length());

            DisputeFocuseDetail updatedDetail = disputeFocuseDetailService.updateReasoning(caseImportId, processedReasoning);

            return ApiResult.success(updatedDetail, "争议焦点说理更新成功");

        } catch (Exception e) {
            log.error("更新争议焦点说理失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("更新争议焦点说理失败: " + e.getMessage());
        }
    }

    /**
     * 删除争议焦点说理
     *
     * @param caseImportId 案件导入ID
     * @return 操作结果
     */
    @DeleteMapping("/{caseImportId}")
    @Operation(summary = "删除争议焦点说理", description = "删除指定案件的争议焦点说理内容")
    public ApiResult<String> deleteReasoning(
            @Parameter(description = "案件导入ID", required = true)
            @PathVariable Long caseImportId) {
        
        try {
            log.info("收到删除争议焦点说理请求，caseImportId: {}", caseImportId);
            
            DisputeFocuseDetail detail = disputeFocuseDetailService.getByCaseImportId(caseImportId);
            if (detail != null) {
                disputeFocuseDetailService.removeById(detail.getId());
                return ApiResult.success("争议焦点说理删除成功");
            } else {
                return ApiResult.success("暂无争议焦点说理数据");
            }
            
        } catch (Exception e) {
            log.error("删除争议焦点说理失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("删除争议焦点说理失败: " + e.getMessage());
        }
    }

    /**
     * 验证生成争议焦点说理的前置条件
     * @param caseImportId 案件导入ID
     * @return 如果验证失败返回错误信息，验证成功返回null
     */
    private String validatePrerequisites(Long caseImportId) {
        try {
            // 1. 检查认定事实
            EvidenceFactsDetails evidenceFactsDetails = evidenceFactsDetailsService.getByCaseImportId(caseImportId);
            String determineFacts = evidenceFactsDetails != null ? StringUtils.defaultIfBlank(evidenceFactsDetails.getDetermineFactsExtract(),evidenceFactsDetails.getDetermineFacts()) : "";

            if (StringUtils.isBlank(determineFacts)) {
                log.warn("未找到认定事实，无法生成争议焦点说理，caseImportId: {}", caseImportId);
                return "未找到认定事实，请先完成案件事实认定后再生成争议焦点说理";
            }

            // 2. 检查争议焦点
            List<DisputeFocus> disputeFocusList = disputeFocusService.getDisputeFocusByCaseId(caseImportId);
            if (disputeFocusList.isEmpty()) {
                log.warn("未找到争议焦点，无法生成争议焦点说理，caseImportId: {}", caseImportId);
                return "未找到争议焦点，请先完成争议焦点分析后再生成争议焦点说理";
            }

            // 3. 检查每个争议焦点是否都有结论
            for (DisputeFocus disputeFocus : disputeFocusList) {
                if (StringUtils.isBlank(disputeFocus.getConclusion())) {
                    log.warn("争议焦点缺少结论，无法生成争议焦点说理，caseImportId: {}, 争议焦点ID: {}, 描述: {}",
                            caseImportId, disputeFocus.getId(), disputeFocus.getDescription());
                    return "存在未设置结论的争议焦点，请先为所有争议焦点设置结论后再生成争议焦点说理";
                }
            }

            log.info("前置条件验证通过，认定事实长度: {}, 争议焦点数量: {}",
                    determineFacts.length(), disputeFocusList.size());
            return null; // 验证通过

        } catch (Exception e) {
            log.error("验证前置条件失败，caseImportId: {}", caseImportId, e);
            return "验证前置条件失败: " + e.getMessage();
        }
    }
}
