package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.DisputeFocus;
import com.smxz.yjzs.service.impl.DisputeFocusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 争议焦点控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/disputeFocus")
@Tag(name = "争议焦点管理", description = "争议焦点相关接口")
public class DisputeFocusController {

    @Autowired
    private DisputeFocusService disputeFocusService;

    /**
     * 获取案件的争议焦点数据
     */
    @Operation(summary = "获取争议焦点数据", description = "获取指定案件的所有争议焦点")
    @GetMapping("/{caseImportId}/getDisputeFocus")
    public ApiResult<List<DisputeFocus>> getDisputeFocus(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取争议焦点数据，caseImportId: {}", caseImportId);
        List<DisputeFocus> disputeFocusList = disputeFocusService.getDisputeFocusByCaseId(caseImportId);
        return ApiResult.success(disputeFocusList);
    }

    /**
     * 保存或更新争议焦点
     * 根据ID判断：ID为null或0时新增，否则更新
     */
    @Operation(summary = "保存争议焦点", description = "保存或更新争议焦点数据，根据ID自动判断新增还是更新")
    @PostMapping("/save")
    public ApiResult<DisputeFocus> saveDisputeFocus(
            @Parameter(description = "争议焦点数据") @RequestBody DisputeFocus disputeFocus) {

        boolean isCreate = disputeFocus.getId() == null || disputeFocus.getId() == 0;
        log.info("{}争议焦点，ID: {}, 描述: {}",
                isCreate ? "新增" : "更新",
                disputeFocus.getId(),
                disputeFocus.getDescription());

        DisputeFocus savedFocus = disputeFocusService.saveDisputeFocus(disputeFocus);
        String message = isCreate ? "争议焦点添加成功" : "争议焦点更新成功";
        return ApiResult.success(savedFocus, message);
    }

    /**
     * 删除争议焦点
     */
    @Operation(summary = "删除争议焦点", description = "删除指定的争议焦点")
    @DeleteMapping("/delete/{focusId}")
    public ApiResult<String> deleteDisputeFocus(
            @Parameter(description = "争议焦点ID") @PathVariable("focusId") Long focusId) {
        log.info("删除争议焦点，focusId: {}", focusId);
        disputeFocusService.deleteDisputeFocus(focusId);
        return ApiResult.success("争议焦点删除成功");
    }

    /**
     * 更新争议焦点（保持向后兼容）
     */
    @Operation(summary = "更新争议焦点", description = "更新指定的争议焦点内容（兼容接口）")
    @PutMapping("/update/{focusId}")
    public ApiResult<DisputeFocus> updateDisputeFocus(
            @Parameter(description = "争议焦点ID") @PathVariable("focusId") Long focusId,
            @Parameter(description = "更新数据") @RequestBody DisputeFocus disputeFocus) {
        log.info("更新争议焦点（兼容接口），focusId: {}", focusId);
        disputeFocus.setId(focusId);
        DisputeFocus updatedFocus = disputeFocusService.saveDisputeFocus(disputeFocus);
        return ApiResult.success(updatedFocus, "争议焦点更新成功");
    }

    /**
     * 删除案件下的所有争议焦点
     */
    @Operation(summary = "删除所有争议焦点", description = "删除指定案件下的所有争议焦点")
    @DeleteMapping("/deleteAll/{caseImportId}")
    public ApiResult<String> deleteAllDisputeFocuses(
            @Parameter(description = "案件ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("删除案件 {} 下的所有争议焦点", caseImportId);

        int deletedCount = disputeFocusService.deleteAllByCaseImportId(caseImportId);

        String message = String.format("成功删除 %d 个争议焦点", deletedCount);
        log.info("删除完成，案件ID: {}, 删除数量: {}", caseImportId, deletedCount);

        return ApiResult.success(message, message);
    }

    /**
     * 重新生成争议焦点
     */
    @Operation(summary = "重新生成争议焦点", description = "使用AI重新生成争议焦点")
    @PostMapping("/{caseImportId}/regenerate")
    public ApiResult<String> regenerate(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("重新生成争议焦点，caseImportId: {}", caseImportId);
        // 先删除原有数据
        disputeFocusService.removeDataForRegenerate(caseImportId);
        // 生成任务
        disputeFocusService.generate(caseImportId);
        return ApiResult.success("争议焦点重新生成任务已启动");
    }

    /**
     * 重新生成争议焦点标签的所有模型任务
     */
    @Operation(summary = "重新生成争议焦点标签所有任务", description = "重新生成争议焦点标签包含的所有模型任务：诉辩关系、争议焦点")
    @PostMapping("/{caseImportId}/regenerate-all")
    public ApiResult<String> regenerateAll(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("重新生成争议焦点标签所有任务，caseImportId: {}", caseImportId);

        try {
            // 调用服务层的统一重新生成方法
            disputeFocusService.regenerateAllFocusTasks(caseImportId);
            return ApiResult.success("争议焦点标签所有任务重新生成已启动");
        } catch (Exception e) {
            log.error("重新生成争议焦点标签所有任务失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("重新生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取争议焦点法条完成进度
     */
    @Operation(summary = "获取争议焦点法条进度", description = "基于争议焦点数据检查法条完成情况")
    @GetMapping("/{caseImportId}/lawProgress")
    public ApiResult<Map<String, Integer>> getLawProgress(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取争议焦点法条进度，caseImportId: {}", caseImportId);

        try {
            Map<String, Integer> progress = disputeFocusService.getLawProgress(caseImportId);
            return ApiResult.success(progress);
        } catch (Exception e) {
            log.error("获取争议焦点法条进度失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("获取法条进度失败: " + e.getMessage());
        }
    }

}