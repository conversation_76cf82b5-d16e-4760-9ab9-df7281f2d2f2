package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.service.DocumentGenerationQueueManager;
import com.smxz.yjzs.service.QueueResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 文书生成队列控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/document-generation-queue")
@Tag(name = "文书生成队列管理", description = "文书生成队列相关接口")
public class DocumentGenerationQueueController {
    
    @Autowired
    private DocumentGenerationQueueManager queueManager;
    
    /**
     * 尝试获取队列权限
     */
    @Operation(summary = "尝试获取队列权限")
    @PostMapping("/acquire/{documentId}")
    public ApiResult<Boolean> acquireQueuePermission(@PathVariable String documentId) {
        try {
            log.info("接收到获取队列权限请求: documentId={}", documentId);
            
            QueueResult result = queueManager.tryAcquire(documentId);
            
            if (result.isCanExecute()) {
                return ApiResult.success(true, "获取队列权限成功");
            } else if (result.isInQueue()) {
                // 决策理由：在队列中等待时返回false，让前端继续轮询
                return ApiResult.success(false, "任务在队列中等待，队列位置: " + result.getQueuePosition());
            } else {
                return ApiResult.error(result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("获取队列权限失败: documentId={}", documentId, e);
            return ApiResult.error("获取权限失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记文书生成完成
     * 前端在insertDocumentFooter前调用此接口
     */
    @Operation(summary = "标记文书生成完成")
    @PostMapping("/complete/{documentId}")
    public ApiResult<Boolean> completeGeneration(@PathVariable String documentId) {
        try {
            log.info("接收到文书生成完成通知: documentId={}", documentId);
            
            if (!queueManager.hasTask(documentId)) {
                return ApiResult.success(true);
            }
            
            queueManager.release(documentId);
            
            return ApiResult.success(true);
            
        } catch (Exception e) {
            log.error("标记文书生成完成失败: documentId={}", documentId, e);
            return ApiResult.error("标记文书生成完成失败");
        }
    }
    
    /**
     * 获取队列状态
     */
    @Operation(summary = "获取队列状态")
    @GetMapping("/status")
    public ApiResult<Map<String, Object>> getQueueStatus() {
        try {
            Map<String, Object> status = queueManager.getQueueStatus();
            return ApiResult.success(status);
        } catch (Exception e) {
            log.error("获取队列状态失败", e);
            return ApiResult.error("获取队列状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制完成文书生成（管理员操作）
     */
    @Operation(summary = "强制完成文书生成")
    @PostMapping("/force-complete/{documentId}")
    public ApiResult<Boolean> forceCompleteGeneration(@PathVariable String documentId) {
        try {
            log.info("接收到强制完成文书生成请求: documentId={}", documentId);
            
            boolean success = queueManager.forceComplete(documentId);
            
            return ApiResult.success(success);
            
        } catch (Exception e) {
            log.error("强制完成文书生成失败: documentId={}", documentId, e);
            return ApiResult.error("强制完成文书生成失败");
        }
    }
    
    /**
     * 获取指定文书的任务状态
     */
    @Operation(summary = "获取文书任务状态")
    @GetMapping("/task-status/{documentId}")
    public ApiResult<Map<String, Object>> getTaskStatus(@PathVariable String documentId) {
        try {
            var task = queueManager.getTask(documentId);
            
            if (task == null) {
                return ApiResult.success(Map.of(
                    "exists", false,
                    "status", "NOT_FOUND"
                ));
            }
            
            return ApiResult.success(Map.of(
                "exists", true,
                "documentId", task.getDocumentId(),
                "status", task.getStatus().name(),
                "createTime", task.getCreateTime(),
                "startTime", task.getStartTime()
            ));
            
        } catch (Exception e) {
            log.error("获取文书任务状态失败: documentId={}", documentId, e);
            return ApiResult.error("获取文书任务状态失败");
        }
    }
}