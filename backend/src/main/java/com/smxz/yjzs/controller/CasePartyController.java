package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.CasePartyTabDTO;
import com.smxz.yjzs.entity.CaseParty;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import com.smxz.yjzs.service.impl.CasePartyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "案件当事人")
@RequestMapping("/api/caseParty")
@RestController
public class CasePartyController {

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private CasePartyService casePartyService;

    @Operation(summary = "获取案件当事人")
    @GetMapping("/list")
    public ApiResult<List<CaseParty>> list(Long caseImportId) {
        return ApiResult.success(casePartyService.listByCaseImportId(caseImportId));
    }

    @Operation(summary = "获取案件当事人页签（会合并）")
    @GetMapping("/listTab")
    public ApiResult<List<CasePartyTabDTO>> listTab(Long caseImportId) {
        return ApiResult.success(casePartyService.listTabByCaseImportId(caseImportId));
    }

    @Operation(summary = "获取案件当事人-诉讼费用专用，含合并项及拆解")
    @GetMapping("/listForLegalFees")
    public ApiResult<List<CasePartyTabDTO>> listForLegalFees(Long caseImportId) {
        return ApiResult.success(casePartyService.listForLegalFeesByCaseImportId(caseImportId));
    }

    @Operation(summary = "重新分析当事人（原逻辑）")
    @PostMapping("/regenerate-case-party/{caseImportId}")
    public ApiResult<Boolean> regenerateCaseParty(@PathVariable("caseImportId") Long caseImportId) {
        casePartyService.analyseCaseParty(caseImportId);
        return ApiResult.success(true, "重新分析当事人任务已启动");
    }

    @Operation(summary = "重新分析当事人（新逻辑）")
    @PostMapping("/regenerate-case-party2/{caseImportId}")
    public ApiResult<Boolean> regenerateCaseParty2(@PathVariable("caseImportId") Long caseImportId) {
        casePartyService.analyseCaseParty(caseImportId);
        return ApiResult.success(true, "重新分析当事人任务已启动");
    }
}
