package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.request.VerificationEvidenceListRequest;
import com.smxz.yjzs.entity.VerificationEvidenceInfo;
import com.smxz.yjzs.service.impl.VerificationEvidenceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 质证情况信息表Controller
 *
 * <AUTHOR>
 * @since 2026-06-29
 */
@Slf4j
@RestController
@RequestMapping("/api/verification-evidence")
@Tag(name = "质证情况信息管理", description = "质证情况信息相关接口")
public class VerificationEvidenceInfoController {

    @Autowired
    private VerificationEvidenceInfoService verificationEvidenceInfoService;

    /**
     * 根据案件ID查询质证情况信息列表
     */
    @Operation(summary = "根据案件ID查询质证情况信息列表")
    @GetMapping("/list/{caseImportId}")
    public ApiResult<List<VerificationEvidenceInfo>> listByCaseImportId(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("查询案件质证情况信息列表，caseImportId: {}", caseImportId);
        List<VerificationEvidenceInfo> list = verificationEvidenceInfoService.listByCaseImportId(caseImportId);
        return ApiResult.success(list);
    }

    /**
     * 根据案件ID和当事人姓名列表查询质证情况信息列表
     */
    @Operation(summary = "根据案件ID和当事人姓名列表查询质证情况信息列表")
    @PostMapping("/listByCaseImportIdAndPartyNames")
    public ApiResult<List<VerificationEvidenceInfo>> listByCaseImportIdAndPartyNames(
            @RequestBody VerificationEvidenceListRequest request) {
        log.info("根据案件ID和当事人姓名列表查询质证情况信息，caseImportId: {}, partyNames: {}", request.getCaseImportId(), request.getPartyNames());
        List<VerificationEvidenceInfo> list = verificationEvidenceInfoService.listByCaseImportIdAndPartyNames(
                request.getCaseImportId(), request.getPartyNames());
        return ApiResult.success(list);
    }

    /**
     * 根据当事人类型查询质证情况信息列表
     */
    @Operation(summary = "根据当事人类型查询质证情况信息列表")
    @GetMapping("/list/{caseImportId}/party-type/{partyType}")
    public ApiResult<List<VerificationEvidenceInfo>> listByPartyType(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "当事人类型") @PathVariable("partyType") String partyType) {
        log.info("根据当事人类型查询质证情况信息，caseImportId: {}, partyType: {}", caseImportId, partyType);
        List<VerificationEvidenceInfo> list = verificationEvidenceInfoService.listByPartyType(caseImportId, partyType);
        return ApiResult.success(list);
    }

    /**
     * 根据采纳状态查询质证情况信息列表
     */
    @Operation(summary = "根据采纳状态查询质证情况信息列表")
    @GetMapping("/list/{caseImportId}/adopt-status/{isAdopt}")
    public ApiResult<List<VerificationEvidenceInfo>> listByAdoptStatus(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId,
            @Parameter(description = "是否采纳") @PathVariable("isAdopt") Integer isAdopt) {
        log.info("根据采纳状态查询质证情况信息，caseImportId: {}, isAdopt: {}", caseImportId, isAdopt);
        List<VerificationEvidenceInfo> list = verificationEvidenceInfoService.listByAdoptStatus(caseImportId, isAdopt);
        return ApiResult.success(list);
    }

    /**
     * 创建质证情况信息
     */
    @Operation(summary = "创建质证情况信息")
    @PostMapping("/create")
    public ApiResult<Boolean> createVerificationInfo(@RequestBody VerificationEvidenceInfo verificationInfo) {
        log.info("创建质证情况信息，案件ID: {}, 当事人: {}", 
                verificationInfo.getCaseImportId(), verificationInfo.getPartyName());
        boolean result = verificationEvidenceInfoService.createVerificationInfo(verificationInfo);
        return ApiResult.success(result);
    }

    /**
     * 更新质证情况信息
     */
    @Operation(summary = "更新质证情况信息")
    @PutMapping("/update")
    public ApiResult<Boolean> updateVerificationInfo(@RequestBody VerificationEvidenceInfo verificationInfo) {
        log.info("更新质证情况信息，ID: {}", verificationInfo.getId());
        boolean result = verificationEvidenceInfoService.updateVerificationInfo(verificationInfo);
        return ApiResult.success(result);
    }

    /**
     * 删除质证情况信息
     */
    @Operation(summary = "删除质证情况信息")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Boolean> deleteVerificationInfo(
            @Parameter(description = "质证情况信息ID") @PathVariable("id") Long id) {
        log.info("删除质证情况信息，ID: {}", id);
        boolean result = verificationEvidenceInfoService.deleteVerificationInfo(id);
        return ApiResult.success(result);
    }

    /**
     * 批量删除案件相关的质证情况信息
     */
    @Operation(summary = "批量删除案件相关的质证情况信息")
    @DeleteMapping("/delete/case/{caseImportId}")
    public ApiResult<Boolean> deleteByCaseImportId(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("批量删除案件相关的质证情况信息，caseImportId: {}", caseImportId);
        boolean result = verificationEvidenceInfoService.deleteByCaseImportId(caseImportId);
        return ApiResult.success(result);
    }

    /**
     * 根据id更新质证情况的采纳状态
     */
    @Operation(summary = "根据id更新质证情况的采纳状态")
    @PutMapping("/update-adopt-status/{id}/{isAdopt}")
    public ApiResult<Boolean> updateAdoptStatus(
            @Parameter(description = "质证情况信息ID") @PathVariable("id") Long id,
            @Parameter(description = "是否采纳") @PathVariable("isAdopt") Integer isAdopt) {
        log.info("根据id更新质证情况采纳状态，ID: {}, isAdopt: {}", id, isAdopt);
        boolean result = verificationEvidenceInfoService.updateAdoptStatus(id, isAdopt);
        return ApiResult.success(result);
    }

    /**
     * 信息
     */
    @Operation(summary = "生成质证情况信息")
    @PostMapping("/generate/{caseImportId}")
    public ApiResult<String> generateVerificationEvidence(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("开始生成质证情况信息，caseImportId: {}", caseImportId);
        verificationEvidenceInfoService.generate(caseImportId);
        return ApiResult.success("质证情况信息生成任务已启动");
    }

} 