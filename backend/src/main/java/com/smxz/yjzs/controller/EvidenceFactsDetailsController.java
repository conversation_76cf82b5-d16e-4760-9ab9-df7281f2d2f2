package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.EvidenceFactsDetails;
import com.smxz.yjzs.service.impl.EvidenceFactsDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 证据事实详情表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@RestController
@RequestMapping("/api/evidence-facts-details")
@Tag(name = "证据事实详情管理", description = "证据事实详情相关接口")
@Slf4j
public class EvidenceFactsDetailsController {

    @Autowired
    private EvidenceFactsDetailsService evidenceDetailsService;


    /**
     * 根据案件ID查询证据详情信息
     */
    @GetMapping("/queryByCase/{caseImportId}")
    @Operation(summary = "根据案件ID查询证据详情", description = "根据案件ID查询最新的证据详情信息")
    public ApiResult<EvidenceFactsDetails> getByCaseImportId(
            @Parameter(description = "案件ID") @PathVariable Long caseImportId) {
        log.info("查询案件证据详情信息，caseImportId: {}", caseImportId);
        try{
            EvidenceFactsDetails evidenceFactsDetails = evidenceDetailsService.getByCaseImportId(caseImportId);
            return ApiResult.success(evidenceFactsDetails);
        } catch (Exception e) {
            log.error("查询该案件证据详情信息失败:", e.getMessage());
            return ApiResult.error("查询该案件证据详情信息失败");
        }

    }


    /**
     * 保存或更新证据详情信息
     */
    @PostMapping("/save")
    @Operation(summary = "保存或更新证据详情", description = "根据是否有ID判断创建新记录或更新现有记录")
    public ApiResult<Boolean> saveOrUpdate(@RequestBody EvidenceFactsDetails evidenceFactsDetails) {
        if (evidenceFactsDetails.getId() == null) {
            // 创建新记录
            log.info("创建证据详情信息，案件ID: {}", evidenceFactsDetails.getCaseImportId());
            boolean result = evidenceDetailsService.createEvidenceFactsDetails(evidenceFactsDetails);
            if (result) {
                return ApiResult.success(true, "证据详情信息创建成功");
            } else {
                return ApiResult.error("证据详情信息创建失败");
            }
        } else {
            // 更新现有记录
            log.info("更新证据详情信息，ID: {}", evidenceFactsDetails.getId());
            boolean result = evidenceDetailsService.updateEvidenceFactsDetails(evidenceFactsDetails);
            if (result) {
                return ApiResult.success(true, "证据详情信息更新成功");
            } else {
                return ApiResult.error("证据详情信息更新失败");
            }
        }
    }

    /**
     * 根据ID删除证据详情信息
     */
    @DeleteMapping("delete/{id}")
    @Operation(summary = "删除证据详情", description = "根据ID删除证据详情信息（逻辑删除）")
    public ApiResult<Boolean> deleteById(
            @Parameter(description = "证据详情ID") @PathVariable Long id) {
        log.info("删除证据详情信息，ID: {}", id);
        boolean result = evidenceDetailsService.deleteEvidenceFactsDetails(id);
        if (result) {
            return ApiResult.success(true, "证据详情信息删除成功");
        } else {
            return ApiResult.error("证据详情信息删除失败");
        }
    }

    /**
     * 批量删除案件相关的证据详情信息
     */
    @DeleteMapping("/case/{caseImportId}")
    @Operation(summary = "批量删除案件证据详情", description = "删除案件相关的所有证据详情信息")
    public ApiResult<Boolean> deleteByCaseImportId(
            @Parameter(description = "案件ID") @PathVariable Long caseImportId) {
        log.info("批量删除案件相关的证据详情信息，caseImportId: {}", caseImportId);
        boolean result = evidenceDetailsService.deleteByCaseImportId(caseImportId);
        if (result) {
            return ApiResult.success(true, "案件相关证据详情信息删除成功");
        } else {
            return ApiResult.error("案件相关证据详情信息删除失败");
        }
    }

    /**
     * 生成无争议事实
     */
    @PostMapping("/generate-undisputed-facts/{caseImportId}")
    @Operation(summary = "生成无争议事实", description = "异步生成案件的无争议事实，从起诉状和答辩状中提取双方认可的事实")
    public ApiResult<String> generateUndisputedFacts(
            @Parameter(description = "案件ID") @PathVariable Long caseImportId) {
        log.info("开始生成无争议事实，caseImportId: {}", caseImportId);
        try {
            // 调用服务层方法，异步执行
            evidenceDetailsService.generateUndisputedFacts(caseImportId);
            return ApiResult.success("无争议事实生成任务已启动，请稍后查询结果");
        } catch (Exception e) {
            log.error("启动无争议事实生成任务失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("启动无争议事实生成任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成认定事实
     */
    @PostMapping("/generate-determine-facts/{caseImportId}")
    @Operation(summary = "生成认定事实", description = "异步生成案件的认定事实，基于起诉意见、答辩意见、庭审笔录、无争议事实和法官意见")
    public ApiResult<String> generateDetermineFacts(
            @Parameter(description = "案件ID") @PathVariable Long caseImportId) {
        log.info("开始生成认定事实，caseImportId: {}", caseImportId);
        try {
            // 先进行前置条件验证
            evidenceDetailsService.validateDetermineFactsPrerequisites(caseImportId);

            // 调用服务层方法，异步执行
            evidenceDetailsService.generateDetermineFactsAsync(caseImportId);
            return ApiResult.success("认定事实生成任务已启动，请稍后查询结果");
        } catch (IllegalStateException e) {
            log.warn("认定事实生成前置条件验证失败，caseImportId: {}, 错误: {}", caseImportId, e.getMessage());
            return ApiResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("启动认定事实生成任务失败，caseImportId: {}", caseImportId, e);
            return ApiResult.error("启动认定事实生成任务失败: " + e.getMessage());
        }
    }
}