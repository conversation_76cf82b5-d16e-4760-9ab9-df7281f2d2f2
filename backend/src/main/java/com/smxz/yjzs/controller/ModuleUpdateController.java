package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.entity.ModuleUpdateRecord;
import com.smxz.yjzs.service.ModuleUpdateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模块更新状态管理控制器
 */
@Slf4j
@Tag(name = "模块更新状态管理")
@RequestMapping("/api/module-update")
@RestController
public class ModuleUpdateController {

    @Autowired
    private ModuleUpdateService moduleUpdateService;

    @Operation(summary = "获取案件所有模块更新状态")
    @GetMapping("/status/{caseImportId}")
    public ApiResult<Map<String, Boolean>> getAllModuleStatus(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        try {
            Map<String, Boolean> statusMap = moduleUpdateService.getAllModuleStatus(caseImportId);
            return ApiResult.success(statusMap);
        } catch (Exception e) {
            log.error("获取模块状态失败，案件ID: {}", caseImportId, e);
            return ApiResult.error("获取模块状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取案件特定模块更新状态（新逻辑）")
    @GetMapping("/status/{caseImportId}/{currentModule}")
    public ApiResult<Map<String, Boolean>> getAllModuleStatusNew(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "当前模块代码") @PathVariable String currentModule) {
        try {
            Map<String, Boolean> statusMap = moduleUpdateService.getAllModuleStatusNew(caseImportId, currentModule);
            return ApiResult.success(statusMap);
        } catch (Exception e) {
            log.error("获取模块状态失败，案件ID: {}, 模块: {}", caseImportId, currentModule, e);
            return ApiResult.error("获取模块状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查特定模块是否需要更新（新逻辑）")
    @GetMapping("/check/{caseImportId}/{sourceModule}/{operatedModule}")
    public ApiResult<Boolean> checkModuleNeedUpdateNew(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "源模块代码") @PathVariable String sourceModule,
            @Parameter(description = "目标操作模块代码") @PathVariable String operatedModule) {
        try {
            boolean needUpdate = moduleUpdateService.checkModuleNeedUpdateNew(caseImportId, sourceModule, operatedModule);
            return ApiResult.success(needUpdate);
        } catch (Exception e) {
            log.error("检查模块更新状态失败，案件ID: {}, 源模块: {}, 目标模块: {}", caseImportId, sourceModule, operatedModule, e);
            return ApiResult.error("检查模块更新状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "标记模块已操作")
    @PostMapping("/mark-operated/{caseImportId}")
    public ApiResult<Boolean> markModuleAsOperated(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "源模块代码") @RequestParam String sourceModule,
            @Parameter(description = "已操作模块代码") @RequestParam String operatedModule) {
        try {
            moduleUpdateService.markModuleAsOperated(caseImportId, sourceModule, operatedModule);
            return ApiResult.success(true, "模块操作标记成功");
        } catch (Exception e) {
            log.error("标记模块操作失败，案件ID: {}, 源模块: {}, 操作模块: {}", caseImportId, sourceModule, operatedModule, e);
            return ApiResult.error("标记模块操作失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取需要更新的模块列表")
    @GetMapping("/need-update/{caseImportId}")
    public ApiResult<List<String>> getNeedUpdateModules(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        try {
            List<String> needUpdateModules = moduleUpdateService.getNeedUpdateModules(caseImportId);
            return ApiResult.success(needUpdateModules);
        } catch (Exception e) {
            log.error("获取需要更新的模块列表失败，案件ID: {}", caseImportId, e);
            return ApiResult.error("获取需要更新的模块列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "重置特定模块更新状态")
    @PostMapping("/reset/{caseImportId}/{moduleCode}")
    public ApiResult<Boolean> resetModuleStatus(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "模块代码") @PathVariable String moduleCode) {
        try {
            moduleUpdateService.resetModuleStatus(caseImportId, moduleCode);
            return ApiResult.success(true, "模块状态重置成功");
        } catch (Exception e) {
            log.error("重置模块状态失败，案件ID: {}, 模块: {}", caseImportId, moduleCode, e);
            return ApiResult.error("重置模块状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量重置模块更新状态")
    @PostMapping("/reset-batch/{caseImportId}")
    public ApiResult<Boolean> batchResetModuleStatus(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "模块代码列表，为空则重置所有模块") @RequestBody(required = false) List<String> moduleCodes) {
        try {
            moduleUpdateService.batchResetModuleStatus(caseImportId, moduleCodes);
            return ApiResult.success(true, "批量重置模块状态成功");
        } catch (Exception e) {
            log.error("批量重置模块状态失败，案件ID: {}, 模块: {}", caseImportId, moduleCodes, e);
            return ApiResult.error("批量重置模块状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "重置案件所有模块状态")
    @PostMapping("/reset-all/{caseImportId}")
    public ApiResult<Boolean> resetAllModuleStatus(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        try {
            moduleUpdateService.batchResetModuleStatus(caseImportId, null);
            return ApiResult.success(true, "重置所有模块状态成功");
        } catch (Exception e) {
            log.error("重置所有模块状态失败，案件ID: {}", caseImportId, e);
            return ApiResult.error("重置所有模块状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取案件模块更新记录详情")
    @GetMapping("/records/{caseImportId}")
    public ApiResult<List<ModuleUpdateRecord>> getModuleUpdateRecords(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId) {
        try {
            List<ModuleUpdateRecord> records = moduleUpdateService.getModuleUpdateRecords(caseImportId);
            return ApiResult.success(records);
        } catch (Exception e) {
            log.error("获取模块更新记录失败，案件ID: {}", caseImportId, e);
            return ApiResult.error("获取模块更新记录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "手动标记模块需要更新")
    @PostMapping("/mark/{caseImportId}/{moduleCode}")
    public ApiResult<Boolean> markModuleNeedUpdate(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportId,
            @Parameter(description = "模块代码") @PathVariable String moduleCode) {
        try {
            moduleUpdateService.markModuleNeedUpdate(caseImportId, moduleCode);
            return ApiResult.success(true, "标记模块需要更新成功");
        } catch (Exception e) {
            log.error("标记模块需要更新失败，案件ID: {}, 模块: {}", caseImportId, moduleCode, e);
            return ApiResult.error("标记模块需要更新失败: " + e.getMessage());
        }
    }
}
