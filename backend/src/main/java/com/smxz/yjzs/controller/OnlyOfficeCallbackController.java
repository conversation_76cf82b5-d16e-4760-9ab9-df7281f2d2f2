package com.smxz.yjzs.controller;

import com.smxz.yjzs.service.OnlyOfficeCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/onlyoffice")
public class OnlyOfficeCallbackController {

    @Autowired
    private OnlyOfficeCallbackService onlyOfficeCallbackService;

    /**
     * OnlyOffice 回调处理
     *
     * @param documentId 文档ID
     * @param callbackData 回调数据
     * @return 处理结果
     */
    @PostMapping("/callback")
    public Map<String, Object> handleCallback(@RequestParam("id") Long documentId,
                                              @RequestBody Map<String, Object> callbackData) {
        return onlyOfficeCallbackService.handleCallback(documentId, callbackData);
    }
} 