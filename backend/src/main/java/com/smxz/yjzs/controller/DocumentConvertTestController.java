/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.yjzs.controller;

// DocumentConvertService 将在运行时通过Spring自动注入
import com.smxz.document.convert.DocumentConvertService;
import com.smxz.yjzs.service.FileHandlerService;
import com.smxz.yjzs.dto.response.FileUploadResponse;
import com.smxz.yjzs.common.utils.UUIDHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.FilenameUtils;
import io.minio.*;
import io.minio.http.Method;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 诚
 * @description PDF转Word功能测试接口
 * @since 2025-08-04
 **/
@RestController
@RequestMapping("/api/document-convert-test")
@Tag(name = "文档转换测试", description = "PDF转Word功能测试相关接口")
@Slf4j
public class DocumentConvertTestController {

    @Autowired
    private DocumentConvertService documentConvertService;
    
    @Autowired
    private MinioClient minioClient;
    
    @Value("${minio.bucketName:uploads}")
    private String minioBucketName;

    @Operation(summary = "测试PDF转Word")
    @PostMapping(value = "/pdf-to-word")
    public ResponseEntity<String> pdfToWord(MultipartFile file) {
        try {
            // 创建临时文件
            File tempPdfFile = File.createTempFile("temp_pdf_", ".pdf");
            File tempWordFile = File.createTempFile("temp_word_", ".docx");

            try {
                // 保存上传的PDF文件到临时文件
                file.transferTo(tempPdfFile);

                // 调用pdfToWord方法（文件到文件）
                documentConvertService.pdfToWord(tempPdfFile, tempWordFile);

                // 检查转换后的文件是否存在且有内容
                log.info("转换后的Word文件大小: {} bytes", tempWordFile.length());
                
                // 将转换后的Word文件上传到MinIO
                String originalFilename = file.getOriginalFilename();
                String filename = FilenameUtils.getBaseName(originalFilename) + ".docx";
                String s3filename = UUIDHelper.generateUUID() + ".docx";
                
                try (FileInputStream fis = new FileInputStream(tempWordFile)) {
                    ObjectWriteResponse response = minioClient.putObject(
                            PutObjectArgs.builder()
                                    .bucket(minioBucketName)
                                    .object(s3filename)
                                    .stream(fis, tempWordFile.length(), -1)
                                    .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                                    .build());
                    
                    // 生成预签名下载链接（有效期1小时）
                    String downloadUrl = minioClient.getPresignedObjectUrl(
                            GetPresignedObjectUrlArgs.builder()
                                    .method(Method.GET)
                                    .bucket(minioBucketName)
                                    .object(s3filename)
                                    .expiry(1, TimeUnit.HOURS)
                                    .build());
                    
                    log.info("Word文件已上传到MinIO，文件ID: {}，下载链接: {}", response.object(), downloadUrl);
                    
                    return ResponseEntity.ok(downloadUrl);
                } catch (Exception e) {
                    log.error("上传文件到MinIO失败", e);
                    throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
                }

            } finally {
                // 清理临时文件
                if (tempPdfFile.exists()) {
                    tempPdfFile.delete();
                }
                if (tempWordFile.exists()) {
                    tempWordFile.delete();
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
