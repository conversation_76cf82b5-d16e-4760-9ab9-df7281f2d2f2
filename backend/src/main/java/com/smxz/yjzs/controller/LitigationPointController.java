package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.UpdateLitigationPointPairRequest;
import com.smxz.yjzs.dto.request.SaveLitigationPointsRequest;
import com.smxz.yjzs.dto.response.LitigationPointsResponse;
import com.smxz.yjzs.service.impl.LitigationPointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 诉辩观点控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/litigationPoints")
@Tag(name = "诉辩观点管理", description = "诉辩观点相关接口")
public class LitigationPointController {

    @Autowired
    private LitigationPointService litigationPointService;

    /**
     * 获取案件的诉辩观点数据
     */
    @Operation(summary = "获取诉辩观点数据", description = "获取指定案件的所有诉辩观点、争议焦点和关系数据")
    @GetMapping("/{caseImportId}/getLitigationPoints")
    public ApiResult<LitigationPointsResponse> getLitigationPoints(
            @Parameter(description = "案件导入ID") @PathVariable("caseImportId") Long caseImportId) {
        log.info("获取诉辩观点数据，caseImportId: {}", caseImportId);
        LitigationPointsResponse response = litigationPointService.getLitigationPointsData(caseImportId);
        return ApiResult.success(response);
    }

    /**
     * 保存诉辩观点
     */
    @Operation(summary = "保存诉辩观点", description = "保存用户添加的诉辩观点和关系")
    @PostMapping("/save")
    public ApiResult<Map<String, Object>> saveLitigationPoints(
            @Parameter(description = "诉辩观点数据") @RequestBody SaveLitigationPointsRequest request) {
        log.info("保存诉辩观点，caseImportId: {}, 观点数量: {}", 
                request.getCaseImportId(), request.getPoints().size());
        
        Map<String, Object> result = litigationPointService.saveLitigationPoints(request);
        return ApiResult.success(result, "诉辩观点保存成功");
    }

    /**
     * 删除诉辩观点
     */
    @Operation(summary = "删除诉辩观点", description = "删除指定的诉辩观点")
    @DeleteMapping("/delete/{pointId}")
    public ApiResult<Map<String, Object>> deleteLitigationPoint(
            @Parameter(description = "观点ID") @PathVariable("pointId") Long pointId) {
        log.info("删除诉辩观点，pointId: {}", pointId);
        
        Map<String, Object> result = litigationPointService.deleteLitigationPoint(pointId);
        return ApiResult.success(result, "观点删除成功");
    }

    /**
     * 更新诉辩观点
     */
    @Operation(summary = "更新诉辩观点", description = "更新指定的诉辩观点内容")
    @PutMapping("/update/{pointId}")
    public ApiResult<Map<String, Object>> updateLitigationPoint(
            @Parameter(description = "观点ID") @PathVariable("pointId") Long pointId,
            @Parameter(description = "更新数据") @RequestBody Map<String, Object> updateData) {
        log.info("更新诉辩观点，pointId: {}", pointId);

        Map<String, Object> result = litigationPointService.updateLitigationPoint(pointId, updateData);
        return ApiResult.success(result, "观点更新成功");
    }

    /**
     * 更新诉辩观点（简化版本）
     */
    @Operation(summary = "更新诉辩观点", description = "更新指定的诉辩观点内容（简化版本）")
    @PutMapping("/updatePoint/{pointId}")
    public ApiResult<Map<String, Object>> updateLitigationPointSimple(
            @Parameter(description = "观点ID") @PathVariable("pointId") Long pointId,
            @Parameter(description = "更新数据") @RequestBody Map<String, Object> updateData) {
        log.info("更新诉辩观点（简化版本），pointId: {}", pointId);

        Map<String, Object> result = litigationPointService.updateLitigationPoint(pointId, updateData);
        return ApiResult.success(result, "观点更新成功");
    }

    /**
     * 添加诉辩关系
     */
    @Operation(summary = "添加诉辩关系", description = "在诉方观点和辩方观点之间建立关系")
    @PostMapping("/relation/add")
    public ApiResult<Map<String, Object>> addLitigationRelation(
            @Parameter(description = "关系数据") @RequestBody Map<String, Object> relationData) {
        log.info("添加诉辩关系，数据: {}", relationData);
        
        Map<String, Object> result = litigationPointService.addLitigationRelation(relationData);
        return ApiResult.success(result, "关系添加成功");
    }

    /**
     * 更新诉辩关系
     */
    @Operation(summary = "更新诉辩关系", description = "更新指定的诉辩关系")
    @PutMapping("/relation/update/{relationId}")
    public ApiResult<Map<String, Object>> updateLitigationRelation(
            @Parameter(description = "关系ID") @PathVariable("relationId") Long relationId,
            @Parameter(description = "更新数据") @RequestBody Map<String, Object> updateData) {
        log.info("更新诉辩关系，relationId: {}", relationId);
        
        Map<String, Object> result = litigationPointService.updateLitigationRelation(relationId, updateData);
        return ApiResult.success(result, "关系更新成功");
    }

    /**
     * 批量更新诉辩观点和关系
     */
    @Operation(summary = "批量更新诉辩观点和关系", description = "同时更新诉方、辩方观点和它们之间的关系")
    @PutMapping("/updatePointPair")
    public ApiResult<Map<String, Object>> updateLitigationPointPair(
            @Parameter(description = "更新数据") @RequestBody UpdateLitigationPointPairRequest request) {
        log.info("批量更新诉辩观点对，案件ID: {}", request.getCaseImportId());

        Map<String, Object> result = litigationPointService.updateLitigationPointPair(request);
        return ApiResult.success(result, "观点对更新成功");
    }

    /**
     * 删除诉辩关系
     */
    @Operation(summary = "删除诉辩关系", description = "删除指定的诉辩关系")
    @DeleteMapping("/relation/delete/{relationId}")
    public ApiResult<Map<String, Object>> deleteLitigationRelation(
            @Parameter(description = "关系ID") @PathVariable("relationId") Long relationId) {
        log.info("删除诉辩关系，relationId: {}", relationId);

        Map<String, Object> result = litigationPointService.deleteLitigationRelation(relationId);
        return ApiResult.success(result, "关系删除成功");
    }

    /**
     * 重新生成诉辩关系
     *
     * @param caseImportId 案件导入ID
     * @param customPrompt 自定义提示词
     * @return 操作结果
     */
    @PostMapping("/regenerate/{caseImportId}")
    @Operation(summary = "重新生成诉辩关系", description = "重新生成诉辩关系，会覆盖原有数据")
    public ApiResult<String> regenerate(@Parameter(description = "案件导入ID", required = true) @PathVariable Long caseImportId) {
        // 先删除原有数据
        litigationPointService.removeDataForRegenerate(caseImportId);
        // 直接执行生成任务
        litigationPointService.generate(caseImportId);

        return ApiResult.success("诉辩关系重新生成任务已启动");
    }
}
