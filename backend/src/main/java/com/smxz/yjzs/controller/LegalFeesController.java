package com.smxz.yjzs.controller;

import com.smxz.yjzs.common.ApiResult;
import com.smxz.yjzs.dto.LegalFeesWithPartyDTO;
import com.smxz.yjzs.entity.LegalFees;
import com.smxz.yjzs.service.impl.LegalFeesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 诉讼费控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/legalFees")
@Tag(name = "诉讼费管理", description = "诉讼费相关接口")
public class LegalFeesController {

    @Autowired
    private LegalFeesService legalFeesService;

    /**
     * 获取案件的诉讼费列表（包含当事人信息）
     */
    @Operation(summary = "获取诉讼费列表", description = "获取指定案件的所有诉讼费信息，包含当事人姓名和诉讼地位")
    @GetMapping("/list")
    public ApiResult<List<LegalFeesWithPartyDTO>> list(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId) {
        log.info("获取诉讼费列表，caseImportRecordId: {}", caseImportRecordId);
        List<LegalFeesWithPartyDTO> legalFeesList = legalFeesService.listWithPartyInfoByCaseImportId(caseImportRecordId);
        return ApiResult.success(legalFeesList);
    }

    /**
     * 获取案件的诉讼费列表（基础信息）
     */
    @Operation(summary = "获取诉讼费基础列表", description = "获取指定案件的所有诉讼费基础信息")
    @GetMapping("/basic/list")
    public ApiResult<List<LegalFees>> basicList(
            @Parameter(description = "案件导入ID") @RequestParam Long caseImportRecordId) {
        log.info("获取诉讼费基础列表，caseImportRecordId: {}", caseImportRecordId);
        List<LegalFees> legalFeesList = legalFeesService.listByCaseImportId(caseImportRecordId);
        return ApiResult.success(legalFeesList);
    }

    /**
     * 保存诉讼费信息
     */
    @Operation(summary = "保存诉讼费信息", description = "保存单个诉讼费记录")
    @PostMapping("/save")
    public ApiResult<Boolean> save(@RequestBody LegalFees legalFees) {
        log.info("保存诉讼费信息: {}", legalFees);
        boolean result = legalFeesService.save(legalFees);
        return ApiResult.success(result);
    }

    /**
     * 批量保存诉讼费信息
     */
    @Operation(summary = "批量保存诉讼费信息", description = "批量保存诉讼费记录")
    @PostMapping("/saveBatch")
    public ApiResult<Boolean> saveBatch(@RequestBody List<LegalFees> legalFeesList) {
        log.info("批量保存诉讼费信息，数量: {}", legalFeesList.size());
        boolean result = legalFeesService.saveBatch(legalFeesList);
        return ApiResult.success(result);
    }

    /**
     * 更新诉讼费信息
     */
    @Operation(summary = "更新诉讼费信息", description = "更新诉讼费记录")
    @PutMapping("/update")
    public ApiResult<Boolean> update(@RequestBody LegalFees legalFees) {
        log.info("更新诉讼费信息: {}", legalFees);
        boolean result = legalFeesService.updateById(legalFees);
        return ApiResult.success(result);
    }

    /**
     * 删除诉讼费信息
     */
    @Operation(summary = "删除诉讼费信息", description = "根据ID删除诉讼费记录")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Boolean> delete(@Parameter(description = "诉讼费ID") @PathVariable Long id) {
        log.info("删除诉讼费信息，id: {}", id);
        boolean result = legalFeesService.removeById(id);
        return ApiResult.success(result);
    }

    /**
     * 根据案件ID删除诉讼费信息
     */
    @Operation(summary = "根据案件ID删除诉讼费信息", description = "删除指定案件的所有诉讼费记录")
    @DeleteMapping("/deleteByCaseId/{caseImportRecordId}")
    public ApiResult<Boolean> deleteByCaseId(
            @Parameter(description = "案件导入ID") @PathVariable Long caseImportRecordId) {
        log.info("根据案件ID删除诉讼费信息，caseImportRecordId: {}", caseImportRecordId);
        legalFeesService.deleteByCaseImportId(caseImportRecordId);
        return ApiResult.success(true);
    }
}
