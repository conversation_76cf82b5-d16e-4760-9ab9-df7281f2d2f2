package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 诉辩观点实体类
 */
@Data
@TableName(value = "litigation_points", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LitigationPoint {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    private Long caseImportId;

    /**
     * 当事人姓名
     */
    private String partyName;

    /**
     * 诉辩方(诉方/辩方)
     */
    private String side;

    /**
     * 观点内容
     */
    private String content;

    /**
     * 观点日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate pointDate;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * AI返回的原始ID（如P1, P2, D1, D2等）
     * 用于建立诉辩关系的映射
     */
    private String pointId;

    /**
     * 证据信息，JSON格式，包含文件ID、文件名、高亮文本和位置信息
     * 格式：[{"fileId":1,"fileName":"xxx.pdf","highlight":"高亮文本","locations":[{...}],"pageNumber":1}]
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> evidence;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 