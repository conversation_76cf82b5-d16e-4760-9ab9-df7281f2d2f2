package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.extractor.common.model.BaseResponse;
import com.smxz.extractor.common.model.extractor.DocumentExtractorResult;
import com.smxz.ocr.model.OcrPreview;
import com.smxz.ocr.model.OcrResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 文件上传记录实体类
 * 用于记录系统中所有文件的上传信asjkljkladsjkldfs息
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "file_upload_records", autoResultMap = true)
public class FileUploadRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件导入ID
     * 关联到案件导入记录表
     */
    private Long caseImportId;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件存储路径
     * 可以是本地路径或MinIO对象存储路径
     */
    private String filePath;

    /**
     * PDF文件存储路径
     * 用于存储转换后的PDF文件路径
     * 当文件类型为PDF时，该字段与filePath相同
     */
    private String pdfPath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     * 例如：pdf, doc, docx, zip, rar等
     */
    private String fileType;

    /**
     * 上传者ID
     */
    private Integer uploaderId;

    /**
     * 上传者姓名
     */
    private String uploaderName;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 文件状态
     * 0: 待处理
     * 1: 处理中
     * 2: 处理完成
     * 3: 处理失败
     */
    private Integer status;

    /**
     * 文件MD5哈希值
     * 用于文件完整性校验和去重
     */
    private String md5Hash;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 提取的文本内容
     * 存储从PDF文件中提取的完整文本内容
     */
    private String extractedText;

    /**
     * OCR识别结果
     * 存储结构化的OCR识别数据，JSON格式
     * 包含文字内容、坐标信息等
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<OcrResult> ocrResult;

    /**
     * OCR预览信息
     * 存储OCR预览图片信息，JSON格式
     * 包含原图、预处理图等预览数据
                                                                                                                                                                                         * 特殊值用于表示OCR状态：
     * - null或空列表：未开始OCR
     * - 包含单个空对象的列表：OCR处理中
     * - 包含有效数据的列表：OCR处理成功
     * - 包含特殊标记(-1)的列表：OCR处理失败
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<OcrPreview> ocrPreviews;

    /**
     * 逻辑删除标识
     * 0: 未删除
     * 1: 已删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 删除时间
     */
    private LocalDateTime deletedTime;

    /**
     * 删除人
     */
    private String deletedBy;


    private DocumentType documentType;


    /**
     * 要素提取结果
     * JSON格式
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private DocumentExtractorResult extract_element_result;

    /**
     * OCR开始时间
     */
    private LocalDateTime ocrStartTime;

    /**
     * OCR结束时间
     */
    private LocalDateTime ocrEndTime;

    /**
     * OCR耗时（毫秒）
     */
    private Long ocrDuration;
}
