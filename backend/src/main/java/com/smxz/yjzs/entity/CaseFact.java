package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 案件事实实体类
 * 用于存储从裁定书中提取的事实认定内容
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "case_fact", autoResultMap = true)
public class CaseFact {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    private Long caseImportId;

    /**
     * 认定事实
     */
    private String recognizedFacts;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
