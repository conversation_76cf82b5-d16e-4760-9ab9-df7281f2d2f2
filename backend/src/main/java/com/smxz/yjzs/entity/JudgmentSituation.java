package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 判项情况实体类
 * 用于记录案件中各当事人的判决结果和意见
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "judgment_situation", autoResultMap = true)
public class JudgmentSituation {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件导入记录ID
     */
    private Long caseImportRecordId;

    /**
     * 判决结果
     */
    private String judgment;

    /**
     * 意见
     */
    private Integer opinion;

    /**
     * 新判决结果
     * 当意见为3变更时有值
     */
    private String newJudgment;

    /**
     * 排序字段
     */
    private Integer xh;

    /**
     * 文书案件类型
     */
    private String documentCaseType;

    /**
     * 1-提取，2-新增
     */
    private Integer dataType;

    /**
     * 当事人姓名
     */
    private String dsrxm;

    /**
     * 诉讼地位
     */
    private String ssdw;
}
