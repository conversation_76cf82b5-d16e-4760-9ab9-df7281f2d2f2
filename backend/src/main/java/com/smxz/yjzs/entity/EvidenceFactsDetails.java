package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 证据事实详情表实体类
 * 用于记录案件中的证据分析结果，包括争议事实、法官意见等
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("evidence_facts_details")
public class EvidenceFactsDetails implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属案件的唯一标识
     * 关联案件导入记录表
     */
    private Long caseImportId;

    /**
     * 无争议事实
     * 记录双方当事人均认可的事实内容
     */
    private String undisputedFacts;

    /**
     * 有争议事实
     * 记录双方当事人存在争议的事实内容
     */
    private String controversialFacts;

    /**
     * 法官意见
     * 记录法官对证据的分析和意见
     */
    private String dictum;

    /**
     * 认定事实-模型生成
     * 记录最终认定的事实内容
     */
    private String determineFacts;

    /**
     * 认定事实-从文书提取
     * 记录最终认定的事实内容
     */
    private String determineFactsExtract;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 逻辑删除标识
     * 0: 未删除
     * 1: 已删除
     */
    @TableLogic
    private Integer deleted;
} 