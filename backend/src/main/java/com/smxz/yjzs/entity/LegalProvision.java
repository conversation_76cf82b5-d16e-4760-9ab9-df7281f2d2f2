package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 法条实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("legal_provisions")
public class LegalProvision {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 法律名称（如"刑法"）
     */
    private String lawName;

    /**
     * 条号
     */
    private Integer articleNo;

    /**
     * 款号
     */
    private Integer paragraphNo;

    /**
     * 项号
     */
    private Integer subparagraphNo;

    /**
     * 条文内容
     */
    private String content;

    /**
     * 父节点ID
     */
    private Long parentId;

    /**
     * 层级：0=法律,1=条,2=款,3=项
     */
    private Integer level;
}
