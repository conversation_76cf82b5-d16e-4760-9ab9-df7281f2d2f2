package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 诉讼费实体类
 * 用于记录案件中各当事人的诉讼费用信息
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "legal_fees", autoResultMap = true)
public class LegalFees {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件导入记录ID
     */
    private Long caseImportRecordId;

    /**
     * 当事人主键ID
     */
    private Long casePartyId;

    /**
     * 费用类型
     */
    private String feeType;

    /**
     * 已缴金额
     */
    private BigDecimal amountPaid;

    /**
     * 承担金额
     */
    private BigDecimal amountToBear;


    /**
     * 当事人姓名
     */
    private String dsrxm;

    /**
     * 诉讼地位
     */
    private String ssdw;
}
