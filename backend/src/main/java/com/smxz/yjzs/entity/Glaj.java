package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 关联案件实体类
 * 用于存储案件的关联案件信息
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "glaj", autoResultMap = true)
public class Glaj {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件导入记录ID
     */
    private Long caseImportRecordId;

    /**
     * 关联案件案号
     */
    private String ah;

    /**
     * 法院名称
     */
    private String corpName;

    /**
     * 关联关系
     * 1-原审，2-后继
     */
    private Integer glgx;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;
}
