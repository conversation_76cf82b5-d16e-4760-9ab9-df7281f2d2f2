package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模块更新记录实体类
 * 用于跟踪和管理系统中各模块的更新状态
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "module_update_records", autoResultMap = true)
public class ModuleUpdateRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的案件导入ID
     */
    private Long caseImportId;

    /**
     * 模块代码
     * 对应ModuleType枚举的code值
     */
    private String moduleCode;

    /**
     * 是否需要更新
     * 0: 不需要更新
     * 1: 需要更新
     */
    private Integer hasUpdate;

    /**
     * 已操作的模块列表
     * 存储已经点击操作过的模块代码，用逗号分隔
     * 例如：EVIDENCE_OVERVIEW,DETERMINE_FACTS
     */
    private String operateModule;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
