package com.smxz.yjzs.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文书生成信息实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("document_generation_info")
public class DocumentGenerationInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属案件的唯一标识
     */
    private Long caseImportId;

    /**
     * 案件类型：民事一审
     */
    private String caseTrialType;

    /**
     * 文书类型：民事判决书
     *
     * 民事一审：civil_judgement_1
     * 民事二审驳回上诉：civil_judgement_2_rejec
     * 民事二审改判：civil_judgement_2_overrule
     * 民事二审部分改判：civil_judgement_2_partial_overrule'
     *
     */
    private String documentType;

    /**
     * 复杂程度
     */
    private String complexity;

    /**
     * 法官意见
     */
    private String judgeOpinion;

    /**
     * 文书内容
     */
    private String documentContent;

    /**
     * 文书地址
     */
    private String documentUrl;

    /**
     * onlyOffice -- 文书key
     */
    private String documentKey;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 生成人
     */
    private String createBy;


    /**
     * 书签
     */
    private String bookmark;
    
} 