package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 诉辩关联关系实体类
 */
@Data
@TableName(value = "litigation_relations", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LitigationRelation {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    private Long caseImportId;

    /**
     * 诉方观点AI ID（如P1, P2等）
     */
    private String plaintiffPointId;

    /**
     * 辩方观点AI ID（如D1, D2等）
     */
    private String defendantPointId;

    /**
     * 关系类型(否认/部分否认/补充/同意等)
     */
    private String relationType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 