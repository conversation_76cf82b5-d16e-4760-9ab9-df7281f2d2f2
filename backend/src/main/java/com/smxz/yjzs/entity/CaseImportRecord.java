package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 案件导入记录实体类
 * 用于记录案件批量导入的处理信息
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "case_import_records", autoResultMap = true)
public class CaseImportRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件名称
     */
    private String caseName;

    /**
     * 案件编号
     */
    private String caseCode;

    /**
     * 案件类型
     */
    private String ajlxdm;

    /**
     * 案由
     * 从庭审笔录中提取的案件性质/类型
     */
    private String caseCause;

    /**
     * 导入文件路径
     */
    private String importFilePath;

    /**
     * 案件分析状态
     * 0: 分析中
     * 1: 已完成
     */
    private Integer importStatus;

    /**
     * 导入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime importTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 导入人员ID
     */
    private Long importerId;

    /**
     * 导入人员姓名
     */
    private String importerName;

    /**
     * 错误信息
     * 导入失败时记录具体错误原因
     */
    private String errorMessage;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 成功导入数
     */
    private Integer successCount;

    /**
     * 失败导入数
     */
    private Integer failCount;

    /**
     * 文件处理状态
     * 0: 处理中
     * 1: 已完成
     */
    private Integer fileStatus;

    /**
     * 逻辑删除标识
     * 0: 未删除
     * 1: 已删除
     */
    @TableLogic
    private Integer deleted;


    /**
     * 0:简单类型案件
     * 1:普通案件类型
     */
    private Integer caseType;

    /**
     * 法院名称
     */
    private String corpName;

    /**
     * 立案日期
     */
    private String larq;

}