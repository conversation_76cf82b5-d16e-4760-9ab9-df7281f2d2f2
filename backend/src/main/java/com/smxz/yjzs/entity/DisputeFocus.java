package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 争议焦点实体类
 */
@Data
@TableName(value = "dispute_focuses", autoResultMap = true)
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DisputeFocus {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联案件导入ID
     */
    private Long caseImportId;

    /**
     * 争议焦点内容概述
     */
    private String description;

    /**
     * 原告主张内容
     */
    private String plaintiffClaim;

    /**
     * 被告主张内容
     */
    private String defendantClaim;

    /**
     * 诉方证据信息，JSON格式，包含文件ID、文件名、高亮文本和位置信息
     * 格式：[{"fileId":1,"fileName":"xxx.pdf","highlight":"高亮文本","locations":[{...}],"pageNumber":1}]
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> plaintiffEvidence;

    /**
     * 辩方证据信息，JSON格式，包含文件ID、文件名、高亮文本和位置信息
     * 格式：[{"fileId":1,"fileName":"xxx.pdf","highlight":"高亮文本","locations":[{...}],"pageNumber":1}]
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> defendantEvidence;
    
    /**
     * 法条引用，JSON格式的字符串数组
     * 格式：["《民法典》第1062条", "《民法典》第1063条", "《民法典》第657条"]
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> laws;
    
    /**
     * 法条详情，JSON格式的字符串数组，与laws字段一一对应
     * 格式：["《民法典》第1062条：夫妻在婚姻关系存续期间所得的下列财产...", "《民法典》第1063条：下列财产为夫妻一方的个人财产...", "..."]
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> lawSummaries;

    /**
     * 争议焦点结论
     * 当选择"是"时保存"是"，选择"否"时保存"否"，选择"其他"时保存具体内容
     */
    private String conclusion;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 