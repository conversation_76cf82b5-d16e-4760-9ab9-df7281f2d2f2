package com.smxz.yjzs.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用证据实体类
 * 用于存储文件证据信息，包括文件ID、文件名、页码、高亮文本和位置信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Evidence {

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 高亮文本内容
     */
    private String highlight;


}
