package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 诚
 * @since 2025-05-16 17:53
 **/
@Data
@TableName(value = "relation_link",autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelationLink {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caseImportId;

    /**
     * 联系id
     */
    private String rid;

    /**
     * 源实体
     */
    private String source;

    /**
     * 目标实体
     */
    private String target;

    /**
     * 关系：婚姻关系、委托关系
     */
    private String type;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> evidence;
}
