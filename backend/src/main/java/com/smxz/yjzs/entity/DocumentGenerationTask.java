package com.smxz.yjzs.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文书生成任务实体
 */
@Data
public class DocumentGenerationTask {
    
    /**
     * 文书ID
     */
    private String documentId;
    
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 任务状态
     * RUNNING: 运行中
     * WAITING: 等待中
     * COMPLETED: 已完成
     * TIMEOUT: 超时
     */
    private TaskStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    public DocumentGenerationTask() {
        this.createTime = LocalDateTime.now();
    }
    
    public DocumentGenerationTask(String documentId) {
        this.documentId = documentId;
        this.createTime = LocalDateTime.now();
        this.status = TaskStatus.WAITING;
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        RUNNING,
        WAITING, 
        COMPLETED,
        TIMEOUT
    }
    
    /**
     * 标记任务开始运行
     */
    public void markAsRunning() {
        this.status = TaskStatus.RUNNING;
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 标记任务完成
     */
    public void markAsCompleted() {
        this.status = TaskStatus.COMPLETED;
    }
    
    /**
     * 标记任务超时
     */
    public void markAsTimeout() {
        this.status = TaskStatus.TIMEOUT;
    }
    
    /**
     * 检查任务是否超时
     * @param timeoutHours 超时小时数
     * @return 是否超时
     */
    public boolean isTimeout(int timeoutHours) {
        if (startTime == null) {
            return false;
        }
        return startTime.plusHours(timeoutHours).isBefore(LocalDateTime.now());
    }
}