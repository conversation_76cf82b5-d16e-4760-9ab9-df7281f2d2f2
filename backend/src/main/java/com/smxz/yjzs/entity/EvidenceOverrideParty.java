package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("evidence_overview_party")
public class EvidenceOverrideParty {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caseImportId;

    private Long evidenceOverviewId;

    private String partyName;

    private String partyType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
