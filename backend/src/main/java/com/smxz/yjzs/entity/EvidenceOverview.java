package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "evidence_overview", autoResultMap = true)
public class EvidenceOverview {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "案件导入id")
    private Long caseImportId;

    @Schema(title = "当事人类型")
    private String partyType;

    @Schema(title = "当事人姓名")
    private String partyName;

    @Schema(title = "证据名称")
    private String evidenceName;

    @Schema(title = "证明目的")
    private String evidencePurpose;

    @Schema(title = "证明综述")
    private String evidenceSummary;

    @Schema(title = "对应的材料文件名")
    private Long originalFileId;

    @Schema(title = "对应的材料文件名")
    private String originalFileName;

    @Schema(title = "引用的起始页")
    private Integer originalFileStartPage;

    @Schema(title = "是否采纳")
    private Boolean adopted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
