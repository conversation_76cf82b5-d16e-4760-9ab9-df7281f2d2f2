package com.smxz.yjzs.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputePoint {

    private String caseCause;

    private List<LitigationPoint> points;

    private List<LitigationRelation> relations;

    private List<DisputeFocus> disputes;
}
