package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 诚
 * @since 2025-05-16 15:47
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "relation_role", autoResultMap = true)
public class RelationRole {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caseImportId;

    /**
     * 实体id
     */
    private String eid;

    /**
     * 实体名称
     */
    private String name;

    /**
     * 实体类型 自然人
      */
    private String type;

    /**
     * 诉讼地位  原告
     */
    private String ssdw;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> evidence;
}
