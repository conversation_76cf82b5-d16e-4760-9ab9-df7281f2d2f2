package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 诉辩观点AI数据接收类
 * 用于接收AI返回的数据，其中id字段为String类型以兼容各种格式（数字或P1、D1等字符串）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LitigationPointAiData {

    /**
     * AI返回的ID（可能是数字或字符串如P1、D1等）
     */
    private String id;

    /**
     * 案件ID
     */
    private Long caseImportId;

    /**
     * 当事人姓名
     */
    private String partyName;

    /**
     * 诉辩方(诉方/辩方)
     */
    private String side;

    /**
     * 观点内容
     */
    private String content;

    /**
     * 观点日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate pointDate;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 证据信息，JSON格式，包含文件ID、文件名、高亮文本和位置信息
     * 格式：[{"fileId":1,"fileName":"xxx.pdf","highlight":"高亮文本","locations":[{...}],"pageNumber":1}]
     */
    private List<Evidence> evidence;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 转换为LitigationPoint实体
     * @return LitigationPoint实体
     */
    public LitigationPoint toLitigationPoint() {
        return LitigationPoint.builder()
                .id(null) // 数据库主键置空，由数据库自动生成
                .caseImportId(this.caseImportId)
                .partyName(this.partyName)
                .side(this.side)
                .content(this.content)
                .pointDate(this.pointDate)
                .sortOrder(this.sortOrder)
                .pointId(this.id) // AI返回的id存储到pointId字段
                .evidence(this.evidence)
                .createTime(this.createTime)
                .updateTime(this.updateTime)
                .build();
    }
}
