package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 矛盾识别实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "contradiction_identification", autoResultMap = true)
public class ContradictionRecognition {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的案件导入ID
     */
    private Long caseImportId;

    /**
     * 当事人类型：原告/被告
     */
    private String type;

    /**
     * 当事人姓名
     */
    private String name;

    /**
     * 矛盾点描述
     */
    private String contradictionPoint;

    /**
     * 详细的矛盾内容描述
     */
    private String content;

    /**
     * 矛盾相关文件信息，JSON格式，使用Evidence溯源对象结构
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Evidence> evidence;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}