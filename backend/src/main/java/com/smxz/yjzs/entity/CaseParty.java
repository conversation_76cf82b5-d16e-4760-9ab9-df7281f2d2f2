package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "case_party", autoResultMap = true)
public class CaseParty {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caseImportId;

    private String partyName;

    private String partyType;

    private String partyLabel;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
