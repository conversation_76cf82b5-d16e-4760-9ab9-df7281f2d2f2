package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 质证情况信息表实体类
 * 用于记录案件中当事人的质证内容及采纳情况
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "verification_evidence_info", autoResultMap = true)
public class VerificationEvidenceInfo implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属案件的唯一标识
     * 关联案件导入记录表
     */
    private Long caseImportId;

    /**
     * 当事人类型
     * 原告/被告
     */
    private String partyType;

    /**
     * 当事人姓名
     */
    private String partyName;

    /**
     * 庭审调查内容
     * 记录法庭调查阶段的询问内容
     */
    private String trialInvestigationContent;

    /**
     * 质证内容
     * JSON格式存储质证详细信息
     */
    private String verificationContent;

    /**
     * 是否采纳
     * 0: 不采纳
     * 1: 采纳
     */
    private Integer isAdopt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 执行时间（毫秒）
     * 记录处理该质证信息所用的时间
     */
    private Long durationMs;

    /**
     * 证据信息，JSON格式，包含文件ID、文件名、高亮文本和位置信息
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<Evidence> evidence;

    /**
     * 逻辑删除标识
     * 0: 未删除
     * 1: 已删除
     */
    @TableLogic
    private Integer deleted;
} 