package com.smxz.yjzs.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库配置实体类
 * 用于存储知识库查询的配置信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "knowledge_base_config", autoResultMap = true)
public class KnowledgeBaseConfig {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 配置键
     * 用于标识不同的知识库配置
     */
    private String configKey;

    /**
     * 配置名称
     * 配置的显示名称
     */
    private String configName;

    /**
     * 关联数据集ID列表
     * 存储为JSON数组
     */
    @TableField(value = "dataset_ids", typeHandler = Fastjson2TypeHandler.class)
    private List<String> datasetIds;

    /**
     * LLM配置
     * 存储为JSON对象
     */
    @TableField(value = "llm_config", typeHandler = Fastjson2TypeHandler.class)
    private LlmConfig llmConfig;

    /**
     * 提示词配置
     * 存储为JSON对象
     */
    @TableField(value = "prompt_config", typeHandler = Fastjson2TypeHandler.class)
    private PromptConfig promptConfig;

    /**
     * 是否启用
     * true: 启用知识库查询
     * false: 使用Agent模型直接提取法条编号
     */
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * LLM配置子对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LlmConfig {
        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 温度参数
         */
        private Float temperature;

        /**
         * Top P参数
         */
        private Float topP;

        /**
         * 存在惩罚参数
         */
        private Float presencePenalty;

        /**
         * 频率惩罚参数
         */
        private Float frequencyPenalty;
    }

    /**
     * 提示词配置子对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromptConfig {
        /**
         * 相似度阈值
         */
        private Float similarityThreshold;

        /**
         * 关键词相似度权重
         */
        private Float keywordsSimilarityWeight;

        /**
         * 返回结果数量
         */
        private Integer topN;

        /**
         * 排序数量
         */
        private Integer topK;

        /**
         * 空响应文本
         */
        private String emptyResponse;

        /**
         * 开场白
         */
        private String opener;

        /**
         * 是否显示引用
         */
        private Boolean showQuote;

        /**
         * 提示词内容
         */
        private String prompt;

        /**
         * 变量配置
         * 提示词中使用的变量列表
         */
        private Object variables;
    }
}
