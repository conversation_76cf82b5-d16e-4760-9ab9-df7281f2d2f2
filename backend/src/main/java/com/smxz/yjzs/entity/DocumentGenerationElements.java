package com.smxz.yjzs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文书生成要素实体类
 * 用于存储文书生成所需的各种要素信息
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "document_generation_elements", autoResultMap = true)
public class DocumentGenerationElements {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件导入记录ID
     */
    private Long caseImportRecordId;

    /**
     * 证据摘要
     */
    private String evidenceSummary;

    /**
     * 当事人基本情况
     */
    private String partyBasicInfo;

    /**
     * 审理经过
     */
    private String trialProcess;

    /**
     * 诉辩信息
     */
    private String litigationDefenseInfo;

    /**
     * 认定事实
     */
    private String recognizedFacts;

    /**
     * 裁判理由
     */
    private String judgmentReason;

    /**
     * 总结_全改
     */
    private String summaryQg;

    /**
     * 总结_部分改
     */
    private String summaryBfg;

    /**
     * 总结_维持
     */
    private String summary;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;
}
