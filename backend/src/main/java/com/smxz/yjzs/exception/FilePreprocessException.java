package com.smxz.yjzs.exception;

import com.smxz.yjzs.common.ResultCode;

/**
 * 文件预处理异常类
 * 专门用于文件预处理服务中的异常处理
 */
public class FilePreprocessException extends BusinessException {

    public FilePreprocessException(String message) {
        super(ResultCode.FILE_PROCESS_ERROR, message);
    }

    public FilePreprocessException(String message, Throwable cause) {
        super(ResultCode.FILE_PROCESS_ERROR.getCode(), message, cause);
    }

    public FilePreprocessException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }

    public FilePreprocessException(ResultCode resultCode, String message, Throwable cause) {
        super(resultCode.getCode(), message, cause);
    }

    /**
     * 文件格式不支持异常
     */
    public static FilePreprocessException unsupportedFileType(String fileType) {
        return new FilePreprocessException(ResultCode.FILE_TYPE_NOT_SUPPORTED, 
                "不支持的文件格式: " + fileType);
    }

    /**
     * 文件转换失败异常
     */
    public static FilePreprocessException conversionFailed(String fileName, Throwable cause) {
        return new FilePreprocessException(ResultCode.FILE_CONVERSION_ERROR, 
                "文件转换失败: " + fileName, cause);
    }

    /**
     * 文件不存在异常
     */
    public static FilePreprocessException fileNotFound(String fileName) {
        return new FilePreprocessException(ResultCode.FILE_NOT_FOUND, 
                "文件不存在: " + fileName);
    }

    /**
     * 压缩包格式不支持异常
     */
    public static FilePreprocessException unsupportedArchiveFormat(String format) {
        return new FilePreprocessException(ResultCode.FILE_TYPE_NOT_SUPPORTED, 
                "仅支持ZIP格式的压缩包，当前格式: " + format);
    }

    /**
     * 文件处理失败异常
     */
    public static FilePreprocessException processingFailed(String fileName, Throwable cause) {
        return new FilePreprocessException(ResultCode.FILE_PROCESS_ERROR, 
                "文件处理失败: " + fileName, cause);
    }

    /**
     * 文本提取失败异常
     */
    public static FilePreprocessException textExtractionFailed(String fileName, Throwable cause) {
        return new FilePreprocessException(ResultCode.FILE_PROCESS_ERROR, 
                "文本提取失败: " + fileName, cause);
    }
}
