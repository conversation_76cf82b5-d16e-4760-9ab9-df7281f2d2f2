package com.smxz.yjzs.common.utils;

import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 案号提取工具类
 * 用于从文本中提取案号信息
 */
@Slf4j
public class CaseNumberExtractUtil {

    /**
     * 案号正则表达式
     * 匹配格式：（年份）地区代码+法院代码+案件类型+案件编号+号
     * 示例：（2024）粤0106民初xxxx号、(2026)桂01民终xxxxxxxx号、（2016）桂01刑终xxxxxxxx号
     * 支持中文括号（）和英文括号()
     */
    private static final Pattern CASE_NUMBER_PATTERN = Pattern.compile(
        "[（(](\\d{4})[）)]([\\u4e00-\\u9fa5\\w]+)(\\d+)([\\u4e00-\\u9fa5]+)(\\w\\d+)号"
    );

    /**
     * 从文本中提取案号
     * 
     * @param text 包含案号的文本
     * @return 提取到的案号，如果未找到返回null
     */
    public static String extractCaseNumber(String text) {
        if (StringUtils.isBlank(text)) {
            log.info("输入文本为空，无法提取案号");
            return null;
        }

        try {
            Matcher matcher = CASE_NUMBER_PATTERN.matcher(text);
            if (matcher.find()) {
                String caseNumber = matcher.group(0);
                log.info("成功提取案号: {}", caseNumber);
                return caseNumber;
            }
        } catch (Exception e) {
            log.error("案号提取过程中发生异常，文本: {}", text, e);
        }

        log.info("未能从文本中提取到案号: {}", text);
        return null;
    }

    /**
     * 比较两个案号是否一致
     * 比较前会去除中英文括号，确保格式一致性
     * 
     * @param caseNumber1 案号1
     * @param caseNumber2 案号2
     * @return 如果两个案号一致返回true，否则返回false
     */
    public static boolean isCaseNumberEqual(String caseNumber1, String caseNumber2) {
        if (StringUtils.isBlank(caseNumber1) || StringUtils.isBlank(caseNumber2)) {
            log.info("案号比较失败，存在空值: caseNumber1={}, caseNumber2={}", caseNumber1, caseNumber2);
            return false;
        }

        // 去除中英文括号后再比较
        String normalized1 = normalizeCaseNumber(caseNumber1.trim());
        String normalized2 = normalizeCaseNumber(caseNumber2.trim());
        
        boolean isEqual = normalized1.equals(normalized2);
        log.info("案号比较结果: {} vs {} = {} (原始: {} vs {})", normalized1, normalized2, isEqual, caseNumber1, caseNumber2);
        
        return isEqual;
    }

    /**
     * 标准化案号格式，将英文括号替换为中文括号
     * 
     * @param caseNumber 原始案号
     * @return 标准化后的案号
     */
    private static String normalizeCaseNumber(String caseNumber) {
        if (StringUtils.isBlank(caseNumber)) {
            return caseNumber;
        }
        
        // 将英文括号替换为中文括号
        return caseNumber.replace("(", "（").replace(")", "）");
    }

    /**
     * 从案件名称中提取案号
     * 案件名称示例：xxxx-（2024）粤0106民初xxxx号xxxx
     * 
     * @param caseName 案件名称
     * @return 提取到的案号，如果未找到返回null
     */
    public static String extractCaseNumberFromCaseName(String caseName) {
        if (StringUtils.isBlank(caseName)) {
            log.info("案件名称为空，无法提取案号");
            return null;
        }

        log.info("从案件名称中提取案号: {}", caseName);
        return extractCaseNumber(caseName);
    }

    /**
     * 验证案号格式是否正确
     * 
     * @param caseNumber 案号
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidCaseNumber(String caseNumber) {
        if (StringUtils.isBlank(caseNumber)) {
            return false;
        }

        return CASE_NUMBER_PATTERN.matcher(caseNumber.trim()).matches();
    }

    /**
     * 从案号中提取法院代码
     * 案号格式示例：xxx（2025）粤01民初441号xxx 或 xxx（2025）粤0606执16106号xxx
     * 提取其中的法院代码：粤01 或 粤0606
     *
     * @param caseCode 案号
     * @return 法院代码，如果提取失败则返回null
     */
    public static String extractCourtCodeFromCaseCode(String caseCode) {
        if (StringUtils.isBlank(caseCode)) {
            return null;
        }

        try {
            // 正则表达式匹配案号中的法院代码
            // 匹配格式：（年份）法院代码+案件类型+案件编号号
            // 例如：（2025）粤01民初441号 或 （2025）粤0605民初xx999号
            // 法院代码格式：中文字符+2-4位数字
            // 案件编号支持字母数字混合格式
            Pattern pattern = Pattern.compile("[（(](\\d{4})[）)]([\\u4e00-\\u9fa5]\\d{2,4})[\\u4e00-\\u9fa5]+[\\w\\d]+号");
            Matcher matcher = pattern.matcher(caseCode);

            if (matcher.find()) {
                String courtCode = matcher.group(2);
                log.debug("从案号: {} 中提取到法院代码: {}", caseCode, courtCode);
                return courtCode;
            }

            log.debug("案号: {} 不匹配标准格式，无法提取法院代码", caseCode);
            return null;

        } catch (Exception e) {
            log.error("从案号: {} 中提取法院代码时发生异常", caseCode, e);
            return null;
        }
    }
}