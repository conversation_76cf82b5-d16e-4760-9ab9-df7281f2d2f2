package com.smxz.yjzs.common.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 法条编号解析工具类
 */
@Slf4j
public class LegalProvisionParseUtil {

    /**
     * 中文数字转阿拉伯数字映射表
     */
    private static final Map<String, Integer> CHINESE_NUMBER_MAP = new HashMap<>();

    static {
        // 基础数字
        CHINESE_NUMBER_MAP.put("一", 1);
        CHINESE_NUMBER_MAP.put("二", 2);
        CHINESE_NUMBER_MAP.put("三", 3);
        CHINESE_NUMBER_MAP.put("四", 4);
        CHINESE_NUMBER_MAP.put("五", 5);
        CHINESE_NUMBER_MAP.put("六", 6);
        CHINESE_NUMBER_MAP.put("七", 7);
        CHINESE_NUMBER_MAP.put("八", 8);
        CHINESE_NUMBER_MAP.put("九", 9);
        CHINESE_NUMBER_MAP.put("十", 10);

        // 十几
        for (int i = 11; i <= 19; i++) {
            CHINESE_NUMBER_MAP.put("十" + getBasicChineseNumber(i - 10), i);
        }

        // 几十
        for (int i = 2; i <= 9; i++) {
            CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "十", i * 10);
            // 几十几
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "十" + getBasicChineseNumber(j), i * 10 + j);
            }
        }

        // 一百到九百九十九
        for (int i = 1; i <= 9; i++) {
            CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "百", i * 100);
            // 几百零几
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "百零" + getBasicChineseNumber(j), i * 100 + j);
            }
            // 几百几十
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "百" + getBasicChineseNumber(j) + "十", i * 100 + j * 10);
                // 几百几十几
                for (int k = 1; k <= 9; k++) {
                    CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "百" + getBasicChineseNumber(j) + "十" + getBasicChineseNumber(k), i * 100 + j * 10 + k);
                }
            }
        }

        // 一千到九千九百九十九
        for (int i = 1; i <= 9; i++) {
            CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千", i * 1000);
            // 几千零几
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千零" + getBasicChineseNumber(j), i * 1000 + j);
            }
            // 几千几十
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千零" + getBasicChineseNumber(j) + "十", i * 1000 + j * 10);
            }
            // 几千几百
            for (int j = 1; j <= 9; j++) {
                CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千" + getBasicChineseNumber(j) + "百", i * 1000 + j * 100);
                // 几千几百零几
                for (int k = 1; k <= 9; k++) {
                    CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千" + getBasicChineseNumber(j) + "百零" + getBasicChineseNumber(k), i * 1000 + j * 100 + k);
                }
                // 几千几百几十
                for (int k = 1; k <= 9; k++) {
                    CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千" + getBasicChineseNumber(j) + "百" + getBasicChineseNumber(k) + "十", i * 1000 + j * 100 + k * 10);
                    // 几千几百几十几
                    for (int l = 1; l <= 9; l++) {
                        CHINESE_NUMBER_MAP.put(getBasicChineseNumber(i) + "千" + getBasicChineseNumber(j) + "百" + getBasicChineseNumber(k) + "十" + getBasicChineseNumber(l), i * 1000 + j * 100 + k * 10 + l);
                    }
                }
            }
        }
    }

    /**
     * 获取基础中文数字
     */
    private static String getBasicChineseNumber(int num) {
        String[] basic = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        return num >= 0 && num < basic.length ? basic[num] : "";
    }

    /**
     * 解析结果类
     */
    @Data
    public static class ParseResult {
        private Integer articleNo;
        private Integer paragraphNo;
        private Integer subparagraphNo;
        private Integer level;
        private Long parentId;
    }

    /**
     * 完整法律条文解析结果类
     */
    @Data
    public static class LegalProvisionParseResult {
        private String lawName;        // 法律名称
        private Integer articleNo;     // 条号
        private Integer paragraphNo;   // 款号
        private Integer itemNo;        // 项号
        private String originalText;   // 原始文本
        private boolean isValid;       // 是否解析成功
        private String errorMessage;   // 错误信息
    }

    /**
     * 解析法条编号
     * 支持格式：
     * - 第二十七条
     * - 第二十七条第二款
     * - 第二十七条第二款第（一）项
     * - 第二十七条第（一）项
     */
    public static ParseResult parseArticleNumber(String articleNumber) {
        if (articleNumber == null || articleNumber.trim().isEmpty()) {
            return null;
        }

        ParseResult result = new ParseResult();
        
        try {
            // 匹配条号
            Pattern articlePattern = Pattern.compile("第([一二三四五六七八九十百千万零]+)条");
            Matcher articleMatcher = articlePattern.matcher(articleNumber);

            if (articleMatcher.find()) {
                String chineseArticle = articleMatcher.group(1);
                result.setArticleNo(convertChineseToNumber(chineseArticle));
                result.setLevel(1); // 条级别
            } else {
                log.warn("无法解析条号: {}", articleNumber);
                return null;
            }

            // 匹配款号
            Pattern paragraphPattern = Pattern.compile("第([一二三四五六七八九十百千万零]+)款");
            Matcher paragraphMatcher = paragraphPattern.matcher(articleNumber);
            
            if (paragraphMatcher.find()) {
                String chineseParagraph = paragraphMatcher.group(1);
                result.setParagraphNo(convertChineseToNumber(chineseParagraph));
                result.setLevel(2); // 款级别
            }

            // 匹配项号
            Pattern subparagraphPattern = Pattern.compile("第[（(]([一二三四五六七八九十百千万零]+)[）)]项");
            Matcher subparagraphMatcher = subparagraphPattern.matcher(articleNumber);

            if (subparagraphMatcher.find()) {
                String chineseSubparagraph = subparagraphMatcher.group(1);
                result.setSubparagraphNo(convertChineseToNumber(chineseSubparagraph));
                // 如果有项号，需要根据是否有款号来确定层级
                if (result.getParagraphNo() != null) {
                    result.setLevel(3); // 有款号的项级别
                } else {
                    // 没有款号的项，实际上是条下直接的项，但在我们的设计中仍然是条级别
                    // 这种情况比较特殊，我们将其视为条级别，但记录项号
                    result.setLevel(1);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("解析法条编号失败: {}", articleNumber, e);
            return null;
        }
    }

    /**
     * 中文数字转阿拉伯数字
     */
    private static Integer convertChineseToNumber(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.trim().isEmpty()) {
            return null;
        }

        // 直接查找映射表
        if (CHINESE_NUMBER_MAP.containsKey(chineseNumber)) {
            return CHINESE_NUMBER_MAP.get(chineseNumber);
        }

        // 尝试解析复杂的中文数字
        Integer result = parseComplexChineseNumber(chineseNumber);
        if (result != null) {
            return result;
        }

        log.warn("无法转换中文数字: {}", chineseNumber);
        return null;
    }

    /**
     * 解析复杂的中文数字
     */
    private static Integer parseComplexChineseNumber(String chineseNumber) {
        try {
            // 使用更智能的解析算法
            return parseChineseNumberAdvanced(chineseNumber);
        } catch (Exception e) {
            log.error("解析复杂中文数字失败: {}", chineseNumber, e);
            return null;
        }
    }

    /**
     * 高级中文数字解析算法
     */
    private static Integer parseChineseNumberAdvanced(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.isEmpty()) {
            return null;
        }

        int result = 0;
        int temp = 0;

        for (int i = 0; i < chineseNumber.length(); i++) {
            char c = chineseNumber.charAt(i);
            String charStr = String.valueOf(c);

            if ("一二三四五六七八九".contains(charStr)) {
                temp = "一二三四五六七八九".indexOf(charStr) + 1;
            } else if ("十".equals(charStr)) {
                if (temp == 0) temp = 1; // 处理"十"开头的情况
                if (result == 0) {
                    result = temp * 10;
                } else {
                    result += temp * 10;
                }
                temp = 0;
            } else if ("百".equals(charStr)) {
                if (temp == 0) temp = 1;
                if (result == 0) {
                    result = temp * 100;
                } else {
                    result += temp * 100;
                }
                temp = 0;
            } else if ("千".equals(charStr)) {
                if (temp == 0) temp = 1;
                result = temp * 1000;
                temp = 0;
            } else if ("万".equals(charStr)) {
                if (temp == 0) temp = 1;
                result = (result + temp) * 10000;
                temp = 0;
            } else if ("零".equals(charStr)) {
                // 零不做处理，只是占位
                continue;
            }
        }

        result += temp;
        return result > 0 ? result : null;
    }

    /**
     * 解析完整的法律条文
     * 支持格式：
     * - 中华人民共和国民法典第一千零六十三条第一款第三项
     * - 民法典第一千零六十三条第一款第三项
     * - 刑法第二百六十四条
     * - 合同法第五十二条第二款
     *
     * @param legalText 法律条文文本
     * @return 解析结果
     */
    public static LegalProvisionParseResult parseLegalProvision(String legalText) {
        LegalProvisionParseResult result = new LegalProvisionParseResult();
        result.setOriginalText(legalText);

        if (legalText == null || legalText.trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("输入文本为空");
            return result;
        }

        try {
            // 正则表达式匹配完整的法律条文
            // 匹配：法律名称 + 第X条 + [第X款] + [第X项]
            Pattern pattern = Pattern.compile(
                "^(.+?)第([一二三四五六七八九十百千万零]+)条" +
                "(?:第([一二三四五六七八九十百千万零]+)款)?" +
                "(?:第[（(]?([一二三四五六七八九十百千万零]+)[）)]?项)?.*$"
            );

            Matcher matcher = pattern.matcher(legalText.trim());

            if (matcher.find()) {
                // 解析法律名称
                String lawName = matcher.group(1);
                if (lawName != null) {
                    // 直接使用原始法律名称，不进行清理
                    result.setLawName(lawName);
                }

                // 解析条号
                String articleStr = matcher.group(2);
                if (articleStr != null) {
                    Integer articleNo = convertChineseToNumber(articleStr);
                    result.setArticleNo(articleNo);
                    log.debug("解析条号: {} -> {}", articleStr, articleNo);
                }

                // 解析款号
                String paragraphStr = matcher.group(3);
                if (paragraphStr != null) {
                    Integer paragraphNo = convertChineseToNumber(paragraphStr);
                    result.setParagraphNo(paragraphNo);
                }

                // 解析项号
                String itemStr = matcher.group(4);
                if (itemStr != null) {
                    Integer itemNo = convertChineseToNumber(itemStr);
                    result.setItemNo(itemNo);
                }

                result.setValid(true);

                // 验证解析结果
                if (result.getArticleNo() == null) {
                    result.setValid(false);
                    result.setErrorMessage("无法解析条号");
                }

            } else {
                result.setValid(false);
                result.setErrorMessage("文本格式不符合法律条文格式");
            }

        } catch (Exception e) {
            log.error("解析法律条文失败: {}", legalText, e);
            result.setValid(false);
            result.setErrorMessage("解析过程中发生异常: " + e.getMessage());
        }

        return result;
    }



    /**
     * 阿拉伯数字转中文数字（用于扩展映射表）
     */
    private static String convertToChineseNumber(int number) {
        if (number <= 0 || number > 999) {
            return null;
        }
        
        String[] units = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] tens = {"", "十", "二十", "三十", "四十", "五十", "六十", "七十", "八十", "九十"};
        String[] hundreds = {"", "一百", "二百", "三百", "四百", "五百", "六百", "七百", "八百", "九百"};
        
        if (number < 10) {
            return units[number];
        } else if (number < 100) {
            int ten = number / 10;
            int unit = number % 10;
            if (ten == 1 && unit == 0) {
                return "十";
            } else if (ten == 1) {
                return "十" + units[unit];
            } else if (unit == 0) {
                return units[ten] + "十";
            } else {
                return units[ten] + "十" + units[unit];
            }
        } else {
            int hundred = number / 100;
            int remainder = number % 100;
            String result = hundreds[hundred];
            if (remainder > 0) {
                if (remainder < 10) {
                    result += "零" + units[remainder];
                } else {
                    result += convertToChineseNumber(remainder);
                }
            }
            return result;
        }
    }
}
