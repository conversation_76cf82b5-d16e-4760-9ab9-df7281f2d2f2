package com.smxz.yjzs.common.utils;

import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.yjzs.enums.TaskCompletionStrategy;

import java.util.ArrayList;
import java.util.List;

/**
 * Agent任务上下文
 * 用于在AOP中传递子任务结果和任务ID
 * 支持一个任务中调用多次模型任务
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public class AgentTaskContext {

    private static final ThreadLocal<List<AgentTaskInfo>> AGENT_TASK_INFO_LIST = new ThreadLocal<>();
    private static final ThreadLocal<Long> CURRENT_TASK_ID = new ThreadLocal<>();
    private static final ThreadLocal<TaskCompletionStrategy> COMPLETION_STRATEGY = new ThreadLocal<>();

    /**
     * 添加任务信息到列表中
     */
    public static void addAgentTaskInfo(AgentTaskInfo agentTaskInfo) {
        List<AgentTaskInfo> taskInfoList = AGENT_TASK_INFO_LIST.get();
        if (taskInfoList == null) {
            taskInfoList = new ArrayList<>();
            AGENT_TASK_INFO_LIST.set(taskInfoList);
        }
        if (agentTaskInfo != null) {
            taskInfoList.add(agentTaskInfo);
        }
    }

    /**
     * 获取所有任务信息列表
     */
    public static List<AgentTaskInfo> getAgentTaskInfoList() {
        List<AgentTaskInfo> taskInfoList = AGENT_TASK_INFO_LIST.get();
        return taskInfoList != null ? new ArrayList<>(taskInfoList) : new ArrayList<>();
    }

    /**
     * 设置任务信息列表
     */
    public static void setAgentTaskInfoList(List<AgentTaskInfo> taskInfoList) {
        if (taskInfoList == null) {
            AGENT_TASK_INFO_LIST.remove();
        } else {
            AGENT_TASK_INFO_LIST.set(new ArrayList<>(taskInfoList));
        }
    }

    /**
     * 清除所有任务信息
     */
    public static void clearAgentTaskInfo() {
        AGENT_TASK_INFO_LIST.remove();
    }

    /**
     * 设置当前任务ID
     */
    public static void setCurrentTaskId(Long taskId) {
        CURRENT_TASK_ID.set(taskId);
    }

    /**
     * 获取当前任务ID
     */
    public static Long getCurrentTaskId() {
        return CURRENT_TASK_ID.get();
    }

    /**
     * 清除当前任务ID
     */
    public static void clearCurrentTaskId() {
        CURRENT_TASK_ID.remove();
    }

    /**
     * 设置任务完成策略
     */
    public static void setCompletionStrategy(TaskCompletionStrategy strategy) {
        COMPLETION_STRATEGY.set(strategy);
    }

    /**
     * 获取任务完成策略
     */
    public static TaskCompletionStrategy getCompletionStrategy() {
        return COMPLETION_STRATEGY.get();
    }

    /**
     * 清除任务完成策略
     */
    public static void clearCompletionStrategy() {
        COMPLETION_STRATEGY.remove();
    }

    /**
     * 清除所有上下文信息
     */
    public static void clearAll() {
        AGENT_TASK_INFO_LIST.remove();
        CURRENT_TASK_ID.remove();
        COMPLETION_STRATEGY.remove();
    }
}
