package com.smxz.yjzs.common.utils;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * 图片尺寸压缩工具类
 * 使用Thumbnailator实现图片等比压缩功能
 */
@Slf4j
public class ImageCompressionUtil {

    /**
     * 默认最大宽度
     */
    private static final int DEFAULT_MAX_WIDTH = 1920;

    /**
     * 默认最大高度
     */
    private static final int DEFAULT_MAX_HEIGHT = 1080;

    /**
     * 等比压缩图片文件（使用默认参数）
     *
     * @param originalFile 原始图片文件
     * @return 压缩后的文件，如果不需要压缩或压缩失败则返回原文件
     */
    public static File compressImageFile(File originalFile) {
        return compressImageFile(originalFile, DEFAULT_MAX_WIDTH, DEFAULT_MAX_HEIGHT);
    }

    /**
     * 等比压缩图片文件
     *
     * @param originalFile 原始图片文件
     * @param maxWidth     最大宽度
     * @param maxHeight    最大高度
     * @return 压缩后的文件，如果不需要压缩或压缩失败则返回原文件
     */
    public static File compressImageFile(File originalFile, int maxWidth, int maxHeight) {
        try {
            // 检查是否为支持的图片格式
            String extension = FilenameUtils.getExtension(originalFile.getName()).toLowerCase();
            if (!isSupportedImageFormat(extension)) {
                log.debug("不支持的图片格式，跳过压缩: {}", originalFile.getName());
                return originalFile;
            }

            // 读取图片尺寸
            BufferedImage originalImage = ImageIO.read(originalFile);
            if (originalImage == null) {
                log.warn("无法读取图片文件: {}", originalFile.getName());
                return originalFile;
            }

            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 检查是否需要压缩
            if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
                log.debug("图片尺寸未超过限制，无需压缩: {} ({}x{})",
                         originalFile.getName(), originalWidth, originalHeight);
                return originalFile;
            }

            // 创建临时压缩文件
            File compressedFile = createTempFile(originalFile, "_resized");

            // 执行等比压缩
            boolean success = performResize(originalFile, compressedFile, maxWidth, maxHeight);

            BufferedImage compressedImage = ImageIO.read(compressedFile);
            log.debug("压缩后图片尺寸: {} ({}x{})",
                    compressedFile.getName(), compressedImage.getWidth(), compressedImage.getHeight());

            if (success) {
                log.info("图片等比压缩成功: {} ({}x{}) -> {} (最大{}x{})",
                        originalFile.getName(), originalWidth, originalHeight,
                        compressedFile.getName(), maxWidth, maxHeight);
                return compressedFile;
            } else {
                // 压缩失败，删除临时文件并返回原文件
                if (compressedFile.exists()) {
                    compressedFile.delete();
                }
                log.warn("图片压缩失败，使用原文件: {}", originalFile.getName());
                return originalFile;
            }

        } catch (Exception e) {
            log.error("图片压缩过程中发生异常: {}", originalFile.getName(), e);
            return originalFile;
        }
    }

    /**
     * 执行图片等比压缩
     */
    private static boolean performResize(File originalFile, File resizedFile, int maxWidth, int maxHeight) {
        try (FileInputStream fis = new FileInputStream(originalFile);
             FileOutputStream fos = new FileOutputStream(resizedFile)) {

            // 使用Thumbnailator进行等比压缩，保持宽高比
            Thumbnails.of(fis)
                    .size(maxWidth, maxHeight)  // 设置最大尺寸，会自动等比缩放
                    .keepAspectRatio(true)      // 保持宽高比
                    .toOutputStream(fos);

            return true;

        } catch (Exception e) {
            log.error("执行图片等比压缩失败: {}", originalFile.getName(), e);
            return false;
        }
    }

    /**
     * 创建临时文件
     */
    private static File createTempFile(File originalFile, String suffix) throws IOException {
        String baseName = FilenameUtils.getBaseName(originalFile.getName());
        String extension = FilenameUtils.getExtension(originalFile.getName());
        String tempFileName = baseName + suffix + "." + extension;

        File tempFile = new File(originalFile.getParent(), tempFileName);
        tempFile.createNewFile();
        return tempFile;
    }

    /**
     * 检查是否为支持的图片格式
     */
    private static boolean isSupportedImageFormat(String extension) {
        return "jpg".equals(extension) || "jpeg".equals(extension) || "png".equals(extension);
    }
}
