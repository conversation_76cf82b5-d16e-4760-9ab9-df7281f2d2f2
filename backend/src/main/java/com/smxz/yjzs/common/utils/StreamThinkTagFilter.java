package com.smxz.yjzs.common.utils;

/**
 * 流式think标签过滤器
 * 用于在流式输出过程中实时过滤<think>标签及其内容
 * 解决标签被分割传输的问题
 */
public class StreamThinkTagFilter {
    
    /**
     * 过滤器状态枚举
     */
    private enum FilterState {
        NORMAL,      // 正常输出状态
        BUFFERING,   // 缓冲状态，可能遇到标签
        IN_THINK     // 在think标签内部，丢弃内容
    }
    
    private FilterState state = FilterState.NORMAL;
    private StringBuilder buffer = new StringBuilder();
    private int thinkNestLevel = 0; // 处理嵌套think标签
    
    // 标签常量
    private static final String THINK_START_TAG = "<think>";
    private static final String THINK_END_TAG = "</think>";
    
    /**
     * 过滤输入的字符串块
     * @param chunk 输入的字符串块
     * @return 过滤后的字符串，如果全部被过滤则返回空字符串
     */
    public String filter(String chunk) {
        if (chunk == null || chunk.isEmpty()) {
            return "";
        }
        
        StringBuilder output = new StringBuilder();
        
        for (char c : chunk.toCharArray()) {
            String result = processChar(c);
            if (result != null) {
                output.append(result);
            }
        }
        
        return output.toString();
    }
    
    /**
     * 处理单个字符
     * @param c 输入字符
     * @return 输出字符串，null表示无输出
     */
    private String processChar(char c) {
        switch (state) {
            case NORMAL:
                return processNormalState(c);
            case BUFFERING:
                return processBufferingState(c);
            case IN_THINK:
                return processInThinkState(c);
            default:
                return String.valueOf(c);
        }
    }
    
    /**
     * 处理正常状态
     */
    private String processNormalState(char c) {
        if (c == '<') {
            // 可能是标签开始，进入缓冲状态
            state = FilterState.BUFFERING;
            buffer.setLength(0);
            buffer.append(c);
            return null;
        } else {
            // 正常字符，直接输出
            return String.valueOf(c);
        }
    }
    
    /**
     * 处理缓冲状态
     */
    private String processBufferingState(char c) {
        buffer.append(c);
        String bufferedContent = buffer.toString();
        
        // 检查是否匹配think开始标签
        if (THINK_START_TAG.startsWith(bufferedContent)) {
            if (THINK_START_TAG.equals(bufferedContent)) {
                // 完全匹配开始标签
                state = FilterState.IN_THINK;
                thinkNestLevel++;
                buffer.setLength(0);
                return null;
            }
            // 部分匹配，继续缓冲
            return null;
        }
        
        // 检查是否匹配think结束标签（在think内部时）
        if (thinkNestLevel > 0 && THINK_END_TAG.startsWith(bufferedContent)) {
            if (THINK_END_TAG.equals(bufferedContent)) {
                // 完全匹配结束标签
                thinkNestLevel--;
                if (thinkNestLevel == 0) {
                    state = FilterState.NORMAL;
                } else {
                    state = FilterState.IN_THINK;
                }
                buffer.setLength(0);
                return null;
            }
            // 部分匹配，继续缓冲
            return null;
        }
        
        // 不匹配任何标签，输出缓冲内容并切换到相应状态
        String output = buffer.toString();
        buffer.setLength(0);
        
        if (thinkNestLevel > 0) {
            // 在think标签内部，丢弃内容
            state = FilterState.IN_THINK;
            return null;
        } else {
            // 正常状态，输出内容
            state = FilterState.NORMAL;
            return output;
        }
    }
    
    /**
     * 处理think标签内部状态
     */
    private String processInThinkState(char c) {
        if (c == '<') {
            // 可能是结束标签，进入缓冲状态
            state = FilterState.BUFFERING;
            buffer.setLength(0);
            buffer.append(c);
        }
        // 在think内部，丢弃所有内容
        return null;
    }
    
    /**
     * 获取流结束时的剩余内容
     * 用于处理流结束时缓冲区中的内容
     * @return 剩余的内容，如果在think标签内则返回空字符串
     */
    public String getRemaining() {
        if (state == FilterState.BUFFERING && thinkNestLevel == 0) {
            // 缓冲区有内容且不在think标签内，输出剩余内容
            String remaining = buffer.toString();
            buffer.setLength(0);
            state = FilterState.NORMAL;
            return remaining;
        }
        return "";
    }
    
    /**
     * 重置过滤器状态
     */
    public void reset() {
        state = FilterState.NORMAL;
        buffer.setLength(0);
        thinkNestLevel = 0;
    }
    
    /**
     * 检查当前是否在think标签内部
     * @return true如果在think标签内部
     */
    public boolean isInThinkTag() {
        return thinkNestLevel > 0;
    }
}
