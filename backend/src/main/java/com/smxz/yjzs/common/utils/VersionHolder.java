package com.smxz.yjzs.common.utils;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.Properties;

public class VersionHolder {

    private static final Logger LOG = LoggerFactory.getLogger(VersionHolder.class);

    private static final String DOLLAR = "$";
    private static final String UNKNOWN = "<unknown>";
    private static final String PROP_FILE = "git.properties";

    @Getter
    private static String buildTime = UNKNOWN;
    @Getter
    private static String buildUserName = UNKNOWN;
    @Getter
    private static String branch = UNKNOWN;
    @Getter
    private static String commitId = UNKNOWN;
    @Getter
    private static String commitTime = UNKNOWN;
    @Getter
    private static String commitMessageFull = UNKNOWN;
    @Getter
    private static String commitUserName = UNKNOWN;
    @Getter
    private static String totalCommitCount = UNKNOWN;

    static {
        ClassLoader classLoader = VersionHolder.class.getClassLoader();
        try (InputStream versionFile = classLoader.getResourceAsStream(PROP_FILE)) {
            if (versionFile != null) {
                Properties properties = new Properties();
                properties.load(versionFile);

                branch = getProperty(properties, "git.branch", UNKNOWN);
                buildTime = getProperty(properties, "git.build.time", UNKNOWN);
                commitId = getProperty(properties, "git.commit.id.abbrev", UNKNOWN);
                commitTime = getProperty(properties, "git.commit.time", UNKNOWN);
                commitMessageFull = getProperty(properties, "git.commit.message.full", UNKNOWN);
                commitUserName = getProperty(properties, "git.commit.user.name", UNKNOWN);
                buildUserName = getProperty(properties, "git.build.user.name", UNKNOWN);
                totalCommitCount = getProperty(properties, "git.total.commit.count", UNKNOWN);
            }
        } catch (Exception e) {
            LOG.info("Parse code version error: unable to read version property file", e);
        }
    }

    private VersionHolder() {

    }

    private static String getProperty(Properties properties, String key, String defaultValue) {
        String value = properties.getProperty(key);
        if (value == null || value.charAt(0) == DOLLAR.charAt(0)) {
            return defaultValue;
        }
        return value;
    }

    public static void main(String[] args) {
        System.out.println(VersionHolder.getBranch());
        System.out.println(VersionHolder.getBuildTime());
        System.out.println(VersionHolder.getCommitId());
        System.out.println(VersionHolder.getCommitTime());
        System.out.println(VersionHolder.getCommitMessageFull());
    }

}