package com.smxz.yjzs.common.utils;

import com.smxz.agent.client.AgentClient;
import com.smxz.agent.dto.AgentTaskInfo;
import com.smxz.agent.task.PropertyLoader;
import com.smxz.agent.task.SyncAgentTask;
import com.smxz.agent.web.util.YamlDatabaseLoader;
import com.smxz.yjzs.enums.TaskCompletionStrategy;
import com.smxz.yjzs.service.TaskStatusManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Agent任务调用工具类
 * 封装SyncAgentTask的使用
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Component
public class AgentTaskUtils {

    @Autowired
    private AgentClient agentClient;

    @Autowired
    private YamlDatabaseLoader yamlDatabaseLoader;

    @Autowired
    private TaskStatusManager taskStatusManager;

    /**
     * 执行同步Agent任务（文本输入，支持AOP任务状态管理）
     *
     * @param agentName Agent名称
     * @param bootYmlName 配置文件名
     * @param extractedText 输入文本
     * @return Agent返回结果
     */
    public String executeTask(String agentName, String taskName, String bootYmlName, String extractedText) {
        return executeTaskInternal(agentName, taskName, bootYmlName, e -> extractedText);
    }

    /**
     * 执行同步Agent任务（Map属性输入，支持AOP任务状态管理）
     *
     * @param agentName Agent名称
     * @param bootYmlName 配置文件名
     * @param propertyMap 属性映射
     * @return Agent返回结果
     */
    public String executeTask(String agentName, String taskName, String bootYmlName, Map<String, String> propertyMap) {
        return executeTaskInternal(agentName, taskName, bootYmlName, propertyMap::get);
    }

    /**
     * 执行流式Agent任务并累积结果（Map属性输入，支持AOP任务状态管理）
     *
     * @param agentName Agent名称
     * @param taskName 任务名称
     * @param bootYmlName 配置文件名
     * @param propertyMap 属性映射
     * @param onComplete 完成时的回调，参数为累积的完整结果
     * @return 流式结果
     */
    public Flux<String> executeStreamTaskWithResult(String agentName, String taskName, String bootYmlName,
                                                   Map<String, String> propertyMap, Consumer<String> onComplete) {
        return executeStreamTaskWithResultInternal(agentName, taskName, bootYmlName, propertyMap::get, onComplete);
    }

    /**
     * 执行同步Agent任务的内部实现
     * 统一处理任务构建、执行和结果更新逻辑
     *
     * @param agentName Agent名称
     * @param taskName 任务名称
     * @param bootYmlName 配置文件名
     * @param propertyLoader 属性加载器
     * @return Agent返回结果
     */
    private String executeTaskInternal(String agentName, String taskName, String bootYmlName,
                                     PropertyLoader propertyLoader) {
        // 在构建任务前获取taskId和完成策略，避免异步线程中ThreadLocal丢失
        Long taskId = AgentTaskContext.getCurrentTaskId();
        TaskCompletionStrategy strategy = AgentTaskContext.getCompletionStrategy();

        SyncAgentTask agentTask = new SyncAgentTask.Builder()
                .client(agentClient)
                .agentName(agentName)
                .taskName(taskName)
                .bootYmlName(bootYmlName)
                .onLoadYaml(yamlDatabaseLoader)
                .onLoadProperty(propertyLoader)
                .finalProcess(result -> updateTaskResult(taskId, result.taskInfo(), strategy))
                .build();

        agentTask.start();

        String res = agentTask.getResult(30, TimeUnit.MINUTES);

        return AiOutputUtils.cleanResponse(res);
    }

    /**
     * 执行流式Agent任务并累积结果的内部实现
     * 统一处理任务构建、执行、结果累积和回调逻辑
     *
     * @param agentName Agent名称
     * @param taskName 任务名称
     * @param bootYmlName 配置文件名
     * @param propertyLoader 属性加载器
     * @param onComplete 完成时的回调，参数为累积的完整结果
     * @return 流式结果
     */
    private Flux<String> executeStreamTaskWithResultInternal(String agentName, String taskName, String bootYmlName,
                                                            PropertyLoader propertyLoader, Consumer<String> onComplete) {
        // 在构建任务前获取taskId，避免异步线程中ThreadLocal丢失
        Long taskId = AgentTaskContext.getCurrentTaskId();

        SyncAgentTask agentTask = new SyncAgentTask.Builder()
                .client(agentClient)
                .agentName(agentName)
                .taskName(taskName)
                .bootYmlName(bootYmlName)
                .onLoadYaml(yamlDatabaseLoader)
                .onLoadProperty(propertyLoader)
                .finalProcess(result -> updateTaskResultForStream(taskId, result.taskInfo()))
                .build();

        agentTask.start();

        // 用于累积完整结果的容器
        StringBuilder resultBuilder = new StringBuilder();

        // 应用流式think标签过滤器并返回结果
        return AiOutputUtils.applyStreamThinkFilter(agentTask.getStreamResult())
                .doOnNext(chunk -> resultBuilder.append(chunk))
                .doOnComplete(() -> {
                    // 先执行用户的完成回调
                    if (onComplete != null) {
                        String fullResult = resultBuilder.toString();
                        onComplete.accept(fullResult);
                    }

                    // 然后更新任务完成时间和耗时
                    taskStatusManager.updateTaskCompletionTime(taskId);
                })
                .doOnError(error -> {
                    log.error("流式任务执行过程中发生错误，agentName: {}, taskName: {}, error: {}",
                            agentName, taskName, error.getMessage(), error);
                    // 更新任务状态为失败
                    if (taskId != null) {
                        String message = ExceptionUtils.getMessage(error);
                        String rootCauseMessage = ExceptionUtils.getRootCauseMessage(error);
                        taskStatusManager.markTaskFailed(taskId, error.getMessage());
                    }
                });
    }

    /**
     * 更新任务结果的通用方法（用于同步任务）
     * 从finalProcess回调中提取出来，便于复用和测试
     *
     * @param taskId 任务ID
     * @param agentTaskInfo Agent任务信息
     * @param strategy 任务完成策略
     */
    private void updateTaskResult(Long taskId, AgentTaskInfo agentTaskInfo, TaskCompletionStrategy strategy) {
        if (taskId == null || agentTaskInfo == null) {
            log.warn("更新任务结果失败，参数为空，taskId: {}, agentTaskInfo: {}", taskId, agentTaskInfo);
            return;
        }

        taskStatusManager.updateTaskResultOnly(taskId, agentTaskInfo);
        
        // 根据完成策略决定是否立即完成任务
        if (strategy == TaskCompletionStrategy.FIRST_SUCCESS) {
            taskStatusManager.markTaskSuccess(taskId);
            log.info("任务完成策略为FIRST_SUCCESS，第一个子任务完成后立即标记任务成功，taskId: {}", taskId);
        }
    }

    /**
     * 更新流式任务结果的方法（不更新完成时间）
     * 用于流式任务的finalProcess回调
     *
     * @param taskId 任务ID
     * @param agentTaskInfo Agent任务信息
     */
    private void updateTaskResultForStream(Long taskId, AgentTaskInfo agentTaskInfo) {
        if (taskId == null || agentTaskInfo == null) {
            log.warn("更新流式任务结果失败，参数为空，taskId: {}, agentTaskInfo: {}", taskId, agentTaskInfo);
            return;
        }

        taskStatusManager.updateTaskResultOnly(taskId, agentTaskInfo);
    }

}

