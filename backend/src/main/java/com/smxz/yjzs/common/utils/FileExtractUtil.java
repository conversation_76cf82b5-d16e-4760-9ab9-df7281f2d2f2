package com.smxz.yjzs.common.utils;

import com.smxz.yjzs.entity.PdfPageContent;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @since 2025-04-15 13:58
 **/
public class FileExtractUtil {

    /**
     * 根据文件后缀名提取文本内容， 目前内容只支持 pdf, doc, docx, ppt, pptx, xls, xlsx 的提取
     * 如果是其它文件的类型，返回 String = null
     * @param path 文件路径
     * @return
     */
    public static String extractString(String path) throws IOException{
        String res = null;
        String suffix = getFileSuffix(path).toLowerCase();
        switch (suffix) {
            case "pdf":
                res = pdf2String(new FileInputStream(path));
                break;
            case "doc":
                res = doc2String(new File(path));
                break;
            case "docx":
                res = docx2String(new File(path));
                break;

        }
        return res  == null? "" : res;
    }

    /**
     * 根据文件后缀名从输入流提取文本内容，支持 pdf, doc, docx 的提取
     * 如果是其它文件的类型，返回 null
     * @param inputStream 文件输入流
     * @param fileName 文件名（用于获取后缀）
     * @return 提取的文本内容
     */
    public static String extractString(InputStream inputStream, String fileName) throws IOException{
        String res = null;
        String suffix = getFileSuffix(fileName).toLowerCase();
        switch (suffix) {
            case "pdf":
                res = pdf2String(inputStream);
                break;
            case "doc":
                res = doc2String(inputStream);
                break;
            case "docx":
                res = docx2String(inputStream);
                break;
        }
        return res == null ? "" : res;
    }

    /**
     * 得到文件后缀
     * @param fileName 文件名
     * @return
     */
    public static String getFileSuffix(String fileName) {
        return FilenameUtils.getExtension(fileName);
    }

    /**
     * 将整个PDF转换为一个字符串
     */
    public static String pdf2String(InputStream inputStream) throws IOException {
        try (PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            return stripper.getText(document);
        }
    }

    /**
     * 将PDF按页面转换为字符串列表
     * @param inputStream PDF输入流
     * @return 每页内容的列表，索引从1开始
     */
    public static List<PdfPageContent> pdf2StringByPages(InputStream inputStream) throws IOException {
        List<PdfPageContent> pages = new ArrayList<>();
        try (PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            
            int numberOfPages = document.getNumberOfPages();
            for (int i = 1; i <= numberOfPages; i++) {
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                String pageText = stripper.getText(document);
                pages.add(PdfPageContent.builder().pageNumber(i).content(pageText).build());
            }
        }
        return pages;
    }

    public static PDDocument  getPdfDocument(InputStream inputStream) throws IOException {
        return Loader.loadPDF(inputStream.readAllBytes());

    }


    /**
     * 提取指定页面范围的PDF内容
     * @param inputStream PDF输入流
     * @param startPage 开始页码（从1开始）
     * @param endPage 结束页码（包含）
     * @return 指定页面范围的内容
     */
    public static String pdf2StringByPageRange(InputStream inputStream, int startPage, int endPage) throws IOException {
        try (PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {
            int maxPages = document.getNumberOfPages();
            // 验证页码范围
            if (startPage < 1 || endPage > maxPages || startPage > endPage) {
                throw new IllegalArgumentException(
                    String.format("Invalid page range. Valid range is 1-%d, but got %d-%d", 
                                maxPages, startPage, endPage));
            }

            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setStartPage(startPage);
            stripper.setEndPage(endPage);
            return stripper.getText(document);
        }
    }

    public static String doc2String(File file) throws IOException {
        try(WordExtractor extractor = new WordExtractor(new FileInputStream(file))){
            return extractor.getText();
        }catch (Exception e){
            throw new IOException("读取doc内容出现异常",e);
        }
    }

    public static String docx2String(File file) throws IOException {
        try(FileInputStream fis = new FileInputStream(file);
            XWPFDocument docx = new XWPFDocument(fis);
            XWPFWordExtractor extractor = new XWPFWordExtractor(docx)){
            return extractor.getText();
        }catch (Exception e){
            throw new IOException("读取docx内容出现异常",e);
        }
    }

    /**
     * 从输入流读取doc文件内容
     */
    public static String doc2String(InputStream inputStream) throws IOException {
        try(WordExtractor extractor = new WordExtractor(inputStream)){
            return extractor.getText();
        }catch (Exception e){
            throw new IOException("读取doc内容出现异常",e);
        }
    }

    /**
     * 从输入流读取docx文件内容
     */
    public static String docx2String(InputStream inputStream) throws IOException {
        try(XWPFDocument docx = new XWPFDocument(inputStream);
            XWPFWordExtractor extractor = new XWPFWordExtractor(docx)){
            return extractor.getText();
        }catch (Exception e){
            throw new IOException("读取docx内容出现异常",e);
        }
    }


}
