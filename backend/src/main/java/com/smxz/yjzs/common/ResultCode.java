package com.smxz.yjzs.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数校验失败"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    BUSINESS_ERROR(1000, "业务处理失败"),
    FILE_UPLOAD_ERROR(1001, "文件上传失败"),
    FILE_PROCESS_ERROR(1002, "文件处理失败"),
    AI_MODEL_ERROR(1003, "AI模型调用失败"),
    DATA_NOT_FOUND(1004, "数据不存在"),
    DATA_ALREADY_EXISTS(1005, "数据已存在"),
    OPERATION_NOT_ALLOWED(1006, "操作不被允许"),
    
    // 案件相关错误 2xxx
    CASE_NOT_FOUND(2001, "案件不存在"),
    CASE_IMPORT_ERROR(2002, "案件导入失败"),
    CASE_ANALYSIS_ERROR(2003, "案件分析失败"),
    CASE_DELETE_ERROR(2004, "案件删除失败"),
    
    // 文件相关错误 3xxx
    FILE_NOT_FOUND(3001, "文件不存在"),
    FILE_TYPE_NOT_SUPPORTED(3002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(3003, "文件大小超出限制"),
    FILE_CONVERSION_ERROR(3004, "文件转换失败"),
    
    // AI分析相关错误 4xxx
    AI_ANALYSIS_TIMEOUT(4001, "AI分析超时"),
    AI_ANALYSIS_FAILED(4002, "AI分析失败"),
    RELATION_GRAPH_ERROR(4003, "关系图谱生成失败"),
    TIMELINE_ANALYSIS_ERROR(4004, "时序链分析失败"),
    DISPUTE_ANALYSIS_ERROR(4005, "争议焦点分析失败"),
    CONTRADICTION_ANALYSIS_ERROR(4006, "矛盾识别分析失败");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态消息
     */
    private final String message;
}
