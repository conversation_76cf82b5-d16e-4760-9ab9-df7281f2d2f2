package com.smxz.yjzs.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class ArchiveUtils {

    /**
     * 支持的压缩文件扩展名（仅支持ZIP格式）
     */
    private static final String[] ARCHIVE_EXTENSIONS = {"zip", "ZIP"};

    /**
     * 默认缓冲区大小
     */
    private static final int BUFFER_SIZE = 8192;

    /**
     * Linux系统文件名最大长度限制（字节）
     */
    private static final int MAX_FILENAME_LENGTH = 255;

    /**
     * 文件名截断后的最大长度，预留扩展名和序号空间
     */
    private static final int TRUNCATED_FILENAME_LENGTH = 200;

    /**
     * 检查文件是否为支持的压缩文件（仅ZIP格式）
     */
    public static boolean isArchiveFile(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        for (String ext : ARCHIVE_EXTENSIONS) {
            if (ext.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 解压ZIP文件（自动检测编码）
     * @param archiveFile 压缩文件
     * @param extractPath 解压目标路径
     * @return 解压后的文件列表
     */
    public static List<File> extractArchive(MultipartFile archiveFile, String extractPath) throws IOException {
        // 检测压缩文件编码
        String charset = CharsetDetectionUtil.getBestCharset(archiveFile);
        log.info("检测到压缩文件编码: {} -> {}", archiveFile.getOriginalFilename(), charset);

        return extractArchive(archiveFile, extractPath, charset);
    }

    /**
     * 解压ZIP文件（指定编码）
     * @param archiveFile 压缩文件
     * @param extractPath 解压目标路径
     * @param charset 文件名编码
     * @return 解压后的文件列表
     */
    public static List<File> extractArchive(MultipartFile archiveFile, String extractPath, String charset) throws IOException {
        if (!isArchiveFile(archiveFile)) {
            throw new IOException("不支持的压缩文件格式: " + archiveFile.getOriginalFilename());
        }

        List<File> extractedFiles = new ArrayList<>();
        Path extractDir = Paths.get(extractPath);
        Path tempFile = null;

        try {
            // 准备解压环境
            Files.createDirectories(extractDir);
            tempFile = createTempFile(archiveFile);

            log.info("开始解压ZIP文件: {}, 使用编码: {}", archiveFile.getOriginalFilename(), charset);

            // 使用指定编码解压ZIP文件
            try (ZipFile zipFile = new ZipFile(tempFile.toFile(), charset)) {
                extractZipFiles(zipFile, extractDir, extractedFiles);
            }

            log.info("解压完成，共解压 {} 个文件", extractedFiles.size());
            return extractedFiles;

        } catch (Exception e) {
            log.error("解压文件失败: {}, 编码: {}", archiveFile.getOriginalFilename(), charset, e);

            // 如果指定编码失败，尝试使用备用编码
            if (!CharsetDetectionUtil.DEFAULT_CHARSET.equals(charset)) {
                log.info("尝试使用默认编码重新解压: {}", CharsetDetectionUtil.DEFAULT_CHARSET);
                return extractArchive(archiveFile, extractPath, CharsetDetectionUtil.DEFAULT_CHARSET);
            }

            throw new IOException("解压文件失败: " + e.getMessage(), e);
        } finally {
            if (tempFile != null) {
                Files.deleteIfExists(tempFile);
            }
        }
    }
    
    /**
     * 创建临时文件
     */
    private static Path createTempFile(MultipartFile file) throws IOException {
        Path tempFile = Files.createTempFile("temp_zip_", "." + FilenameUtils.getExtension(file.getOriginalFilename()));
        file.transferTo(tempFile.toFile());
        log.debug("创建临时文件: {}, 大小: {} bytes", tempFile.toString(), file.getSize());
        return tempFile;
    }

    /**
     * 解压ZIP文件内容
     */
    private static void extractZipFiles(ZipFile zipFile, Path extractDir, List<File> extractedFiles) throws IOException {
        // 第一步：分析ZIP文件结构，决定处理模式
        StructureAnalysisResult analysisResult = analyzeZipStructure(zipFile);
        log.debug("ZIP文件结构分析完成，处理模式: {}", analysisResult.getProcessingMode());

        Enumeration<ZipEntry> entries = zipFile.getEntries();
        Map<String, Integer> fileNameCounters = new HashMap<>();

        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();

            // 跳过目录条目
            if (entry.isDirectory()) {
                continue;
            }

            String originalEntryName = entry.getName();
            String entryName = processEntryName(originalEntryName, analysisResult);

            // 处理文件名过长的问题，只影响临时文件系统
            String safeFileName = getSafeFileName(entryName, fileNameCounters);
            Path targetPath = extractDir.resolve(safeFileName);

            // 安全检查：防止路径遍历攻击
            if (!targetPath.normalize().startsWith(extractDir.normalize())) {
                log.warn("跳过可能的路径遍历攻击文件: {}", originalEntryName);
                continue;
            }

            // 创建父目录
            Files.createDirectories(targetPath.getParent());

            // 解压文件
            extractZipEntry(zipFile, entry, targetPath);

            // 创建ExtractedFileInfo对象，保存原始文件名信息
            File extractedFile = targetPath.toFile();
            // 将原始文件名作为文件的属性保存（通过自定义File子类）
            ExtractedFile fileWithOriginalName = new ExtractedFile(extractedFile, originalEntryName);
            extractedFiles.add(fileWithOriginalName);

            if (!entryName.equals(safeFileName)) {
                log.info("文件名过长已截断: {} -> {}", entryName, safeFileName);
            } else {
                log.debug("已解压文件: {} -> {}", originalEntryName, entryName);
            }
        }
    }

    /**
     * 处理模式枚举
     */
    private enum ProcessingMode {
        FLATTEN,           // 完全扁平化：所有文件都放在同一目录
        KEEP_STRUCTURE,    // 保持完整结构：维持原有目录结构
        REMOVE_ROOT_DIR    // 去掉根目录：去掉第一级目录但保持子目录结构
    }

    /**
     * 结构分析结果
     */
    private static class StructureAnalysisResult {
        private final ProcessingMode processingMode;
        private final String rootDirectory;

        public StructureAnalysisResult(ProcessingMode processingMode, String rootDirectory) {
            this.processingMode = processingMode;
            this.rootDirectory = rootDirectory;
        }

        public ProcessingMode getProcessingMode() {
            return processingMode;
        }

        public String getRootDirectory() {
            return rootDirectory;
        }
    }

    /**
     * 分析ZIP文件结构，决定处理模式
     *
     * @param zipFile ZIP文件
     * @return 结构分析结果
     */
    private static StructureAnalysisResult analyzeZipStructure(ZipFile zipFile) throws IOException {
        Enumeration<ZipEntry> entries = zipFile.getEntries();
        Set<String> rootLevelDirectories = new HashSet<>();
        boolean hasRootLevelFiles = false;

        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();
            String entryName = entry.getName();

            // 跳过空条目
            if (entryName == null || entryName.trim().isEmpty()) {
                continue;
            }

            // 分析根级别条目
            if (entryName.contains("/")) {
                // 包含路径分隔符，提取根目录名
                String rootDir = entryName.substring(0, entryName.indexOf("/"));
                if (!rootDir.isEmpty()) {
                    rootLevelDirectories.add(rootDir);
                }
            } else {
                // 不包含路径分隔符
                if (entry.isDirectory()) {
                    // 根级别目录
                    rootLevelDirectories.add(entryName);
                } else {
                    // 根级别文件
                    hasRootLevelFiles = true;
                }
            }
        }

        // 决定处理模式
        ProcessingMode mode;
        String rootDir = null;

        if (rootLevelDirectories.size() == 1) {
            // 只有一个根目录 -> 去掉根目录模式（不管是否有根级别文件）
            mode = ProcessingMode.REMOVE_ROOT_DIR;
            rootDir = rootLevelDirectories.iterator().next();
            log.debug("ZIP结构分析 - 采用去掉根目录模式，根目录: {}", rootDir);
        } else {
            // 多个根目录或无根目录 -> 保持结构
            mode = ProcessingMode.KEEP_STRUCTURE;
            log.debug("ZIP结构分析 - 采用保持结构模式，根级别目录数: {}, 根级别文件: {}",
                    rootLevelDirectories.size(), hasRootLevelFiles);
        }

        return new StructureAnalysisResult(mode, rootDir);
    }

    /**
     * 根据分析结果处理文件名
     *
     * @param originalEntryName 原始文件路径
     * @param analysisResult 结构分析结果
     * @return 处理后的文件路径
     */
    private static String processEntryName(String originalEntryName, StructureAnalysisResult analysisResult) {
        switch (analysisResult.getProcessingMode()) {
            case FLATTEN:
                // 完全扁平化：只保留文件名
                String flattenedName = FilenameUtils.getName(originalEntryName);
                log.debug("扁平化处理: {} -> {}", originalEntryName, flattenedName);
                return flattenedName;

            case REMOVE_ROOT_DIR:
                // 去掉根目录：移除第一级目录但保持子目录结构
                String rootDir = analysisResult.getRootDirectory();
                if (rootDir != null && originalEntryName.startsWith(rootDir + "/")) {
                    String processedName = originalEntryName.substring(rootDir.length() + 1);
                    log.debug("去掉根目录处理: {} -> {}", originalEntryName, processedName);
                    return processedName;
                }
                // 如果不是预期的根目录结构，fallback到原始名称
                log.debug("根目录处理异常，保持原名: {}", originalEntryName);
                return originalEntryName;

            case KEEP_STRUCTURE:
            default:
                // 保持原有结构
                log.debug("保持原有结构: {}", originalEntryName);
                return originalEntryName;
        }
    }

    /**
     * 解压单个ZIP条目
     */
    private static void extractZipEntry(ZipFile zipFile, ZipEntry entry, Path targetPath) throws IOException {
        try (InputStream inputStream = zipFile.getInputStream(entry);
             BufferedInputStream bufferedInput = new BufferedInputStream(inputStream);
             OutputStream outputStream = Files.newOutputStream(targetPath);
             BufferedOutputStream bufferedOutput = new BufferedOutputStream(outputStream)) {

            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;

            while ((bytesRead = bufferedInput.read(buffer)) != -1) {
                bufferedOutput.write(buffer, 0, bytesRead);
            }

            bufferedOutput.flush();
        }
    }

    /**
     * 获取安全的文件名（处理文件名过长问题）
     * @param originalFileName 原始文件名
     * @param fileNameCounters 文件名计数器，用于处理重名
     * @return 安全的文件名
     */
    private static String getSafeFileName(String originalFileName, Map<String, Integer> fileNameCounters) {
        // 提取文件名和扩展名
        String baseName = FilenameUtils.getBaseName(originalFileName);
        String extension = FilenameUtils.getExtension(originalFileName);

        // 如果文件名长度在限制内，直接返回
        if (getFileNameByteLength(originalFileName) <= MAX_FILENAME_LENGTH) {
            return originalFileName;
        }

        // 构建截断后的文件名
        String truncatedBaseName = truncateFileName(baseName, extension);
        String safeFileName = extension.isEmpty() ? truncatedBaseName : truncatedBaseName + "." + extension;

        // 处理重名问题
        String finalFileName = handleDuplicateFileName(safeFileName, fileNameCounters);

        return finalFileName;
    }

    /**
     * 获取文件名的字节长度（UTF-8编码）
     */
    private static int getFileNameByteLength(String fileName) {
        return fileName.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
    }

    /**
     * 截断文件名到安全长度
     */
    private static String truncateFileName(String baseName, String extension) {
        // 计算扩展名占用的字节数（包括点号）
        int extensionBytes = extension.isEmpty() ? 0 : (1 + getFileNameByteLength(extension));

        // 计算基础文件名可用的最大字节数
        int maxBaseNameBytes = TRUNCATED_FILENAME_LENGTH - extensionBytes;

        // 如果基础文件名已经在限制内，直接返回
        if (getFileNameByteLength(baseName) <= maxBaseNameBytes) {
            return baseName;
        }

        // 按字符截断，确保不超过字节限制
        StringBuilder truncated = new StringBuilder();
        for (char c : baseName.toCharArray()) {
            String temp = truncated.toString() + c;
            if (getFileNameByteLength(temp) > maxBaseNameBytes) {
                break;
            }
            truncated.append(c);
        }

        return truncated.toString();
    }

    /**
     * 处理重名文件，添加序号后缀
     */
    private static String handleDuplicateFileName(String fileName, Map<String, Integer> fileNameCounters) {
        String baseName = FilenameUtils.getBaseName(fileName);
        String extension = FilenameUtils.getExtension(fileName);

        // 检查是否已存在相同文件名
        Integer counter = fileNameCounters.get(fileName);
        if (counter == null) {
            // 首次出现，记录并返回原文件名
            fileNameCounters.put(fileName, 1);
            return fileName;
        }

        // 已存在重名，生成带序号的文件名
        String finalFileName;
        do {
            counter++;
            String suffix = "_" + counter;
            String newBaseName = baseName + suffix;
            finalFileName = extension.isEmpty() ? newBaseName : newBaseName + "." + extension;

            // 确保新文件名不超过长度限制
            if (getFileNameByteLength(finalFileName) > MAX_FILENAME_LENGTH) {
                // 重新截断基础文件名以容纳序号
                int suffixBytes = getFileNameByteLength(suffix);
                int extensionBytes = extension.isEmpty() ? 0 : (1 + getFileNameByteLength(extension));
                int maxBaseNameBytes = TRUNCATED_FILENAME_LENGTH - suffixBytes - extensionBytes;

                String truncatedBaseName = truncateFileNameToBytes(baseName, maxBaseNameBytes);
                newBaseName = truncatedBaseName + suffix;
                finalFileName = extension.isEmpty() ? newBaseName : newBaseName + "." + extension;
            }
        } while (fileNameCounters.containsKey(finalFileName));

        // 记录新文件名
        fileNameCounters.put(fileName, counter);
        fileNameCounters.put(finalFileName, 1);

        return finalFileName;
    }

    /**
     * 将文件名截断到指定字节长度
     */
    private static String truncateFileNameToBytes(String fileName, int maxBytes) {
        if (getFileNameByteLength(fileName) <= maxBytes) {
            return fileName;
        }

        StringBuilder truncated = new StringBuilder();
        for (char c : fileName.toCharArray()) {
            String temp = truncated.toString() + c;
            if (getFileNameByteLength(temp) > maxBytes) {
                break;
            }
            truncated.append(c);
        }

        return truncated.toString();
    }

    /**
     * 获取ZIP文件的文件列表（用于预览，不解压）
     * @param archiveFile 压缩文件
     * @return 文件名列表
     */
    public static List<String> listZipFiles(MultipartFile archiveFile) throws IOException {
        return listZipFiles(archiveFile, CharsetDetectionUtil.getBestCharset(archiveFile));
    }

    /**
     * 获取ZIP文件的文件列表（指定编码）
     * @param archiveFile 压缩文件
     * @param charset 文件名编码
     * @return 文件名列表
     */
    public static List<String> listZipFiles(MultipartFile archiveFile, String charset) throws IOException {
        if (!isArchiveFile(archiveFile)) {
            throw new IOException("不支持的压缩文件格式: " + archiveFile.getOriginalFilename());
        }

        List<String> fileNames = new ArrayList<>();
        Path tempFile = null;

        try {
            tempFile = createTempFile(archiveFile);

            try (ZipFile zipFile = new ZipFile(tempFile.toFile(), charset)) {
                Enumeration<ZipEntry> entries = zipFile.getEntries();

                while (entries.hasMoreElements()) {
                    ZipEntry entry = entries.nextElement();
                    if (!entry.isDirectory()) {
                        fileNames.add(entry.getName());
                    }
                }
            }

            log.info("ZIP文件包含 {} 个文件: {}", fileNames.size(), archiveFile.getOriginalFilename());
            return fileNames;

        } finally {
            if (tempFile != null) {
                Files.deleteIfExists(tempFile);
            }
        }
    }

    /**
     * 扩展File类，用于保存原始文件名信息
     */
    public static class ExtractedFile extends File {
        private final String originalFileName;

        public ExtractedFile(File file, String originalFileName) {
            super(file.getPath());
            this.originalFileName = originalFileName;
        }

        /**
         * 获取原始文件名（解压前的文件名）
         */
        public String getOriginalFileName() {
            return originalFileName;
        }

        /**
         * 获取原始文件的基础名称（不含扩展名）
         */
        public String getOriginalBaseName() {
            return FilenameUtils.getBaseName(originalFileName);
        }

        /**
         * 获取原始文件的扩展名
         */
        public String getOriginalExtension() {
            return FilenameUtils.getExtension(originalFileName);
        }
    }
}