package com.smxz.yjzs.common.utils;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * 符号处理工具类
 * 提供多语言符号转换、忽略符号差异的匹配等功能
 */
public class SymbolUtils {
    
    /**
     * 多语言符号映射表，用于忽略符号差异的匹配
     * key: 各种语言的符号, value: 对应的标准符号
     */
    private static final Map<Character, Character> SYMBOL_MAPPING = new HashMap<>();
    
    static {
        // 定义符号映射关系
        char[][] mappings = {
                // 中文符号
                {'（', '('}, {'）', ')'}, {'【', '['}, {'】', ']'}, {'《', '<'}, {'》', '>'},
                {'？', '?'}, {'：', ':'}, {'；', ';'}, {'，', ','}, {'。', '.'}, {'！', '!'},
                {'“', '"'}, {'”', '"'}, {'‘', '\''}, {'’', '\''}, {'—', '-'}, {'－', '-'},
                {'·', '.'}, {'、', ','}, {'…', '.'}, {'￥', '$'}, {'％', '%'}, {'＃', '#'},
                {'＠', '@'}, {'＆', '&'}, {'＊', '*'}, {'＋', '+'}, {'＝', '='}, {'｛', '{'},
                {'｝', '}'}, {'｜', '|'}, {'＼', '\\'}, {'／', '/'},

                // 日文符号
                {'「', '"'}, {'」', '"'}, {'『', '"'}, {'』', '"'}, {'［', '['}, {'］', ']'},
                {'＜', '<'}, {'＞', '>'}, {'・', '.'}, {'～', '~'}, {'＿', '_'},

                // 全角符号转半角
                {'　', ' '}, // 全角空格
                {'！', '!'}, {'＂', '"'}, {'＃', '#'}, {'＄', '$'}, {'％', '%'}, {'＆', '&'},
                {'＇', '\''}, {'＊', '*'}, {'＋', '+'}, {'，', ','}, {'－', '-'}, {'．', '.'},
                {'／', '/'}, {'：', ':'}, {'；', ';'}, {'＜', '<'}, {'＝', '='}, {'＞', '>'},
                {'？', '?'}, {'＠', '@'}, {'＾', '^'}, {'＿', '_'}, {'｀', '`'}, {'｛', '{'},
                {'｜', '|'}, {'｝', '}'}, {'～', '~'},

                // 全角数字
                {'０', '0'}, {'１', '1'}, {'２', '2'}, {'３', '3'}, {'４', '4'},
                {'５', '5'}, {'６', '6'}, {'７', '7'}, {'８', '8'}, {'９', '9'},

                // 全角字母
                {'Ａ', 'A'}, {'Ｂ', 'B'}, {'Ｃ', 'C'}, {'Ｄ', 'D'}, {'Ｅ', 'E'}, {'Ｆ', 'F'}, {'Ｇ', 'G'},
                {'Ｈ', 'H'}, {'Ｉ', 'I'}, {'Ｊ', 'J'}, {'Ｋ', 'K'}, {'Ｌ', 'L'}, {'Ｍ', 'M'}, {'Ｎ', 'N'},
                {'Ｏ', 'O'}, {'Ｐ', 'P'}, {'Ｑ', 'Q'}, {'Ｒ', 'R'}, {'Ｓ', 'S'}, {'Ｔ', 'T'}, {'Ｕ', 'U'},
                {'Ｖ', 'V'}, {'Ｗ', 'W'}, {'Ｘ', 'X'}, {'Ｙ', 'Y'}, {'Ｚ', 'Z'},
                {'ａ', 'a'}, {'ｂ', 'b'}, {'ｃ', 'c'}, {'ｄ', 'd'}, {'ｅ', 'e'}, {'ｆ', 'f'}, {'ｇ', 'g'},
                {'ｈ', 'h'}, {'ｉ', 'i'}, {'ｊ', 'j'}, {'ｋ', 'k'}, {'ｌ', 'l'}, {'ｍ', 'm'}, {'ｎ', 'n'},
                {'ｏ', 'o'}, {'ｐ', 'p'}, {'ｑ', 'q'}, {'ｒ', 'r'}, {'ｓ', 's'}, {'ｔ', 't'}, {'ｕ', 'u'},
                {'ｖ', 'v'}, {'ｗ', 'w'}, {'ｘ', 'x'}, {'ｙ', 'y'}, {'ｚ', 'z'}
        };

        // 填充映射表
        for (char[] mapping : mappings) {
            SYMBOL_MAPPING.put(mapping[0], mapping[1]);
        }
    }

    /**
     * 忽略多语言符号差异的contains方法
     * 
     * @param source 源字符串
     * @param target 目标字符串
     * @return 是否包含
     */
    public static boolean containsIgnoreSymbol(String source, String target) {
        // 处理null值情况
        if (source == null || target == null) {
            return false;
        }
        
        // 标准化字符串，将各种语言符号替换为标准符号
        String normalizedSource = normalizeString(source);
        String normalizedTarget = normalizeString(target);
        
        return normalizedSource.contains(normalizedTarget);
    }
    
    /**
     * 标准化字符串，将各种语言符号替换为对应的标准符号
     * 
     * @param str 原始字符串
     * @return 标准化后的字符串
     */
    private static String normalizeString(@NotNull String str) {
        StringBuilder sb = new StringBuilder(str.length());
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            // 如果是特殊符号，替换为对应的标准符号；否则保留原字符
            sb.append(SYMBOL_MAPPING.getOrDefault(c, c));
        }
        return sb.toString();
    }
}