package com.smxz.yjzs.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务异常码枚举
 */
@Getter
@AllArgsConstructor
public enum ExceptionCode {
    
    // 矛盾识别相关异常 5xxx
    CONTRADICTION_ANALYSIS_ERROR(5001, "矛盾识别分析失败"),
    CONTRADICTION_AGENT_EXECUTION_ERROR(5002, "矛盾识别Agent调用失败"),
    CONTRADICTION_RESULT_PARSING_ERROR(5003, "矛盾识别结果解析失败"),
    CONTRADICTION_DATA_SAVE_ERROR(5004, "矛盾识别数据保存失败");
    
    /**
     * 异常码
     */
    private final Integer code;
    
    /**
     * 异常消息
     */
    private final String message;
}