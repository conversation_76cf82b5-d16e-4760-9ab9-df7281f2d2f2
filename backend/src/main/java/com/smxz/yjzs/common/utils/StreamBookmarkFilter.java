package com.smxz.yjzs.common.utils;

import com.smxz.yjzs.constant.CaseTypeConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 流式书签过滤器
 * 用于在流式输出过程中实时插入书签标记
 * 在最后一个"综上所述"前添加"裁判依据"书签（前面必须是换行符）
 * 在"判决如下："后的换行符后添加"判决主文"书签
 */
public class StreamBookmarkFilter {
    
    /**
     * 过滤器状态枚举
     */
    private enum FilterState {
        NORMAL,                    // 正常输出状态
        BUFFERING_ZSSS,           // 缓冲"综上所述"状态
        BUFFERING_AFTER_ZSSS,     // 缓冲"综上所述"后的7个字符
        BUFFERING_PJRX,           // 缓冲"判决如下"状态
        BUFFERING_PJRX_COLON,     // 缓冲"判决如下："后的内容
        WAITING_NEWLINE_AFTER_PJRX, // 等待"判决如下："后的换行符
        STOP_BUFFERING            // 停止缓冲状态
    }
    
    private FilterState state = FilterState.NORMAL;
    private StringBuilder buffer = new StringBuilder();
    private StringBuilder afterZsssBuffer = new StringBuilder(); // 缓冲"综上所述"后的内容
    private StringBuilder outputBuffer = new StringBuilder(); // 输出缓冲区
    private char lastChar = '\0'; // 记录上一个字符
    private boolean foundValidZsss = false; // 是否找到有效的"综上所述"
    private boolean panjueRuxiaBookmarkInserted = false; // 标记"判决主文"书签是否已插入
    private boolean cppjBookmarkInserted = false; // 标记"裁判依据"书签是否已插入
    private String documentType; // 文书类型
    
    // 关键词常量
    private static final String ZSSS = "综上所述";
    private static final String PJRX = "判决如下";
    private static final String CPPJ_BOOKMARK = "<bookmark>裁判依据</bookmark>";
    
    /**
     * 默认构造函数
     */
    public StreamBookmarkFilter() {
        this.documentType = null;
    }
    
    /**
     * 带文书类型的构造函数
     * @param documentType 文书类型
     */
    public StreamBookmarkFilter(String documentType) {
        this.documentType = documentType;
    }
    
    /**
     * 获取判决主文书签
     * @return 根据文书类型返回相应的书签
     */
    private String getPjzwBookmark() {
        if (StringUtils.equals(CaseTypeConstants.DocumentCaseType.CIVIL_JUDGEMENT_1,documentType)) {
            return "<bookmark>判决主文</bookmark>";
        } else {
            return "<bookmark>裁判结果</bookmark>";
        }
    }
    
    /**
     * 过滤输入的字符串块
     * @param chunk 输入的字符串块
     * @return 过滤后的字符串列表
     */
    public List<String> filter(String chunk) {
        if (chunk == null || chunk.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> output = new ArrayList<>();
        
        for (char c : chunk.toCharArray()) {
            List<String> result = processChar(c);
            output.addAll(result);
        }
        
        return output;
    }
    
    /**
     * 处理单个字符
     * @param c 输入字符
     * @return 输出字符串列表
     */
    private List<String> processChar(char c) {
        List<String> output = new ArrayList<>();
        
        switch (state) {
            case NORMAL:
                output.addAll(processNormalState(c));
                break;
            case BUFFERING_ZSSS:
                output.addAll(processBufferingZsssState(c));
                break;
            case BUFFERING_AFTER_ZSSS:
                output.addAll(processBufferingAfterZsssState(c));
                break;
            case BUFFERING_PJRX:
                output.addAll(processBufferingPjrxState(c));
                break;
            case BUFFERING_PJRX_COLON:
                output.addAll(processBufferingPjrxColonState(c));
                break;
            case WAITING_NEWLINE_AFTER_PJRX:
                output.addAll(processWaitingNewlineAfterPjrxState(c));
                break;
            case STOP_BUFFERING:
                // 停止缓冲状态，直接输出所有字符
                output.add(String.valueOf(c));
                break;
        }
        
        // 更新上一个字符
        lastChar = c;
        return output;
    }
    
    /**
     * 处理正常状态
     */
    private List<String> processNormalState(char c) {
        List<String> output = new ArrayList<>();
        
        // 决策理由：如果已经插入了裁判依据书签，不再进行任何模式匹配，直接输出
        if (cppjBookmarkInserted) {
            // 裁判依据书签已插入，只处理判决如下的逻辑
            if (c == '判' && !panjueRuxiaBookmarkInserted) {
                // 可能是"判决如下"开始，进入缓冲状态
                state = FilterState.BUFFERING_PJRX;
                buffer.setLength(0);
                buffer.append(c);
            } else {
                // 正常字符，直接输出
                output.add(String.valueOf(c));
            }
        } else {
            // 裁判依据书签未插入，正常处理模式匹配
            if (c == '综' && (lastChar == '\n' || lastChar == '\r')) {
                // 决策理由：只有当"综"前面是换行符时，才可能是最后一个"综上所述"
                // 可能是"综上所述"开始，进入缓冲状态
                state = FilterState.BUFFERING_ZSSS;
                buffer.setLength(0);
                buffer.append(c);
            } else if (c == '判' && !panjueRuxiaBookmarkInserted) {
                // 可能是"判决如下"开始，进入缓冲状态
                state = FilterState.BUFFERING_PJRX;
                buffer.setLength(0);
                buffer.append(c);
            } else {
                // 正常字符，直接输出
                output.add(String.valueOf(c));
            }
        }
        
        return output;
    }
    
    /**
     * 处理缓冲"综上所述"状态
     */
    private List<String> processBufferingZsssState(char c) {
        List<String> output = new ArrayList<>();
        buffer.append(c);
        String bufferedContent = buffer.toString();
        
        // 检查是否匹配"综上所述"
        if (ZSSS.startsWith(bufferedContent)) {
            if (ZSSS.equals(bufferedContent)) {
                // 完全匹配"综上所述"，立即插入裁判依据书签
                if (!cppjBookmarkInserted) {
                    output.add(CPPJ_BOOKMARK);
                    cppjBookmarkInserted = true;
                }
                // 输出"综上所述"
                output.add(bufferedContent);
                buffer.setLength(0);
                // 决策理由：插入裁判依据书签后回到正常状态，继续处理判决如下的逻辑
                state = FilterState.NORMAL;
            }
            // 部分匹配，继续缓冲
        } else {
            // 不匹配，输出缓冲内容并切换到正常状态
            output.add(bufferedContent);
            buffer.setLength(0);
            state = FilterState.NORMAL;
        }
        
        return output;
    }

    /**
     * 处理缓冲"综上所述"后续字符状态（已废弃，保留用于兼容性）
     */
    private List<String> processBufferingAfterZsssState(char c) {
        List<String> output = new ArrayList<>();
        // 直接切换到正常状态
        output.add(String.valueOf(c));
        state = FilterState.NORMAL;
        return output;
    }

    /**
     * 处理缓冲"判决如下"状态
     */
    private List<String> processBufferingPjrxState(char c) {
        List<String> output = new ArrayList<>();
        buffer.append(c);
        String bufferedContent = buffer.toString();
        
        // 检查是否匹配"判决如下"
        if (PJRX.startsWith(bufferedContent)) {
            if (PJRX.equals(bufferedContent)) {
                // 完全匹配"判决如下"，进入等待冒号状态
                state = FilterState.BUFFERING_PJRX_COLON;
                // 继续缓冲，等待冒号
            }
            // 部分匹配，继续缓冲
        } else {
            // 不匹配，输出缓冲内容并切换到正常状态
            output.add(bufferedContent);
            buffer.setLength(0);
            state = FilterState.NORMAL;
        }
        
        return output;
    }
    
    /**
     * 处理缓冲"判决如下："状态
     */
    private List<String> processBufferingPjrxColonState(char c) {
        List<String> output = new ArrayList<>();
        buffer.append(c);
        
        if (c == '：' || c == ':') {
            // 匹配到冒号，输出"判决如下："并等待换行符
            output.add(buffer.toString());
            buffer.setLength(0);
            state = FilterState.WAITING_NEWLINE_AFTER_PJRX;
        } else {
            // 不是冒号，输出缓冲内容并切换到正常状态
            output.add(buffer.toString());
            buffer.setLength(0);
            state = FilterState.NORMAL;
        }
        
        return output;
    }
    
    /**
     * 处理等待"判决如下："后换行符状态
     */
    private List<String> processWaitingNewlineAfterPjrxState(char c) {
        List<String> output = new ArrayList<>();
        
        if (c == '\n' || c == '\r') {
            // 遇到换行符，先输出换行符，但不立即插入书签
            // 决策理由：需要等到最后一个换行符才插入书签，避免连续换行时插入位置错误
            output.add(String.valueOf(c));
            // 保持当前状态，继续等待可能的后续换行符
        } else if (Character.isWhitespace(c)) {
            // 决策理由：跳过空格、制表符等空白字符，继续等待换行符
            // 是空白字符（空格、制表符等），直接输出但保持当前状态
            output.add(String.valueOf(c));
            // 保持 WAITING_NEWLINE_AFTER_PJRX 状态，继续等待换行符
        } else {
            // 不是换行符也不是空白字符，说明换行符序列结束了
            // 决策理由：此时插入书签，确保在最后一个换行符之后、第一个非换行字符之前插入
            if (!panjueRuxiaBookmarkInserted) {
                output.add(getPjzwBookmark());
                panjueRuxiaBookmarkInserted = true;
            }
            // 输出当前字符并切换到正常状态
            output.add(String.valueOf(c));
            state = FilterState.NORMAL;
        }
        
        return output;
    }
    
    /**
     * 插入裁判依据书签（已废弃，保留用于兼容性）
     */
    private void insertCppjBookmark(List<String> output) {
        if (!cppjBookmarkInserted) {
            output.add(CPPJ_BOOKMARK);
            cppjBookmarkInserted = true;
        }
    }
    
    /**
     * 获取流结束时的剩余内容
     * @return 剩余的内容列表
     */
    public List<String> getRemaining() {
        List<String> output = new ArrayList<>();
        
        // 决策理由：处理流结束时还在等待换行符后内容的情况
        if (state == FilterState.WAITING_NEWLINE_AFTER_PJRX && !panjueRuxiaBookmarkInserted) {
            // 流结束了但还没插入判决主文书签，现在插入
            output.add(getPjzwBookmark());
            panjueRuxiaBookmarkInserted = true;
        }
        
        // 处理缓冲状态的剩余内容
        if (buffer.length() > 0) {
            // 直接输出剩余内容
            output.add(buffer.toString());
        }
        
        if (afterZsssBuffer.length() > 0) {
            // 输出剩余的"综上所述"后续内容
            output.add(afterZsssBuffer.toString());
        }
        
        // 重置状态
        reset();
        return output;
    }
    
    /**
     * 重置过滤器状态
     */
    public void reset() {
        state = FilterState.NORMAL;
        buffer.setLength(0);
        afterZsssBuffer.setLength(0);
        outputBuffer.setLength(0);
        lastChar = '\0';
        foundValidZsss = false;
        panjueRuxiaBookmarkInserted = false;
        cppjBookmarkInserted = false;
    }
}