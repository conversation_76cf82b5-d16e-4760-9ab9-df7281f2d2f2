package com.smxz.yjzs.common.utils;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * <AUTHOR> 诚
 * @since 2025-05-21 12:44
 **/
@Component
public class ThreadPoolUtils {


    @Qualifier("taskExecutor")
    @Resource()
    private ThreadPoolTaskExecutor taskExecutor;

    @Qualifier("ocrTaskExecutor")
    @Resource()
    private ThreadPoolTaskExecutor ocrTaskExecutor;

    /**
     * 执行无返回值的任务
     */
    public void execute(Runnable task) {
        taskExecutor.execute(task);
    }

    /**
     * 提交有返回值的任务
     */
    public <T> Future<T> submit(Callable<T> task) {
        return taskExecutor.submit(task);
    }

    /**
     * 提交多个任务并等待所有完成
     */
    public void executeAll(List<Runnable> tasks) {
        List<Future<?>> futures = new ArrayList<>();
        for (Runnable task : tasks) {
            futures.add(taskExecutor.submit(task));
        }
        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务执行异常", e);
            }
        }
    }

    /**
     * 使用CompletableFuture提交任务
     */
    public CompletableFuture<Void> runAsync(Runnable task) {
        return CompletableFuture.runAsync(task, taskExecutor);
    }

    /**
     * 使用CompletableFuture提交有返回值的任务
     */
    public <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, taskExecutor);
    }

    // OCR专用线程池方法
    
    /**
     * 使用OCR线程池执行无返回值的任务
     */
    public void executeOcr(Runnable task) {
        ocrTaskExecutor.execute(task);
    }

    /**
     * 使用OCR线程池提交有返回值的任务
     */
    public <T> Future<T> submitOcr(Callable<T> task) {
        return ocrTaskExecutor.submit(task);
    }

    /**
     * 使用OCR线程池和CompletableFuture提交任务
     */
    public CompletableFuture<Void> runAsyncOcr(Runnable task) {
        return CompletableFuture.runAsync(task, ocrTaskExecutor);
    }

    /**
     * 使用OCR线程池和CompletableFuture提交有返回值的任务
     */
    public <T> CompletableFuture<T> supplyAsyncOcr(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, ocrTaskExecutor);
    }


}
