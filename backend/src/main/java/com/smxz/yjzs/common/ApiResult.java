package com.smxz.yjzs.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 统一API响应结果
 * @param <T> 数据类型
 */
@Data
@Accessors(chain = true)
public class ApiResult<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;
    
    public ApiResult() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ApiResult(Integer code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应
     */
    public static <T> ApiResult<T> success() {
        return new ApiResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null);
    }
    
    /**
     * 成功响应带数据
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }
    
    /**
     * 成功响应带数据和消息
     */
    public static <T> ApiResult<T> success(T data, String message) {
        return new ApiResult<>(ResultCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResult<T> error() {
        return new ApiResult<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), ResultCode.INTERNAL_SERVER_ERROR.getMessage(), null);
    }
    
    /**
     * 失败响应带消息
     */
    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), message, null);
    }
    
    /**
     * 失败响应带状态码和消息
     */
    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message, null);
    }
    
    /**
     * 失败响应带状态码枚举
     */
    public static <T> ApiResult<T> error(ResultCode resultCode) {
        return new ApiResult<>(resultCode.getCode(), resultCode.getMessage(), null);
    }
    
    /**
     * 失败响应带状态码枚举和数据
     */
    public static <T> ApiResult<T> error(ResultCode resultCode, T data) {
        return new ApiResult<>(resultCode.getCode(), resultCode.getMessage(), data);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
