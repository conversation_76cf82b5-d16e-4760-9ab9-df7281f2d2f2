package com.smxz.yjzs.common.utils;



import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTabStop;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTabJc;

import java.math.BigInteger;

public class WordUtils {
    /**
     * 将 HTML 字符串转为 Word（.docx）字节流，保留样式
     */
    public static byte[] htmlToWord(String html) {
//        try {
//            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
//            XHTMLImporterImpl xhtmlImporter = new XHTMLImporterImpl(wordMLPackage);
//            wordMLPackage.getMainDocumentPart().getContent().addAll(
//                xhtmlImporter.convert(html, null)
//            );
//            ByteArrayOutputStream out = new ByteArrayOutputStream();
//            wordMLPackage.save(out);
//            return out.toByteArray();
//        } catch (Exception e) {
//            throw new RuntimeException("HTML转Word失败", e);
//        }
        return null;
    }

    /**
     * 将字符串内容转换为符合最高人民法院标准格式的判决书Word文档
     * 根据最高法院官方样式：https://www.court.gov.cn/susongyangshi/xiangqing/471.html
     * @param content 要转换的字符串内容
     * @return Word文档的字节数组
     */
    public static byte[] jsonToWord(String content) {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("内容不能为空");
        }

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页边距（按照最高法院标准：左右2.5cm=1800 twips, 上下2cm=1440 twips）
            document.getDocument().getBody().addNewSectPr().addNewPgMar()
                .setLeft(BigInteger.valueOf(1800));
            document.getDocument().getBody().getSectPr().getPgMar()
                .setRight(BigInteger.valueOf(1800));
            document.getDocument().getBody().getSectPr().getPgMar()
                .setTop(BigInteger.valueOf(1440));
            document.getDocument().getBody().getSectPr().getPgMar()
                .setBottom(BigInteger.valueOf(1440));

            String[] lines = content.split("\n");

            // 解析判决书结构
            JudgmentStructure structure = parseJudgmentStructure(lines);

            // 按照最高法院标准格式生成文档
            generateStandardJudgment(document, structure);

            document.write(out);
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 判决书结构数据类
     */
    private static class JudgmentStructure {
        String courtName;           // 法院名称
        String caseNumber;          // 案号
        String plaintiff;           // 原告信息
        String defendant;           // 被告信息
        String thirdParty;          // 第三人信息
        String caseOrigin;          // 案件由来和审理经过
        String plaintiffClaims;     // 原告诉称
        String defendantResponse;   // 被告辩称
        String thirdPartyStatement; // 第三人述称
        String factFinding;         // 事实认定
        String courtOpinion;        // 本院认为
        String judgmentBasis;       // 判决依据
        String judgmentResult;      // 判决结果
        String caseAcceptanceFee;   // 案件受理费
        String appealRights;        // 上诉权利告知
        String judgeChief;          // 审判长
        String judges;              // 审判员
        String clerk;               // 书记员
        String judgmentDate;        // 判决日期
    }

    /**
     * 解析判决书结构
     */
    private static JudgmentStructure parseJudgmentStructure(String[] lines) {
        JudgmentStructure structure = new JudgmentStructure();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) continue;

            // 解析各个部分
            if (line.contains("人民法院") && (line.contains("判决书") || line.contains("裁定书"))) {
                structure.courtName = line;
            } else if (line.matches(".*\\(\\d{4}\\).*\\d+号.*")) {
                structure.caseNumber = line;
            } else if (line.startsWith("原告：") || line.startsWith("原告")) {
                structure.plaintiff = extractMultiLineSection(lines, i, "原告");
            } else if (line.startsWith("被告：") || line.startsWith("被告")) {
                structure.defendant = extractMultiLineSection(lines, i, "被告");
            } else if (line.startsWith("第三人：") || line.startsWith("第三人")) {
                structure.thirdParty = extractMultiLineSection(lines, i, "第三人");
            } else if (line.contains("本院于") && line.contains("立案")) {
                structure.caseOrigin = line;
            } else if (line.contains("向本院提出诉讼请求")) {
                structure.plaintiffClaims = extractMultiLineSection(lines, i, "诉讼请求");
            } else if (line.contains("辩称")) {
                structure.defendantResponse = extractMultiLineSection(lines, i, "辩称");
            } else if (line.startsWith("本院认为")) {
                structure.courtOpinion = extractMultiLineSection(lines, i, "本院认为");
            } else if (line.startsWith("判决如下：") || line.startsWith("一、")) {
                structure.judgmentResult = extractMultiLineSection(lines, i, "判决");
            } else if (line.contains("审判长")) {
                structure.judgeChief = line;
            } else if (line.contains("审判员")) {
                structure.judges = line;
            } else if (line.contains("书记员")) {
                structure.clerk = line;
            } else if (line.matches(".*\\d{4}年\\d{1,2}月\\d{1,2}日.*") ||
                      line.matches(".*二〇\\d{2}年.*月.*日.*")) {
                structure.judgmentDate = line;
            }
        }

        return structure;
    }

    /**
     * 提取多行段落内容
     */
    private static String extractMultiLineSection(String[] lines, int startIndex, String sectionType) {
        StringBuilder content = new StringBuilder();
        content.append(lines[startIndex].trim());

        for (int i = startIndex + 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) break;

            // 判断是否到了下一个段落
            if (isNewSection(line)) break;

            content.append("\n").append(line);
        }

        return content.toString();
    }

    /**
     * 判断是否是新的段落开始
     */
    private static boolean isNewSection(String line) {
        return line.startsWith("原告：") || line.startsWith("被告：") || line.startsWith("第三人：") ||
               line.startsWith("本院认为") || line.startsWith("判决如下：") ||
               line.contains("审判长") || line.contains("书记员") ||
               line.matches(".*\\d{4}年\\d{1,2}月\\d{1,2}日.*");
    }

    /**
     * 按照最高法院标准格式生成判决书文档
     */
    private static void generateStandardJudgment(XWPFDocument document, JudgmentStructure structure) {
        // 1. 标题部分：法院名称
        if (structure.courtName != null) {
            createCourtNameTitle(document, structure.courtName);
        }

        // 2. 文书名称：民事判决书
        createDocumentTitle(document, "民事判决书");

        // 3. 案号
        if (structure.caseNumber != null) {
            createCaseNumber(document, structure.caseNumber);
        }

        // 4. 当事人基本情况
        createPartySection(document, structure);

        // 5. 案件由来和审理经过
        if (structure.caseOrigin != null) {
            createCaseOrigin(document, structure.caseOrigin);
        }

        // 6. 当事人诉辩意见
        createClaimsAndDefenses(document, structure);

        // 7. 事实认定
        if (structure.factFinding != null) {
            createFactFinding(document, structure.factFinding);
        }

        // 8. 本院认为（理由部分）
        if (structure.courtOpinion != null) {
            createCourtOpinion(document, structure.courtOpinion);
        }

        // 9. 判决依据
        if (structure.judgmentBasis != null) {
            createJudgmentBasis(document, structure.judgmentBasis);
        }

        // 10. 判决主文
        if (structure.judgmentResult != null) {
            createJudgmentResult(document, structure.judgmentResult);
        }

        // 11. 迟延履行责任告知
        createDelayedPerformanceNotice(document);

        // 12. 诉讼费用负担
        if (structure.caseAcceptanceFee != null) {
            createCaseAcceptanceFee(document, structure.caseAcceptanceFee);
        }

        // 13. 署名部分（审判长、审判员、日期、书记员）
        createSignatureSection(document, structure);
    }

    /**
     * 创建法院名称标题
     */
    private static void createCourtNameTitle(XWPFDocument document, String courtName) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingBefore(400);
        paragraph.setSpacingAfter(200);

        XWPFRun run = paragraph.createRun();
        run.setText(courtName);
        run.setFontFamily("宋体");
        run.setFontSize(16);
        run.setBold(false);
    }

    /**
     * 创建文书名称标题
     */
    private static void createDocumentTitle(XWPFDocument document, String title) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingAfter(200);

        XWPFRun run = paragraph.createRun();
        run.setText(title);
        run.setFontFamily("黑体");
        run.setFontSize(18);
        run.setBold(true);
    }

    /**
     * 创建案号
     */
    private static void createCaseNumber(XWPFDocument document, String caseNumber) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingAfter(300);

        XWPFRun run = paragraph.createRun();
        run.setText(caseNumber);
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 创建当事人基本情况部分
     */
    private static void createPartySection(XWPFDocument document, JudgmentStructure structure) {
        // 原告信息
        if (structure.plaintiff != null) {
            createPartyInfo(document, structure.plaintiff);
        }

        // 被告信息
        if (structure.defendant != null) {
            createPartyInfo(document, structure.defendant);
        }

        // 第三人信息
        if (structure.thirdParty != null) {
            createPartyInfo(document, structure.thirdParty);
        }

        // 当事人部分后添加空行
        document.createParagraph();
    }

    /**
     * 创建当事人信息
     */
    private static void createPartyInfo(XWPFDocument document, String partyInfo) {
        String[] lines = partyInfo.split("\n");
        for (String line : lines) {
            if (line.trim().isEmpty()) continue;

            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.LEFT);
            paragraph.setSpacingAfter(100);

            // 委托代理人信息需要缩进
            if (line.contains("委托诉讼代理人") || line.contains("委托代理人")) {
                paragraph.setIndentationLeft(720); // 缩进2字符
            }

            XWPFRun run = paragraph.createRun();
            run.setText(line.trim());
            run.setFontFamily("宋体");
            run.setFontSize(14);
        }
    }

    /**
     * 创建案件由来和审理经过
     */
    private static void createCaseOrigin(XWPFDocument document, String caseOrigin) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.BOTH);
        paragraph.setSpacingAfter(200);
        paragraph.setIndentationFirstLine(420); // 首行缩进2字符

        XWPFRun run = paragraph.createRun();
        run.setText(caseOrigin);
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 创建当事人诉辩意见部分
     */
    private static void createClaimsAndDefenses(XWPFDocument document, JudgmentStructure structure) {
        // 原告诉称
        if (structure.plaintiffClaims != null) {
            createSectionWithTitle(document, structure.plaintiffClaims, false);
        }

        // 被告辩称
        if (structure.defendantResponse != null) {
            createSectionWithTitle(document, structure.defendantResponse, false);
        }

        // 第三人述称
        if (structure.thirdPartyStatement != null) {
            createSectionWithTitle(document, structure.thirdPartyStatement, false);
        }
    }

    /**
     * 创建事实认定部分
     */
    private static void createFactFinding(XWPFDocument document, String factFinding) {
        createSectionWithTitle(document, factFinding, false);
    }

    /**
     * 创建本院认为部分
     */
    private static void createCourtOpinion(XWPFDocument document, String courtOpinion) {
        createSectionWithTitle(document, courtOpinion, false);
    }

    /**
     * 创建判决依据部分
     */
    private static void createJudgmentBasis(XWPFDocument document, String judgmentBasis) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.BOTH);
        paragraph.setSpacingBefore(150);
        paragraph.setSpacingAfter(150);
        paragraph.setIndentationFirstLine(420); // 首行缩进2字符

        XWPFRun run = paragraph.createRun();
        run.setText(judgmentBasis);
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 创建判决主文部分
     */
    private static void createJudgmentResult(XWPFDocument document, String judgmentResult) {
        // 先写"判决如下："
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        titleParagraph.setSpacingBefore(200);
        titleParagraph.setSpacingAfter(150);
        titleParagraph.setIndentationFirstLine(420); // 首行缩进2字符

        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("判决如下：");
        titleRun.setFontFamily("宋体");
        titleRun.setFontSize(14);

        // 然后写判决内容
        String[] lines = judgmentResult.split("\n");
        for (String line : lines) {
            if (line.trim().isEmpty()) continue;
            if (line.contains("判决如下")) continue; // 跳过重复的标题

            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.LEFT);
            paragraph.setSpacingAfter(100);

            // 判决主文项目缩进
            if (line.matches("^[一二三四五六七八九十]+、.*") || line.matches("^\\d+[、.].*")) {
                paragraph.setIndentationLeft(420); // 缩进2字符
            }

            XWPFRun run = paragraph.createRun();
            run.setText(line.trim());
            run.setFontFamily("宋体");
            run.setFontSize(14);
        }
    }

    /**
     * 创建迟延履行责任告知
     */
    private static void createDelayedPerformanceNotice(XWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.BOTH);
        paragraph.setSpacingBefore(150);
        paragraph.setSpacingAfter(150);
        paragraph.setIndentationFirstLine(420); // 首行缩进2字符

        XWPFRun run = paragraph.createRun();
        run.setText("如果未按本判决指定的期间履行给付金钱义务，应当依照《中华人民共和国民事诉讼法》第二百五十三条规定，加倍支付迟延履行期间的债务利息。");
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 创建案件受理费部分
     */
    private static void createCaseAcceptanceFee(XWPFDocument document, String feeInfo) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.BOTH);
        paragraph.setSpacingAfter(150);
        paragraph.setIndentationFirstLine(420); // 首行缩进2字符

        XWPFRun run = paragraph.createRun();
        run.setText(feeInfo);
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 创建署名部分（按照最高法院标准格式，全部右对齐）
     * 格式：审判长、审判员 -> 空两行 -> 日期 -> 空一行 -> 书记员
     */
    private static void createSignatureSection(XWPFDocument document, JudgmentStructure structure) {
        // 在署名前添加适当间距，确保署名在页面下方
        addSpacingBeforeSignature(document);

        // 1. 审判长（右对齐，无冒号）
        if (structure.judgeChief != null) {
            createJudgeSignatureRight(document, structure.judgeChief, true);
        }

        // 2. 审判员（右对齐，无冒号）
        if (structure.judges != null) {
            String[] judgeLines = structure.judges.split("\n");
            for (String judgeLine : judgeLines) {
                if (judgeLine.trim().contains("审判员")) {
                    createJudgeSignatureRight(document, judgeLine.trim(), false);
                }
            }
        }

        // 3. 空两行
        document.createParagraph();
        document.createParagraph();

        // 4. 日期（右对齐，中文格式）
        if (structure.judgmentDate != null) {
            createJudgmentDateSignatureRight(document, structure.judgmentDate);
        }

        // 5. 空一行
        document.createParagraph();

        // 6. 书记员（右对齐，在最下面，无冒号）
        if (structure.clerk != null) {
            createJudgeSignatureRight(document, structure.clerk, false);
        }
    }

    /**
     * 在署名前添加适当间距，确保署名在页面下方
     */
    private static void addSpacingBeforeSignature(XWPFDocument document) {
        XWPFParagraph spacingPara = document.createParagraph();
        spacingPara.setSpacingBefore(800); // 较大的前置间距，将署名推到页面下方
    }

    /**
     * 创建右对齐的审判人员署名行（无冒号，姓名对齐）
     * @param document Word文档
     * @param signatureLine 署名行内容
     * @param isFirst 是否是第一行（审判长）
     */
    private static void createJudgeSignatureRight(XWPFDocument document, String signatureLine, boolean isFirst) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.RIGHT); // 强制右对齐

        // 设置间距
        if (isFirst) {
            paragraph.setSpacingBefore(300); // 审判长前面较大间距
        } else {
            paragraph.setSpacingBefore(80);  // 其他人员正常间距
        }
        paragraph.setSpacingAfter(80);

        // 解析职务和姓名，去掉冒号
        String cleanLine = signatureLine.replace(":", "").replace("：", ""); // 去掉中英文冒号
        String[] parts = cleanLine.split("\\s+");

        XWPFRun run = paragraph.createRun();
        if (parts.length >= 2) {
            String position = parts[0]; // 审判长、审判员、书记员
            String name = parts[1]; // 姓名

            // 格式化姓名，确保对齐
            String formattedName = formatNameForAlignment(name);

            // 使用固定格式确保对齐
            run.setText(position + "  " + formattedName);
        } else {
            // 如果解析失败，直接使用原文本（去掉冒号）
            run.setText(cleanLine);
        }

        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 格式化姓名以确保对齐
     * 2个字的姓名中间加空格，3个字的姓名保持原样
     * @param name 原始姓名
     * @return 格式化后的姓名
     */
    private static String formatNameForAlignment(String name) {
        if (name == null || name.trim().isEmpty()) {
            return name;
        }

        String trimmedName = name.trim();

        // 如果是2个字的姓名，中间加空格
        if (trimmedName.length() == 2) {
            return trimmedName.charAt(0) + "   " + trimmedName.charAt(1);
        }
        // 如果是3个字的姓名，保持原样
        else if (trimmedName.length() == 3) {
            return trimmedName;
        }
        // 其他情况（1个字或4个字以上），保持原样
        else {
            return trimmedName;
        }
    }

    /**
     * 创建右对齐的判决日期署名（中文格式）
     * @param document Word文档
     * @param dateText 日期文本
     */
    private static void createJudgmentDateSignatureRight(XWPFDocument document, String dateText) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.RIGHT); // 强制右对齐
        paragraph.setSpacingBefore(160);
        paragraph.setSpacingAfter(160);

        XWPFRun run = paragraph.createRun();
        // 确保日期是中文格式
        String chineseDate = formatToChineseDate(dateText);
        run.setText(chineseDate);
        run.setFontFamily("宋体");
        run.setFontSize(14);
    }

    /**
     * 将日期格式化为中文格式
     * @param dateText 原始日期文本
     * @return 中文格式日期
     */
    private static String formatToChineseDate(String dateText) {
        if (dateText == null || dateText.trim().isEmpty()) {
            return "二〇二四年  月  日";
        }

        // 如果已经是中文格式，直接返回
        if (dateText.contains("年") && dateText.contains("月") && dateText.contains("日")) {
            return dateText;
        }

        // 尝试解析数字格式的日期
        try {
            // 匹配 2023-06-25 或 2023/06/25 格式
            if (dateText.matches("\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}")) {
                String[] parts = dateText.split("[-/]");
                String year = convertToChineseNumber(parts[0]);
                String month = convertToChineseNumber(parts[1]);
                String day = convertToChineseNumber(parts[2]);
                return year + "年" + month + "月" + day + "日";
            }

            // 匹配 2023年6月25日 格式
            if (dateText.matches("\\d{4}年\\d{1,2}月\\d{1,2}日")) {
                // 使用正则表达式提取年月日
                Pattern pattern = Pattern.compile("(\\d{4})年(\\d{1,2})月(\\d{1,2})日");
                Matcher matcher = pattern.matcher(dateText);
                if (matcher.find()) {
                    String year = convertToChineseNumber(matcher.group(1));
                    String month = convertToChineseNumber(matcher.group(2));
                    String day = convertToChineseNumber(matcher.group(3));
                    return year + "年" + month + "月" + day + "日";
                }
            }
        } catch (Exception e) {
            // 解析失败，返回默认格式
        }

        return dateText; // 如果无法解析，返回原文
    }

    /**
     * 将数字转换为中文数字
     * @param number 数字字符串
     * @return 中文数字
     */
    private static String convertToChineseNumber(String number) {
        if (number == null || number.trim().isEmpty()) {
            return "";
        }

        try {
            int num = Integer.parseInt(number.trim());

            // 年份特殊处理（如2023 -> 二〇二三）
            if (num >= 1000 && num <= 9999) {
                return convertYearToChinese(num);
            }

            // 月日处理
            if (num >= 1 && num <= 31) {
                return convertMonthDayToChinese(num);
            }

        } catch (NumberFormatException e) {
            // 解析失败，返回原文
        }

        return number;
    }

    /**
     * 将年份转换为中文（如2023 -> 二〇二三）
     */
    private static String convertYearToChinese(int year) {
        String[] digits = {"〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String yearStr = String.valueOf(year);
        StringBuilder result = new StringBuilder();

        for (char c : yearStr.toCharArray()) {
            int digit = Character.getNumericValue(c);
            result.append(digits[digit]);
        }

        return result.toString();
    }

    /**
     * 将月日转换为中文（如25 -> 二十五）
     */
    private static String convertMonthDayToChinese(int num) {
        if (num <= 0 || num > 31) return String.valueOf(num);

        String[] units = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] tens = {"", "十", "二十", "三十"};

        if (num < 10) {
            return units[num];
        } else if (num == 10) {
            return "十";
        } else if (num < 20) {
            return "十" + units[num - 10];
        } else {
            int tenDigit = num / 10;
            int unitDigit = num % 10;
            return tens[tenDigit] + (unitDigit > 0 ? units[unitDigit] : "");
        }
    }

    // 院印方法已删除，按照用户要求不需要院印标记

    /**
     * 创建带标题的段落
     */
    private static void createSectionWithTitle(XWPFDocument document, String content, boolean isBold) {
        String[] lines = content.split("\n");
        for (String line : lines) {
            if (line.trim().isEmpty()) continue;

            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.BOTH);
            paragraph.setSpacingAfter(150);
            paragraph.setIndentationFirstLine(420); // 首行缩进2字符

            XWPFRun run = paragraph.createRun();
            run.setText(line.trim());
            run.setFontFamily("宋体");
            run.setFontSize(14);
            if (isBold) {
                run.setBold(true);
            }
        }
    }

    /**
     * 处理简单的HTML标签
     * @param line 包含HTML标签的行
     * @return 处理后的纯文本
     */
    private static String processHtmlTags(String line) {
        if (line == null) {
            return "";
        }

        // 移除常见的HTML标签
        String processed = line
            .replaceAll("<[^>]*>", "") // 移除所有HTML标签
            .replaceAll("&nbsp;", " ") // 替换HTML空格
            .replaceAll("&lt;", "<")   // 替换HTML小于号
            .replaceAll("&gt;", ">")   // 替换HTML大于号
            .replaceAll("&amp;", "&")  // 替换HTML&符号
            .replaceAll("&quot;", "\"") // 替换HTML引号
            .trim();

        return processed;
    }

    /**
     * 将字符串内容转换为Word文档格式（增强版，支持更丰富的格式）
     * @param content 要转换的字符串内容
     * @param title 文档标题
     * @return Word文档的字节数组
     */
    public static byte[] jsonToWordEnhanced(String content, String title) {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("内容不能为空");
        }

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 添加文档标题
            if (title != null && !title.trim().isEmpty()) {
                XWPFParagraph titleParagraph = document.createParagraph();
                titleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun titleRun = titleParagraph.createRun();
                titleRun.setText(title);
                titleRun.setBold(true);
                titleRun.setFontSize(18);
                titleRun.setFontFamily("黑体");
                titleRun.setColor("000000");
                
                // 添加标题后的空行
                document.createParagraph();
            }

            // 按行分割内容
            String[] lines = content.split("\n");
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) {
                    // 空行，添加换行
                    document.createParagraph();
                } else {
                    // 处理非空行
                    XWPFParagraph paragraph = document.createParagraph();
                    paragraph.setAlignment(ParagraphAlignment.LEFT);
                    XWPFRun run = paragraph.createRun();
                    
                    // 设置默认字体
                    run.setFontFamily("宋体");
                    run.setFontSize(12);
                    run.setColor("000000");
                    
                    // 判断行类型并设置相应格式
                    if (line.startsWith("#")) {
                        // 标题行
                        run.setBold(true);
                        run.setFontSize(16);
                        run.setFontFamily("黑体");
                        run.setText(line.substring(1).trim());
                    } else if (line.contains("=== ") && line.contains(" ===")) {
                        // 分隔符行
                        run.setBold(true);
                        run.setFontSize(14);
                        run.setColor("666666");
                        run.setText(line);
                    } else if (line.startsWith("文件名称：") || line.startsWith("文件名：")) {
                        // 文件名行
                        run.setBold(true);
                        run.setFontSize(13);
                        run.setColor("0000FF");
                        run.setText(line);
                    } else if (line.startsWith("•") || line.startsWith("-") || line.startsWith("*")) {
                        // 列表项
                        paragraph.setIndentationLeft(720); // 缩进
                        run.setText(line);
                    } else {
                        // 普通文本
                        run.setText(line);
                    }
                }
            }

            // 保存文档
            document.write(out);
            return out.toByteArray();

        } catch (IOException e) {
            throw new RuntimeException("生成Word文档失败", e);
        }
    }
}
