package com.smxz.yjzs.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.extractor.common.enums.DocumentType;
import com.smxz.yjzs.entity.FileUploadRecord;
import com.smxz.yjzs.mapper.FileUploadRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件记录获取工具类
 * 提供统一的文件获取逻辑，优先根据DocumentType获取，如果没有documentType再根据文件名获取
 */
@Slf4j
@Component
public class FileRecordUtils {

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    /**
     * 获取案件所有文件记录（过滤逻辑删除的记录）
     */
    public List<FileUploadRecord> getFileRecords(Long caseImportId) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .eq(FileUploadRecord::getDeleted, 0); // 过滤逻辑删除的记录
        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.debug("获取案件文件记录，caseImportId: {}, 有效文件数量: {}", caseImportId, records.size());
        return records;
    }

    /**
     * 根据DocumentType获取文件记录
     * @param caseImportId 案件ID
     * @param documentType 文档类型
     * @return 文件记录列表
     */
    public List<FileUploadRecord> getFileRecordsByDocumentType(Long caseImportId, DocumentType documentType) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .eq(FileUploadRecord::getDocumentType, documentType)
                   .eq(FileUploadRecord::getDeleted, 0) // 过滤逻辑删除的记录
                   .orderByDesc(FileUploadRecord::getUploadTime);
        
        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.info("根据DocumentType获取文件记录，caseImportId: {}, documentType: {}, 文件数量: {}", 
                caseImportId, documentType, records.size());
        return records;
    }

    /**
     * 根据文件名关键字获取文件记录
     * @param caseImportId 案件ID
     * @param fileName 文件名关键字
     * @return 文件记录列表
     */
    public List<FileUploadRecord> getFileRecordsByFileName(Long caseImportId, String fileName) {
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .like(FileUploadRecord::getFileName, fileName)
                   .eq(FileUploadRecord::getDeleted, 0) // 过滤逻辑删除的记录
                   .orderByDesc(FileUploadRecord::getUploadTime);
        
        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.info("根据文件名获取文件记录，caseImportId: {}, fileName: {}, 文件数量: {}", 
                caseImportId, fileName, records.size());
        return records;
    }

    /**
     * 优先根据DocumentType获取文件记录，如果没有则根据文件名获取
     * @param caseImportId 案件ID
     * @param documentType 文档类型（优先）
     * @param fileName 文件名关键字（备选）
     * @return 文件记录列表
     */
    public List<FileUploadRecord> getFileRecords(Long caseImportId, DocumentType documentType, String fileName) {
        // 优先根据DocumentType查找
        if (documentType != null) {
            List<FileUploadRecord> records = getFileRecordsByDocumentType(caseImportId, documentType);
            if (!records.isEmpty()) {
                log.info("根据DocumentType找到文件，caseImportId: {}, documentType: {}, 文件数量: {}", 
                        caseImportId, documentType, records.size());
                return records;
            }
            log.warn("根据DocumentType未找到文件，caseImportId: {}, documentType: {}", caseImportId, documentType);
        }
        
        // 如果DocumentType没有找到文件，则根据文件名查找
        if (StringUtils.isNotBlank(fileName)) {
            List<FileUploadRecord> records = getFileRecordsByFileName(caseImportId, fileName);
            if (!records.isEmpty()) {
                log.info("根据文件名找到文件，caseImportId: {}, fileName: {}, 文件数量: {}", 
                        caseImportId, fileName, records.size());
                return records;
            }
            log.warn("根据文件名未找到文件，caseImportId: {}, fileName: {}", caseImportId, fileName);
        }
        
        log.warn("未找到匹配的文件记录，caseImportId: {}, documentType: {}, fileName: {}", 
                caseImportId, documentType, fileName);
        return List.of();
    }

    /**
     * 获取起诉状文件记录
     * 优先根据DocumentType.QSZ获取，如果没有则根据文件名"起诉状"获取
     */
    public List<FileUploadRecord> getPlaintiffRecords(Long caseImportId) {
        return getFileRecords(caseImportId, DocumentType.QSZ, "起诉状");
    }

    /**
     * 获取答辩状文件记录
     * 优先根据DocumentType.DBZ获取，如果没有则根据文件名"答辩状"获取
     */
    public List<FileUploadRecord> getDefendantRecords(Long caseImportId) {
        return getFileRecords(caseImportId, DocumentType.DBZ, "答辩状");
    }

    /**
     * 获取庭审笔录文件记录
     * 优先根据DocumentType.TSBL获取，如果没有则根据文件名"庭审笔录"或"开庭笔录"获取
     */
    public List<FileUploadRecord> getCourtRecords(Long caseImportId) {
        // 先尝试DocumentType.TSBL
        List<FileUploadRecord> records = getFileRecords(caseImportId, DocumentType.TSBL, null);
        if (!records.isEmpty()) {
            return records;
        }
        
        // 如果没有找到，尝试根据文件名查找
        records = getFileRecordsByFileName(caseImportId, "庭审笔录");
        if (!records.isEmpty()) {
            return records;
        }
        
        // 最后尝试"开庭笔录"
        return getFileRecordsByFileName(caseImportId, "开庭笔录");
    }

    /**
     * 获取判决书/裁定书文件记录
     * 优先根据DocumentType获取，如果没有则根据文件名获取
     */
    public List<FileUploadRecord> getJudgmentRecords(Long caseImportId) {
        // 可以根据需要添加判决书的DocumentType
        // 目前先根据文件名查找
        LambdaQueryWrapper<FileUploadRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecord::getCaseImportId, caseImportId)
                   .eq(FileUploadRecord::getDeleted, 0)
                   .and(wrapper -> wrapper
                       .like(FileUploadRecord::getFileName, "判决书")
                       .or()
                       .like(FileUploadRecord::getFileName, "裁定书"))
                   .orderByDesc(FileUploadRecord::getUploadTime);
        
        List<FileUploadRecord> records = fileUploadRecordMapper.selectList(queryWrapper);
        log.info("获取判决书/裁定书文件记录，caseImportId: {}, 文件数量: {}", caseImportId, records.size());
        return records;
    }
}
