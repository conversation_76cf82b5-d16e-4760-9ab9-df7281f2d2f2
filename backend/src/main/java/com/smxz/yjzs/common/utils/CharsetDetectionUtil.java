package com.smxz.yjzs.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 字符编码检测工具类
 * 使用 juniversalchardet 库检测文件编码
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-30
 */
@Slf4j
public class CharsetDetectionUtil {
    
    /**
     * 默认编码 - GBK（适用于中文Windows系统创建的压缩文件）
     */
    public static final String DEFAULT_CHARSET = "GBK";
    
    /**
     * 备用编码列表，按优先级排序
     */
    private static final String[] FALLBACK_CHARSETS = {
        "GBK", "UTF-8", "GB2312", "ISO-8859-1"
    };
    
    /**
     * 检测压缩文件的编码
     * 
     * @param archiveFile 压缩文件
     * @return 检测到的编码，如果检测失败则返回默认编码
     */
    public static String detectArchiveCharset(MultipartFile archiveFile) {
        try (InputStream inputStream = archiveFile.getInputStream();
             BufferedInputStream bufferedStream = new BufferedInputStream(inputStream)) {
            
            log.info("开始检测压缩文件编码: {}", archiveFile.getOriginalFilename());
            
            // 读取文件头部数据进行编码检测
            byte[] buffer = new byte[4096];
            int bytesRead = bufferedStream.read(buffer);
            
            if (bytesRead > 0) {
                String detectedCharset = detectCharsetFromBytes(buffer, bytesRead);
                if (detectedCharset != null) {
                    log.info("检测到压缩文件编码: {} -> {}", archiveFile.getOriginalFilename(), detectedCharset);
                    return detectedCharset;
                }
            }
            
            // 如果检测失败，根据文件名推测编码
            String guessedCharset = guessCharsetByFilename(archiveFile.getOriginalFilename());
            log.info("编码检测失败，根据文件名推测编码: {} -> {}", archiveFile.getOriginalFilename(), guessedCharset);
            return guessedCharset;
            
        } catch (IOException e) {
            log.warn("检测压缩文件编码时发生异常: {}", archiveFile.getOriginalFilename(), e);
            return DEFAULT_CHARSET;
        }
    }
    
    /**
     * 从字节数组检测编码
     * 
     * @param bytes 字节数组
     * @param length 有效字节长度
     * @return 检测到的编码，如果检测失败则返回null
     */
    public static String detectCharsetFromBytes(byte[] bytes, int length) {
        try {
            UniversalDetector detector = new UniversalDetector(null);
            detector.handleData(bytes, 0, length);
            detector.dataEnd();
            
            String detectedCharset = detector.getDetectedCharset();
            detector.reset();
            
            if (detectedCharset != null) {
                // 验证检测到的编码是否有效
                if (isValidCharset(detectedCharset)) {
                    return normalizeCharsetName(detectedCharset);
                }
            }
            
            return null;
        } catch (Exception e) {
            log.debug("字节编码检测失败", e);
            return null;
        }
    }
    
    /**
     * 根据文件名推测编码
     * 
     * @param filename 文件名
     * @return 推测的编码
     */
    public static String guessCharsetByFilename(String filename) {
        if (filename == null) {
            return DEFAULT_CHARSET;
        }
        
        // 检查文件名是否包含中文字符
        if (containsChinese(filename)) {
            return "GBK"; // 包含中文的文件名通常来自中文系统，使用GBK
        }
        
        // 检查文件名是否全为ASCII字符
        if (isAscii(filename)) {
            return "UTF-8"; // ASCII文件名可以安全使用UTF-8
        }
        
        return DEFAULT_CHARSET;
    }
    
    /**
     * 获取最佳编码选择
     * 结合检测结果和系统环境做出最佳选择
     * 
     * @param archiveFile 压缩文件
     * @return 最佳编码选择
     */
    public static String getBestCharset(MultipartFile archiveFile) {
        // 首先尝试检测编码
        String detectedCharset = detectArchiveCharset(archiveFile);
        
        // 如果检测到的是常见的中文编码，直接使用
        if ("GBK".equalsIgnoreCase(detectedCharset) || 
            "GB2312".equalsIgnoreCase(detectedCharset) ||
            "UTF-8".equalsIgnoreCase(detectedCharset)) {
            return detectedCharset;
        }
        
        // 对于其他编码，根据经验选择
        if ("WINDOWS-1252".equalsIgnoreCase(detectedCharset) ||
            "ISO-8859-1".equalsIgnoreCase(detectedCharset)) {
            // 这些编码通常表示检测不准确，使用GBK作为中文系统的默认选择
            return "GBK";
        }
        
        return detectedCharset != null ? detectedCharset : DEFAULT_CHARSET;
    }
    
    /**
     * 验证编码名称是否有效
     */
    private static boolean isValidCharset(String charsetName) {
        try {
            return Charset.isSupported(charsetName);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 标准化编码名称
     */
    private static String normalizeCharsetName(String charsetName) {
        if (charsetName == null) {
            return DEFAULT_CHARSET;
        }
        
        String normalized = charsetName.toUpperCase();
        
        // 标准化常见编码名称
        switch (normalized) {
            case "UTF8":
                return "UTF-8";
            case "GB2312":
            case "GBK":
            case "GB18030":
                return "GBK"; // 统一使用GBK，它向下兼容GB2312
            case "ISO-8859-1":
            case "LATIN1":
                return "ISO-8859-1";
            default:
                return charsetName;
        }
    }
    
    /**
     * 检查字符串是否包含中文字符
     */
    private static boolean containsChinese(String str) {
        if (str == null) {
            return false;
        }
        
        for (char c : str.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FFF) { // 中文字符范围
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查字符串是否全为ASCII字符
     */
    private static boolean isAscii(String str) {
        if (str == null) {
            return true;
        }
        
        for (char c : str.toCharArray()) {
            if (c > 127) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 尝试多种编码解压，找到最佳编码
     * 
     * @param archiveFile 压缩文件
     * @return 最佳编码，如果都失败则返回默认编码
     */
    public static String findBestCharsetByTrial(MultipartFile archiveFile) {
        // 首先尝试检测到的编码
        String detectedCharset = detectArchiveCharset(archiveFile);
        if (testCharsetWithArchive(archiveFile, detectedCharset)) {
            return detectedCharset;
        }
        
        // 如果检测的编码不工作，尝试备用编码
        for (String charset : FALLBACK_CHARSETS) {
            if (!charset.equals(detectedCharset) && testCharsetWithArchive(archiveFile, charset)) {
                log.info("通过试验找到最佳编码: {} -> {}", archiveFile.getOriginalFilename(), charset);
                return charset;
            }
        }
        
        log.warn("无法找到合适的编码，使用默认编码: {} -> {}", archiveFile.getOriginalFilename(), DEFAULT_CHARSET);
        return DEFAULT_CHARSET;
    }
    
    /**
     * 测试指定编码是否能正确处理压缩文件
     * 这里只是一个简单的测试，实际项目中可以根据需要扩展
     */
    private static boolean testCharsetWithArchive(MultipartFile archiveFile, String charset) {
        // 这里可以实现一个简单的测试逻辑
        // 比如尝试用指定编码读取压缩文件的文件列表
        // 如果没有异常且文件名看起来正常，则认为编码正确
        
        try {
            // 验证编码是否被支持
            Charset.forName(charset);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
