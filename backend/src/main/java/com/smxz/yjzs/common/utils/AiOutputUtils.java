package com.smxz.yjzs.common.utils;

import reactor.core.publisher.Flux;

public class AiOutputUtils {

    public static String removeThink(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("(?s)<think>.*?</think>", "");
    }

    public static String removeCodeBlock(String str) {
        if (str == null) {
            return null;
        }
        return str.replace("```json", "").replace("```", "").replace("*", "")
                .replace("#","").replace("---", "");
    }

    public static String getJsonPart(String str) {
        if (str == null) {
            return null;
        }

        return AiOutputUtils.removeThink(str).replace("```json", "").replace("```", "");
    }

    /**
     * 清理AI响应内容
     * 移除markdown代码块标记和<think>标签，并去除首尾空白
     *
     * @param response AI响应内容
     * @return 清理后的内容
     */
    public static String cleanResponse(String response) {
        if (response == null) {
            return null;
        }

        String cleaned = response;

        // 移除<think>标签及其内容
        cleaned = removeThink(cleaned);

        // 移除markdown代码块标记
        cleaned = removeCodeBlock(cleaned);

        // 去除首尾空白
        return cleaned.trim();
    }

    /**
     * 对流式输出应用think标签过滤器
     * 解决流式传输中think标签被分割的问题
     *
     * @param sourceFlux 原始流式数据
     * @return 过滤后的流式数据
     */
    public static Flux<String> applyStreamThinkFilter(Flux<String> sourceFlux) {
        StreamThinkTagFilter filter = new StreamThinkTagFilter();

        return sourceFlux
                .map(filter::filter)
                .filter(chunk -> !chunk.isEmpty())
                .concatWith(Flux.defer(() -> {
                    // 流结束时处理剩余内容
                    String remaining = filter.getRemaining();
                    return remaining.isEmpty() ? Flux.empty() : Flux.just(remaining);
                }));
    }

    /**
     * 对流式输出应用完整的AI响应清理
     * 包括think标签过滤和markdown代码块清理
     *
     * @param sourceFlux 原始流式数据
     * @return 清理后的流式数据
     */
    public static Flux<String> applyStreamCleanup(Flux<String> sourceFlux) {
        return applyStreamThinkFilter(sourceFlux)
                .map(chunk -> {
                    // 清理markdown代码块标记
                    return removeCodeBlock(chunk);
                });
    }

}
