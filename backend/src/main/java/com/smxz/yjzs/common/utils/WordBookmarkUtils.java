package com.smxz.yjzs.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.VerticalAlign;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * Word文档书签替换工具类
 * 用于处理Word模板中的书签替换功能
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
public class WordBookmarkUtils {

    /**
     * 替换Word文档中的书签
     *
     * @param templatePath 模板文件路径（相对于resources目录）
     * @param bookmarkMap  书签替换映射
     * @return 替换后的Word文档字节数组
     */
    public static byte[] replaceBookmarks(String templatePath, Map<String, String> bookmarkMap) {
        try (InputStream templateStream = new ClassPathResource(templatePath).getInputStream();
             XWPFDocument document = new XWPFDocument(templateStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 遍历所有段落进行书签替换
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceBookmarksInParagraph(paragraph, bookmarkMap);
            }

            // 保存文档到输出流
            document.write(outputStream);
            log.info("Word文档书签替换完成，替换了{}个书签", bookmarkMap.size());
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Word文档书签替换失败", e);
            throw new RuntimeException("Word文档书签替换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 在段落中替换书签
     *
     * @param paragraph   段落对象
     * @param bookmarkMap 书签替换映射
     */
    private static void replaceBookmarksInParagraph(XWPFParagraph paragraph, Map<String, String> bookmarkMap) {
        String paragraphText = paragraph.getText();
        if (paragraphText == null || paragraphText.isEmpty()) {
            return;
        }

        // 检查段落是否包含书签
        boolean hasBookmark = false;
        for (String bookmark : bookmarkMap.keySet()) {
            if (paragraphText.contains("【" + bookmark + "】")) {
                hasBookmark = true;
                break;
            }
        }

        if (!hasBookmark) {
            return;
        }

        // 保存段落格式信息
        ParagraphFormat paragraphFormat = saveParagraphFormat(paragraph);

        // 保存第一个有效run的格式信息
        RunFormat runFormat = saveRunFormat(paragraph);

        // 清除原有的runs
        List<XWPFRun> runs = paragraph.getRuns();
        for (int i = runs.size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }

        // 替换书签并重新创建run
        String newText = paragraphText;
        for (Map.Entry<String, String> entry : bookmarkMap.entrySet()) {
            String bookmark = "【" + entry.getKey() + "】";
            String replacement = entry.getValue() != null ? entry.getValue() : "";
            newText = newText.replace(bookmark, replacement);
        }

        // 如果替换后文本为空，创建一个空的run以保持段落结构
        if (newText == null || newText.trim().isEmpty()) {
            XWPFRun emptyRun = paragraph.createRun();
            emptyRun.setText("");
            applyRunFormat(emptyRun, runFormat);
        } else {
            // 处理包含换行符的文本
            if (newText.contains("\n")) {
                // 如果包含换行符，需要分割文本并使用addBreak()
                String[] lines = newText.split("\n", -1); // 使用-1保留空字符串
                for (int i = 0; i < lines.length; i++) {
                    XWPFRun run = paragraph.createRun();
                    run.setText(lines[i]);
                    // 应用保存的格式
                    applyRunFormat(run, runFormat);

                    // 除了最后一行，都要添加换行符
                    if (i < lines.length - 1) {
                        run.addBreak();
                    }
                }
            } else {
                // 没有换行符，直接设置文本
                XWPFRun newRun = paragraph.createRun();
                newRun.setText(newText);
                // 应用保存的格式
                applyRunFormat(newRun, runFormat);
            }
        }

        // 恢复段落格式
        applyParagraphFormat(paragraph, paragraphFormat);

        log.debug("替换段落书签: {} -> {}", paragraphText, newText);
    }

    /**
     * 格式化审判组织成员信息
     * 实现角色和姓名的对齐
     *
     * @param members 审判组织成员列表，每个元素包含role和name
     * @return 格式化后的字符串
     */
    public static String formatTrialMembers(List<Map<String, String>> members) {
        if (members == null || members.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        
        // 计算最长的角色名称长度（以字符为单位）
        int maxRoleLength = 0;
        
        for (Map<String, String> member : members) {
            String role = member.get("role");
            if (role != null) {
                maxRoleLength = Math.max(maxRoleLength, getDisplayLength(role));
            }
        }

        // 格式化每个成员
        for (int i = 0; i < members.size(); i++) {
            Map<String, String> member = members.get(i);
            String role = member.get("role");
            String name = member.get("name");
            
            if (role != null && name != null) {
                // 角色对齐
                String paddedRole = padString(role, maxRoleLength);
                
                result.append(paddedRole).append("    ").append(name);
                
                // 除了最后一个，都要换行
                if (i < members.size() - 1) {
                    result.append("\n");
                }
            }
        }

        return result.toString();
    }

    /**
     * 使用统一对齐标准格式化审判组织成员信息
     *
     * @param members 要格式化的成员列表
     * @param allMembers 所有成员列表（用于计算统一的对齐标准）
     * @return 格式化后的字符串
     */
    public static String formatTrialMembersWithUnifiedAlignment(List<Map<String, String>> members, List<Map<String, String>> allMembers) {
        if (members == null || members.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        // 计算所有成员中最长的角色名称长度（以字符为单位）
        int maxRoleLength = 0;

        for (Map<String, String> member : allMembers) {
            String role = member.get("role");
            if (role != null) {
                maxRoleLength = Math.max(maxRoleLength, getDisplayLength(role));
            }
        }

        // 格式化当前成员列表
        for (int i = 0; i < members.size(); i++) {
            Map<String, String> member = members.get(i);
            String role = member.get("role");
            String name = member.get("name");

            if (role != null) {
                // 使用统一的角色对齐标准
                String paddedRole = padString(role, maxRoleLength);
                String displayName = name != null ? name : "";
                
                result.append(paddedRole).append("    ").append(displayName);

                // 除了最后一个，都要换行
                if (i < members.size() - 1) {
                    result.append("\n");
                }
            }
        }

        return result.toString();
    }

    /**
     * 计算字符串的显示长度（中文字符算2个长度，英文字符算1个长度）
     *
     * @param str 字符串
     * @return 显示长度
     */
    private static int getDisplayLength(String str) {
        if (str == null) {
            return 0;
        }
        
        int length = 0;
        for (char c : str.toCharArray()) {
            // 中文字符范围
            if (c >= 0x4E00 && c <= 0x9FFF) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }

    /**
     * 使用空格对字符串进行右填充（优化版）
     *
     * @param str        原字符串
     * @param targetLength 目标显示长度
     * @return 填充后的字符串
     */
    private static String padString(String str, int targetLength) {
        if (str == null) {
            str = "";
        }
        
        int currentLength = getDisplayLength(str);
        if (currentLength >= targetLength) {
            return str;
        }
        
        StringBuilder result = new StringBuilder(str);
        int paddingNeeded = targetLength - currentLength;
        
        // 使用半角空格进行填充，确保对齐的一致性
        for (int i = 0; i < paddingNeeded; i++) {
            result.append(" ");
        }
        
        return result.toString();
    }

    /**
     * 段落格式信息类
     */
    private static class ParagraphFormat {
        public String alignment;
        public int spacingBefore;
        public int spacingAfter;
        public int spacingBetween;
        public int indentationLeft;
        public int indentationRight;
        public int indentationFirstLine;
    }

    /**
     * Run格式信息类
     */
    private static class RunFormat {
        public String fontFamily;
        public int fontSize;
        public boolean isBold;
        public boolean isItalic;
        public boolean isUnderline;
        public String color;
        public boolean isStrike;
        public String vertAlign;
    }

    /**
     * 保存段落格式信息
     */
    private static ParagraphFormat saveParagraphFormat(XWPFParagraph paragraph) {
        ParagraphFormat format = new ParagraphFormat();

        try {
            // 保存对齐方式
            if (paragraph.getAlignment() != null) {
                format.alignment = paragraph.getAlignment().toString();
            }

            // 保存间距信息
            format.spacingBefore = (int) paragraph.getSpacingBefore();
            format.spacingAfter = (int) paragraph.getSpacingAfter();
            format.spacingBetween = (int) paragraph.getSpacingBetween();

            // 保存缩进信息
            format.indentationLeft = (int) paragraph.getIndentationLeft();
            format.indentationRight = (int) paragraph.getIndentationRight();
            format.indentationFirstLine = (int) paragraph.getIndentationFirstLine();

        } catch (Exception e) {
            log.warn("保存段落格式时出现异常", e);
        }

        return format;
    }

    /**
     * 保存Run格式信息
     */
    private static RunFormat saveRunFormat(XWPFParagraph paragraph) {
        RunFormat format = new RunFormat();

        // 设置默认值
        format.fontFamily = "宋体";
        format.fontSize = 12;
        format.isBold = false;
        format.isItalic = false;
        format.isUnderline = false;
        format.color = "000000";
        format.isStrike = false;
        format.vertAlign = null;

        List<XWPFRun> runs = paragraph.getRuns();
        if (!runs.isEmpty()) {
            // 找到第一个有文本内容的run
            XWPFRun sourceRun = null;
            for (XWPFRun run : runs) {
                if (run.getText(0) != null && !run.getText(0).trim().isEmpty()) {
                    sourceRun = run;
                    break;
                }
            }

            // 如果没有找到有文本的run，使用第一个run
            if (sourceRun == null && !runs.isEmpty()) {
                sourceRun = runs.get(0);
            }

            if (sourceRun != null) {
                try {
                    // 保存字体信息
                    if (sourceRun.getFontFamily() != null) {
                        format.fontFamily = sourceRun.getFontFamily();
                    }

                    if (sourceRun.getFontSize() != -1) {
                        format.fontSize = sourceRun.getFontSize();
                    }

                    // 保存样式信息
                    format.isBold = sourceRun.isBold();
                    format.isItalic = sourceRun.isItalic();
                    format.isStrike = sourceRun.isStrikeThrough();

                    // 保存下划线信息
                    if (sourceRun.getUnderline() != null) {
                        format.isUnderline = true;
                    }

                    // 保存颜色信息
                    if (sourceRun.getColor() != null) {
                        format.color = sourceRun.getColor();
                    }

                    // 保存上下标信息 - POI中使用不同的方法
                    // 注意：POI的XWPFRun可能没有直接的上下标获取方法，这里先跳过
                    format.vertAlign = null;

                } catch (Exception e) {
                    log.warn("保存Run格式时出现异常", e);
                }
            }
        }

        return format;
    }

    /**
     * 应用段落格式
     */
    private static void applyParagraphFormat(XWPFParagraph paragraph, ParagraphFormat format) {
        try {
            if (format.alignment != null) {
                // 这里需要根据字符串转换回对应的枚举值
                // 由于POI的限制，我们保持原有的对齐方式
            }

            // 应用间距
            if (format.spacingBefore > 0) {
                paragraph.setSpacingBefore(format.spacingBefore);
            }
            if (format.spacingAfter > 0) {
                paragraph.setSpacingAfter(format.spacingAfter);
            }
            if (format.spacingBetween > 0) {
                paragraph.setSpacingBetween(format.spacingBetween);
            }

            // 应用缩进
            if (format.indentationLeft > 0) {
                paragraph.setIndentationLeft(format.indentationLeft);
            }
            if (format.indentationRight > 0) {
                paragraph.setIndentationRight(format.indentationRight);
            }
            if (format.indentationFirstLine != 0) {
                paragraph.setIndentationFirstLine(format.indentationFirstLine);
            }

        } catch (Exception e) {
            log.warn("应用段落格式时出现异常", e);
        }
    }

    /**
     * 应用Run格式
     */
    private static void applyRunFormat(XWPFRun run, RunFormat format) {
        try {
            // 应用字体
            if (format.fontFamily != null) {
                run.setFontFamily(format.fontFamily);
            }

            if (format.fontSize > 0) {
                run.setFontSize(format.fontSize);
            }

            // 应用样式
            run.setBold(format.isBold);
            run.setItalic(format.isItalic);
            run.setStrikeThrough(format.isStrike);

            // 应用下划线
            if (format.isUnderline) {
                run.setUnderline(UnderlinePatterns.SINGLE);
            }

            // 应用颜色
            if (format.color != null && !format.color.equals("000000")) {
                run.setColor(format.color);
            }

            // 应用上下标 - 暂时跳过，POI API可能不同
            // 注意：POI的上下标设置方法可能与预期不同，这里先跳过以避免编译错误
            // if (format.vertAlign != null) {
            //     // 需要根据实际POI版本调整API调用
            // }

        } catch (Exception e) {
            log.warn("应用Run格式时出现异常", e);
        }
    }
}
