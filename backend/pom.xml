<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.smxz</groupId>
    <artifactId>t<PERSON><PERSON><PERSON><PERSON><PERSON></artifactId>
    <version>1.0.0</version>
    <description>天枢智鉴定 Backend Project</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.4</version>
    </parent>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <minio.version>8.5.7</minio.version>
        <poi.version>5.2.5</poi.version>
        <pdfbox.version>3.0.1</pdfbox.version>
        <guava.version>32.1.3-jre</guava.version>
        <gson.version>2.10.1</gson.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-io.version>2.15.1</commons-io.version>
        <knife4j.version>4.5.0</knife4j.version>
        <kotlin.version>2.1.21</kotlin.version>
        <xz-agent.version>1.6.0-SNAPSHOT</xz-agent.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Thumbnailator 图片压缩 -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.20</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.27.1</version>
        </dependency>

        <!-- knife4j -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!-- Database -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>3.5.11</version>
        </dependency>

        <!-- MinIO -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>

        <!-- poi office 套件-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <!-- PDFBox 套件 -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${pdfbox.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>${pdfbox.version}</version>
        </dependency>

        <!-- Google Guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.57</version>
        </dependency>

        <!-- Gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <!-- Apache Commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- SMXZ Document Convert Spring Boot Starter -->
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-document-convert-spring-boot-starter</artifactId>
            <version>1.3.6</version>
        </dependency>

        <!-- SMXZ OCR Spring Boot Starter -->
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-ocr</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- SMXZ Ragflow Spring Boot Starter -->
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-ragflow-spring-boot-starter</artifactId>
            <version>1.3.4</version>
        </dependency>

        <!-- Apache Ant for ZIP file handling with encoding support -->
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.14</version>
        </dependency>

        <!-- 编码检测库 -->
        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>2.4.0</version>
        </dependency>

        <!-- 引入 Spring AI 相关依赖 start -->
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>dynamic-ai-model-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!-- 引入 Spring AI 相关依赖 end -->
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>pdf-text-search</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-logback</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-log4jdbc-p6spy</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-agent-spring-boot-starter</artifactId>
            <version>${xz-agent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-agent-web-starter</artifactId>
            <version>${xz-agent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-document-extractor-client-spring-boot-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>2.1.21</version>
        </dependency>
        <dependency>
            <groupId>com.smxz</groupId>
            <artifactId>smxz-console</artifactId>
            <version>1.1.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 