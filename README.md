# 天枢智鉴 (<PERSON><PERSON><PERSON><PERSON>)

## 项目简介

天枢智鉴是一个基于AI的法院智能辅助审判系统，主要用于辅助法官进行案件分析、证据整理、争议焦点识别等工作。

## 技术架构

- **后端**: Spring Boot 3.x + MyBatis + MySQL
- **前端**: Vue 3.x + TypeScript + Element Plus  
- **部署**: Docker + GitLab CI/CD
- **AI集成**: 支持多种大语言模型

## 开发规范

### 分支管理策略 (Git Flow)

我们采用 Git Flow 分支模型进行开发：

```
main (生产环境)
└── develop (开发主分支)
    ├── feature/* (功能分支)
    └── release/* (发布分支)
```

**分支说明：**
- `main`: 生产环境分支，只包含稳定的发布版本
- `develop`: 开发主分支，包含最新的开发代码
- `feature/*`: 功能分支，从 develop 分支创建
- `release/*`: 发布分支，用于发布前的测试和修复

### 代码提交规范

#### 1. 提交消息格式 (Conventional Commits)

```
<type>(<scope>): <subject>

<body>

<footer>
```

**示例：**
```
feat(用户管理): 添加用户权限验证功能

- 实现基于角色的权限控制
- 添加权限中间件
- 更新用户登录逻辑

Closes #123
```

#### 2. 提交类型 (type)

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化，不影响功能的变更
- `refactor`: 重构代码，既不是新增功能也不是修复bug
- `perf`: 性能优化
- `test`: 添加或修改测试
- `build`: 构建系统或外部依赖变更
- `ci`: CI配置文件和脚本变更
- `chore`: 其他不修改src或测试文件的变更

#### 3. 作用域 (scope)

根据功能模块划分，例如：
- `用户管理`
- `案件分析`  
- `证据管理`
- `文档生成`
- `系统配置`

#### 4. 开发流程

**功能开发流程：**
```bash
# 1. 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/用户权限管理

# 2. 开发完成后提交
git add .
git commit -m "feat(用户管理): 添加角色权限控制功能"

# 3. 推送功能分支
git push origin feature/用户权限管理

# 4. 创建 Merge Request 到 develop 分支
# 5. 代码审查通过后合并到 develop
```

**发布流程：**
```bash
# 1. 从develop创建发布分支
git checkout -b release/v1.2.0

# 2. 发布测试和bug修复
git commit -m "fix(系统): 修复登录超时问题"

# 3. 发布完成后合并到main和develop
git checkout main
git merge release/v1.2.0
git tag v1.2.0

git checkout develop  
git merge release/v1.2.0
```

### 代码审查要求

1. **必须代码审查**: 所有代码都必须经过至少一人审查
2. **自动化检查**: 提交前必须通过CI/CD检查
3. **测试覆盖**: 新功能需要包含相应的单元测试
4. **文档更新**: API变更需要同步更新文档

### 代码质量标准

1. **后端规范**: 
   - 遵循阿里巴巴Java开发手册
   - 使用Checkstyle进行代码风格检查
   - 单元测试覆盖率不低于80%

2. **前端规范**:
   - 遵循Vue.js官方风格指南  
   - 使用ESLint + Prettier进行代码检查
   - 组件需要编写对应的测试用例

### 版本号管理

采用语义化版本号 (Semantic Versioning)：`MAJOR.MINOR.PATCH`

- `MAJOR`: 不兼容的API修改
- `MINOR`: 向下兼容的功能性新增
- `PATCH`: 向下兼容的问题修正

## 快速开始

### 环境要求

- **Java**: JDK 17+
- **Node.js**: 18.x+
- **MySQL**: 8.0+
- **Docker**: 20.x+

### 本地开发

#### 后端启动
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

#### 前端启动  
```bash
cd frontend
npm install
npm run dev
```

#### Docker部署
```bash
# 构建镜像
docker-compose -f Dockerfiles/docker-compose.yml build

# 启动服务
docker-compose -f Dockerfiles/docker-compose.yml up -d
```

## API文档

项目启动后访问：`http://localhost:8080/swagger-ui.html`

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/新功能`)
3. 提交代码 (`git commit -m 'feat(模块): 添加新功能'`)
4. 推送分支 (`git push origin feature/新功能`)
5. 创建 Merge Request

## 许可证

本项目采用 [MIT License](LICENSE)

## 联系方式

- 项目负责人: [联系邮箱]
- 技术支持: [技术支持邮箱]
- 问题反馈: [GitLab Issues](http://**************/fayuan/tianshuzhijian/-/issues)