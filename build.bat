@echo off
echo Starting build process...

REM Build frontend
echo Building frontend...
cd frontend
call npm run build
powershell -Command "Compress-Archive -Path 'dist\*' -DestinationPath 'dist.zip' -Force"

REM Build backend
echo Building backend...
cd ..\backend
call mvn clean package -DskipTests

REM Create release directory
echo Creating release directory...
cd ..
if exist release rmdir /s /q release
mkdir release

REM Copy files
copy frontend\dist.zip release\
copy backend\target\yuejuanzhushou-1.0.0.jar release\

echo Build completed! Files are in release directory
pause
